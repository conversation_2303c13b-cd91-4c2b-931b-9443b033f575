import { session } from '@/utils';

/** 图片路径拼接 */
export function getImagePath(imgUrl?: string) {
  if (imgUrl?.startsWith('http://') || imgUrl?.startsWith('https://')) {
    // 图片路径方案, 如果协议是完整的,则直接返回名称
    return imgUrl;
  }

  if (imgUrl) {
    return `${session.getAliyunPath()}${imgUrl}`;
  }

  return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAAAAAAZai4+AAAA90lEQVR42u3XvWoCQRQFYN//MUaDBhV/WIKQJlGwiQiKut63sVhsJFvPFb5T3uorhuGcQaTMAAsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLKw3r/PdvzlVZbVN60rQVWdvSm21F1rKftazIWvSzFlhYWNVYo5Ss730u1qSj3GOTifV5bUop40tEO8vDGh2jnZfhISLi9JGGtYuIy+Snu+2zsL665vU8bnKwZi/Vqnte1VnT1Uumfvl3qoG/OUvzbZ1yYiQdZFY1FhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFtYbsB7WnQ9DzKnnDwAAAABJRU5ErkJggg==';
}
