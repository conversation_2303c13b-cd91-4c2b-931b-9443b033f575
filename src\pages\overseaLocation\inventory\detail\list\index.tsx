import React, { useEffect, useRef, useState } from 'react';
import type { ConnectRC, Dispatch } from '@umijs/max';
import { connect } from '@umijs/max';
import { Button, Card, Input, InputNumber, Select, Tooltip } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { QueryFilter } from '@ant-design/pro-form';
import { cloneDeep, forIn } from 'lodash';
import { HiveModule } from '@portal/hive-sdk';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { QUALITYTYPE } from '@/utils/const';
import { HumpToUnderline, GetPageQuery } from '@/utils/util';
import styles from './index.less';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiQueryWarehouseOptions } from '@/pages/overseaLocation/api';
import { apiInventoryAgeList } from '../../inventoryApi';
import { apiVirtualWarehouseOptions } from '@/pages/pagesApi';

type Iprops = {
  dispatch: Dispatch;
  loading: boolean;
};

const { Item } = ProForm;

const OverseaInventoryDetailListPage: ConnectRC<Iprops> = ({ dispatch }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<any>(1);
  const tableRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [searchValues, setSearchValues] = useState<any>({});
  const { sku = '', warehouseId, warehouseCode, warehouseName } = GetPageQuery();

  useEffect(() => {
    if (sku) {
      formRef.current?.setFieldsValue({ sku, warehouseIds: [warehouseId] });
      setSearchValues({ sku, warehouseIds: [warehouseId] });
    }
  }, []);

  const columns: ProColumns[] = [
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.reservoirArea,
      key: 'warehouseAreaName',
      dataIndex: 'warehouseAreaName',
      width: 150,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.warehouseOrganization,
      key: 'warehouseCode',
      dataIndex: 'warehouseCode',
      valueType: 'select',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.transactionNote,
      key: 'docNo',
      dataIndex: 'docNo',
      // width: 220,
      search: false,
      // render: (_: any) => <span style={{ color: '#1890FF' }}>{_}</span>,
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      key: 'goodsName',
      dataIndex: 'goodsName',
      // ellipsis: true,
      width: 200,
      render: (_: any, record: any) => {
        return (
          <div>
            <div>{record.goodsName}</div>
            <div>{record.goodsEnName}</div>
            {/* <Tooltip title={record.goodsName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsName}</span>
            </Tooltip>
            <Tooltip title={record.goodsEnName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsEnName}</span>
            </Tooltip> */}
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      key: 'sku',
      dataIndex: 'sku',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.commodityQualityAssurance,
      key: 'shelfLife',
      dataIndex: 'shelfLife',
      search: false,
      width: 150,
      valueType: 'date',
      // render: (_: any, record: any) => moment(record.shelfLife).format('YYYY-MM-DD'),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.receiptBatch,
      key: 'batchNo',
      dataIndex: 'batchNo',
      width: 150,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.stockAge,
      key: 'storageAge',
      dataIndex: 'storageAge',
      width: 120,
      render: (_: any) => (
        <span>
          {_}
          {I18N.Src__Pages__Freight__Lcl.List.day}
        </span>
      ),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.qualityStatus,
      key: 'qualityType',
      dataIndex: 'qualityType',
      width: 120,
      search: false,
      render: (_: any, record: any) => {
        return <span>{QUALITYTYPE[record.qualityType]}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.quantity,
      key: 'quantity',
      dataIndex: 'quantity',
      width: 120,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.associatedUpstream,
      key: 'relaNo',
      dataIndex: 'relaNo',
      width: 250,
      search: false,
      render: (_: any, record: any) => {
        return (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.applicationForm}
              {record.applyDocNo}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.performanceOrder}
              {record.performanceDocNo}
            </div>
            {/** 后端取值错误,前端暂时隐藏 */}
            {/* <div>WMS: {record.wmsNo}</div> */}
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
      dataIndex: 'virtualWarehouseCode',
      search: false,
    },
  ];

  const handleSearchList = async (params: any) => {
    const { pageSize, current: currentPage } = params;

    const values = cloneDeep(searchValues);

    if (sku) {
      values.sku = sku;
      values.warehouseIds = [warehouseId];
      HiveModule.history.replace('/oversea-location/inventory/detail');
    }

    const { data } = await apiInventoryAgeList({
      currentPage,
      pageSize,
      condition: {
        ...values,
      },
    });
    const result = data || {};

    const sensorsData: any = {};

    forIn(params, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    // 海外仓库存库龄管理列表查询埋点
    sensorsTrack('WWL_PORTAL_OVERSEA_INVENTORY_DETAIl_LIST_SEARCH', sensorsData);

    return {
      data: result.records,
      total: result.totalSize ? Number(result.totalSize) : 0,
    };
  };

  const handleTableSearch = async (values: any) => {
    setSearchValues(values);
    setPageIndex(1);
    tableRef.current?.reload();
  };

  return (
    <PageContainer>
      <Card className={styles['inventory-detail-search-form']}>
        <QueryFilter
          defaultCollapsed={false}
          onFinish={handleTableSearch}
          onReset={() => handleTableSearch({})}
          formRef={formRef}
          submitter={{
            submitButtonProps: {
              loading,
            },
          }}
        >
          <Item
            {...{
              name: 'warehouseIds',
              label:
                I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index
                  .warehouseOrganization,
            }}
          >
            <ZSearchSelect
              {...{
                request: async (queryParam: string) =>
                  apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
                mode: 'multiple',
              }}
            />
          </Item>
          <Item
            name="goodsName"
            label={I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName}
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="sku"
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1
            }
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="batchNo"
            label={I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.receiptBatch}
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="associatedDocNo"
            label={
              I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.associatedUpstream
            }
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            label={
              I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.inventoryQuantity
            }
          >
            <div className={styles['display-inline']}>
              <Item name="qualityType" noStyle>
                <Select
                  allowClear
                  options={[
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece1,
                      value: 'GOOD',
                    },
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts1,
                      value: 'BAD',
                    },
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index
                          .abnormalParts1,
                      value: 'ERROR',
                    },
                  ]}
                />
              </Item>
              <Item name="minQuantity" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
              <div className={styles.split}>~</div>
              <Item name="maxQuantity" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
            </div>
          </Item>
          <Item label={I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.stockAge}>
            <div className={styles['display-inline']}>
              <Item name="minStorageAge" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
              <div className={styles.split}>~</div>
              <Item name="maxStorageAge" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
            </div>
          </Item>
          <Item
            {...{
              name: 'virtualWarehouseList',
              label: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
            }}
          >
            <ZSearchSelect
              {...{
                mode: 'multiple',
                request: async (queryParam: string) =>
                  apiVirtualWarehouseOptions({ queryParam }, { valueName: 'id' }),
              }}
            />
          </Item>
        </QueryFilter>
      </Card>
      <ProTable
        className={styles['inventory-detail-list']}
        request={(params: any) => {
          setLoading(true);
          const res = handleSearchList(params);

          res?.finally(() => {
            setLoading(false);
          });
          return res;
        }}
        columns={columns}
        rowKey="id"
        actionRef={tableRef}
        scroll={{ x: 'max-content' }}
        search={false}
        pagination={{
          showSizeChanger: true,
          current: pageIndex,
          defaultPageSize: 10,
          position: ['bottomLeft'],
        }}
        onChange={(pagination: any) => {
          setPageIndex(pagination.current);
        }}
      />
    </PageContainer>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.models.inventory,
}))(OverseaInventoryDetailListPage);
