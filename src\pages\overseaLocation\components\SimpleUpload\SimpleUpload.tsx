import { compUtils, useSafeStateUpdate } from '@/utils';
import type { UploadProps } from 'antd';
import { Form, message, Upload } from 'antd';
import type { UploadProps as RcUploadProps, UploadProgressEvent } from 'rc-upload/lib/interface';
import type { UploadChangeParam, UploadFile } from 'antd/lib/upload/interface';
import classnames from 'classnames';
import type { FormInstance } from 'rc-field-form';
import type { Ref } from 'react';
import React, { useRef, useImperativeHandle, useState, useEffect } from 'react';
import './SimpleUpload.less';
import { textMap } from './text-map';

/** 文件额外属性 */
type FileExtra = Record<string, any>;

/** 文件上传文件对象 */
export type TypeUploadFile<T extends FileExtra = {}, FileResponse = any> = {
  /** 额外扩展的File属性, 目前标准上传apiUploadFile会返回filePath */
  filePath?: string;
} & T &
  UploadFile<FileResponse>;

type TypeUploadRequest<T extends FileExtra = {}> = (
  fileList: TypeUploadFile<T>[],
  action?: TypeSimpleUploadRef,
) => Promise<any>;

export type TypeSimpleUploadProps<T extends FileExtra = {}> = {
  ref?: Ref<TypeSimpleUploadRef<T> | undefined>;
  /** 会自动注入loading */
  children?: React.ReactNode;
  /** 按钮额外部分 */
  btnExtra?: (params: { loading: boolean }) => React.ReactNode;
  /** 按钮额外部分位置, 默认right */
  btnExtraAlign?: 'top' | 'right';
  /** 文件后缀, 如 ['jpg', 'png']
   * @ 不传表示无限制, 会自动忽略大小写 */
  fileSuffix?: string[];
  /** 文件大小限制, 默认10mb
   * @ 和产品约定无特殊说明就是10MB
   */
  maxFileSizeMb?: number;
  /** 文件数量限制, 默认1
   * @为1时多次选择会自动替换上一个 */
  maxCount?: number;
  /** 上传请求, 有什么需求和逻辑在此自行解决 */
  uploadRequest?: TypeUploadRequest<T>;
  /** 是否手动上传; 默认 是
   * @ 如果需要自动上传, 优先考虑 customRequest
   * @ 如果用action url上传优先考虑使用sysApi/getUploadPropsConfig
   */
  allowManual?: boolean;
  /** upload-list-text 布局;
   * @ 默认在按钮下方 */
  listLayout?: 'right';
  /** 方便form.item 接入, 原来的更名为onOriginChange */
  onChange?: (fileList: TypeUploadFile<T>[]) => void;
  /** 原来的onChange */
  onOriginChange?: (
    info: UploadChangeParam<TypeUploadFile<T>>,
    action: TypeSimpleUploadRef,
  ) => void;
  /** 方便Form.Item 接入 */
  value?: TypeUploadFile<T>[];
  /** form表单,用于重置校验 */
  form?: FormInstance;
  /** 是否允许校验文件状态
   * @ 默认 false
   */
  allowValidFileStatus?: boolean;
  /** customRequest入参onProgress,用于更新进度, 仅在有customRequest时生效 */
  onProgressCustom?: (
    params: { uploadRef: React.MutableRefObject<TypeSimpleUploadRef> } & UploadProgressEvent,
    file: TypeUploadFile<T>,
  ) => void;
  /** 监听文件变化,包括组件销毁时 */
  listenFileChange?: (fileList: TypeUploadFile<T>[], prevFileList: TypeUploadFile<T>[]) => void;
} & Omit<UploadProps, 'maxCount' | 'onChange'>;

export type TypeSimpleUploadRef<T extends FileExtra = any> = {
  /** 上传中 */
  isLoading: boolean;
  /** 当前文件列表 */
  fileList: TypeUploadFile<T>[];
  /** 组件唯一实例key */
  uniqueKey: string;
  /** 手动发起请求, 仅fileList有值时调用 */
  fetchUploadRequest: () => Promise<any>;
  /** 设置文件 */
  setFileList: React.Dispatch<React.SetStateAction<Partial<TypeUploadFile<T>>[]>>;
};

/** 模块唯一 */
let globalIndex = 0;

function SimpleUpload(inProps: TypeSimpleUploadProps, ref: Ref<TypeSimpleUploadRef | undefined>) {
  const { props, loading, uploadRef, statusItemNode } = useConfig({ inProps });

  useListenFileChange({ props });

  const btn = React.isValidElement(props.children)
    ? React.cloneElement(props.children as React.ReactElement, {
        loading,
        disabled: inProps.disabled,
      })
    : props.children;
  let btnGroup = btn;

  if (inProps.btnExtra) {
    const btnExtraAlign = inProps.btnExtraAlign ?? 'right';
    const btnExtraMap = {
      top: <div onClick={(e) => e.stopPropagation()}>{inProps.btnExtra({ loading })}</div>,
      right: (
        <span
          style={{ paddingLeft: 10 }}
          onClick={(e) => {
            /** 防止额外按钮打开文件选择 */
            e.stopPropagation();
          }}
        >
          {inProps.btnExtra({ loading })}
        </span>
      ),
    };
    const nodeExtra = btnExtraMap[btnExtraAlign];

    btnGroup = (
      <>
        {btnExtraAlign === 'top' && nodeExtra}
        {btn}
        {btnExtraAlign === 'right' && nodeExtra}
      </>
    );
  }

  useImperativeHandle(ref, () => uploadRef.current);

  return (
    <>
      <Upload {...(props as any)}>{btnGroup}</Upload>
      {statusItemNode}
    </>
  );
}

export default React.forwardRef(SimpleUpload) as <T extends FileExtra = {}>(
  props: TypeSimpleUploadProps<T>,
) => JSX.Element;

function useConfig({ inProps }: { inProps: TypeSimpleUploadProps }) {
  const uploadRef = useRef<TypeSimpleUploadRef>({ fileList: [] } as any);
  const { statusItemNode, clearStatusError, uniqueKey } = useValidFileStatus({
    uploadRef,
    inProps,
  });
  const { safeUpdateFn } = useSafeStateUpdate();
  const {
    maxCount = 1,
    fileSuffix = [],
    maxFileSizeMb = 10,
    listLayout,
    allowManual = true,
    uploadRequest,
    onChange,
    ...uploadProps
  } = inProps;
  const [innerFileList, setInnerFileList] = useState<TypeUploadFile[]>([]);
  const fileList = inProps.value !== undefined ? inProps.value : innerFileList;
  const [loading, setLoading] = useState(false);
  const setFileList: NonNullable<TypeSimpleUploadProps['onChange']> = (inFileList) => {
    setInnerFileList(inFileList);
    onChange?.(inFileList);
  };
  const defaultProps = {
    fileList,
    maxCount,
    disabled: loading,
    className: classnames(
      'zw-simple-upload',
      listLayout === 'right' && 'zw-simple-upload__list-right',
    ),
    beforeUpload(file, chooseFileList /** 当前选中待beforeUpload的文件数组 */) {
      const { name } = file;
      const arr = name.split('.');
      const suffix = arr[arr.length - 1]?.toLocaleLowerCase?.();
      const sizeMb = file.size / 1024 / 1024;
      const curFileList = uploadRef.current.fileList;

      /** 已有数量+chooseFileList.length 超过最大数量, 全部抛弃,提示文件数量超出最大限制 */
      if (curFileList.length + chooseFileList.length > maxCount && maxCount > 1) {
        const destroyKey = 'simpleUploadMaxCount';

        message.destroy(destroyKey); /** 销毁历史提示 */
        message.error({
          content: textMap.limitCount(maxCount),
          key: destroyKey,
        });
        return Upload.LIST_IGNORE;
      }

      if (fileSuffix.length > 0 && fileSuffix.includes(suffix) === false) {
        message.error(textMap.limitFileType(fileSuffix));
        return Upload.LIST_IGNORE;
      }
      if (sizeMb > maxFileSizeMb) {
        message.error(textMap.limitFileSize(maxFileSizeMb));
        return Upload.LIST_IGNORE;
      }
      if (maxCount === 1 && curFileList.length === 1) {
        /** 当maxCount === 1时, 再次选择文件是直接替换, 但其实原文件被移除了, 所以触发一次onRemove */
        curFileList[0].status = 'removed';
        uploadProps.onRemove?.(curFileList[0]);
      }
      return allowManual ? false : true;
    },
    onChange(info) {
      /** 内部组件Upload每次文件file.status变化都会触发onChange */
      uploadRef.current.fileList = info.fileList;
      /** inProps.onOriginChange触发要晚于inProps.onChange事件, 因为setFileList内部包含inProps?.onChange调用 */
      setFileList(info.fileList);
      inProps.onOriginChange?.(info, uploadRef.current);
      /** 有可能onOriginChange对FileList数据有处理,再次触发onChange, 但是不要去改变setFileList和onOriginChange的顺序 */
      inProps?.onChange?.(info.fileList);
      clearStatusError();
    },
    /** 这里采用解构赋值, 确保onProgress不会以undefined传给Upload组件 */
    ...(inProps.customRequest
      ? ({
          onProgress(e, originFileObj: TypeUploadFile<any>) {
            const { percent } = e;
            const file = uploadRef.current.fileList.find(
              (item) => item?.originFileObj === originFileObj,
            );

            if (file) {
              file.percent = percent;
              file.status = 'uploading';
              if (percent && percent >= 100) {
                file.url = originFileObj.url;
              }
            }

            inProps.onProgressCustom?.({ ...e, uploadRef }, file);
            uploadRef.current?.setFileList?.([...uploadRef.current.fileList]);
          },
        } as RcUploadProps)
      : undefined),
  } as UploadProps;
  const props = compUtils.propsMerge(defaultProps, uploadProps) as TypeSimpleUploadProps;

  async function fetchUploadRequest() {
    const curFileList = uploadRef.current.fileList;

    if (props.uploadRequest !== undefined || curFileList.length === 0) return;

    const res = uploadRequest?.(curFileList, uploadRef.current);

    if (res?.finally) {
      setLoading(true);
      res?.finally(
        safeUpdateFn(() => {
          setFileList([...curFileList.filter((item) => item.status === 'done')]);
          setLoading(false);
          clearStatusError();
        }),
      );
    }
    return res;
  }

  Object.assign(uploadRef.current, {
    isLoading: loading,
    fileList,
    uniqueKey,
    fetchUploadRequest,
    setFileList: setFileList as TypeSimpleUploadRef['setFileList'],
  });

  return {
    props,
    loading,
    fileList,
    fetchUploadRequest,
    uploadRef,
    statusItemNode,
  };
}

/** 校验文件状态
 * 利用无标签FormItem特性, 将error传递至外层Form.Item
 */
function useValidFileStatus({
  inProps,
  uploadRef,
}: {
  inProps: TypeSimpleUploadProps;
  uploadRef: React.MutableRefObject<TypeSimpleUploadRef>;
}) {
  const form = Form.useFormInstance ? Form.useFormInstance() : inProps.form;
  const [uniqueKey] = useState(() => `_ZwSimpleUpload_${globalIndex++}`);
  const clearStatusError = () => {
    if (inProps.allowValidFileStatus && !Form.useFormInstance && !inProps.form) {
      console.warn('antd版本过低, 不支持 Form.useFormInstance 获取上下文form实例, 请手动传入');
    }
    let isAllDone = true;

    for (const file of uploadRef.current.fileList) {
      file.status !== 'done' && (isAllDone = false);
    }
    if (inProps.allowValidFileStatus && form && isAllDone) {
      /** 这里不可以使用  validateFields([uniqueKey]), 会触发不必要的校验 */
      form?.setFields?.([{ name: uniqueKey, errors: [] }]);
    }
  };
  const statusItemNode = inProps.allowValidFileStatus ? (
    <Form.Item
      {...{
        /** 不会产生标签元素, error校验仅在form.validateFields()触发 */
        noStyle: true,
        name: uniqueKey,
        rules: [
          {
            async validator() {
              if (Array.isArray(uploadRef.current.fileList) === false) {
                return;
              }
              const fl = uploadRef.current.fileList;
              const errorMap = {
                error: textMap.errorFileError,
                uploading: textMap.errorFileUploading,
              } as const;

              for (const f of fl) {
                const error = errorMap[f.status as keyof typeof errorMap];

                if (error) {
                  throw new Error(error);
                }
              }

              if (uploadRef.current.isLoading) {
                throw new Error(errorMap.uploading);
              }
            },
          },
        ],
      }}
    >
      <></>
    </Form.Item>
  ) : null;

  return {
    statusItemNode,
    clearStatusError,
    uniqueKey,
  };
}

/** 监听文件变化, 包括销毁时触发 */
function useListenFileChange({ props }: { props: TypeSimpleUploadProps }) {
  const prevFileList = useRef<TypeUploadFile[]>([]);

  useEffect(() => {
    props?.listenFileChange?.(props.fileList || [], prevFileList.current);
    prevFileList.current = props.fileList || [];
  });
  useEffect(() => {
    return () => {
      props?.listenFileChange?.([], prevFileList.current);
    };
  }, [props?.listenFileChange]);
}
