module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'build',
        'chore',
        'ci',
        'docs',
        'feat',
        'fix',
        'improvement', // 改进
        'perf', // 性能
        'refactor', // 重构
        'revert', // 恢复
        'style',
        'test',
        'WIP',
        'jira',
        'merge',
      ],
    ],
    'type-case': [
      2,
      'always',
      [
        'lower-case', // default
        'upper-case', // UPPERCASE
      ],
    ],
    'subject-case': [
      2,
      'never',
      [
        /* 禁止如下英文格式输入 */
        'snake-case', // snake_case
      ],
    ],
    'header-max-length': [2, 'always', 150],
  },
};
