import { includesInArray } from '@/pages/overseaLocation/utils';

const emptyText = '-' as const;

/** 处理空值渲染 */
export function emptyRender<T>(value: T | null | undefined, render?: (item: T) => React.ReactNode) {
  return includesInArray(['', undefined, null], value) ? emptyText : render ? render(value) : value;
}

export const eR = emptyRender;

/** 处理数组空值渲染 */
export function emptyRenderArray<T>(
  arr: T[] | null | undefined,
  map: (item: T, i: number, arr: T[]) => React.ReactNode,
) {
  return arr && arr.length > 0 ? arr.map(map) : emptyText;
}

/** 处理数组空值渲染Join拼接 */
export function emptyRenderArrayJoin<T>(arr: T[] | null | undefined, joinStr: string) {
  const validArr = (arr || []).filter(
    (item) => includesInArray(['', undefined, null], item) === false,
  );

  return validArr.length > 0 ? validArr.join(joinStr) : emptyText;
}
