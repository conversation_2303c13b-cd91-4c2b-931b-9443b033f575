export default {
  OutWarehouseList: {
    errorMessage: '报错信息',
    addressType: '地址类型：',
    recipientEmail: '收件人邮编：',
    recipientProvince: '收件人省/州：',
    recipientCountry: '收件人国家：',
    addressee: '收件人：',
    residence: '住宅',
    nonResidential: '非住宅',
    consigneesLetter: '收货人信息',
    commodityDetails: '商品明细',
    platformShop: '平台店铺ID：',
    platformSeller: '平台卖家ID：',
    customerAssociation: '客户关联单：',
    customerSales: '客户销售单：',
    orderPlatform: '订单平台：',
    orderSource: '订单来源：',
    clickToView: '点击查看',
    logisticsProgress: '物流进展：',
    trackingSheetNo: '追踪单号：',
    logisticsChannel: '物流渠道：',
    logisticsType: '物流类型：',
    batchRetry: '批量重试',
    createIssue: '创建出库申请',
    addressee1: '收件人',
    customerSales1: '客户销售单号',
    customerAssociation1: '客户关联单号',
    retry: '重试',
    applyForWholeDocument: '申请整单取消',
    retrySucceeded: '重试成功',
    batchRetry1: '批量重试成功',
    chineseSymbols: '物流状态类型',
    chineseSymbols1: '物流拦截',
    chineseSymbols2: '合计费用：',
    chineseSymbols3: '尾程费用：',
    chineseSymbols4: '库内费用：',
    chineseSymbols5: '费用金额',
    chineseSymbols6: '跟踪单号：',
    chineseSymbols7: '物流类型',
    chineseSymbols8: '订单平台',
    chineseSymbols9: '请输入，支持批量检索',
    export: '导出',
  },
  OutboundCompensateDetail: {
    chineseSymbols: '文件集合',
    warehouse: '仓库CODE',
    chineseSymbols1: '客户名称',
    chineseSymbols2: '基础信息',
  },
} as const;
