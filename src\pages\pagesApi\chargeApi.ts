import { promiseCacheApi } from '@/utils';
import { request } from 'umi';

/**
 * 中台提供, 获取币种下拉
 * @ 已做 promiseCacheApi 缓存
 *  */
export const apiCustomerCurrencyOptions = promiseCacheApi(async (params: { keyword?: string }) => {
  return request<
    NsApi.TypeResponseData<
      {
        uniqueCode?: string;
        code?: string;
        cnName?: string;
        isEnable?: number;
        conversionCurrency?: string;
      }[]
    >
  >('/zouwu-oms-system/portal/currency/standardCurrencyList', {
    method: 'GET',
    params,
  }).then((res) => {
    const { data } = res;

    return (data || []).map((item) => {
      return {
        ...item,
        label: item.code,
        value: item.code,
      };
    });
  });
});
