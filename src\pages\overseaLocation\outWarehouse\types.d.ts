/* eslint-disable @typescript-eslint/no-shadow */
/** 出库订单列表 */
type TypezOutWarehouseTable = {
  /** 出库单号 */
  id?: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 业务类型（201-标准出库，202-退仓出库,  203-TOB出库） */
  businessType?: number;
  /** 单据状态 */
  orderStatus?: number;
  /** 单据状态后端匹配名称 */
  orderStatusName?: string;
  /** 单据状态归属tabKey */
  orderStatusMapping?: string;
  /** 单据状态-value */
  orderStatusValue?: string;
  /** 订单类型-value */
  orderTypeValue?: string;
  /** 订单环节 */
  orderLinkType?: number;
  /** 订单人姓名 */
  orderOwnerName?: string;
  /** 订单人证件号及类型 */
  orderOwnerIdNo?: string;
  /** 订单人证件号及类型 */
  orderOwnerIdType?: number;
  /** 异常单类型 */
  errorType?: number;
  /** 发货仓库编码 */
  deliveryWarehouseCode?: string;
  /** 发货仓库名称 */
  deliveryWarehouseName?: string;
  /** 发货仓库类型 */
  deliveryWarehouseType?: number;
  /** 下单仓库Code */
  createDeliveryWarehouseCode?: string;
  /** 下单仓库名称 */
  createDeliveryWarehouseName?: string;
  /** 下单仓库id */
  createDeliveryWarehouseId?: string;
  /** 下单仓库类型 */
  createDeliveryWarehouseType?: number;
  /** 虚拟仓库code */
  virtualWarehouseCode?: string;
  /** 虚拟仓库名称 */
  virtualWarehouseName?: string;
  /** 订单来源 */
  orderSource?: number;
  /** 订单来源平台名称 */
  orderSourcePlatformName?: string;
  /** 客户销售单号 */
  customerSalesNo?: string;
  /** 客户关联单号 */
  customerRelatedNo?: string;
  /** 平台卖家id */
  platformSellerId?: string;
  /** 平台店铺id */
  platformStoreId?: string;
  /** 总箱数 */
  totalBoxQuantity?: number;
  /** 总件数 */
  totalQuantity?: number;
  /** 待执行数量 */
  notExecutedQuantity?: number;
  /** 已执行数量 */
  executedQuantity?: number;
  /** 派送服务名称 */
  dispatchServiceName?: number;
  /** 派送服务商 */
  dispatchServiceProvider?: string;
  /** 追踪单号 */
  trackingNo?: string;
  /** 追踪物流进展 */
  trackingProgress?: string;
  /** 创建时间 */
  createDate?: number;
  /** 更新时间 */
  updateDate?: number;
  /** 履约完结天数 */
  fulfillmentDays?: number;
  /** 预计出库日期 */
  expectOutboundDate?: string;
  /** 订单备注（仅仓库人员可见） */
  warehouseRemark?: string;
  /** 订单取消状态 */
  cancelOrderStatus?: number;
  /** 申请人ID */
  createById?: string;
  /** 申请人名称 */
  createByName?: string;
  /** 申请方公司id */
  companyId?: string;
  /** 申请方公司 */
  companyName?: string;
  /** 订单销售金额 */
  salesAmount?: number;
  /** Ebay交易ID号 */
  ebaySalesId?: string;
  /** Ebay平台交易单号 */
  ebayPlatformSalesNo?: string;
  /** 订单销售币种 */
  salesCurrency?: string;
  /** 备注 */
  remark?: string;
  /** 最后修改人 */
  updateByName?: string;
  /** 索赔币种，取得仓库币种 */
  currency?: string;
  /** 履约单号 */
  complianceNo?: string;
  /** 索赔状态 */
  compensateStatus?: string;
  /** 索赔状态后端翻译 */
  compensateStatusName?: string;
  /** 物流进展 */
  latestNode?: string;
  /** 物流是否拦截标记 */
  logisticsIntercepted?: boolean;
  /** 费用金额类目 */
  chargeAmount?: {
    /** 申请单号 */
    podOrderNo?: string;
    /** 库内费用 */
    warehouseAmount?: number;
    /** 库内币种 */
    warehouseCurrency?: string;
    /** 尾程费用 */
    expressAmount?: number;
    /** 尾程币种 */
    expressCurrency?: string;
    /** 总金额 */
    totalAmount?: number;
    /** 总金额币种 */
    totalCurrency?: string;
  };
  /** 物流信息 */
  orderExpressDTO?: {
    /** 发件人国家 */
    senderCountry?: number;
    /** 发件人省 */
    senderProvince?: number;
    /** 发件人城市 */
    senderCity?: number;
    /** 发件人详细地址1 */
    senderAddress1?: string;
    /** 发件人详细地址2 */
    senderAddress2?: string;
    /** 发件人详细地址3 */
    senderAddress3?: string;
    /** 发件人姓名 */
    senderName?: string;
    /** 发件人电话 */
    senderPhoneNumber?: string;
    /** 发件人公司 */
    senderCompanyName?: string;
    /** 发件人详细地址 */
    senderAddressList?: string[];
    /** 收件人国家 */
    recipientCountry?: string;
    /** 收件人省/州 */
    recipientProvince?: string;
    /** 收件人城市 */
    recipientCity?: string;
    /** 收件人详细地址列表 */
    recipientAddressList?: string[];
    /** 收件人姓名 */
    recipientName?: string;
    /** 收件人证件类型（1-身份证，2-护照，99-其他） */
    recipientIdType?: number;
    /** 收件人证件号码 */
    recipientIdNo?: string;
    /** 收件人联系电话列表 */
    recipientPhoneNumberList?: string[];
    /** 收件人邮箱 */
    recipientEmail?: string;
    /** 收件人邮编 */
    recipientPostcode?: string;
    /** 收件人分邮编 */
    recipientBranchPostcode?: string;
    /** 收件人EORI号 */
    recipientEoriNo?: string;
    /** 收件人公司 */
    recipientCompanyName?: string;
    /** 收件人是否FBA */
    recipientIsFba?: boolean;
    /** 门牌号 */
    recipientHouseNumber?: string;
    /** 签名服务 */
    signature?: boolean;
    /** 是否带电 */
    electric?: boolean;
    /** 保险服务 */
    insured?: boolean;
    /** 投保金额 */
    insuredAmount?: number;
    /** 投保币种 */
    insuredAmountCurrency?: string;
    /** 收件人地址是否住宅地址 0=否 1=是 */
    residential?: boolean;
    /** 后端翻译是否偏远 */
    remoteName?: string;
    /** 偏远 0：正常 1：偏远 2：超偏远 */
    remote?: number;
    /** 签署服务 */
    signatureType?: string;
    /** 计费重 */
    billingWeight?: number;
    /** 计费重单位 */
    billingWeightUnit?: string;
    /** 实际重量 */
    actualWeight?: number;
    /** 实际重量单位 */
    actualWeightUnit?: string;
    /** 实际体积 */
    actualVolume?: number;
    /** 创建人名 */
    actualVolumeUnit?: string;
    /** 客户记账码 */
    accountNumber?: string;
    /** 美国的区号 */
    zone?: string;
  };
  /** 商品清单 */
  goodsList?: import('./components').TypeOutboundGoodsTB[];
  /** 包裹信息 | 包裹清单 */
  packageList: NsOutboundDetail.TypeOutboundPackageItem[];
  /** 配送信息 */
  deliveryDTOList?: NsOutbound.TypeDeliveryInfo[];
};

/** 出库单详情 */
type TypeOutboundDetail = NsOutboundDetail.TypeOutboundDetailInner;

/** 出库详情对象很多, 需要建立namespace防止污染, 方便复用 */
declare namespace NsOutboundDetail {
  type TypeOutboundDetailInner = TypezOutWarehouseTable & {
    /** 取消记录 */
    orderCancelRecord?: TypeOrderCancelRecord;
    /** 派送单列表 */
    deliveryList?: [NsOutbound.TypeDeliveryInfo];
    /** 订单节点列表 */
    orderStatusList?: TypeOrderOutStatusData[];
    /** 包裹信息 | 包裹清单 */
    packageList: TypeOutboundPackageItem[];
    /** 仓库履约 */
    performanceList?: TypeOrderOutPerformanceData[];
  };

  /** 订单取消记录 */
  type TypeOrderCancelRecord = {
    /** 订单取消状态*/
    cancelOrderStatus?: number;
    /** 创建人id */
    createById?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 异常原因, 门户不展示 */
    errorMsg?: string;
  };

  /** 订单节点列表 */
  type TypeOrderOutStatusData = {
    /** 创建人ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 订单状态（节点） 201-建单，202-订单审核，203-通知仓库出库，204-已发货，205-履约完结，206-费用确认，207-结单，208-已取消 */
    orderStatus?: number;
  };

  /** 包裹信息 | 包裹清单 */
  type TypeOutboundPackageItem = {
    /** 主键 id */
    id?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 履约单号 */
    complianceNo?: string;
    /** 包裹编号 */
    packageCode?: string;
    /** 包裹长度（cm） */
    packageLength?: number;
    /** 包裹宽度（cm） */
    packageWidth?: number;
    /** 包裹高度（cm） */
    packageHeight?: number;
    /** 包裹重量（kg） */
    packageWeight?: number;
    /** 投保金额 */
    insuredAmount?: number;
    /** 子追踪单号 */
    trackingNo?: string;
    /** label附件 */
    file?: NsOutbound.TypeFileItem;
    /** 包裹商品列表 */
    packageGoodsList?: {
      /** id */
      id?: number;
      /** SKU */
      sku?: string;
      /** 商品类型 */
      goodsType?: number;
      /** 商品类型 */
      combinedGoodsSku?: string;
      /** 商品图片 */
      goodsPicture?: string;
      /** 商品名称 */
      goodsName?: string;
      /** 商品英文名称 */
      goodsEnName?: string;
      /** 平台编码 */
      platformCode?: string;
      /** 商品申报价值 */
      goodsValue?: number;
      /** 价值单位 */
      valueUnit?: string;
      /** 商品净重 */
      goodsWeight?: number;
      /** 重量单位 */
      weightUnit?: string;
      /** 商品长度 */
      goodsLength?: number;
      /** 商品宽度 */
      goodsWidth?: number;
      /** 商品高度 */
      goodsHeight?: number;
      /** 体积单位 */
      volumeUnit?: string;
      /** 总件数 */
      totalQuantity?: number;
      /** 删除标志 */
      deleted?: boolean;
    }[];
  };

  /** 仓库履约 */
  type TypeOrderOutPerformanceData = {
    /** 履约单号 */
    complianceNo?: string;
    /** 商品id */
    orderGoodsId?: number;
    /** 好件数量 */
    goodQuantity?: number;
    /** 坏件数量 */
    badQuantity?: number;
    /** 异常件数量 */
    errorQuantity?: number;
    /** 库存流水号 */
    serialNo?: string;
    /** WMS签收单据号 */
    wmsNo?: string;
    /** 商品Name */
    goodsName?: string;
    /** 商品英文名称 */
    goodsEnName?: string;
    /** 已执行数量 */
    executedQuantity?: number;
    /** 执行类型 */
    fulfillmentType?: string;
    /** remark */
    remark?: string;
    /** 商品图片 */
    goodsPicture?: string;
    /** */
    stockDtoList?: TypeOrderOutPerformanceNum[];
    /** 全部数量 */
    totalQuantity?: number;
  };

  /** 出库单数量详情 */
  type TypeOrderOutPerformanceNum = {
    /** 履约单号 */
    complianceNo?: string;
    /** 好件数量 */
    goodQuantity?: number;
    /** 坏件数量 */
    badQuantity?: number;
    /** 异常件数量 */
    errorQuantity?: number;
    /** 库存流水号 */
    serialNo?: string;
    /** WMS签收单据号 */
    wmsNo?: string;
    /** 触发时间 */
    createDate?: string;
  };
}
