import React from 'react';
import { Form, InputNumber, Space, Table } from 'antd';
import classnames from 'classnames';
import { LoadButton, SearchAutoComplete } from '@/components';
import { cssFormListTable } from '@/styles';
import { asyncCheckRepeat, overseaRulesMap } from '@/utils-business';
import { apiGoodsSkuOptions } from '@/pages/pagesApi';
import { ruleUtils } from '@/utils';
import { isOMS, isPortal } from '@/oms-portal-diff-import';

const { List: FormList, Item: FormItem } = Form;

type TypeProps = {};

const listName = 'orderGoodsList';

/** 商品清单 */
export default function FormGoodsOrder(props: TypeProps) {
  const form = Form.useFormInstance();

  return (
    <div className={classnames(cssFormListTable.FormListTable)}>
      <FormList
        {...{
          name: listName,
          initialValue: [],
          rules: [
            {
              async validator(rule, val) {
                if (isOMS && (Array.isArray(val) === false || val.length > 50 || val.length < 1)) {
                  throw new Error('商品不能少于1条, 且不超过50条');
                }
              },
            },
          ],
        }}
      >
        {function (fieldList, action, { errors }) {
          const dataSource = form.getFieldValue(listName) || [];

          // console.log('fieldList-->', fieldList);
          return (
            <>
              <Table
                {...{
                  scroll: { x: 'max-content' },
                  dataSource: fieldList.map((item) => ({ key: item.key })),
                  pagination: false,
                  columns: [
                    {
                      title: '商品SKU',
                      dataIndex: 'sku',
                      width: 360,
                      render(_val, record, index) {
                        return (
                          <FormItem
                            {...{
                              label: '商品SKU',
                              name: [index, 'sku'],
                              rules: [
                                overseaRulesMap.overseaSKU[0],
                                {
                                  async validator(rule, val) {
                                    return asyncCheckRepeat(form.getFieldValue(listName), {
                                      findValue: val,
                                      valueName: 'sku',
                                    });
                                  },
                                },
                              ],
                            }}
                          >
                            <SearchAutoComplete
                              {...{
                                disabled: isPortal,
                                autoLoad: false,
                                async request(sku: string) {
                                  return apiGoodsSkuOptions({ sku });
                                },
                              }}
                            />
                          </FormItem>
                        );
                      },
                    },
                    {
                      title: '件数(PCS)',
                      dataIndex: 'totalQuantity',
                      render(val, record, index) {
                        return (
                          <FormItem
                            {...{
                              label: '件数(PCS)',
                              name: [index, 'totalQuantity'],
                              rules: [
                                ruleUtils.ruleNoZeroPositiveInteger,
                                { required: true, type: 'number', min: 1, max: 100 },
                              ],
                              children: <InputNumber {...{ disabled: isPortal }} />,
                            }}
                          />
                        );
                      },
                    },
                    {
                      title: '操作',
                      dataIndex: 'operate',
                      width: 100,
                      render(val, record, index) {
                        return (
                          <Space size={20} align={'start'}>
                            <LoadButton
                              {...{
                                disabled: isPortal,
                                popconfirmProps: {
                                  title: '确认删除吗？',
                                },
                                onClick() {
                                  action.remove(index);
                                },
                              }}
                            >
                              删除
                            </LoadButton>
                          </Space>
                        );
                      },
                    },
                  ],
                }}
              />
              {isOMS && (
                <div
                  className={cssFormListTable.btnAdd}
                  onClick={() => {
                    action.add({});
                  }}
                >
                  + 新增
                </div>
              )}
              <Form.ErrorList errors={errors} />
            </>
          );
        }}
      </FormList>
    </div>
  );
}
