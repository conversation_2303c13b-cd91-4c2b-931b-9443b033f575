import { request } from 'umi';
import { downloadFileByFullUrl, dateUtils } from '@/utils';

/** 批量出库列表 */
export function apiBatchOutWarehouseList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 状态（1=待下发，2=已下发，3=异常） */
      state?: number;
      /** 业务类型（201=标准出库） */
      businessType?: string;
    };
  }>,
) {
  /** 历史接口 '/api/website/web/outstorehouse/listByPage.do' */
  return request<NsApi.TypeResponseList<TypezBatchOutWarehouseTB[]>>(
    '/zouwu-oms-order/portal/outbound/import/listByPage',
    {
      data,
      method: 'POST',
    },
  ); /* .then((res) => dateUtils.listDateFormatter(res, ['createDate'])); */
}

/** 批量出库详情列表 */
export function apiBatchOutWarehouseDetailList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 订单导入记录id */
      id?: number;
      /** 业务类型（201=标准出库） */
      businessType?: string;
      /** 订单状态 */
      orderStatus?: number;
      /** 配送服务类型 */
      distributionServiceType?: number;
    };
  }>,
) {
  return request<
    NsApi.TypeResponseData<{
      detailConditionPage: {
        records: any[];
        totalSize: number;
      };
      orderImportRecord: TypezOrderImportRecord;
    }>
  >('/zouwu-oms-order/portal/outbound/outboundOrderImportPage', {
    data,
    method: 'POST',
  });
}

/** 删除全部未下发订单 */
export function apiBatchOutWarehouseRemoveAll(data: { id: number }) {
  /** 历史接口 '/api/website/web/outstorehouse/deleteAll.do' */
  return request('/zouwu-oms-order/portal/outbound/import/deleteAll', { data, method: 'POST' });
}

/** 删除未下发订单 */
export function apiBatchOutWarehouseRemove(data: { id: string }) {
  return request('/api/website/web/outstorehouse/delete.do', { data, method: 'POST' });
}

/** 删除批量出库导入的单条订单 */
export function apiBatchOutWarehouseRemoveOne(data: { id?: string }) {
  /** 历史接口 '/api/website/web/outstorehouse/deleteById.do' */
  return request('/zouwu-oms-order/portal/outbound/import/deleteById', { data, method: 'POST' });
}

/** 批量导入 */
export function apiBatchOutWarehouseImport(data: { file: Blob; directory: string }) {
  return request('/zouwu-oms-order/portal/outbound/create/imports/batchImportOrder', {
    data,
    method: 'UPLOAD',
  });
}

/** 上传自供面单 */
export function apiBatchOutWarehouseUploadFaceSheet(data: {
  outboundOrderId: string;
  directory: 'overseaLocation';
  file: Blob;
}) {
  return request('/zouwu-oms-order/portal/upload/faceSheet', { data, method: 'UPLOAD' });
}

/** 批量上传面单 */
export function apiBatchOutWarehouseBatchImportFaceSheet(data: {
  recordId: string;
  directory: 'overseaLocation';
  file: Blob;
}) {
  return request<
    NsApi.TypeResponseData<{
      failedCount: number;
      succeedCount: number;
      totalCount: number;
    }>
  >('/zouwu-oms-order/portal/batchImportFaceSheet', {
    data,
    method: 'UPLOAD',
  });
}

/** 批量下发(批量创建订单) */
export function apiBatchOutWarehouseSubmitRecord(data: {
  /** 导入ID */
  id?: string;
  /** 订单ID */
  orderIds?: string[];
}) {
  /** 历史接口 '/api/website/web/outstorehouse/submitOutboundOrderImportRecord.do' */
  return request('/zouwu-oms-order/portal/outbound/import/submitOutboundOrderImportRecord', {
    data,
    method: 'POST',
  });
}

/** 下载批量出库模版 */
export function apiBatchOutWarehouseGetTemplate() {
  /** 历史接口 '/api/website/web/outstorehouse/getTemplate.do' */
  return request('/zouwu-oms-order/portal/outbound/import/getTemplate', {
    method: 'GET',
  }).then(async ({ data }) => {
    /** result 应该是个完整路径 */
    const pathname = data;
    const arr = pathname.split('/');
    const fileName = arr[arr.length - 1];

    return downloadFileByFullUrl(data, fileName);
  });
}

/** 批量出库单保存草稿
 * @deprecated 废弃, 统一为 apiOutWarehouseSaveDraft
 */
export function apiBatchOutOrderSaveDraft(data: any) {
  return request('/api/website/web/outstorehouse/editOutBoundOrder.do', { data, method: 'POST' });
}

/** 批量出库单提交并下发
 * @编辑页面使用
 * @deprecated 已废弃
 */
export function apiBatchOutOrderSubmit(data: any) {
  return request('/api/website/web/outstorehouse/submitByImport.do', { data, method: 'POST' });
}
