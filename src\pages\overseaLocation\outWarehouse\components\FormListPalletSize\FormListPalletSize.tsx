import React, { useEffect, useMemo } from 'react';
import { Form, Input, InputNumber, Space, Table } from 'antd';
import classnames from 'classnames';
import { LoadButton, SearchSelect } from '@/components';
import { cssFormListTable } from '@/styles';
import { includesInArray, ruleUtils, typeUtils } from '@/utils';
import { apiMapDictType } from '@/api';
import { EnumOutboundBusinessType } from '../../OutboundEnum';
import type { TypeOutboundGoodsTB } from '../FormGoodsChoose';
import { TextValue } from '@/views';

const { List: FormList, Item: FormItem } = Form;

type TypeProps = {
  /** 弹窗修改展示, 有特殊逻辑, 默认 false */
  isModalModify?: boolean;
};

/** NamePathDeliveryInfo */
function nPDelivery<T>(x: T): ['deliveryInfo', T] {
  return ['deliveryInfo', x];
}

const listName = nPDelivery('palletSizeList');

/** 托盘尺寸功能开发 */
export default function FormListPalletSize(props: TypeProps) {
  const { isModalModify = false } = props;
  const form = Form.useFormInstance();
  const isPalletize = Form.useWatch(nPDelivery('isPalletize'), form) === true;
  const goodsList = Form.useWatch('goodsList', form) as TypeOutboundGoodsTB[];
  const businessType = Form.useWatch('businessType', form);
  const isTOB = Number(businessType) === EnumOutboundBusinessType.tob;
  const palletSizeList = Form.useWatch(listName, form) as NsOutbound.TypePalletSizeItem[];
  /** 仅TOB单且 打托 有托盘尺寸 */
  const hasPalletSize = isTOB && isPalletize;
  const { skuOptions } = useMemo(
    function () {
      /** 监听商品变化, 修改托盘尺寸 */
      const frontIsInit = form.getFieldValue('frontIsInit') === true;
      /** 避免闭包陷阱, 不要从外部goodsList取值 */
      const curGoodsList: TypeOutboundGoodsTB[] = form.getFieldValue('goodsList') || [];

      if (isPalletize !== true) {
        return { skuOptions: [] };
      }
      const skuOptionsAll: {}[] = [];
      /** 是否有商品存在打托设置 */
      let hasGoodsPallet = false;

      curGoodsList.forEach((item) => {
        skuOptionsAll.push({ label: item.sku, value: item.sku });
        if (Boolean(item.goodsPallet?.palletType)) {
          hasGoodsPallet = true;
        }
      });

      if (frontIsInit) {
        // console.log('goodsList-->', goodsList, hasGoodsPallet);
        form.setFields([
          /** 当商品存在托盘维护时, 按sku生成行数据, 否则按默认数据生成 */
          hasGoodsPallet
            ? {
                name: listName,
                value: curGoodsList.map((item) => ({
                  palletSizeType: item.goodsPallet?.palletType,
                  palletSizeQuantity:
                    item.totalQuantity && item.goodsPallet?.goodsNum
                      ? Math.ceil(item.totalQuantity / item.goodsPallet.goodsNum)
                      : '',
                  remark: item.goodsPallet?.remark,
                  skuList: [item.sku],
                })),
              }
            : /** 仅生成默认数据 */ { name: listName, value: [{ palletSizeType: '10' }] },
        ]);
      }

      return { skuOptions: skuOptionsAll };
    },
    [isPalletize, goodsList],
  );

  useEffect(
    function () {
      /** 监听托盘尺寸变化, 修改打托数 */
      if (isPalletize !== true) {
        return;
      }
      const num = (palletSizeList || []).reduce((prev, cur) => {
        /** 求和计算, 区分值是否有效, 无效时保持值为空 */
        if (typeUtils.isSafeNumOrNumString(cur.palletSizeQuantity) === false) {
          return prev;
        }
        return (Number(prev) + Number(cur.palletSizeQuantity)).toString();
      }, '');

      form.setFields([{ name: nPDelivery('palletQuantity'), value: num }]);
      form.validateFields([listName]);
    },
    [isPalletize, palletSizeList],
  );

  if (hasPalletSize === false) {
    return null;
  }

  return (
    <div className={classnames(cssFormListTable.FormListTable)}>
      <Form.Item hidden>
        <TextValue {...{ label: '是否初始化完成', name: 'frontIsInit' }} />
      </Form.Item>
      <FormItem label="托盘尺寸">
        <FormList
          {...{
            name: listName,
            initialValue: [],
            rules: [
              {
                async validator(rule, val) {
                  if (Array.isArray(val) === false) {
                    return;
                  }
                  /** 无指定sku行数 */
                  let skuEmptyRowNum = 0;
                  let skuTotal = 0;
                  const skuList: string[] = [];

                  val.forEach(function (item) {
                    if (
                      includesInArray([undefined, null], item.skuList) ||
                      item.skuList?.length === 0
                    ) {
                      skuEmptyRowNum += 1;
                    }
                    skuTotal += (item.skuList || []).length;
                    Array.prototype.push.apply(skuList, item.skuList || []);
                  });
                  if (skuEmptyRowNum > 1) {
                    throw new Error('无指定sku的托盘尺寸只能有一行');
                  }

                  if (skuList.length !== Array.from(new Set(skuList)).length) {
                    throw new Error('不同行存在重复的sku');
                  }
                },
              },
            ],
          }}
        >
          {function (fieldList, action, { errors }) {
            const dataSource = form.getFieldValue(listName) || [];

            // console.log('fieldList-->', fieldList);
            return (
              <>
                <Table
                  {...{
                    dataSource: fieldList.map((item) => ({ key: item.key })),
                    pagination: false,
                    columns: [
                      {
                        title: '托盘尺寸',
                        dataIndex: 'palletSizeType',
                        width: 210,
                        render(val, record, index) {
                          return (
                            <FormItem
                              {...{
                                label: '托盘尺寸',
                                name: [index, 'palletSizeType'],
                              }}
                            >
                              <SearchSelect {...{ request: apiMapDictType.palletSizeType }} />
                            </FormItem>
                          );
                        },
                      },
                      {
                        title: '尺寸托数',
                        dataIndex: 'palletSizeQuantity',
                        width: 200,
                        render(val, record, index) {
                          return (
                            <FormItem
                              {...{
                                label: '尺寸托数',
                                name: [index, 'palletSizeQuantity'],
                                rules: [
                                  { required: isModalModify },
                                  ruleUtils.ruleNoZeroPositiveInteger,
                                ],
                              }}
                            >
                              <InputNumber stringMode />
                            </FormItem>
                          );
                        },
                      },
                      {
                        title: '指定SKU',
                        dataIndex: 'skuList',
                        width: 280,
                        render(val, record, index) {
                          return (
                            <FormItem {...{ label: '指定SKU', name: [index, 'skuList'] }}>
                              <SearchSelect {...{ mode: 'multiple', options: skuOptions }} />
                            </FormItem>
                          );
                        },
                      },
                      {
                        title: '打托备注',
                        dataIndex: 'remark',
                        render(val, record, index) {
                          return (
                            <FormItem
                              {...{
                                label: '打托备注',
                                name: [index, 'remark'],
                                rules: [{ max: 255 }],
                              }}
                            >
                              <Input />
                            </FormItem>
                          );
                        },
                      },
                      {
                        title: '操作',
                        dataIndex: 'operate',
                        width: 100,
                        render(val, record, index) {
                          return (
                            <Space size={20} align={'start'}>
                              <LoadButton
                                {...{
                                  popconfirmProps: {
                                    title: '确认删除吗？',
                                  },
                                  onClick() {
                                    action.remove(index);
                                  },
                                }}
                              >
                                删除
                              </LoadButton>
                            </Space>
                          );
                        },
                      },
                    ],
                  }}
                />
                <div
                  className={cssFormListTable.btnAdd}
                  onClick={() => {
                    action.add({});
                  }}
                >
                  + 新增
                </div>
                <Form.ErrorList errors={errors} />
              </>
            );
          }}
        </FormList>
      </FormItem>
    </div>
  );
}
