import React, { useEffect, useState } from 'react';
import { history } from 'umi';
import { Button, Card, Form, message, Space } from 'antd';
import { PageContainer } from '@/common-import';
import { historyGetQuery, historyGoPrev, session, tipsUtils } from '@/utils';
import { CustomizeDivider, FooterAffix } from '@/views';
import { LoadButton } from '@/components';
import {
  apiReturnOrderCreate,
  apiReturnOrderDetail,
  apiReturnOrderEdit,
  apiReturnOrderNoCreate,
} from './ReturnApi';
import { fileListMerge, FormBaseInfo, FormGoodsOrder, FormOtherInfo } from './components';

/** 退件新增编辑页面 */
export default function ReturnEdit() {
  const { form, asyncSubmit } = useConfig();

  return (
    <PageContainer title={false}>
      <Card>
        <Form {...{ form, layout: 'vertical' }}>
          <FormBaseInfo />
          <CustomizeDivider title={'商品清单'} />
          <FormGoodsOrder />
          <CustomizeDivider title={'其他信息'} />
          <FormOtherInfo />
        </Form>
      </Card>
      <FooterAffix layout={'center'}>
        <Space>
          <Button onClick={() => historyGoPrev()}>返回</Button>
          <LoadButton type="primary" onClick={asyncSubmit}>
            提交
          </LoadButton>
        </Space>
      </FooterAffix>
    </PageContainer>
  );
}

function usePageInfo() {
  const { podOrderNo } = historyGetQuery();
  const [isUpdate] = useState(history.location.pathname.endsWith('-edit'));

  return {
    isUpdate,
    podOrderNo,
  };
}

function useConfig() {
  const { isUpdate, podOrderNo } = usePageInfo();
  const [form] = Form.useForm<TypeReturnOrderFormData>();

  async function asyncInit() {
    if (isUpdate === false) {
      const { data } = await apiReturnOrderNoCreate();

      form.setFieldsValue({
        podOrderNo: data,
        createByName: session.get('userInfo')?.userName,
      });
    }
    if (podOrderNo) {
      const { data } = await apiReturnOrderDetail({ podOrderNo });

      if (isUpdate === false) {
        /** copy 订单data处理 */
        delete data.podOrderNo;
        delete data.id;
        (data.orderGoodsList || []).forEach((item) => {
          delete item.id;
        });
        (data.orderFileList || []).forEach((item) => {
          delete item.id;
        });
      }

      form.setFieldsValue(data);
    }
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return;
    }
    const formData = form.getFieldsValue();

    await (isUpdate ? apiReturnOrderEdit : apiReturnOrderCreate)({
      ...formData,
      ...fileListMerge(formData),
    });

    message.success('提交成功');
    historyGoPrev();
  }

  useEffect(() => {
    asyncInit();
  }, []);

  return {
    form,
    asyncSubmit,
    isUpdate,
  };
}
