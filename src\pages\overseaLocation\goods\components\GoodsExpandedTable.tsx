import I18N from '@/utils/I18N';
import React from 'react';

import { useIntl } from '@umijs/max';
import { Table } from 'antd';
import './GoodsExpandedTable.less';
import GoodsImage from '@/components/GoodsImage';

interface IProps {
  goodsList: any[];
}

const GoodsExpandedTable: React.FC<IProps> = ({ goodsList }) => {
  const columns: any[] = [
    {
      title: 'extra',
      dataIndex: 'extra',
      key: 'extra',
      width: 48,
      render: (_: any) => {
        return <span />;
      },
    },
    {
      title: 'label',
      key: 'label',
      dataIndex: 'label',
      render: (_: any) => {
        return <span />;
      },
      width: 90,
    },
    {
      title: 'pic',
      key: 'goodsPictureUrl',
      dataIndex: 'goodsPictureUrl',
      render: (_: any, record: any) => {
        return <GoodsImage className="sub-goods-img" src={record.pictureUrl} />;
      },
      width: 220,
    },
    {
      title: I18N.Src__Pages__Freight__Lcl.List.name,
      key: 'name',
      dataIndex: 'name',
      width: 240,
      // render: (_: any) => {
      //   return <div className="goods-name-box">{_}</div>;
      // },
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      width: 220,
      render: (_: any) => <span style={{ color: '#1890FF' }}>{_}</span>,
    },
    {
      title: I18N.Src__Pages__Order__Detail.SiItem.remarks,
      key: 'comment',
      dataIndex: 'comment',
    },
    {
      title: 'option',
      key: 'option',
      dataIndex: 'option',
      // width: 100,
      render: (_: any) => <span />,
    },
  ];

  return (
    <Table
      rowKey="id"
      dataSource={goodsList}
      columns={columns}
      showHeader={false}
      pagination={false}
      size="small"
    />
  );
};

export default GoodsExpandedTable;
