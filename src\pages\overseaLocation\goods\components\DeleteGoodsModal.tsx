import React, { useEffect, useState } from 'react';
import { Button, Modal, Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import './DeleteGoodsModal.less';

interface IProps {
  visible: boolean;
  onOk?: (params: any) => void;
  onCancel: () => void;
  id: string;
  goodsName: string;
  exist: boolean;
  combinedGoodsList: any[];
}

const DeleteGoodsModal: React.FC<IProps> = ({
  visible,
  onCancel,
  onOk,
  id,
  goodsName,
  exist,
  combinedGoodsList,
}) => {
  const [goodsGroups, setGoodsGroups] = useState<any[]>([]);

  useEffect(() => {
    if (combinedGoodsList?.length > 0) {
      const goodsGroupList = combinedGoodsList.map((group) => {
        const goodsNameList =
          group.goodsList
            ?.filter((item: any) => item.goodsId !== id)
            .map((item: any) => item.name) || [];

        goodsNameList.unshift(goodsName);
        return {
          id: group.id,
          pictureUrl: group.pictureUrl,
          name: group.name,
          enName: group.enName,
          goodsNames: goodsNameList.join(','),
          goodsCount: group.goodsList?.length || 0,
        };
      });

      setGoodsGroups(goodsGroupList);
    }
  }, [combinedGoodsList]);

  return (
    <Modal
      width={720}
      title={I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal.deletePrompt}
      maskClosable={false}
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
      footer={
        exist
          ? [
              <Button key={1} size="middle" type="primary" onClick={onCancel}>
                {I18N.Src__Pages__Enterprise__Account.Index.determine}
              </Button>,
            ]
          : [
              <Button key={2} size="middle" onClick={onOk}>
                {I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal.okToDelete}
              </Button>,
              <Button key={3} size="middle" type="primary" onClick={onCancel}>
                {I18N.Src__Components__UploadFileModal.Index.cancel}
              </Button>,
            ]
      }
    >
      <div className="goods-delete-modal">
        {exist && (
          <div className="exist-order-desc">
            <ExclamationCircleOutlined className="exist-order-icon" />
            <span>
              {I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal.thisProductHas}
            </span>
          </div>
        )}
        {!exist && (
          <div>
            <div className="rela-goods-group-desc">
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal
                  .theCommodityIsClosed
              }
            </div>
            {goodsGroups?.length > 0 && (
              <div className="rela-goods-group-list">
                {goodsGroups?.map((item) => {
                  return (
                    <div className="rela-item" key={item.id}>
                      <div>
                        <GoodsImage className="goods-img" src={item.pictureUrl} />
                      </div>
                      <div className="goods-desc">
                        <div className="title">
                          <Tooltip placement="topLeft" title={item.name}>
                            <span className="goods-text-show">{item.name}</span>
                          </Tooltip>
                        </div>
                        <div className="desc">
                          <Tooltip placement="topLeft" title={item.goodsNames}>
                            <span className="goods-text-show" style={{ marginRight: 10 }}>
                              {item.goodsNames}
                            </span>
                          </Tooltip>
                          {I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal.etc}
                          {item.goodsCount}{' '}
                          {
                            I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal
                              .items
                          }
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default DeleteGoodsModal;
