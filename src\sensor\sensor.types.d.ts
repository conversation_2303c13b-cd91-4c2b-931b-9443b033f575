type TypeEnumSensor = typeof import('@/sensor/event').EnumSensor;
/** 方便全局使用, 神策埋点名称 */
declare const EnumSensor: TypeEnumSensor;

declare interface Window {
  sensorsTrack: (key: string, data?: any) => void;
  Sensors: {
    track: (key: string, data?: any) => void;
  };
  ssTrack: typeof import('@/sensor/sensor').ssTrack;
  EnumSensor: TypeEnumSensor;
}

/** 方便全局使用, 添加神策埋点 */
declare const ssTrack: typeof import('@/sensor/sensor').ssTrack;
