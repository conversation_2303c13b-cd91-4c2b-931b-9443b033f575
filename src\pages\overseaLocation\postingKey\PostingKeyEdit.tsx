import I18N from '@/utils/I18N';
import type { FormInstance, ModalProps } from 'antd';
import { Modal, Button, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { LoadButton } from '../components';
import { BaseInfo } from './components';
import { apiAddPostingKey, apiUpdatePostingKey } from './postingKeyApi';

type TypeProps = {
  /** 区分编辑还是新增 */
  isUpdate?: boolean;
  /** 操作文案 */
  btnText?: string;
  /** 数据内容 */
  editData?: Record<any, any>;
  /** 刷新 */
  reload: () => void;
  /** 按钮是否可用，默认可用 */
  disabled?: boolean;
};
export default function PostingKeyEdit(props: TypeProps) {
  const { btnText, isUpdate, disabled = false, editData } = props;
  const { formRef, modalConfig, openModalAndSetData } = useConfig(props);

  return (
    <>
      <Modal {...modalConfig}>
        <BaseInfo ref={formRef} isUpdate={isUpdate} editData={editData} />
      </Modal>

      {isUpdate ? (
        <LoadButton onClick={openModalAndSetData} disabled={disabled}>
          {btnText}
        </LoadButton>
      ) : (
        <LoadButton onClick={openModalAndSetData} type="primary" disabled={disabled}>
          {btnText}
        </LoadButton>
      )}
    </>
  );
}
PostingKeyEdit.defaultProps = {
  btnText: I18N.Src__Pages__OverseaLocation__Goods__Group__List.Index.newlyAdded,
  isUpdate: false,
};
function useConfig(props: TypeProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const { isUpdate, editData, reload } = props;
  const formRef = useRef<{ form: FormInstance }>();
  const { handleForm } = useSubmit({ formRef, isUpdate, editData });

  /** 关闭弹窗 */
  const closeModal = () => {
    setVisible(false);
  };
  /** 打开弹窗 */
  const openModal = () => {
    setVisible(true);
  };
  const modalConfig = {
    title: isUpdate
      ? I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyEdit.editPosting
      : I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyEdit.newBookkeeping,
    visible,
    width: 800,
    destroyOnClose: true,
    maskClosable: false,
    onCancel: closeModal,
    footer: [
      <Button type="default" onClick={closeModal} key="close">
        {I18N.Src__Components__UploadFileModal.Index.cancel}
      </Button>,
      <LoadButton
        type="primary"
        onClick={async () => {
          const res = await handleForm();

          if (res) {
            closeModal();
            reload();
          }
        }}
        key="submit"
      >
        {I18N.Src__Pages__Enterprise__Account.Index.determine}
      </LoadButton>,
    ],
  } as ModalProps;

  /** 打开弹出层并且设置值 */
  const openModalAndSetData = () => {
    openModal();
  };

  return { formRef, modalConfig, openModalAndSetData };
}

/** 处理表单 */
type TypeFormSubmit = {
  formRef: React.MutableRefObject<
    | {
        form: FormInstance;
      }
    | undefined
  >;
  isUpdate?: boolean;
  editData?: Record<any, any>;
};
function useSubmit(submitProps: TypeFormSubmit) {
  const { formRef, isUpdate } = submitProps;
  /** 获取表单值 */
  const getFormData = () => {
    return formRef?.current?.form?.getFieldsValue();
  };
  /** 表单验证 */
  const handleFormValidate = () => {
    return formRef?.current?.form?.validateFields();
  };

  /** 处理表单操作 */
  const handleForm = async () => {
    /** 执行验证表单验证 */
    try {
      await handleFormValidate();
    } catch (error) {
      return false;
    }
    const formData = getFormData();

    const res = await (!isUpdate ? apiAddPostingKey : apiUpdatePostingKey)({
      ...formData,
      orderSourcePlatformCode: formData?.orderSourcePlatformCode?.value,
      orderSourcePlatformName: formData?.orderSourcePlatformCode?.label,
      warehouseIds: (formData?.warehouseIds || []).map((item: any) => {
        return item.value;
      }),
    });

    // if (res?.success) {
    message.success(
      isUpdate
        ? I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyEdit.updateSucceeded
        : I18N.Src__Pages__OverseaLocation__Goods__Action.Index.successfullyAdded,
    );
    return true;
    // }
  };

  return { handleForm };
}
