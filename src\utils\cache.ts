import {
  SetLocalStorage,
  GetLocalStorage,
  RemoveLocalStorage,
  RemoveAllLocalStorage,
} from './storage/local';
import {
  SetSessionStorage,
  GetSessionStorage,
  RemoveSessionStorage,
  RemoveAllSessionStorage,
} from './storage/session';

// 网站列表习惯
export const SetGlobalPageTableConfig = (tableConfig: object) =>
  SetLocalStorage('globalPageTableConfig', tableConfig);
export const GetGlobalPageTableConfig = () => GetLocalStorage('globalPageTableConfig');
export const RemoveGlobalPageTableConfig = () => RemoveLocalStorage('globalPageTableConfig');

// 网站列表习惯
export const SetOrderListMode = (mode: string) => SetLocalStorage('orderListMode', mode);
export const GetOrderListMode = () => GetLocalStorage('orderListMode');
export const RemoveOrderListMode = () => RemoveLocalStorage('orderListMode');

// 网站标识
// export const SetGlobalFlag = (token: string) => SetLocalStorage('webGlobalFlag', token);
// export const GetGlobalFlag = () => GetLocalStorage('webGlobalFlag');
// export const RemoveGlobalFlag = () => RemoveSessionStorage('webGlobalFlag');

// 用户搜索记录
type PageType =
  | 'lclSearchHistory'
  | 'fclSearchHistory'
  | 'fobSearchHistory'
  | 'visitorSearchHistory'
  | 'trailerHistory';
export const SetHistoryData = (pageType: PageType, history: any[]) =>
  SetLocalStorage(pageType, history);
export const GetHistoryData = (pageType: PageType) => GetLocalStorage(pageType);
export const RemoveHistoryData = (pageType: PageType) => RemoveLocalStorage(pageType);

// 用户登录信息
export const SetAccountInfo = (info: any) => SetLocalStorage('webAccountInfo', info);
export const GetAccountInfo = () => GetLocalStorage('webAccountInfo');
export const RemoveAccountInfo = () => RemoveLocalStorage('webAccountInfo');

// 是否验证过分享邮箱
export const SetVerifySiFlag = (uuid: any) => SetSessionStorage(`si-action-${uuid}`, true);
export const GetVerifySiFlag = (uuid: any) => GetSessionStorage(`si-action-${uuid}`);
export const RemoveVerifySiFlag = (uuid: any) => RemoveSessionStorage(`si-action-${uuid}`);

// 用户登录来源
export const SetAccountLoginSource = (info: 'WEB' | 'JIJIA') =>
  SetLocalStorage('account_login_source', info);
export const GetAccountLoginSource = () => GetLocalStorage('account_login_source');
export const RemoveAccountLoginSource = () => RemoveLocalStorage('account_login_source');

// 清空所有storage
export const RemoveAllStorage = () => {
  RemoveAllLocalStorage();
  RemoveAllSessionStorage();
};
