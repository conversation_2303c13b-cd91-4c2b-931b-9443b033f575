import React, { useContext } from 'react';
import { RowContext } from './RowLayoutContext';
import RowLayout from './RowLayout';
import type { TypeRowLayoutProps } from './RowLayout.types';

/** RowLayout 局部中内的 row 式布局, 主要是为了继承父级属性, 命名上区分也更直观 */
export default function RowInner(inProps: TypeRowLayoutProps) {
  const { parentProps } = useContext(RowContext);

  return (
    <RowLayout
      {...{
        useFragment: true,
        gutter: inProps.gutter ?? parentProps?.gutter,
        columnNum: inProps.columnNum ?? parentProps?.columnNum,
        ...inProps,
      }}
    />
  );
}
