/* 方便第三方模块快捷导出 */
export { default as ProTable } from '@ant-design/pro-table';
export { default as ProForm, ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
export { default as ProCard } from '@ant-design/pro-card';
export { default as ProDescriptions } from '@ant-design/pro-descriptions';

/** 第三方类型导出 */
export type { ActionType, ProTableProps, ProColumnType, ProColumns } from '@ant-design/pro-table';
export type { ProFormInstance, ProFormProps } from '@ant-design/pro-form';
export type { AxiosResponse } from 'umi';
export type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';

/** 自定义组件替换第三方组件 */
export { PageContainer } from '@/components/PageContainer';
export type { ModuleTo } from '@hive/sdk-core/es/history/types';
export { HiveModule, Link } from '@portal/hive-sdk';
