export default {
  Compare: {
    freightRateComparison: '运价对比',
    motherShipPort: '母船港口',
    bargeTransshipment: '驳船转运方式',
    barge1: '驳船ETD',
    totalPrice: '总价',
    aSurcharge: '附加费',
    unitPrice: '单价',
    flightSchedule: '班期',
    direct: '直达',
    transfer: '中转',
    directTransit: '{val1}/{val2}',
    boxTypeAndQuantity: '箱型箱量',
    freightRate1: '运价',
  },
  ComparePopup: {
    startComparison: '开始对比',
    comparisonData: '对比数据不能少于两条',
  },
  Detail: {
    viewDetails: '查看详情',
    surchargeStatement: '附加费明细',
    oceanFreight: '海运费',
    expenseDetails: '费用明细',
    useFreeAndHeapFree: '免用免堆存说明',
    remarksOnWeightLimit: '限重备注',
    specialInstructions: '特别说明',
    bargeDescription: '驳船说明',
    motherShipTransshipment: '母船转运方式:',
    mothership: '母船ETD:',
    bargeTransshipment: '驳船转运方式:',
    transferInformation: '中转信息',
    moreShippingDates: '更多船期',
    closing: '截关/ETD',
    freightRateDetails: '运价详情',
  },
  List: {
    contrast: '对比',
    cancelComparison: '取消对比',
    weightLimitDescription: '限重说明',
    thisIsABarge: '该ETD为驳船ETD',
    region: '区域',
    transshipmentMode: '转运方式：',
    motherShip: '母船ETD：',
    bargeOnly: '驳船ETD 仅供参考，实际以客户配驳为主',
    barge1: '驳船ETD：',
    dangerousGoodsPreparation: '危险品备注',
    noData: '暂无数据',
    singleTicket: '单票',
    priceComparisonData: '比价数据最多3条',
    gateway: '门户',
    closing: '截关',
    closing1: '{val1}/ETD',
    classStatus: '舱位状态',
    nonBarge: '非驳船',
    sunday: '周日',
    saturday: '周六',
    friday: '周五',
    thursday: '周四',
    wednesday: '周三',
    tuesday: '周二',
    monday: '周一',
  },
};
