import { request } from 'umi';
import type { TypeUploadFile } from '../components';

/** 文件上传类型枚举 */
export enum EnumUploadFileType {
  /** picture */
  picture = 1,
  /** excel */
  excel = 2,
  /** 证件 */
  credentials = 3,
  /** zip 文件 */
  zip = 4,
  /** 临时文件 */
  temp = 5,
}

/** 调用OMS文件上传 */
export function apiUploadFile(data: { pathType: EnumUploadFileType; file: File }) {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value as any);
  });

  return request<
    NsApi.TypeResponseData<{
      /** 文件绝对路径 */
      filePath: string;
      /** 原文件名 */
      originFileName: string;
    }>
  >('/zouwu-oms-system/portal/file/upload', {
    data: formData,
    method: 'POST',
    headers: {
      'Content-type': 'multipart/form-data',
    },
  });
}

/** 创建和OMS同名api */
export const apiUploadFileNew = apiUploadFile;

/** 默认header头属性, 因为xhr请求和umi request配置不共享请求 */
export function getHeadersDefault() {
  return {
    /** 标记门户发起的接口请求 */
    portal: 'wwl-zouwu-front',
  };
}

/* antd Upload组件参数, 文件上传 */
export function getUploadPropsConfig() {
  /**
   * 这个是xhr独立请求, 不受 umi request配置影响
   * 因为只有原生xhr才可以监控上传进度
   */
  return {
    action: '/zouwu-oms-system/portal/file/upload',
    headers: {
      ...getHeadersDefault(),
    },
  };
}

/** 处理状态为done的文件, fileDoneProcess 和 getUploadPropsConfig 是一对, 所以挂载为子方法 */
getUploadPropsConfig.fileDoneProcess = function (
  file: TypeUploadFile<{}, Awaited<ReturnType<typeof apiUploadFileNew>>>,
) {
  const { status, response } = file;

  if (status === 'done') {
    const { filePath, originFileName } = response?.data || {};

    file.filePath = filePath;
    file.fileName = originFileName;
    file.url = filePath;
  }
};
