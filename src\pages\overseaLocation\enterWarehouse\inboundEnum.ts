import { includesInArray } from '@/utils';
import I18N from '@/utils/I18N';

/** 入库单下发类型 */
export enum EnumIssuanceType {
  /** 系统 */
  system = 1,
  /** 人工 */
  manual = 2,
}

/** 入库业务类型 */
export enum EnumInboundBusinessType {
  /** 环世头程入库 */
  FIRST_LEG = 101,
  /** 代发头程入库 */
  DROP_SHIPPING_FIRST_LEG = 102,
  /** 退货入库 */
  RETURN_INBOUND = 104,
  /** TOB入库 */
  TOB_INBOUND = 105,
}

/** 入库单状态所属TabKey类型 */
export enum EnumInboundTabKey {
  /** 待客户处理 */
  TO_BE_SUBMITTED = 'TO_BE_SUBMITTED',
  /** 提交审核 | 补充头程物流信息 */
  TO_BE_LOGISTICS = 'TO_BE_LOGISTICS',
  /** 入库单待通知仓库 | OMS侧待下发 */
  NOTIFY_WAREHOUSE_PENDING = 'NOTIFY_WAREHOUSE_PENDING',
  /** 运输中 */
  SHIPPING = 'SHIPPING',
  /** 签收中 */
  SIGN_FOR_ING = 'SIGN_FOR_ING',
  /** 履约完结 */
  FULFILLMENT_OVER = 'FULFILLMENT_OVER',
  /** 已取消 */
  ORDER_CANCEL = 'ORDER_CANCEL',
}

/** 入库订单 tab options
 * @ OMS的状态才是真实的, 门户侧被折叠到部分tab中了
 */
export const INBOUND_TAB_OPTIONS = [
  {
    label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole,
    value: [-1],
    tabKey: 'TOTAL',
  },
  {
    /** 待客户处理 */
    // label: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted,
    label: '待客户处理',
    value: [100 /** 草稿 */, 101 /** 客户建单 */],
    tabKey: 'TO_BE_SUBMITTED',
    color: '#FFA940',
  },
  // { label: '提交审核', value: 102, tabKey: 'TO_BE_SUBMITTED2' },
  {
    /** 产品说这个节点没用, 以后大概率也用不上, 入库列表优化顺手干掉了 */
    label: I18N.Src__Utils.Const.headToBeSupplemented,
    value: [102 /** 提交审核 */, 103 /** 补充头程物流信息 */],
    tabKey: 'TO_BE_LOGISTICS',
    color: '#FFA940',
    hidden: true,
  },
  /** OMS 待通知仓库, 门户侧展示 运输中
   * tabKey: NOTIFY_WAREHOUSE_PENDING
   */
  {
    label: I18N.Src__Utils.Const.inTransit,
    value: [
      104 /** OMS侧入库预报通知 */, 105 /** 运输中 */, 130 /* OMS 属于待下发, 门户是运输中 */,
      140 /** OMS 侧入库异常 */,
    ],
    tabKey: 'SHIPPING',
    color: '#00C853',
  },
  {
    label: I18N.Src__Utils.Const.signingIn,
    value: [106],
    tabKey: 'SIGN_FOR_ING',
    color: '#00C853',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1,
    value: [107, 108],
    tabKey: 'FULFILLMENT_OVER',
    color: '#00AAFF',
  },
  // { label: '已结单', value: 109, tabKey: 'SETTLEMENT_OVER' },
  {
    label: I18N.Src__Pages__Order__TrailerOrder.Config.canceled,
    value: [110],
    tabKey: 'ORDER_CANCEL',
    color: '#999',
  },
];

/** orderStatus 基于 number 快速查找
 */
export const inboundOrderStatusItemMap = {} as Record<
  number,
  typeof INBOUND_TAB_OPTIONS[number] | typeof INBOUND_TAB_OPTIONS[number]
>;

[...INBOUND_TAB_OPTIONS].forEach((item) => {
  const orderStatusArr = item.value;

  orderStatusArr.forEach((val) => (inboundOrderStatusItemMap[val] = item));
});

/** 对比订单状态, orderStatus 属于 orderStatusType
 */
export function isEqualInboundOrderStatus(statusType: EnumInboundTabKey[], orderStatus?: number) {
  const res = inboundOrderStatusItemMap[orderStatus!];

  return includesInArray(statusType, res?.tabKey);
}
