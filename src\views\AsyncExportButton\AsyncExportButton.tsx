import type { FormInstance, FormProps, ModalProps } from 'antd';
import { notification, Button, message, Alert, Input, Form, Modal } from 'antd';
import React, { useState } from 'react';
import { historyGoPush, tipsUtils, useSafeStateUpdate, LoadButton, goToPath } from './aide-import';
import type { TypeLoadButtonProps } from './aide-import';
import { textMap } from './text-map';
import { useDictTypeValueEnum } from '@/api';
import type { EnumExportDataSource } from '@/constants/enum';

type TypeParamsOptions = {
  form: FormInstance<{
    fileName?: string;
  }>;
  modalController: {
    asyncOpen: () => Promise<void>;
    close: () => void;
  };
};

export type TypeAsyncExportButtonProps = {
  /** 按钮名称 */
  children?: React.ReactNode;
  /** 导出数据来源, 会作为初始化数据源 */
  exportDataSource?: EnumExportDataSource;
  modalProps?: ModalProps;
  formChildren?: (params: { nodeFileName: JSX.Element }) => JSX.Element;
  formProps?: FormProps;
  buttonProps?: Omit<TypeLoadButtonProps, 'onClick'> & {
    onClick?: (e: React.MouseEvent, params: TypeParamsOptions) => void | Promise<void>;
  };
  request: (params: TypeParamsOptions) => Promise<void>;
};

/** 异步导出按钮 */
export default function AsyncExportButton(inProps: TypeAsyncExportButtonProps) {
  const { modalConfig, modalController, form } = useModalConfig({ inProps });
  const { exportDataSource } = inProps;
  const { dictTypeMap } = useDictTypeValueEnum(['exportDataSource']);
  const nodeFileName = (
    <Form.Item
      {...{
        label: textMap.txtFileName,
        name: 'fileName',
        rules: [{ required: true }, { type: 'string', max: 30 }],
        initialValue: dictTypeMap.exportDataSource[exportDataSource!],
      }}
    >
      <Input />
    </Form.Item>
  );

  return (
    <>
      <LoadButton
        {...{
          type: 'default',
          ...inProps.buttonProps,
          async onClick(e) {
            await inProps.buttonProps?.onClick?.(e, { form, modalController });
            await modalController.asyncOpen();
          },
        }}
      >
        {inProps.children || textMap.txtBtn}
      </LoadButton>
      <Modal {...modalConfig}>
        <Form form={form} {...inProps.formProps}>
          {typeof inProps.formChildren === 'function'
            ? inProps.formChildren({ nodeFileName })
            : nodeFileName}
          <Alert {...{ showIcon: true, type: 'warning', message: textMap.txtAlert }} />
        </Form>
      </Modal>
    </>
  );
}

function useModalConfig({ inProps }: { inProps: TypeAsyncExportButtonProps }) {
  const { safeUpdateFn } = useSafeStateUpdate();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const modalController = {
    async asyncOpen() {
      safeUpdateFn(() => setVisible(true))();
    },
    close() {
      form.resetFields();
      safeUpdateFn(() => setVisible(false))();
    },
  };
  const modalConfig = {
    visible,
    title: textMap.txtModalTitle,
    keyboard: false,
    maskClosable: false,
    ...inProps.modalProps,
    /** 不在loading中才允许关闭 */
    closable: loading === false,
    onCancel() {
      modalController.close();
    },
    footer: [
      <Button
        key="cancel"
        {...{
          disabled: loading,
          onClick: modalController.close,
        }}
      >
        {textMap.txtModalCancel}
      </Button>,
      <LoadButton
        key="submit"
        {...{
          type: 'primary',
          async onClick() {
            const onClick = () => {
              historyGoPush({ newTab: true, pathname: goToPath });
            };

            setLoading(true);
            try {
              await form.validateFields();
            } catch (err) {
              message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
              console.log(err);
              return;
            }
            await inProps.request({ form, modalController });
            notification.open({
              duration: 5,
              message: textMap.txtNotifyMessage,
              description: (
                <>
                  {textMap.txtDes1} <a onClick={onClick}>{textMap.txtDes2}</a> {textMap.txtDes3}
                </>
              ),
              btn: (
                <Button type="primary" onClick={onClick}>
                  {textMap.txtGoto}
                </Button>
              ),
            });
            modalController.close();
          },
          onClickFinally() {
            setLoading(false);
          },
        }}
      >
        {textMap.txtModalConfirm}
      </LoadButton>,
    ],
  } as ModalProps;

  return {
    modalConfig,
    modalController,
    form,
  };
}
