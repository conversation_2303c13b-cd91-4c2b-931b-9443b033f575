import React from 'react';
import { Form, Input, InputNumber, Space, Table, Tooltip } from 'antd';
import classnames from 'classnames';
import { LoadButton } from '@/components';
import { cssFormListTable } from '@/styles';
import { TextValue } from '@/views';
import type { FormInstance } from 'antd/es/form';
import { eR, ruleUtils, useModalSet } from '@/utils';
import { ModalChooseGoods } from './ModalChooseGoods';
import css from './FormGoodsBoxInfo.less';
import { overseaRulesMap } from '@/utils-business';
import { SerialNumber } from './SerialNumber';

const { List: FormList } = Form;

type TypeProps = {
  /** 组件属性 */
};

const listName = 'boxList';

/** 入库-物流箱子信息 */
export function FormGoodsBoxInfo(props: TypeProps) {
  const form = Form.useFormInstance();
  const { modalActions, modalNodeList } = useModalSet({
    chooseGoods: ModalChooseGoods,
  });

  return (
    <div className={classnames(cssFormListTable.FormListTable, css.FormGoodsBoxInfo)}>
      {modalNodeList}
      <FormList {...{ name: listName, initialValue: [] }}>
        {function (fieldList, action, { errors }) {
          const dataSource = form.getFieldValue(listName) || [];

          return (
            <>
              <Table<NsInbound.TypeLogisticsBoxInfo>
                {...{
                  dataSource: fieldList.map(
                    (item) => ({ key: item.key } as NsInbound.TypeLogisticsBoxInfo),
                  ),
                  pagination: false,
                  scroll: { x: 'max-content' },
                  columns: [
                    {
                      title: '序号',
                      dataIndex: 'num',
                      width: 120,
                      render(val, record, rowIndex) {
                        return <SerialNumber {...{ rowIndex }} />;
                      },
                    },
                    {
                      title: '箱子数量',
                      dataIndex: 'boxQuantity',
                      width: 120,
                      render(val, record, rowIndex) {
                        return (
                          <Form.Item
                            {...{
                              label: '箱子数量',
                              name: [rowIndex, 'boxQuantity'],
                              rules: [
                                { required: true },
                                ruleUtils.calcRuleInteger({ min: 1, max: 10000 }),
                              ],
                              children: <InputNumber />,
                            }}
                          />
                        );
                      },
                    },
                    {
                      title: 'SKU',
                      dataIndex: 'orderGoodsList',
                      width: 150,
                      render(value, record, rowIndex) {
                        return renderBoxGoodsFieldList({
                          form,
                          rowIndex,
                          fieldName: 'sku',
                          onChooseGoods() {
                            const orderGoodsList: NsInbound.TypeBoxGoodsInfo[] =
                              form.getFieldValue([listName, rowIndex, 'orderGoodsList']) || [];

                            modalActions.chooseGoods?.open(
                              {
                                initSkuArr: orderGoodsList.map((o) => o.sku!),
                              },
                              {
                                submitSuccessCB(newRows) {
                                  const newRecords = newRows.map((row) => ({
                                    ...row,
                                    /** 单箱数量, 默认为1 */
                                    quantityPerBox: 1,
                                  }));

                                  form.setFields([
                                    {
                                      name: [listName, rowIndex, 'orderGoodsList'],
                                      value: [...orderGoodsList, ...newRecords],
                                    },
                                  ]);
                                },
                              },
                            );
                          },
                        });
                      },
                    },
                    {
                      title: '商品名称',
                      dataIndex: 'goodsName',
                      width: 150,
                      render(value, record, rowIndex) {
                        return renderBoxGoodsFieldList({ form, rowIndex, fieldName: 'goodsName' });
                      },
                    },
                    {
                      title: '商品净重',
                      dataIndex: 'goodsWeight',
                      width: 80,
                      render(value, record, rowIndex) {
                        return renderBoxGoodsFieldList({
                          form,
                          rowIndex,
                          fieldName: 'goodsWeight',
                        });
                      },
                    },
                    {
                      title: '商品体积',
                      dataIndex: 'goodsVolume',
                      width: 180,
                      render(value, record, rowIndex) {
                        return renderBoxGoodsFieldList({
                          form,
                          rowIndex,
                          fieldName: 'goodsVolume',
                        });
                      },
                    },
                    {
                      title: '单箱数量',
                      dataIndex: 'quantityPerBox',
                      width: 120,
                      render(value, record, rowIndex) {
                        return renderBoxGoodsFieldList({
                          form,
                          rowIndex,
                          fieldName: 'quantityPerBox',
                        });
                      },
                    },
                    {
                      title: '箱唛号',
                      dataIndex: 'shippingMarkNo',
                      width: 180,
                      render(value, record, index) {
                        return (
                          <Form.Item
                            {...{
                              label: '箱唛号',
                              name: [index, 'shippingMarkNo'],
                              rules: [...overseaRulesMap.shippingMarkNo],
                              children: <Input />,
                            }}
                          />
                        );
                      },
                    },
                    {
                      title: '箱子尺寸',
                      dataIndex: 'boxDimensions',
                      width: 300,
                      render(val, record, index) {
                        return (
                          <Space>
                            <Form.Item
                              {...{
                                label: '长',
                                name: [index, 'boxLength'],
                                rules: [ruleUtils.ruleNumAndFloatTwo],
                                children: <InputNumber placeholder="长" style={{ width: 76 }} />,
                              }}
                            />
                            <Form.Item
                              {...{
                                label: '宽',
                                name: [index, 'boxWidth'],
                                rules: [ruleUtils.ruleNumAndFloatTwo],
                                children: <InputNumber placeholder="宽" style={{ width: 76 }} />,
                              }}
                            />
                            <Form.Item
                              {...{
                                label: '高',
                                name: [index, 'boxHeight'],
                                rules: [ruleUtils.ruleNumAndFloatTwo],
                                children: (
                                  <InputNumber
                                    placeholder="高"
                                    style={{ width: 118 }}
                                    addonAfter={'CM'}
                                  />
                                ),
                              }}
                            />
                          </Space>
                        );
                      },
                    },
                    {
                      title: '箱子重量',
                      dataIndex: 'boxWeight',
                      width: 150,
                      render(val, record, index) {
                        return (
                          <Form.Item
                            {...{
                              label: '',
                              name: [index, 'boxWeight'],
                              rules: [ruleUtils.ruleNumAndFloatTwo],
                              children: <InputNumber {...{ addonAfter: 'KG' }} />,
                            }}
                          />
                        );
                      },
                    },
                    {
                      title: '操作',
                      dataIndex: 'operate',
                      width: 60,
                      align: 'center',
                      fixed: 'right',
                      render(val, record, index) {
                        return (
                          <TextValue
                            {...{
                              name: [index, 'orderGoodsList'],
                              children(params) {
                                const orderGoodsList =
                                  (params.value as NsInbound.TypeLogisticsBoxInfo['orderGoodsList']) ||
                                  [];

                                return (
                                  <Space {...{ direction: 'vertical', size: 10 }}>
                                    <LoadButton
                                      {...{
                                        danger: true,
                                        type: 'link',
                                        popconfirmProps: {
                                          title: '确认删除吗？',
                                        },
                                        onClick() {
                                          action.remove(index);
                                        },
                                      }}
                                    >
                                      删除
                                    </LoadButton>
                                    {orderGoodsList.map((goodsInfo, goodsIndex) => {
                                      if (orderGoodsList.length <= 1) {
                                        return null;
                                      }
                                      return (
                                        <LoadButton
                                          key={`${goodsInfo.sku}_goodsIndex`}
                                          {...{
                                            danger: true,
                                            type: 'link',
                                            popconfirmProps: {
                                              title: '确认删除吗？',
                                            },
                                            onClick() {
                                              orderGoodsList.splice(goodsIndex, 1);
                                              form.setFields([
                                                {
                                                  name: [listName, index, 'orderGoodsList'],
                                                  value: [...orderGoodsList],
                                                },
                                              ]);
                                            },
                                          }}
                                        >
                                          删除
                                        </LoadButton>
                                      );
                                    })}
                                  </Space>
                                );
                              },
                            }}
                          />
                        );
                      },
                    },
                  ],
                }}
              />
              <div
                className={cssFormListTable.btnAdd}
                onClick={() => {
                  action.add({});
                }}
              >
                + 添加箱子
              </div>
              <Form.ErrorList errors={errors} />
            </>
          );
        }}
      </FormList>
    </div>
  );
}

/** 渲染箱子商品清单 */
function renderBoxGoodsFieldList({
  form,
  rowIndex,
  fieldName,
  onChooseGoods,
}: {
  form: FormInstance;
  rowIndex: number;
  fieldName: 'sku' | 'goodsName' | 'goodsWeight' | 'goodsVolume' | 'quantityPerBox';
  onChooseGoods?: () => void;
}) {
  const hasAddGoodsBtn = fieldName === 'sku';

  return (
    <TextValue
      {...{
        name: [rowIndex, 'orderGoodsList'],
        children(params) {
          const orderGoodsList = (params.value as NsInbound.TypeBoxGoodsInfo[]) || [];
          const valueRenderMap = {
            goodsWeight: (record: NsInbound.TypeBoxGoodsInfo) => `KG: ${eR(record.goodsWeight)}`,
            goodsVolume: (record: NsInbound.TypeBoxGoodsInfo) =>
              `CM: ${eR(record.goodsLength)} * ${eR(record.goodsWidth)} * ${eR(
                record.goodsHeight,
              )}`,
          };
          const textMap = {
            quantityPerBox: orderGoodsList.reduce((prev, cur) => {
              return prev + (cur.quantityPerBox || 0);
            }, 0),
          };

          return (
            <Space {...{ direction: 'vertical', size: 10 }}>
              {orderGoodsList.length > 1
                ? textMap[fieldName as keyof typeof textMap] ?? '多个'
                : null}
              {orderGoodsList.map((goodsInfo, goodsIndex) => {
                const itemNodeMap = {
                  quantityPerBox: (
                    <Form.Item
                      {...{
                        label: '单箱数量',
                        name: [rowIndex, 'orderGoodsList', goodsIndex, fieldName],
                        style: { marginBottom: 0 },
                        rules: [
                          {
                            required: true,
                          },
                          ruleUtils.calcRuleInteger({ min: 1, max: 10000 }),
                        ],
                        children: <InputNumber />,
                      }}
                    />
                  ),
                };
                const itemNode = itemNodeMap[fieldName as keyof typeof itemNodeMap];

                if (itemNode) {
                  return itemNode;
                }

                return (
                  <Form.Item
                    key={`${goodsInfo.sku}_goodsIndex`}
                    {...{
                      label: fieldName,
                      name: [rowIndex, 'orderGoodsList', goodsIndex, fieldName],
                      noStyle: true,
                      children: (
                        <TextValue
                          {...{
                            children({ value }) {
                              const val =
                                valueRenderMap[fieldName as keyof typeof valueRenderMap]?.(
                                  goodsInfo,
                                ) || value;

                              return (
                                <Tooltip title={val}>
                                  <div className={css.goodsInfoItem}>{val}</div>
                                </Tooltip>
                              );
                            },
                          }}
                        />
                      ),
                    }}
                  />
                );
              })}
              {hasAddGoodsBtn && (
                <div
                  className={cssFormListTable.btnAdd}
                  style={{ width: 150 }}
                  onClick={() => {
                    onChooseGoods?.();
                  }}
                >
                  + 添加商品
                </div>
              )}
            </Space>
          );
        },
      }}
    />
  );
}
