.ant-table {
  .ant-table-thead > tr > th {
    // color: rgba(0, 0, 0, 0.8);
    // font-weight: bold;
    white-space: nowrap; // 表单都所有情况都不换行
    word-break: break-all; // 表单头部单词永不换行
  }
  .ant-table-tbody > tr > td {
    max-width: 300px;
    word-break: break-all;

    &.table-cell-option {
      /* 考虑权限会隐藏按钮, 操作栏宽度自适应, 永不换行 */
      word-break: keep-all;
    }
  }
}

/** pro-table 自带搜索栏 */
.ant-pro-table {

  .ant-pro-card-body {
    /* pro-table 使用了 inline-style样式, 所以只能 important 了 */
    padding-top: 16px !important;
  }

  .ant-pro-table-list-toolbar {
    .ant-pro-table-list-toolbar-container {
      /* tab栏和table, card-header 之间的上下间距 */
      margin-bottom: 8px;
      padding: 0;
    }

    .ant-pro-table-list-toolbar-extra-line {
      /* tab栏和table, card-header 之间的上下间距 */
      margin-bottom: 8px;
    }

    .ant-tabs-tab {
      /** tab栏, active 蓝杠上下间距 */
      padding-bottom: 6px;
    }

    .ant-tabs-top > .ant-tabs-nav .ant-tabs-nav-list {
      /* tab信息右侧空间,如果一点不加可能会导致出现折叠tab */
      margin-right: 6px;
    }
  }
}

/* table 表头 固定 */
.zw-table-sticky {
  /* 无tab时表头固定 */
  .ant-table-sticky-holder {
    top: @gl-header-height - 1 !important;
  }

  /* 存在tab时 */
  &.zw-table-sticky__has-tab {
    .ant-tabs {
      position: sticky;
      top: @gl-header-height - 1;
      z-index: 3;
    }
    .ant-table-sticky-holder {
      top: 38px + @gl-header-height - 1 !important;
    }
  }
}
