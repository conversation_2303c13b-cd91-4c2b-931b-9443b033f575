import { ProFormDependency } from '@ant-design/pro-form';
import { Col, Form, Input, Row, Select, InputNumber, message } from 'antd';
import React, { useReducer } from 'react';
import I18N from '@/utils/I18N';
import type { LOGISTICS_PROVIDER_TYPE_DESC } from '@/utils/const';
import { LOGISTICS_PROVIDER_TYPE } from '@/utils/const';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiMapDictType, apiQueryOverseaContainerBox } from '@/pages/overseaLocation/api';
import { LOGISTICS_CHANNEL_PROVIDER_TYPE } from '@/pages/overseaLocation/enum';
import { regexUtils } from '@/pages/overseaLocation/utils';

type TypeProps = {};

/** 配送类型 */
export type TypeChannel = keyof typeof LOGISTICS_PROVIDER_TYPE_DESC;

/** 入库单-物流信息 */
const LogisticsInfo = () => {
  return (
    <Form.Item>
      {/* 隐藏字段物流id = */}
      <Form.Item name={['logisticsData', 'id']} hidden>
        <Input />
      </Form.Item>
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item
            name={['logisticsData', 'logisticsProvider']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.logisticsChannel
            }
            rules={[{ required: true }]}
          >
            <Select
              options={LOGISTICS_PROVIDER_TYPE.map((item) => ({
                value: item.value.toString(),
                label: item.label,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <ProFormDependency name={['logisticsData', 'logisticsProvider']}>
          {(depValues) => {
            const { logisticsData } = depValues;
            // 动态渲染不同的内容

            return renderColumns(logisticsData?.logisticsProvider).map((item, index) => {
              return (
                <Col span={item.colSpan} key={index}>
                  {item.children}
                </Col>
              );
            });
          }}
        </ProFormDependency>
      </Row>
    </Form.Item>
  );
};

/**
 * 依据不同的类型渲染不同的模块字段
 */
function renderColumns(channel: TypeChannel) {
  const required = true;

  /** 追踪单号,在快递、卡车需要展示 */
  const trapNum = {
    key: 'logisticsNo',
    colSpan: 8,
    children: (
      <Form.Item
        name={['logisticsData', 'logisticsNo']}
        label={
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .trackingSheetNo1
        }
        rules={[{ required }]}
      >
        <Input />
      </Form.Item>
    ),
  };
  /** 不同类型下的字段 */
  const diffField = {
    /** MWB(海运) */
    1: [
      {
        key: 'containerModel',
        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'containerModel']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .containerType1
            }
            rules={[{ required }]}
          >
            <ZSearchSelect
              {...{
                request: () => apiQueryOverseaContainerBox(),
                searchByLocal: true,
              }}
            />
          </Form.Item>
        ),
      },
      {
        key: 'containerNo',
        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'containerNo']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .containerNo1
            }
            rules={[
              { required },
              {
                async validator(rule, value, callback) {
                  const trimValue = value?.trim?.();

                  if (/^[A-Za-z]{4}[0-9]{7}(-[A-Za-z0-9]{1,3})?$/.test(trimValue) === false) {
                    throw new Error(
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo.chineseSymbols,
                    );
                  }
                },
              },
            ]}
          >
            <Input />
          </Form.Item>
        ),
      },
      {
        key: 'logisticsNo',
        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'logisticsNo']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .billOfLadingNo1
            }
            rules={[{ required }]}
          >
            <Input />
          </Form.Item>
        ),
      },
    ],
    /** AWB(空运) */
    // 2: [],
    /** TRUCK(卡车) */
    3: [
      {
        key: 'totalTrayNum',
        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'totalTrayNum']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .totalNumberOfPallets1
            }
            rules={[
              { required },
              {
                pattern: regexUtils.nonZeroPositiveInteger,
                message:
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                    .pleaseEnterAPositive1,
              },
              { type: 'number', min: 1, max: 35 },
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        ),
      },
      trapNum,
    ],
    /** 快递  */
    5: [
      {
        key: 'totalTrayNum',

        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'logisticsChannelProvider']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .logisticsChannel1
            }
            rules={[{ required }]}
          >
            <ZSearchSelect
              {...{
                options: LOGISTICS_CHANNEL_PROVIDER_TYPE,
                searchByLocal: true,
              }}
            />
          </Form.Item>
        ),
      },
      trapNum,
      {
        colSpan: 8,
        children: (
          <Form.Item
            name={['logisticsData', 'tempTotalNum']}
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
                .totalNumberOfBoxes1
            }
          >
            <Input disabled />
          </Form.Item>
        ),
      },
    ],
  };

  return diffField[channel] ?? [];
}

export default LogisticsInfo;
