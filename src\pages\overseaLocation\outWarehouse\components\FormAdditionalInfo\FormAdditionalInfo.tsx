// kiwi-disable
import React from 'react';
import { RowLayout } from '@/views';
import { Form, Input, InputNumber } from 'antd';
import { SearchSelect } from '@/components';
import { apiMapDictType } from '@/api';
import type { FormInstance } from 'antd';
import { ruleUtils } from '@/utils';

const { Item } = Form;
const { ruleNumLetterHyphen } = ruleUtils;

type TypeProps = {
  form: FormInstance;
};

/** 出库附加信息 */
export default function FormAdditionalInfo(props: TypeProps) {
  const { form } = props;
  const orderSourcePlatformCode = Number(Form.useWatch('orderSourcePlatformCode'));
  /** 订单来源是Ebay */
  const isEbay = orderSourcePlatformCode === 2;

  return (
    <RowLayout columnNum={4}>
      <Item name="orderSourcePlatformCode" label={'订单来源平台'}>
        <SearchSelect
          placeholder={'请选择来源平台'}
          request={apiMapDictType.orderSourcePlatform}
          onChange={(value) => {
            form.setFieldsValue({
              ebayPlatformSalesNo: undefined,
              customerSalesNo: undefined,
            });
          }}
        />
      </Item>
      <Item
        name="customerRelatedNo"
        label={'客户关联单号'}
        tooltip={'客户系统下单号或管控单号'}
        rules={[
          ruleNumLetterHyphen,
          {
            max: 50,
          },
        ]}
      >
        <Input placeholder={'请输入客户关联单号'} />
      </Item>
      <Item
        name="customerSalesNo"
        label={'客户销售单号'}
        tooltip={'电商平台建议输入平台交易号'}
        rules={[
          ruleNumLetterHyphen,
          {
            max: 50,
          },
        ]}
      >
        <Input placeholder={'请输入客户销售单号'} />
      </Item>
      <Item
        name="platformStoreId"
        label={'平台店铺ID'}
        rules={[
          ruleNumLetterHyphen,
          {
            max: 30,
          },
        ]}
      >
        <Input placeholder={'请输入平台店铺ID'} />
      </Item>
      <Item
        name="platformSellerId"
        label={'平台卖家ID'}
        rules={[
          ruleNumLetterHyphen,
          {
            max: 30,
          },
        ]}
      >
        <Input placeholder={'请输入平台卖家ID'} />
      </Item>
      {isEbay && (
        <Item
          name="ebayPlatformSalesNo"
          label={'Ebay平台交易单号'}
          rules={[
            ruleNumLetterHyphen,
            {
              max: 30,
            },
          ]}
        >
          <Input placeholder={'请输入Ebay平台交易单号'} />
        </Item>
      )}
      {isEbay && (
        <Item
          name="ebaySalesId"
          label={'Ebay交易ID号'}
          rules={[
            ruleNumLetterHyphen,
            {
              max: 30,
            },
          ]}
        >
          <Input placeholder={'请输入Ebay交易ID号'} />
        </Item>
      )}
      {/* <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.orderSales}>
        <Input.Group compact>
          <Item name="salesCurrency" noStyle>
            <Input
              style={{ width: '30%' }}
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseEnterCurrency
              }
              disabled
            />
          </Item>
          <Item name="salesAmount" noStyle>
            <InputNumber
              style={{ width: '70%' }}
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseEnterPin
              }
              max={9999999999}
              precision={2}
              min={0.01}
            />
          </Item>
        </Input.Group>
      </Item> */}
      {/* <Item
        name="orderOwnerName"
        label={I18N.Src__Pages__Order__TrailerOrder.Index.orderContact}
        rules={[
          { type: 'string' },
          {
            max: 50,
            message: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.lengthCannot2,
          },
        ]}
      >
        <Input
          placeholder={
            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseEnterTheSubscription1
          }
        />
      </Item> */}
      {/* <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.orderContact}>
        <Input.Group compact>
          <Item name="orderOwnerIdType" noStyle>
            <Select
              style={{ width: '30%' }}
              placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
              options={certificateTypeOptions}
            />
          </Item>
          <Item
            name="orderOwnerIdNo"
            noStyle
            rules={[
              {
                max: 30,
                message: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.lengthCannot1,
              },
              ruleNumLetterHyphen,
            ]}
          >
            <Input
              style={{ width: '70%' }}
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                  .pleaseEnterTheCertificate
              }
            />
          </Item>
        </Input.Group>
      </Item> */}
      <Item
        name="warehouseRemark"
        label={'订单备注'}
        tooltip={'此备注信息只有仓库操作人员可见，海外买家不可见'}
        rules={[
          {
            type: 'string',
            max: 200,
          },
        ]}
      >
        <Input.TextArea rows={4} placeholder={'请输入订单备注'} />
      </Item>
    </RowLayout>
  );
}
