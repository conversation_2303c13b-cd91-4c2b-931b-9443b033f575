import './FormProDescriptions.less';
import type { ProDescriptionsItemProps, ProDescriptionsProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import classNames from 'classnames';
import { compUtils } from '@/utils';
import React from 'react';

type TypeFormProDescriptionsProps<T> = NsType.ArrayItemExpand<
  ProDescriptionsProps<T>,
  'columns',
  false | null | undefined
>;

export default function FormProDescriptions<T extends Record<string, any>>(
  props: TypeFormProDescriptionsProps<T>,
) {
  const defaultProps = {
    className: classNames(['form-pro-descriptions', 'ant-pro-descriptions']),
    layout: 'vertical' as const,
  };

  return (
    <ProDescriptions<T>
      {...{
        ...compUtils.propsMerge(defaultProps, props),
        columns: (props.columns || []).filter(Boolean) as ProDescriptionsItemProps<T>[],
      }}
    />
  );
}
