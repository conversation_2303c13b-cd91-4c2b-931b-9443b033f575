import React, { useEffect, useState } from 'react';
import { Form, Table, Select, Input, Button, Popconfirm, message, InputNumber, Space } from 'antd';

import type { ValidatorRule } from 'rc-field-form/lib/interface';
import type { Dispatch } from '@umijs/max';
import { connect, useIntl } from '@umijs/max';
import type { FormInstance } from 'antd/lib/form';
import { cloneDeep } from 'lodash';
import I18N from '@/utils/I18N';
import { CommonRulesMap } from '@/utils/rules';
import { useRefresh } from '@/utils/util';
import GoodsImage from '@/components/GoodsImage';
import './GoodsBelongSkuTableForm.less';
import { apiSearchGoodsBySkuOrName } from '../goodsApi';

interface IProps {
  name: string;
  rules?: ValidatorRule[];
  form: FormInstance;
  initGoods: any[];
  dispatch: Dispatch;
}

const GoodsBelongSkuTableForm: React.FC<IProps> = ({
  name: prefix,
  rules,
  dispatch,
  form,
  initGoods,
}) => {
  const [goodsOptions, setGoodsOptions] = useState<any[]>([]);
  const [selectedSkus, setSelectedSkus] = useState<string[]>([]);
  const refresh = useRefresh();

  useEffect(() => {
    setSelectedSkus(initGoods?.filter((o: any) => o && o.sku).map((item: any) => item.sku) || []);
  }, [initGoods]);

  const handleInputSearch = async (keyword: string = '') => {
    // const result = await dispatch({
    //   type: 'goods/searhOverseaLocationGoodsList',
    //   payload: {
    //     currentPage: 1,
    //     pageSize: 20,
    //     condition: {
    //       name: keyword,
    //     },
    //   },
    // });

    // setGoodsOptions(result?.data || []);
    const { data } = await apiSearchGoodsBySkuOrName({
      currentPage: 1,
      pageSize: 20,
      condition: {
        name: keyword,
      },
    });

    setGoodsOptions(data.records || []);
  };

  const handleSelect = (value: any, option: any, index: number) => {
    const nextGoodsList = cloneDeep(form.getFieldValue(prefix));
    const goods = cloneDeep(option.data);

    nextGoodsList[index] = {
      goodsId: goods.id,
      sku: goods.sku,
      pictureUrl: goods.pictureUrl,
      name: goods.name,
    };
    form.setFieldsValue({
      [prefix]: nextGoodsList,
    });
    setSelectedSkus(
      nextGoodsList?.filter((o: any) => o && o.sku).map((item: any) => item.sku) || [],
    );
    refresh.trigger();
  };

  const handleClearSku = (index: number) => {
    const nextGoodsList = cloneDeep(form.getFieldValue(prefix));

    nextGoodsList[index] = {
      goodsId: '',
      sku: '',
      pictureUrl: '',
      name: '',
    };

    form.setFieldsValue({
      [prefix]: nextGoodsList,
    });

    setSelectedSkus(
      nextGoodsList?.filter((o: any) => o && o.sku).map((item: any) => item.sku) || [],
    );
    refresh.trigger();
  };

  return (
    <Form.List name={prefix} rules={rules}>
      {(fields, { add, remove }, { errors }) => {
        const tableData = form.getFieldValue(prefix) || [];

        const columns = [
          {
            title: 'SKU',
            width: '20%',
            render: (_: any, field: any, index: number) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'sku']}
                  fieldKey={[field.fieldKey, 'sku']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                  ]}
                >
                  <Select
                    style={{ width: '100%' }}
                    showSearch
                    placeholder={
                      I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm
                        .nameInEnglish
                    }
                    onSearch={(value) => handleInputSearch(value)}
                    onFocus={() => handleInputSearch('')}
                    onSelect={(value, option) => handleSelect(value, option, index)}
                    onClear={() => handleClearSku(index)}
                    allowClear
                    optionLabelProp="label"
                    filterOption={false}
                    dropdownMatchSelectWidth={500}
                    dropdownRender={(menu: any) => {
                      return (
                        <>
                          <div className="option-label select-title">
                            <div style={{ width: 150 }}>SKU</div>
                            <div style={{ width: 150 }}>
                              {I18N.Src__Pages__Freight__Lcl.List.name}
                            </div>
                            <div style={{ width: 200 }}>
                              {
                                I18N.Src__Pages__OverseaLocation__Goods__Components
                                  .GoodsBelongSkuTableForm.englishName
                              }
                            </div>
                          </div>
                          {menu}
                        </>
                      );
                    }}
                  >
                    {goodsOptions?.map((item: any) => {
                      return (
                        <Select.Option
                          value={item.sku}
                          key={item.sku}
                          label={item.sku}
                          data={item}
                          disabled={selectedSkus.includes(item.sku)}
                        >
                          <div className="option-label">
                            <div style={{ width: 150 }}>{item.sku}</div>
                            <div style={{ width: 150 }}>{item.name}</div>
                            <div style={{ width: 200 }}>{item.enName}</div>
                          </div>
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.quantity,
            width: '20%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'number']}
                  fieldKey={[field.fieldKey, 'number']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                  ]}
                >
                  <InputNumber style={{ width: '100%' }} min={1} maxLength={9} />
                </Form.Item>
              );
            },
          },
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm.commodity,
            render: (_: any, field: any, index: number) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'lable']}
                  fieldKey={[field.fieldKey, 'lable']}
                >
                  <div className="option-label">
                    {tableData[index] && tableData[index]?.sku ? (
                      <GoodsImage src={tableData[index]?.pictureUrl} className="goods-img" />
                    ) : null}
                    <>{tableData && tableData[index] ? tableData[index].name : ''}</>
                  </div>
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Order__Detail.SiItem.remarks,
            width: '20%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'comment']}
                  fieldKey={[field.fieldKey, 'comment']}
                  rules={[
                    {
                      max: 255,
                      message:
                        I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm
                          .mostRemarks,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Common__Template.Index.operation,
            width: '10%',
            render: (_: any, field: any) => {
              return (
                <Form.Item {...field} fieldKey={[field.fieldKey, 'action']}>
                  <span>
                    <Popconfirm
                      title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                      onConfirm={() => {
                        remove(field.name);
                        const nextGoodsList = cloneDeep(form.getFieldValue(prefix));

                        setSelectedSkus(
                          nextGoodsList
                            ?.filter((o: any) => o && o.sku)
                            .map((item: any) => item.sku) || [],
                        );
                        refresh.trigger();
                      }}
                    >
                      <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                    </Popconfirm>
                  </span>
                </Form.Item>
              );
            },
          },
        ];

        return (
          <>
            <Space style={{ marginBottom: 10 }}>
              <Button type="primary" onClick={() => add()}>
                {
                  I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm
                    .increase
                }
              </Button>
            </Space>
            <Table
              // title={() => (
              //   <Space>
              //     <Button type="primary" onClick={() => add({})}>
              //       增加SKU
              //     </Button>
              //   </Space>
              // )}
              columns={columns}
              rowKey="sku"
              dataSource={fields}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content', y: 300 }}
            />
            <Form.ErrorList errors={errors} />
          </>
        );
      }}
    </Form.List>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.models.goods,
}))(GoodsBelongSkuTableForm);
