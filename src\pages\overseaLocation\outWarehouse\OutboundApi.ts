import { request } from 'umi';
import { EnumFrontChannelType, fileListDeconStruct } from './components';
import moment from 'moment';
import { apiBatchOutWarehouseRemoveOne } from '../importManage/importApi';

/** 出库列表页面查询 */
export function apiOutWarehouseList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 业务类型 */
      businessTypeList?: number[];
      /** 申请单号 */
      podOrderNoList?: string[];
      /** 客户销售单号 */
      customerSalesNoList?: string[];
      /** 客户关联单号 */
      customerRelatedNoList?: string[];
      /** 平台卖家id */
      platformSellerIdList?: string[];
      /** 平台店铺id */
      platformStoreIdList?: string[];
      /** SKU */
      sku?: string;
      /** zone信息 */
      zone?: string;
      /** 订单来源平台 */
      orderSourcePlatformCodeList?: string[];
      /** 派送服务类型 */
      dispatchServiceTypeList?: number[];
      /** 创建时间起 */
      createDateBegin?: string;
      /** 创建时间止 */
      createDateEnd?: string;
      /** 发货仓库id列表 */
      warehouseIdList?: string[];
      /** 下单仓库id */
      createDeliveryWarehouseIdList?: string[];
      /** 物流追踪单 */
      trackingNoList?: string[];
      /** 订单状态 */
      orderStatus?: string;
      /** 收件人姓名 */
      recipientName?: string;
    };
  }>,
) {
  return request<
    NsApi.TypeResponseData<{
      records: TypezOutWarehouseTable[];
      tabNum: Record<string, number>;
    }>
  >('/zouwu-oms-order/portal/outbound/list', { data, method: 'POST' });
}

function removeDeleted(list: any) {
  return (Array.isArray(list) ? list : []).filter((item) => item?.deleted !== true);
}

/** 处理出库表单数据, 删除delete标记数据,生成deliveryDTO等, 草稿单不调用 */
export function transOutboundFormData(data: TypeOutboundFormData) {
  /** form表单内部已完成优化, 统一使用 deliveryInfo, 类型 NsOutbound.TypeDeliveryInfo */
  /* TODO rewriteData 和  transOutboundFormData 二次处理数据设计不好, 待后端优化草稿接口后, 有希望统一 */
  /** express 因为历史原因, 数据结构已有两种情况
   * 1. 保存草稿, 使用 deliveryList, 且删除需要传递delete = true 标记
   * 2. 提交订单, 使用 deliveryDTO, 无需删除标记
   * 3. 费用试算, 使用 deliveryDTO, 无需删除标记
   * 4. 编辑详情返回, 取值 deliveryList?.[0]
   */
  const deliveryDTO = removeDeleted(data?.express?.deliveryList)?.[0] || data?.deliveryInfo || {};
  const newData = {
    ...data,
    deliveryInfo: undefined,
    express: {
      ...data?.express,
      deliveryDTO: {
        ...deliveryDTO,
        fileList: removeDeleted(deliveryDTO?.fileList),
      },
    },
    goodsList: removeDeleted(data?.goodsList),
  };

  return newData;
}

/** 虚拟仓一期新版出库订单提交,  因为草稿提交接口没有迁移至OMS, 所以这个接口要移除不必要的deleted数据
 * 草稿单提交 和 出库提交 数据结构不一致, 所以出库提交要额外加工一次数据
 */
export function apiOutWarehouseSubmit(data: TypeOutboundFormData) {
  return request('/zouwu-oms-order/portal/outbound/create/outboundSubmit', {
    data: transOutboundFormData(data),
    method: 'POST',
  });
}

/**
 * 获取申请单号
 * type: 1 入库, 2出库
 */
export function apiOutboundPodNoCreate() {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-order/portal/common/getOrderNo', {
    method: 'POST',
    data: {
      type: 2,
    },
  });
}

/** 保存草稿 */
export function apiOutWarehouseSaveDraft(data: any) {
  return request('/zouwu-oms-order/portal/outbound/create/saveDraft', {
    data: data,
    method: 'POST',
  });
}

/** 出库草稿单, 复制单回显接口 */
export function apiOutWarehouseGetFormData(data: { id: string }) {
  return request<NsApi.TypeResponseData<TypeOutboundFormData>>(
    '/zouwu-oms-order/portal/outbound/getForm',
    {
      method: 'POST',
      data,
    },
  ).then((res) => {
    const detailData = res?.data || {};
    const originDeliveryInfo = detailData?.express?.deliveryList?.[0] || {};

    return {
      ...res,
      data: {
        ...detailData,
        deliveryWarehouseCode: detailData.createDeliveryWarehouseCode,
        frontChannelType: originDeliveryInfo.customerChannelCode
          ? EnumFrontChannelType.customerChannel
          : EnumFrontChannelType.dispatchChannel,
        companyInfo: detailData?.companyId
          ? { label: detailData?.companyName, value: detailData?.companyId }
          : undefined,
        deliveryInfo: Object.assign(originDeliveryInfo, {
          ...fileListDeconStruct(originDeliveryInfo?.fileList || []),
          palletSizeList: originDeliveryInfo?.palletSizeList || [],
          estimatedPickupDate: originDeliveryInfo.estimatedPickupDate
            ? moment(originDeliveryInfo.estimatedPickupDate)
            : undefined,
          dispatchServiceNameInfo: originDeliveryInfo.dispatchServiceName
            ? {
                label: originDeliveryInfo.dispatchServiceNameStr,
                value: originDeliveryInfo.dispatchServiceName,
              }
            : undefined,
          dispatchServiceTypeInfo: originDeliveryInfo.dispatchServiceType
            ? {
                label: originDeliveryInfo.dispatchServiceTypeStr,
                value: originDeliveryInfo.dispatchServiceType,
              }
            : undefined,
        }),
      },
    };
  });
}

/** 出库详情页面接口 */
export function apiOutWarehouseDetail(data: { id?: string; podOrderNo?: string }) {
  return request<NsApi.TypeResponseData<TypeOutboundDetail>>(
    '/zouwu-oms-order/portal/outbound/detail',
    {
      method: 'POST',
      data: {
        /** 如果存在podOrderNo, 则不传id */
        id: data?.podOrderNo ? undefined : data?.id,
        podOrderNo: data?.podOrderNo,
      },
    },
  ).then((res) => {
    const detailData = res.data;
    const deliveryInfo = detailData?.deliveryList?.[0] || {};

    return {
      ...res,
      data: {
        ...detailData,
        deliveryList: [
          {
            ...deliveryInfo,
            ...fileListDeconStruct(deliveryInfo?.fileList || []),
          },
        ],
      } as TypeOutboundDetail,
    };
  });
}

/** 编辑异常单收件人信息 */
export function apiOutWarehouseEditErrorOrder(data: {
  /** 申请单号 */ podOrderNo?: string;
  /** 履约单号 */
  complianceNo?: string;
  /** 收件人省/州 */
  recipientProvince?: string;
  /** 收件人城市 */
  recipientCity?: string;
  /** 收件人详细地址列表 */
  recipientAddressList?: string[];
  /** 收件人姓名 */
  recipientName?: string;
  /** 收件人联系电话列表 */
  recipientPhoneNumberList?: string[];
  /** 收件人邮编 */
  recipientPostcode?: string;
  /** 收件人分邮编 */
  recipientBranchPostcode?: string;
  /** 邮编 */
  recipientEmail?: string;
}) {
  /* 历史接口 '/api/website/web/outstorehouse/editErrorOrder.do' */
  return request<
    NsApi.TypeResponseData<{
      showWindow: boolean;
      message: string;
    }>
  >('/zouwu-oms-order/portal/order/outbound/error/edit', {
    data,
    method: 'POST',
  });
}

/** 资金不足, 出库订单重试 */
export function apiOutWarehouseFundsRetry(data: { podOrderNos: string[] }) {
  return request<NsApi.TypeResponseData<any>>('/api/website/web/outstorehouse/fundsRetry.do', {
    method: 'POST',
    data,
  });
}

/** 出库订单物流轨迹详情 */
export type TypeLogisticsDetail = {
  /** 物流商 */
  logisticsChannel?: string;
  /** 追踪单号 */
  trackingNo?: string;
  /** 最新节点 */
  latestNode?: string;
  /** 物流轨迹节点 */
  logisticsTrackDetails?: {
    /** 轨迹节点描述 */
    actionCodeDesc?: string;
    /** 轨迹节点地址 */
    actionAddress?: string;
    /** 时区 */
    timeZone?: string;
    /** 轨迹节点时间 */
    time?: string;
  }[];
};

/** 出库订单物流轨迹详情查询 */
export function apiOutboundLogisticsDetail(data: { orderId: string }) {
  return request<NsApi.TypeResponseData<TypeLogisticsDetail>>(
    '/zouwu-oms-order/portal/outbound/logistics/queryLogisticsTrack',
    { method: 'POST', data },
  );
}

/** 提交出库申请赔付 */
export function apiOutboundCompensateApply(data: TypeOutboundCompensateApplyFormData) {
  return request<NsApi.TypeResponseData>(
    '/zouwu-oms-order/portal/outbound/compensate/applyCompensate',
    {
      method: 'POST',
      data,
    },
  );
}

/** 出库申请赔付详情 */
export function apiOutboundCompensateDetail(data: { /** 履约单号 */ complianceNo: string }) {
  return request<NsApi.TypeResponseData<TypeOutboundCompensateApplyDetail>>(
    '/zouwu-oms-order/portal/outbound/compensate/getCompensateDetail',
    { method: 'POST', data },
  );
}

/** 出库单申请物流拦截 */
export function apiOutboundLogisticsInterceptApply(data: {
  /** 出库单号 */
  orderId?: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 备注 */
  remark?: string;
}) {
  return request<NsApi.TypeResponseData<TypeOutboundCompensateApplyDetail>>(
    '/zouwu-oms-order/portal/order/logistics/intercept/submit',
    { method: 'POST', data },
  );
}

/** 出库补充面单 */
export function apiOutboundSupplementLabel(data: {
  /** 申请单号 */
  podOrderNo?: string;
  /** 订单附件集合 */
  fileList: NsOutbound.TypeFileItem[];
  /** 追踪单号 */
  trackingNo?: string;
}) {
  return request('/zouwu-oms-order/portal/outbound/express/uploadLabel', { method: 'POST', data });
}

/** 出库列表数据导出 */
export function apiOutboundExport(data: any) {
  return request('/zouwu-oms-system/portal/order/export/outbound', { method: 'POST', data });
}

/** 查询出库详情费用清单 */
export function apiOutboundChargeList(data: {
  /** 出库单申请单号 */
  podOrderNo?: string;
  /** 计费类型(1:应收 2:应付 12:口岸应付) */
  costType?: number | string;
}) {
  return request<
    NsApi.TypeResponseData<
      {
        /** 费用清单id */
        id?: number;
        /** 出库单申请单号 */
        podOrderNo?: string;
        /** 费用账单号 */
        chargeRecordBillNo?: string;
        /** 计费类型(1:应收 2:应付 12:口岸应付) */
        costType?: number;
        /** 费用code */
        gmChargeCode?: string;
        /** 费用名称 */
        gmChargeName?: string;
        /** 费用金额 */
        chargeTotal?: number;
        /** 计费币种 */
        chargeCurrency?: string;
        /** 结算状态(1:待结算，2:已结算，3:已失效) */
        status?: number;
        /** 结算对象名称 付款单时为：仓库的运营公司 收款单时为：下单公司 */
        settlementTargetId?: string;
        /** 结算对象名称 */
        settlementTargetName?: string;
        /** 数据来源名称(1:系统生成，2:手动添加，3:系统补录，4:导入，5:独立结算单) */
        dataSource?: number;
        /** 备注 */
        remark?: string;
        /** 创建人ID */
        createById?: number;
        /** 创建人 */
        createByName?: string;
      }[]
    >
  >('/zouwu-oms-order/portal/outbound/order/charge/record/bill/list', {
    data: data,
    method: 'POST',
  });
}

/** 标准出库单取消, 排除取消中, 已完结订单, 草稿单 */
export function apiOutboundCancel(data: { podOrderNo?: string }) {
  return request<NsApi.TypeResponseData<number | null>>(
    '/zouwu-oms-order/portal/order/outbound/cancel/toc',
    {
      data,
      method: 'POST',
    },
  );
}

/** TOB出库单取消 */
export function apiOutboundTOBCannel(data: {
  /** 申请单号 */
  podOrderNo?: string;
}) {
  return request('/zouwu-oms-order/portal/order/outbound/cancel/tob', {
    data,
    method: 'POST',
  });
}

/** 草稿单删除 */
export function apiOutboundDraftDelete(data: { id?: string; orderSource?: number }) {
  if (Number(data?.orderSource) === 5) {
    return apiBatchOutWarehouseRemoveOne(data);
  }
  return request<NsApi.TypeResponseData<number | null>>(
    '/zouwu-oms-order/portal/outbound/create/delete',
    {
      data,
      method: 'POST',
    },
  );
}
