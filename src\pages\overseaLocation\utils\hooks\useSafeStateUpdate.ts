import { useEffect, useRef } from 'react';

/** 安全的状态更新 beforeUnmounted */
function useSafeStateUpdate() {
  const isUnmount = useRef(false);
  const safeUpdateFn = <FN extends (...args: any[]) => any>(effect: FN) => {
    return function (...args: Parameters<FN>) {
      if (isUnmount.current === false) {
        return effect(...args);
      }
    } as FN;
  };

  useEffect(() => {
    return () => {
      isUnmount.current = true;
    };
  }, []);

  return {
    isUnmount,
    safeUpdateFn,
  };
}
export default useSafeStateUpdate;
