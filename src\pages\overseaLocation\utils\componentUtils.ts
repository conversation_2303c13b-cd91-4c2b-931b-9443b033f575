import classNames from 'classnames';
import { typeUtils } from './typeof';

type TypeProps = {
  className?: string;
  [P: string]: any;
};

type TypeInProps = TypeProps | undefined | boolean;

function propsMerge<T extends TypeProps | undefined | null, S extends TypeInProps>(
  defaultProps: T,
  props: S,
  fnObjs?: Record<string, ((...args: any[]) => any) | false | undefined | null>,
) {
  const { isObject } = typeUtils;

  /** 如果不是object 和 undefined, 直接返回props, defaultProps无效 */
  if (isObject(props) === false && props !== undefined) {
    return props as any as TypeProps;
  } else if (props === undefined && isObject(defaultProps) === false) {
    return defaultProps;
  }
  /* 处理props合并, 会有一些值有特殊处理 */
  const newProps = {
    ...defaultProps,
    ...(props as TypeProps),
    className: classNames(defaultProps?.className, (props as TypeProps)?.className),
  } as TypeProps;

  if (fnObjs) {
    /** 可用于组件事件改写,而不是直接替换
     * @内部事件优先处理 */
    Object.keys(fnObjs).forEach((key) => {
      const fn = fnObjs[key];

      if (typeof fn === 'function') {
        const outerFn = newProps[key];

        newProps[key] = function (...args: any[]) {
          fn?.(...args);
          return outerFn?.(...args);
        };
      }
    });
  }
  return newProps;
}

export const compUtils = {
  propsMerge,
};
