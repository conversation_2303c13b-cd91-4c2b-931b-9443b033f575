import I18N from '@/utils/I18N';
import type { AutoCompleteProps, FormInstance } from 'antd';
import { AutoComplete } from 'antd';
import type { Ref } from 'react';
import React, { useImperativeHandle, useState } from 'react';
import { useRequest } from 'umi';
import { apiCountryCityOptions } from './FormRecipientAddress/RecipientAddressImport';

const { Option } = AutoComplete;

type TypeCityAutoCompleteProps = {
  form: FormInstance;
  value?: any;
  onChange?: (value?: any) => void;
};

export type TypeCityAutoCompleteRef = {
  options: any[];
  fetchOptions: () => void;
};

const CityAutoComplete = React.forwardRef(
  (props: TypeCityAutoCompleteProps, ref: Ref<TypeCityAutoCompleteRef | undefined>) => {
    const { form, ...rest } = props;
    const request = async (searchValue?: string) => {
      const { express } = form.getFieldsValue();
      let res = [] as Awaited<ReturnType<typeof apiCountryCityOptions>>;

      if (express?.recipientProvince) {
        res = await apiCountryCityOptions({
          searchValue: searchValue?.trim(),
          parentCode: express.recipientProvince,
        });
      }
      return res;
    };
    const {
      loading,
      data: dataOptions,
      run,
    } = useRequest(request, {
      debounceInterval: 300,
      formatResult: (response) => response,
      manual: true,
    });
    const options = dataOptions || [];
    const fetchOptions = async (searchValue?: string) => {
      /** run返回是null, 根本不是异步 */
      run(searchValue);
    };
    const config = {
      style: {
        width: '33%',
      },
      placeholder:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components.CityAutoComplete
          .pleaseEnterTheCity,
      filterOption: false,
      onSearch: (searchValue) => {
        fetchOptions(searchValue);
      },
    } as AutoCompleteProps;

    useImperativeHandle(ref, () => ({
      options,
      fetchOptions,
    }));

    return (
      <AutoComplete {...config} {...rest}>
        {options.map((item) => {
          return (
            <Option key={item.value} value={item.label}>
              {item.label}
            </Option>
          );
        })}
      </AutoComplete>
    );
  },
);

export default CityAutoComplete;
