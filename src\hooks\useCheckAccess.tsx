import I18N from '@/utils/I18N';
import { Modal } from 'antd';
import React from 'react';
import { useModel } from 'umi';
import debounce from 'debounce-promise';
import REGEX from '@/utils/regex';
import { checkUserNameWaitActivate } from '@/service/entry';

export function useCheckAccess() {
  const { initialState } = useModel('@@initialState');
  const { webUser = {} } = initialState;

  const checkAccess = () => {
    return new Promise((resolve, reject) => {
      if (!webUser.category.length || !webUser.category.includes('ISCUSTOMER')) {
        Modal.info({
          title: I18N.Src__Hooks.UseCheckAccess.noPermission,
          content: <p>{I18N.Src__Hooks.UseCheckAccess.youAreNotACommittee}</p>,
        });

        reject();
      }

      if (webUser.memCompanyId && webUser.customerStatus === 1) {
        resolve(true);
      } else {
        Modal.info({
          title: I18N.Src__Hooks.UseCheckAccess.noPermission,
          content: <p>{I18N.Src__Hooks.UseCheckAccess.yourCurrent}</p>,
        });

        reject(false);
      }
    });
  };

  type CheckUserStatusParams = {
    userName: string;
    /** 找回密码类型 1 手机 2 邮箱 */
    type?: 1 | 2;
    /** 验证来源 1手机登录 2手机注册 3手机忘记密码 */
    source?: 1 | 2 | 3;
    /** 手机/邮箱验证码 */
    verificationCode?: string;
  };

  type CheckUserStatusResult = {
    existAccount?: boolean; //	账号是否存在
    inviteAccount?: boolean; //	是否为邀请账号
    waitActive?: boolean; //	是否为待激活账号
  };

  const getUserNameType = (userName: string) => {
    if (REGEX.EMAIL.test(userName)) {
      return 'email';
    }

    if (REGEX.MOBILE.test(userName)) {
      return 'mobile';
    }

    return '';
  };

  /**
   * errorMessage 提示错误语
   */
  const checkUserStatus = debounce(async (params: CheckUserStatusParams, errorMessage?: string) => {
    const userName = params.userName || '';
    const userNameMode = getUserNameType(userName);

    // 只有符合手机或者邮箱格式的name字段，才触发校验
    if (!['email', 'mobile'].includes(userNameMode)) {
      return Promise.resolve(true);
    }
    const response = await checkUserNameWaitActivate(params);

    const result: CheckUserStatusResult = response && response.success ? response.result : {};

    if (result.inviteAccount && result.waitActive) {
      return Promise.reject(
        new Error(
          errorMessage ||
            (userNameMode === 'mobile'
              ? I18N.Src__Hooks.UseCheckAccess.thisAccountIsToBe1
              : I18N.Src__Hooks.UseCheckAccess.thisAccountIsToBe),
        ),
      );
    }

    return Promise.resolve(true);
  }, 200);

  return {
    checkAccess,
    checkUserStatus,
  };
}
