/** 文件上传通用工具 */
import { EnumUploadFileType, getUploadPropsConfig } from '@/api';
import type { TypeSimpleUploadProps, TypeUploadFile } from '@/components';
import { TextValue } from '@/views';
import type { FormInstance } from 'antd';
import type { NamePath } from 'antd/lib/form/interface';
import React from 'react';
import { includesInArray } from '@/utils';

/** label附件上传, 单文件, 仅支持png, pdf
 * @ 用于出库附件/出库物流补充附件
 */
function calcLabelUploadProps({
  form,
  namePathMap,
  onFileChange,
}: {
  form: FormInstance;
  namePathMap: { filePath: NamePath; fileName: NamePath };
  onFileChange?: (file: TypeUploadFile, fileList: TypeUploadFile[]) => void;
}): TypeSimpleUploadProps {
  const fileSuffix = ['png', 'pdf'];

  return {
    ...getUploadPropsConfig(),
    form,
    allowValidFileStatus: true,
    fileSuffix,
    accept: fileSuffix.map((item) => `.${item}`).join(','),
    allowManual: false,
    data: {
      pathType: EnumUploadFileType.temp,
    },
    onOriginChange(info) {
      const file = info.file || {};
      const { status, response } = file;
      const { filePath, originFileName } = response?.data || {};

      (file as any).filePath = filePath;
      file.fileName = originFileName;
      file.url = status === 'done' ? filePath : undefined;
      if (includesInArray(['done', 'removed'], status)) {
        form.setFields([
          {
            name: namePathMap.filePath,
            value: status === 'done' ? filePath : undefined,
          },
          {
            name: namePathMap.fileName,
            value: status === 'done' ? originFileName : undefined,
          },
        ]);
        onFileChange?.(file, info.fileList);
      }
    },
    btnExtra: () => {
      return (
        <span style={{ display: 'none' }}>
          <TextValue
            {...{
              name: namePathMap.filePath,
              label: 'labelFilePath',
            }}
          />
          <TextValue
            {...{
              name: namePathMap.fileName,
              label: 'labelFileName',
            }}
          />
        </span>
      );
    },
  };
}

export const fileUploadUtils = {
  calcLabelUploadProps,
};
