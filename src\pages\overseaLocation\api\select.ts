/* eslint-disable no-return-assign */
import { request } from '@umijs/max';
import { useCallback, useEffect, useState } from 'react';
import { includesInArray, promiseCacheApi, typeUtils } from '@/pages/overseaLocation/utils';

type TypeDictOption = { label: string; value: string };

/** 一次性获取全量字典接口 */
export const apiDictTypeOptionsGetAll = promiseCacheApi(async function () {
  return request<
    NsApi.TypeResponseData<
      Record<EnumDictType, { dictionaryName: string; dictionaryCode: string }[]>
    >
  >('/zouwu-oms-system/portal/dictionary/listAll', {
    method: 'POST',
  });
});

function dictValueToNumber(options: TypeDictOption[], dictName: EnumDictType) {
  /** 特定字典需要将value转number类型 */
  if (includesInArray([] as const, dictName)) {
    return options.map((item) => ({
      ...item,
      value: Number(item.value) as any as string /** 这个as any是为了兼容其字典 */,
    }));
  }
  return options;
}

/** 字典类型 */
export const apiDictTypeOptions = promiseCacheApi(
  async (type: EnumDictType): Promise<{ label: string; value: string }[]> => {
    /* request<NsApi.TypeResponseData<{ dictionaryName: string; dictionaryCode: string }[]>>(
      '/zouwu-oms-system/portal/dictionary/listEnableAndDictionaryType',
      {
        data: { dictionaryType: type },
        method: 'POST',
      },
    ) */
    return apiDictTypeOptionsGetAll().then(({ data }) => {
      const arr = data[type] || [];
      const options = arr.map((item) => ({
        label: item.dictionaryName,
        value: item.dictionaryCode,
      }));

      return dictValueToNumber(options, type);
    });
  },
);

type TypeDictResultType = 'string' | 'valueEnum' | 'valueEnumPromise' | 'optionsPromise';
type TypeDictTypeCalc<E extends EnumDictType, T extends TypeDictResultType = 'string'> = {
  string: E;
  valueEnum: Record<string, string>;
  valueEnumPromise: () => Promise<Record<string, string>>;
  optionsPromise: () => ReturnType<typeof apiDictTypeOptions>;
}[T];

/** 请保障key和value一致 */
export enum EnumDictType {
  /** 商品 */
  /** SKU标签打印尺寸格式 */
  SkuLabelTemplate = 'SkuLabelTemplate',

  /** 仓库类型 */
  warehouseType = 'warehouseType',

  /** 订单取消状态-OMS */
  cancelOrderStatus = 'cancelOrderStatus',
  /** 订单取消状态-门户 */
  cancelOrderStatusPortal = 'cancelOrderStatusPortal',
  /** 条码规范 */
  barcodeType = 'barcodeType',
  /** 门户入库订单状态 */
  portalInboundOrderStatus = 'portalInboundOrderStatus',
  /** 入库快递物流渠道商 */
  logisticsChannelProviderType = 'logisticsChannelProviderType',
  /** 入库-头程标记(履约标记) */
  complianceType = 'complianceType',
  /** 入库-协同状态(履约状态) */
  teamWorkStatus = 'teamWorkStatus',
  /** 出库业务类型 */
  outboundBusinessType = 'outboundBusinessType',
  /** 出库订单签署服务 */
  outboundSignatureTypes = 'outboundSignatureTypes',
  /** 包材规格类型 */
  outboundPackageMaterialTypeEnum = 'outboundPackageMaterialTypeEnum',
  /** 配送服务名称 */
  DcDispatchServiceName = 'DcDispatchServiceName',
  /** 配送服务类型 */
  DcDispatchServiceType = 'DcDispatchServiceType',
  /** 地址偏远属性 */
  addressRemoteAttribute = 'addressRemoteAttribute',
  /** 订单类型 门户1 ERP2 电商平台3 FBA4 门户导入5 */
  orderSource = 'orderSource',
  /** 订单来源平台 Amazon 1 Ebay 2 Wayfair 3 overstock 4 lowes 5  home depot6 其它电商平台99 */
  orderSourcePlatform = 'orderSourcePlatform',
  /** 身份证字典 */
  certificateType = 'certificateType',
  /** 物流轨迹类型 未上网NOT_ONLINE 异常ERROR  运输中SHIPMENT  已签收DELIVERED */
  logisticsTrackType = 'logisticsTrackType',
  /** 索赔状态类型 */
  compensationStatusType = 'compensationStatusType',
  /** 出库托盘尺寸类型 */
  palletSizeType = 'palletSizeType',

  /** 记账码 */
  accountingCodeExpressService = 'accountingCodeExpressService',

  /**
   *
   * ============== 退货管理相关 =============
   *
   */
  /** 物流拦截状态 */
  orderLogisticsInterceptStatus = 'orderLogisticsInterceptStatus',
  /** 退货面单状态 */
  returnGoodsLabelStatus = 'returnGoodsLabelStatus',

  /** 退件包裹状态	  */
  returnPackageStatus = 'returnPackageStatus',
  /** 退件包裹审核状态 */
  orderReviewType = 'orderReviewType',
  /** 退件订单处理状态 */
  returnOrderStatus = 'returnOrderStatus',
  /** 退件业务类型 */
  returnBusinessType = 'returnBusinessType',

  /** =============== 库存管理 ============= */
  /** 出库状态 */
  allBusinessType = 'allBusinessType',
  /** 库存交易流水操作类型 */
  inventoryOperationType = 'inventoryOperationType',
  /** 出库单标签 */
  orderTag = 'orderTag',

  /** 费用 */
  /** 费用类别 */
  chargeType = 'chargeType',
  /** 费用账单状态(结算状态) */
  chargeRecordBillStatus = 'chargeRecordBillStatus',
  /** 费用清单业务场景 */
  chargeRecordBillBusinessType = 'chargeRecordBillBusinessType',
  /** 发票状态 */
  invoiceStatus = 'invoiceStatus',
  /** 核销状态 */
  allocationStatus = 'allocationStatus',

  /** 资金流水:业务场景 */
  cashFlowBusinessType = 'cashFlowBusinessType',
  /** 资金流水:流水类型 */
  cashFlowType = 'cashFlowType',

  /** ============ 导出列表 ============= */
  /** 导出状态 */
  exportStatus = 'exportStatus',
  /** 导出数据来源 */
  exportDataSource = 'exportDataSource',
  /** ============ 导入列表 ============= */
  /** 导入类型 */
  importType = 'importType',
  /** 导入状态 */
  importStatus = 'importStatus',
  /** 单据识别对象 */
  IdentityNoType = 'IdentityNoType',
}

type TypeEnumDictKeys = keyof typeof EnumDictType;

type TypeDictType<T extends TypeDictResultType = 'string'> = {
  -readonly /** 这里不要用 TypeEnumDictKeys, 必须展开写, 才能推导出注释 */
  [K in keyof typeof EnumDictType]: TypeDictTypeCalc<typeof EnumDictType[K], T>;
};

/** 字典apiMap快速使用 */
export const apiMapDictType = new Proxy({} as TypeDictType<'optionsPromise'>, {
  get(target, p: TypeEnumDictKeys) {
    if (target[p] !== undefined) {
      return target[p];
    }
    const req = () => {
      return apiDictTypeOptions(EnumDictType[p]);
    };

    target[p] = req;
    return req;
  },
});

/** 字典DictTypeValueEnum值快速使用 */
export const apiMapDictTypeValueEnum = new Proxy({} as TypeDictType<'valueEnumPromise'>, {
  get(target, p: TypeEnumDictKeys) {
    if (target[p] !== undefined) {
      return target[p];
    }
    const req = () => {
      const valueMap: Record<string, string> = {};

      return apiMapDictType[p]().then((options) => {
        options.forEach((item) => (valueMap[item.value] = item.label));
        return valueMap;
      });
    };

    target[p] = req;
    return req;
  },
});

/** hooks的字典枚举使用 */
export function useDictTypeValueEnum<T extends TypeEnumDictKeys>(
  /** 为true加载全部枚举 */
  dictTypeArr: readonly T[] | true,
  params: {
    /** 是否手动, 默认false */
    manual?: boolean;
  } = {},
) {
  type TypeValueEnumResult = Pick<TypeDictType<'valueEnum'>, T>;
  const typeArr =
    dictTypeArr === true ? (Object.keys(EnumDictType) as TypeEnumDictKeys[]) : dictTypeArr;
  const createValueEnumMap = () => {
    const valueEnumMap = {} as { [P in typeof typeArr[number]]: Record<string, string> };

    typeArr.forEach((typeName) => (valueEnumMap[typeName] = {}));
    return valueEnumMap;
  };
  const [dictTypeMap, setDictTypeMap] = useState(createValueEnumMap);
  const asyncGetDictValueEnum = useCallback(async () => {
    const valueEnumMap = createValueEnumMap();

    const allPromise = typeArr.map((typeName) => {
      return apiMapDictTypeValueEnum[typeName]().then(
        (valueEnum) => (valueEnumMap[typeName] = valueEnum),
      );
    });

    await Promise.all(allPromise);
    setDictTypeMap(valueEnumMap);

    return valueEnumMap as TypeValueEnumResult;
  }, []);

  useEffect(() => {
    if (params.manual !== true) {
      asyncGetDictValueEnum();
    }
  }, []);

  return {
    /** 字典项取值 */
    dictTypeMap: dictTypeMap as TypeValueEnumResult,
    /** 重新请求字典项 */
    asyncGetDictValueEnum,
  };
}
