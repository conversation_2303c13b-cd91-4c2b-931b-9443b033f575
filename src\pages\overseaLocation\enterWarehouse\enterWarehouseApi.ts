import { request } from '@umijs/max';
import { downloadFileByFullUrl } from '../utils';

/** 查询入库订单列表 */
export function apiEnterWarehouseList(
  data: NsApi.TypeRequestListQuery<{
    condition: any;
  }>,
) {
  return request<
    NsApi.TypeResponseData<{
      records: TypeEnterWarehouseTB[];
      countStatus: Record<string, number>;
    }>
  >('/zouwu-oms-order/portal/order/inbound/list', { data, method: 'POST' });
}

/**
 * 获取申请单号
 * type: 1 入库, 2出库
 */
export function apiInboundPodNoCreate() {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-order/portal/common/getOrderNo', {
    method: 'POST',
    data: {
      type: 1,
    },
  });
}

/** 删除草稿入库单 */
export function apiInboundDraftDelete(data: { orderId: string }) {
  return request('/zouwu-oms-order/portal/inbound/create/delete', { data, method: 'POST' });
}

/** 取消入库单 */
export function apiInboundOrderCancel(data: { orderId: string }) {
  return request('/zouwu-oms-order/portal/inbound/create/cancelOrder', { data, method: 'POST' });
}

/** 门户-草稿编辑详情查询 */
export async function apiInboundDraftDetail(data: {
  /** 单号ID */
  id?: number;
  /** 申请单号 */
  podOrderNo?: string;
}) {
  return request<NsApi.TypeResponseData<TypeInboundDraftDetail>>(
    '/zouwu-oms-order/portal/order/inbound/draft/detail',
    { method: 'post', data },
  );
}

/** 入库单草稿创建 */
export function apiInboundDraftCreate(data: TypeInboundFormData) {
  return request('/zouwu-oms-order/portal/inbound/create/saveDraft', { data, method: 'POST' });
}

/** 入库单草稿编辑 */
export function apiInboundDraftEdit(data: TypeInboundFormData) {
  return request('/zouwu-oms-order/portal/inbound/create/edit', { data, method: 'POST' });
}

/**
 * 入库单退回草稿
 */
export function apiInboundBackDraft(data: { podOrderNo?: string }) {
  return request<NsApi.TypeResponseData<any>>(
    '/zouwu-oms-order/portal/inbound/create/returnDraft',
    { data, method: 'POST' },
  );
}

/** 入库单提交 */
export function apiInboundOrderSubmit(data: TypeInboundFormData) {
  return request('/zouwu-oms-order/portal/inbound/create/inboundSubmit', { data, method: 'POST' });
}

/** 通过商品id查询执行详情 */
export function apiInboundDetail(data: { id: string }) {
  return request<NsApi.TypeResponseData>('/zouwu-oms-order/portal/order/inbound/orderDoorInfo', {
    data,
    method: 'POST',
  });
}

/** 通过商品id查询执行详情 */
export function apiInboundDoorExecuteInfoByGoods(data: { id: string }) {
  return request<NsApi.TypeResponseData>(
    '/zouwu-oms-order/portal/order/inbound/received/doorExecuteInfoByGoods',
    { data, method: 'POST' },
  );
}

/**
 * 船公司列表
 * @中台数据来源
 */
export function apiListShippingCompany(data: { searchValue: string }) {
  return request<
    NsApi.TypeResponseData<
      {
        id: string;
        shippingCompany: string;
        shippingCompanyCode: string;
      }[]
    >
  >('/api/website/web/instorehouse/listShippingCompany.do', {
    method: 'POST',
    data,
  }).then((res) => {
    return (res?.result || []).map((item) => {
      return {
        ...item,
        label: item.shippingCompany,
        value: item.shippingCompanyCode,
      };
    });
  });
}

/** 到仓详情列表 */
export function apiReachedRecords(data: { id: string }) {
  return request<NsApi.TypeResponseList<TypezReachedRecords[]>>(
    '/api/website/web/instorehouse/doorReachedRecordsByOrderGoodsId.do',
    { data, method: 'POST' },
  );
}

/** 入库预约单详情 */
export type TypeInboundReserveSendWarehouseDetail = {
  /** 入库订单 */
  podOrderNo?: string;
  /** 海运集装箱号 */
  containerNo?: string;
  /** 提单号 */
  logisticsNo?: string;
  /** 海运集装箱型号 */
  containerModel?: string;
  /** ETA */
  expectTime?: string;
  /** ETD */
  expectSendTime?: string;
  /** 预约仓库 */
  warehouseCode?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 电话 */
  contactPersonTel?: string;
  /** 邮箱 */
  contactPersonEmail?: string;
  /** 通知邮箱 */
  informEmail?: string;
  /** 地址 */
  address?: string;
  /** 预约送仓跳转链接 */
  doorTrailerLink?: string;
  /** 是否是环世履约 */
  isWWLCompliance?: boolean;
};

/** 入库单预约详情 */
export function apiInboundReserveDetail(data: { podOrderNo?: string }) {
  return request<NsApi.TypeResponseData<TypeInboundReserveSendWarehouseDetail>>(
    '/zouwu-oms-order/portal/orderTeamWork/deliveryAppointment',
    { data, method: 'POST' },
  );
}

/** 入库单预约邮箱提交 */
export function apiInboundReserveEmailSubmit(data: { podOrderNo?: string; emailList?: string }) {
  return request('/zouwu-oms-order/portal/orderTeamWork/submitEmail', { data, method: 'POST' });
}

/** 根据协同单号获取加密code */
export function apiInboundGetTeamWorkCode(data: { teamWorkNo?: string }) {
  return request('/zouwu-oms-order/portal/orderTeamWork/getTeamWorkCode', { data, method: 'POST' });
}

/** 入库单下载箱唛 */
export function apiInboundBoxMarkDownload(data: { podOrderNo?: string }) {
  return request('/zouwu-oms-system/portal/InBound/print/box/mark', {
    data,
    method: 'POST',
  }).then((res) => downloadFileByFullUrl(res?.data));
}
