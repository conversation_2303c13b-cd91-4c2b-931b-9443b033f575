/** 出库单类型 */
export enum EnumOutboundBusinessType {
  /** 标准出库 */
  normal = 201,
  /** TOB 出库 */
  tob = 203,
}

/** 订单来源
 * @ 1-门户，2-erp， 3-电商系统', 4-FBA   5-门户导入
 */
export enum EnumOutOrderSource {
  /** 门户键单 */
  portal = 1,
  /** ERP 建单, 禁止编辑和删除草稿单 */
  erp = 2,

  /** 门户导入 */
  portal_import = 5,
}

/** 出库建单附件类型
 * @ 1-Label附件 99-其他附件 10-托盘文件 20-箱唛文件 30-产品文件 40-AMZX文件 50-BOL文件
 */
export enum EnumOutboundFileType {
  /** label 附件 */
  'label' = 1,
  /** 其它附件 */
  'other' = 99,
  /** 托盘贴 */
  'pallet' = 10,
  /** 箱唛贴 */
  'boxMark' = 20,
  /** 产品贴 */
  'product' = 30,
  /** AMZX 文件 */
  'AMZX' = 40,
  /** BOL文件 */
  'BOL' = 50,
  /** POD 附件,签收履约完结后,后端从17渠道下载
   * @ 仅详情展示
   */
  'POD' = 60,
}
