export default {
  GoodsImport: {
    chineseSymbols: '执行结果',
    chineseSymbols1: '记录行',
    serialNo: '序号',
    chineseSymbols2: '条错误数据，请修改后重新上传。',
    chineseSymbols3: '条，存在',
    common: '共',
    chineseSymbols4: '错误信息',
    chineseSymbols5: '批量新增',
    chineseSymbols6: '导入成功，请十分钟之后再做入库。',
    chineseSymbols7: '🚀 ~ file: GoodsImport.tsx ~ line 51 ~ uploadRequest ~ uploadRes',
    chineseSymbols8: '单次导入商品数量限制500条，超过数量请分批导入。',
    chineseSymbols9: 'SKU请按最小售卖单位填写；',
    chineseSymbols10: 'SKU需要全局唯一，如果重复则无法导入；',
    chineseSymbols11: '请按商品实际尺寸重量填写；',
    chineseSymbols12: '重量：单位为KG（千克），支持三位小数；',
    chineseSymbols13: '长/宽/高：单位为CM（厘米），支持两位小数；',
    chineseSymbols14: '下载：《商品导入模版》',
  },
} as const;
