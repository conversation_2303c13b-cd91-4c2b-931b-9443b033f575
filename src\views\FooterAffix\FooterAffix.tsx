import './FooterAffix.less';
import type { AffixProps } from 'antd';
import { Affix } from 'antd';
import classnames from 'classnames';

export type TypeFooterAffixProps = {
  /** 子元素布局, 默认靠右 */
  layout?: 'center' | 'right';
} & AffixProps;

export default function (props: TypeFooterAffixProps) {
  const { children, layout = 'right', ...args } = props;
  const classMap = {
    center: 'layout-center',
    right: 'layout-right',
  };

  return (
    <Affix offsetBottom={0} className="footer-affix" {...args}>
      <div className={classnames('fixed-bottom', classMap[layout])}>{children}</div>
    </Affix>
  );
}
