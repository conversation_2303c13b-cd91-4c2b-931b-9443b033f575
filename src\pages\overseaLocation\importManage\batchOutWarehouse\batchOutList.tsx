import { But<PERSON>, <PERSON>, Popconfirm, Upload, message, Divider, Alert } from 'antd';
import React, { useRef, useState } from 'react';
import { Link } from '@portal/hive-sdk';

import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';
import { PageContainer } from '@/components/PageContainer';
import {
  apiBatchOutWarehouseImport,
  apiBatchOutWarehouseList,
  apiBatchOutWarehouseRemoveAll,
  apiBatchOutWarehouseGetTemplate,
} from '../importApi';
import { OVERSEA_LOCATION_BATCH_OUT_STATUS_ENUM } from '@/utils/const';
import { ossPathCombine } from '@/pages/overseaLocation/utils';
import { LoadButton, ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiMapDictType } from '@/pages/overseaLocation/api';

function BatchOutWarehouse() {
  const { config, actionRef } = useConfig();
  const [uploadLoading, setUploadLoading] = useState(false);

  return (
    <PageContainer>
      {/* <Modal
        {...{
          title: '批量出库',
          visible: true,
        }}
      /> */}
      <ProTable
        {...config}
        {...{
          headerTitle: (
            <Space>
              <Alert
                message={
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                    .getTheLatest
                }
                type="warning"
                showIcon
              />
              <LoadButton
                type="default"
                onClick={async () => {
                  await apiBatchOutWarehouseGetTemplate();
                }}
              >
                {
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                    .getTemplate
                }
              </LoadButton>
              <Upload
                {...{
                  beforeUpload(file) {
                    const { name } = file;
                    const arr = name.split('.');
                    const suffix = arr[arr.length - 1];
                    const sizeMb = file.size / 1024 / 1024;
                    const maxFileSizeMb = 1.5;
                    const fileFormat = ['xlsx', 'xls'];

                    if (fileFormat.includes(suffix) === false) {
                      message.error(
                        I18N.template(
                          I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                            .BatchOutList.onlyLatticesAreSupported,
                          { val1: fileFormat.join(', ') },
                        ),
                      );
                      return false;
                    }
                    if (sizeMb > maxFileSizeMb) {
                      message.error(
                        I18N.template(
                          I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload
                            .fileSize,
                          { val1: maxFileSizeMb },
                        ),
                      );
                      return false;
                    }
                  },
                  customRequest: async (params) => {
                    setUploadLoading(true);
                    try {
                      const res = await apiBatchOutWarehouseImport({
                        file: params.file as Blob,
                        directory: 'overseaLocation',
                      });

                      actionRef.current?.reload();
                      message.success(
                        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                          .BatchOutList.batchImport1,
                      );
                    } finally {
                      setUploadLoading(false);
                    }
                  },
                  showUploadList: false,
                }}
              >
                <Button type="primary" loading={uploadLoading}>
                  {
                    I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                      .batchImport
                  }
                </Button>
              </Upload>
            </Space>
          ),
        }}
      />
    </PageContainer>
  );
}

export default BatchOutWarehouse;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const columns = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiBatchOutWarehouseList>[0];

      const { data } = await apiBatchOutWarehouseList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    actionRef,
  };
}

function ErrorRed(props: { record: TypezBatchOutWarehouseTB; children: React.ReactNode }) {
  const { record } = props;

  return (
    <span
      style={{
        color: record.exceptionNum! > 0 ? 'red' : '',
      }}
    >
      {props.children}
    </span>
  );
}

function useColumns() {
  const columns = [
    // { title: '主键id', dataIndex: 'id', search: false },
    {
      title: I18N.Src__Pages__Message__Notice.Index.creationTime,
      dataIndex: 'createDate',
      search: false,
    },
    {
      title: I18N.Src__Pages__Home.Index.businessType,
      dataIndex: 'businessType',
      valueType: 'select',
      search: true,
      request: apiMapDictType.outboundBusinessType,
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: apiMapDictType.outboundBusinessType,
              include: ['201' /** 标准出库 */, '203' /** TOB出库 */],
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList.batchNo,
      dataIndex: 'batchNo',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
          .numberOfImports,
      dataIndex: 'importNum',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
          .numberOfIssues,
      dataIndex: 'submitNum',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
          .numberToBeIssued,
      dataIndex: 'draftNum',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
          .heteroconstant,
      dataIndex: 'exceptionNum',
      search: false,
      render(dom, record) {
        return <ErrorRed record={record}>{dom}</ErrorRed>;
      },
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.state,
      dataIndex: 'state',
      search: true,
      valueEnum: OVERSEA_LOCATION_BATCH_OUT_STATUS_ENUM,
      render(text, record) {
        return (
          <ErrorRed record={record}>
            {
              OVERSEA_LOCATION_BATCH_OUT_STATUS_ENUM[
                record.state as keyof typeof OVERSEA_LOCATION_BATCH_OUT_STATUS_ENUM
              ]
            }
          </ErrorRed>
        );
      },
    },
    // { title: '原文件地址', dataIndex: 'originalFilePath', search: false },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      key: 'action',
      search: false,
      width: 300,
      render: (text, record, index, action) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <LoadButton disabled={record.state === 1 /** 解析中 */}>
              <Link
                to={`/oversea-location/import-manage/batch-out-warehouse/batch-order-detail?id=${record.id}`}
              >
                {I18N.Src__Pages__Freight__YjtFcl.Detail.viewDetails}
              </Link>
            </LoadButton>
            <LoadButton disabled={record.state === 1 /** 解析中 */}>
              <a
                target="_blank"
                download
                href={ossPathCombine(record.originalFilePath as string, false)}
                rel="noreferrer"
              >
                {
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                    .downloadOriginal
                }
              </a>
            </LoadButton>

            <LoadButton
              {...{
                popconfirmProps: {
                  title:
                    I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                      .okToDelete,
                },
                onClick: async () => {
                  const res = await apiBatchOutWarehouseRemoveAll({ id: record.id! });

                  action?.reload();
                  //   if (res?.success === true) {
                  message.success(
                    I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                      .deleted1,
                  );
                  //   }
                },
                /** 全部下发 */
                disabled: record.state === 4,
              }}
            >
              {
                I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOutList
                  .deleted
              }
            </LoadButton>
          </Space>
        );
      },
    },
  ] as ProColumns<TypezBatchOutWarehouseTB>[];

  return columns;
}
