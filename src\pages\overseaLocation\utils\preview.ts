import { request } from 'umi';
import type { AxiosResponse } from '@/common-import';
import { includesInArray } from './utils';

/** Blob文件通过a表标签下载处理 */
export async function downloadBlob(blob: Blob, filename: string = '') {
  const objectUrl = URL.createObjectURL(blob);
  const downloadLink = document.createElement('a');

  downloadLink.setAttribute('href', objectUrl);
  downloadLink.setAttribute('download', decodeURIComponent(filename));
  downloadLink.setAttribute('target', '_blank');
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
  URL.revokeObjectURL(objectUrl);
}

/** 通过request的返回结果下载文件
 * 需要传递getResponse: true
 */
export async function downloadByResponse(res: AxiosResponse<Blob>, inFileName?: string) {
  const { data, headers } = res;

  const blob = new Blob([data]);
  const fileName =
    inFileName ||
    decodeURIComponent(headers['content-disposition']?.match(/filename=(.+)/)?.[1] || '');

  return downloadBlob(blob, fileName);
}

/** 通过完整url下载文件, GET请求
 * @ url不要在此函数内部加工
 * @ 内部通过原生fetch请求
 */
function downloadFileByAjax(fullPath: string, inFileName?: string) {
  /** 阿里云的oss服务禁止传cookie */
  // const credentials = fullPath.startsWith('https://eshipping.oss-cn-shanghai.aliyuncs.com')
  //   ? 'same-origin'
  //   : 'include';

  // return request(fullPath, {
  //   method: 'GET',
  //   responseType: 'blob',
  //   getResponse: true,
  //   credentials,
  // }).then((res) => downloadByResponse(res, inFileName));

  // 这里不要用request,  request的公共前缀拼接处理逻辑有问题
  // 等修复吧
  return fetch(fullPath).then((response) => {
    response.blob().then((blob: Blob) => downloadBlob(blob, inFileName));
  });
}

/**
 * 通过 <a target='_blank' href='filePath'></a> 下载文件
 * 特定文件 pdf, png, txt 会在chrome预览
 */
export function downloadFileByLink(filePath: string, filename?: string) {
  const a = document.createElement('a');

  a.href = filePath;
  /** 仅同源下载, download 重命名才生效 */
  a.download = filename || '';
  a.target = '_blank';
  a.click();
}

/**
 * @description 传入文件地址进行下载
 * @param filePath 文件路径, 必须全路径
 * @param filename 文件名称, 仅同源下载有效
 * @ 强制chrome 不预览 pdf, png, txt 等类型文件
 */
export async function downloadFileByFullUrl(filePath: string, inFilename?: string) {
  const fileType = filePath.split('.').pop();
  const fileName = filePath.split('/').pop();

  /** 强制这些后缀的文件也进入下载模式, 而不是chrome的预览 */
  if (includesInArray(['pdf', 'png', 'txt'], fileType?.toLowerCase())) {
    return downloadFileByAjax(filePath, fileName);
  }

  return downloadFileByLink(filePath, fileName);
}
