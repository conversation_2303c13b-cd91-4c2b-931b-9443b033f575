import I18N from '@/utils/I18N';
import type { ModalProps, TableProps } from 'antd';
import { Modal, Table } from 'antd';
import React, { useState } from 'react';

/** 列表关联仓库 */
export type TypeListItemShowMoreProps = {
  dataList: Record<string, string>[];
  columns: TableProps<any>['columns'];
  /** 列表展示内容字段名 */
  labelPropName?: string;
  /** 超过这个数量就展示更多, 默认3 */
  amount?: number;
  /** 数据量为0时展示内容, 默认'-' */
  emptyShow?: React.ReactNode;
  /** 按钮文字 */
  btnText?: string;
};
function ListItemShowMore({
  dataList,
  columns,
  labelPropName = 'label',
  amount = 3,
  emptyShow = '-',
  btnText = I18N.Src__Pages__OverseaLocation__Components__ListItemShowMore.ListItemShowMore.seeMore,
}: TypeListItemShowMoreProps) {
  const [visible, setVisible] = useState(false);
  const total = dataList?.length;
  const listNode = dataList
    .filter((_name, i) => i < amount)
    .map((item) => <div key={item[labelPropName]}>{item[labelPropName]}</div>);

  if (dataList.length < 1) {
    return emptyShow as JSX.Element;
  }

  const TableConfig = {
    rowKey: 'label',
    pagination: { hideOnSinglePage: total <= 10, pageSizeOptions: ['10'] },
    dataSource: dataList,
    columns,
  } as TableProps<any>;
  const modalConfig = {
    visible,
    footer: false,
    destroyOnClose: true,
    onCancel: () => setVisible(false),
  } as ModalProps;
  const moreNode = total > amount && (
    <Modal {...modalConfig}>
      <Table {...TableConfig} />
    </Modal>
  );

  return (
    <>
      {listNode}
      {moreNode && <a onClick={() => setVisible(true)}>{btnText}</a>}
      {moreNode}
    </>
  );
}

export default ListItemShowMore;
