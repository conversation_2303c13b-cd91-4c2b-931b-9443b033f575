export default {
  CityAutoComplete: {
    pleaseEnterTheCity: '请输入城市',
  },
  EditAddress: {
    editAddress: '编辑地址',
    modifyAddress: '修改地址',
  },
  RecipientAddress: {
    notMatchedTo: '未匹配到对应的校验规则, 默认不校验',
    no1: '门牌号不能超过8位',
    recipientDetails: '收件人详细地址不能超过30位',
    recipientDetails1: '收件人详细地址须在2-35字符之间',
    pleaseEnterReceipt: '请输入收件人详细地址',
    branchPostcode: '分邮编长度不能超过20位数字',
    pleaseEnterTheMail: '请输入邮编',
    postalCodeOnlyAllows: '邮编仅允许5位数字',
    pleaseEnterReceipt1: '请输入收件城市',
    pleaseSelect: '请选择收件省份',
    pleaseSelect1: '请选择收件国家',
    pleaseEnterAPositive: '请输入正确格式的邮箱',
    onlyInput: '只能输入数字、英文、空格、特定字符包含(+ - .)',
    telephoneLength: '电话长度不能超过30位',
    telephoneRequired: '电话必填',
    theTelephoneMust: '电话必须包含数字,而且包含数字必须大于9位',
    receivingContact: '收件联系人必填',
    receivingContact1: '收件联系人须在2-35个字符之间',
    pleaseEnterTheDoor: '请输入门牌号',
    houseNumber: '门牌号',
    recipientDetails2: '收件人详细地址',
    pleaseEnterAScore: '请输入分邮编',
    postalCode1: '分邮编',
    recipientCountry: '收件国家/省/市',
    pleaseEnterReceipt2: '请输入收件联系人邮箱',
    receivingContact2: '收件联系人邮箱',
    pleaseEnterReceipt3: '请输入收件联系人电话',
    pleaseEnterReceipt4: '请输入收件联系人姓名',
  },
} as const;
