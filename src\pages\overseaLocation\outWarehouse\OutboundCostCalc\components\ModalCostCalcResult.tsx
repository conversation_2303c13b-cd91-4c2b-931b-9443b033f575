import type { TypeZouModalRef } from '@/components';
import { ZouModal, ZouProTable } from '@/components';
import React, { useImperativeHandle, useRef, useState } from 'react';
import type { TypeCostCalcResult } from '../OutboundCostCalcApi';
import { eR } from '@/utils';

type TypeProps = {
  /** 组件Props定义 */
};

type TypeOpenParams = {
  /** 组件打开参数定义 */
} & TypeCostCalcResult;

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};

type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

/** 费用试算结果展示 */
export default React.forwardRef(function ModalCostCalcResult(
  props: TypeProps,
  ref: React.Ref<TypeModalRef | undefined>,
) {
  const { modalRef, detailData, asyncInit, asyncClear, asyncSubmit } = useConfig();

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams, operate?: TypeOperateMap) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title: '费用试算结果',
          afterClose() {
            asyncClear();
          },
          cancelButtonProps: {
            style: { display: 'none' },
          },
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      <ZouProTable
        {...{
          tableType: 'simple',
          options: false,
          dataSource: detailData.chargeList || [],
          toolbar: {
            menu: {
              type: 'tab',
              activeKey: '1',
              items: [{ label: '应收', key: '1' }],
            },
          },
          toolBarRender() {
            return [
              <div key="total" style={{ fontSize: 18, fontWeight: 'bold' }}>
                合计 : {eR(detailData.total)} {detailData.currency}
              </div>,
            ];
          },
          columns: [
            { title: '费用名称', dataIndex: 'chargeName' },
            { title: '费用CODE', dataIndex: 'chargeCode' },
            { title: '结算金额', dataIndex: 'amount' },
            { title: '结算币种', dataIndex: 'currency' },
          ],
        }}
      />
    </ZouModal>
  );
});

function useConfig() {
  const [detailData, setDetailData] = useState<TypeCostCalcResult>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const modalRef = useRef<TypeZouModalRef>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    setOperateMap(operate || {});
    setDetailData(params);
  }

  async function asyncClear() {
    setOperateMap({});
    setDetailData({});
  }

  async function asyncSubmit() {
    operateMap?.submitSuccessCB?.();
  }

  return {
    detailData,
    operateMap,
    asyncInit,
    asyncClear,
    asyncSubmit,
    modalRef,
  };
}
