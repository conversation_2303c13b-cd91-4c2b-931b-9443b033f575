import { exportUtils } from '@/utils-business';
import { request } from '@umijs/max';

/** 资金流水列表 */
export function apiCashFlowList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 费用单号 */
      chargeRecordBillNo?: string;
      /** 业务关联单据 */
      podOrderNo?: string;
      /** 流水类型1: 充值2: 冻结3: 解冻4: 扣款5: 退款 */
      flowTypeList?: number[];
      /** 费用名 */
      chargeName?: string[];
      /** 业务场景 1: 入库订单 2: 出库订单 3: 仓租 4: 充值 */
      businessTypeList?: number[];
      /** 开始时间 */
      startCreateDate?: string;
      /** 结束时间 */
      endCreateDate?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypezCashFlowTB[]>>(
    '/zouwu-oms-charge/portal/charge/cashFlowRecord/list',
    {
      data,
      method: 'POST',
    },
  );
}

/** 传入条件统计资金流水 */
export function apiCustomerCashFlowCount(data: {
  /** 费用单号 */
  chargeRecordBillNo?: string;
  /** 业务关联单据 */
  podOrderNo?: string;
  /** 流水类型1: 充值2: 冻结3: 解冻4: 扣款5: 退款 */
  flowTypeList?: number[];
  /** 费用名 */
  chargeName?: string[];
  /** 业务场景 1: 入库订单 2: 出库订单 3: 仓储 4: 充值 */
  businessTypeList?: number[];
  /** 开始时间 */
  startCreateDate?: string;
  /** 结束时间 */
  endCreateDate?: string;
  /** 币种 */
  currency?: string;
}) {
  return request<
    NsApi.TypeResponseData<{
      /** 充值总金额 */
      rechargeAmount?: number;
      /** 扣款总金额 */
      deductAmount?: number;
      /** 退款总金额 */
      refundAmount?: number;
      /** 冻结总金额 */
      freezeAmount?: number;
    }>
  >('/zouwu-oms-charge/portal/charge/cashFlowRecord/count', { method: 'POST', data });
}

/** 客户资金流水导出 */
export function apiCashFlowExport(data: {
  /** 结算币种 */
  currency?: string;
  /** 费用单号 */
  chargeRecordBillNo?: string;
  /** 业务关联单据 */
  podOrderNo?: string;
  /** 流水类型 1: 入款2: 冻结3: 解冻4: 扣款5: 退款 */
  flowTypeList?: number[];
  /** 费用名，传code */
  gmChargeCodeList?: string[];
  /** 业务场景 1: 入库订单2: 出库订单3: 仓储4: 充值 */
  businessTypeList?: number[];
  /** 开始时间 */
  startCreateDate?: string;
  /** 结束时间 */
  endCreateDate?: string;
  /** 分片最大id */
  rollId?: string;
  /** 没批限制 */
  limitNum?: number;
  /** 公司id */
  companyId?: string;
  /** 自定义文件名 */
  fileName?: string;
}) {
  return request('/zouwu-oms-system/portal/charge/cashFlowRecord/exportNew', {
    method: 'POST',
    data: exportUtils.transExportPageQuery(data),
  });
}
