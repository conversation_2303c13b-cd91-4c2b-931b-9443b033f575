import { apiGoodsListForGoodsChoose } from './goodsApi';

/** 通过商品列表查询, 刷新商品信息 */
export async function asyncGoodsInfoRefresh(
  arr: NsOutbound.TypeGoodsChooseTB[],
  warehouseCurrency?: string,
) {
  /** 商品不存在,不触发查询 */
  if (arr.length === 0) {
    return arr;
  }
  const skuArr = arr.map((item) => item.sku!);

  const {
    data: { records },
  } = await apiGoodsListForGoodsChoose({
    pageSize: skuArr.length,
    currentPage: 1,
    condition: {
      skuList: skuArr,
      warehouseCurrency,
    },
  });
  const skuMap = {} as any as Record<string, NsOutbound.TypeGoodsChooseTB>;

  (records || []).forEach((record) => {
    if (!record.sku) return;

    skuMap[record.sku] = record;
  });

  const newArr = arr.map((item) => {
    const info = skuMap[item.sku!];

    if (!info) return item;

    // console.log('商品信息被更新', item, info);

    /** 更新信息 */
    return {
      ...item,
      ...info,
    };
  });

  return newArr;
}
