import { promisePolling } from '@/utils';
import { request } from 'umi';

let stsToken: Awaited<ReturnType<typeof apiOssAliToken>>;
let stsTokenPromise: Promise<typeof stsToken> | undefined;

/** 获取阿里云OSS上传零时token */
export const apiOssAliToken = promisePolling(
  async function apiOssAliToken() {
    /** 参考逻辑 scripts-ts-node/sts.ts
     * 具体 逻辑后端接口决定
     */
    return request<
      NsApi.TypeResponseData<{
        /** 域名后缀, 后端返回格式, http://oss-cn-shanghai.aliyuncs.com/ */
        endpoint: string;
        /** `http://${bucket}.${region}.aliyuncs.com/` 拼接 */
        host: string;
        /** 上传路径 */
        rootPath: string;
        /** 域名前缀 */
        bucketName: string;
        /** sts 临时key */
        accessKeyId: string;
        /** sts 临时key */
        accessKeySecret: string;
        /** stsToken */
        token: string;
      }>
    >('/zouwu-oms-system/portal/fileOp/token', { method: 'POST' }).then((res) => {
      const { data } = res;
      const { accessKeyId, accessKeySecret, endpoint, token, bucketName, host, rootPath } = data;
      const stsInfo = {
        accessKeyId,
        accessKeySecret,
        endpoint,
        host,
        rootPath,
        stsToken: token,
        bucket: bucketName,
        region: endpoint.match(/(?<=(http:(\/\/))|(https:(\/\/)))[^\.]*(?=.*)/)?.[0],
      };

      stsToken = stsInfo;
      return stsInfo;
    });
  },
  function ({ result, next }) {
    next.resolve(result);
  },
  {
    /** dev环境redis特别不稳定,尝试2次 */
    retry: 2,
  },
);

/** token是全局唯一的 */
export async function asyncGetStsToken() {
  if (stsToken) {
    return stsToken;
  }
  if (stsTokenPromise) {
    return stsTokenPromise;
  }
  stsTokenPromise = new Promise<Awaited<ReturnType<typeof apiOssAliToken>>>((resolve, reject) => {
    apiOssAliToken()
      .then(resolve)
      .catch((error) => {
        stsTokenPromise = undefined;
        reject(error);
      });
  });

  return stsTokenPromise;
}
