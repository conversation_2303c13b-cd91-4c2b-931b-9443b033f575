import { message } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import I18N from '@/utils/I18N';

type TypeDate = string | Moment;

/** 判断在允许的时间范围内
 * @产品默认导出时间范围不超过93天(3个月)
 */
function isAllowDaysLimit<T extends number = 93>(
  start?: TypeDate,
  end?: TypeDate,
  maxDays = 93 as T | number,
  options: {
    /** 是否允许message提示, 默认允许 */
    allowMsg?: boolean;
  } = {},
) {
  const { allowMsg = true } = options;
  const day = start && end ? moment(end).diff(moment(start), 'day') + 1 : -1;

  if (day > maxDays || day < 0) {
    allowMsg &&
      message.error(
        I18N.template(I18N.Src__Pages__OverseaLocation__Utils.ExportUtils.exportDate1, {
          val1: maxDays,
        }),
      );
    return false;
  }
  return true;
}

/** 批量 判断在允许的时间范围内
 * @全部不通过,即为false
 */
function isBatchAllowDaysLimit<T extends number = 93>(
  timeArr: [string?, string?][],
  maxDays = 93 as T | number,
) {
  const resArr = timeArr.map((item) =>
    isAllowDaysLimit(item[0], item[1], maxDays, { allowMsg: false }),
  );
  /** 不存在true时 */
  const isExistTrue = resArr.includes(true);

  if (isExistTrue === false) {
    message.error(
      I18N.template(I18N.Src__Pages__OverseaLocation__Utils.ExportUtils.exportDate1, {
        val1: maxDays,
      }),
    );
  }

  return isExistTrue;
}

/** 不包含今天
 * @不包含返回true
 */
function isNotIncludeToday(
  end?: TypeDate,
  options: {
    /** 默认允许message提示 */
    allowMsg?: boolean;
    /** msgName名称，默认：导出日期 */
    msgName?: string;
  } = {},
): boolean {
  const today = moment();
  const {
    allowMsg = true,
    msgName = I18N.Src__Pages__OverseaLocation__Utils.ExportUtils.chineseSymbols1,
  } = options;

  if (!end || moment(end).isSameOrAfter(today, 'day')) {
    allowMsg &&
      message.error(
        I18N.template(I18N.Src__Pages__OverseaLocation__Utils.ExportUtils.chineseSymbols, {
          val1: msgName,
        }),
      );
    return false;
  }
  return true;
}

/** 转换列表导出PageQuery数据结构 */
function transExportPageQuery(data: Record<string, any> & { fileName?: string }) {
  const { fileName, ...condition } = data || {};

  return {
    pageQuery: { condition },
    fileName,
  };
}

export const exportUtils = {
  isAllowDaysLimit,
  isNotIncludeToday,
  isBatchAllowDaysLimit,
  transExportPageQuery,
};
