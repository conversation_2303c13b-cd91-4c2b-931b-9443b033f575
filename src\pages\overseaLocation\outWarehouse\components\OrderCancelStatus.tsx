import { Space, Tag, Tooltip } from 'antd';
import React from 'react';
import {
  SyncOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { useDictTypeValueEnum } from '@/api';
import { includesInArray } from '@/utils';
import { isOMS } from '@/oms-portal-diff-import';

/** 取消状态展示 */
export default function OrderCancelStatus({
  cancelOrderStatus,
  errorMsg,
}: {
  /** 取消状态码 */
  cancelOrderStatus?: number | null;
  /** 异常原因, 门户不展示 */
  errorMsg?: string;
}) {
  const { dictTypeMap } = useDictTypeValueEnum(['cancelOrderStatus', 'cancelOrderStatusPortal']);
  const tagMap = {
    /** 取消中-WMS */
    0: {
      icon: <SyncOutlined spin />,
      color: 'processing',
    },
    /** 取消中-OMS */
    5: {
      icon: <SyncOutlined spin />,
      color: 'processing',
    },
    /** 取消失败 */
    20: {
      icon: <CloseCircleOutlined />,
      color: 'default',
    },
    /** 取消异常 */
    21: {
      icon: <ExclamationCircleOutlined />,
      color: 'warning',
    },
  };

  return includesInArray([0, 5, 20, 21] as const, cancelOrderStatus) ? (
    <Space {...{ size: 0 }}>
      <Tooltip {...{ title: isOMS ? errorMsg : undefined }}>
        <Tag {...tagMap[cancelOrderStatus]} style={{ marginRight: 0 }}>
          {
            (isOMS ? dictTypeMap.cancelOrderStatus : dictTypeMap.cancelOrderStatusPortal)[
              cancelOrderStatus
            ]
          }
          {isOMS && errorMsg && <QuestionCircleOutlined style={{ marginLeft: 6 }} />}
        </Tag>
      </Tooltip>
    </Space>
  ) : null;
}

// 后端enum定义
// OrderOutboundCancelStatusEnum
// /**
//  * WMS取消中
//  */
// WMS_ORDER_CANCELING(0, "WMS取消中"),

// /**
//  * OMS取消中
//  */
// OMS_ORDER_CANCELING(5, "OMS取消中"),

// /**
//  * 取消成功
//  */
// CANCEL_SUCCESS(10, "取消成功"),

// /**
//  * 取消失败
//  */
// CANCEL_FAIL(20, "取消失败"),

// /**
//  * 取消异常
//  */
// CANCEL_ERROR(21, "取消异常"),
