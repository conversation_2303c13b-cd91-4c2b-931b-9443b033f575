import css from './ElementCopy.less';
import { Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import type { ParagraphProps } from 'antd/lib/typography/Paragraph';

type TypeElementCopyProps = {
  children: React.ReactNode;
  className?: string;
  copyProps?: ParagraphProps['copyable'];
};

/** 通过outerText实现元素文本复制 */
export default function ElementCopy(inProps: TypeElementCopyProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [txtArea] = useState(() => {
    const dom = document.createElement('textarea');

    dom.value = dom.style.position = 'absolute';
    dom.style.left = '-500px';
    document.body.appendChild(dom);
    return dom;
  });

  useEffect(() => {
    return () => {
      txtArea.remove();
    };
  }, []);

  return (
    <Typography.Paragraph
      {...{
        className: classnames(css['element-copy'], inProps.className),
        copyable: {
          onCopy(e) {
            txtArea.value = ref.current?.outerText || '';
            txtArea.select();
            document.execCommand('copy');
          },
          ...(inProps.copyProps as Exclude<ParagraphProps['copyable'], boolean>),
        },
      }}
    >
      <div className={classnames('left-box')} ref={ref}>
        {inProps.children}
      </div>
    </Typography.Paragraph>
  );
}
