/** 交易流水列表 */
type TypeTransactionFlowListTD = {
  /** id*/
  id?: number;
  /** 仓库id*/
  warehouseId?: number;
  /** 仓库名称*/
  warehouseName?: string;
  /** 仓库code*/
  warehouseCode?: string;
  /** 库区id*/
  warehouseAreaId?: number;
  /** 库区code*/
  warehouseAreaCode?: string;
  /** 库区名称*/
  warehouseAreaName?: string;
  /** 库区类型*/
  warehouseAreaType?: string;
  /** 商品id*/
  goodsId?: number;
  /** 商品名称*/
  goodsName?: string;
  /** 商品英文名称*/
  goodsEnName?: string;
  /** 商品图片地址(相对路径)*/
  pictureUrl?: string;
  /** 商品有效期*/
  shelfLife?: string;
  /** 商品sku*/
  sku?: string;
  /** 批次号*/
  batchNo?: number;
  /** 交易场景*/
  businessType?: number;
  /** 操作类型*/
  operationType?: string;
  /** 数量*/
  quantity?: number;
  /** 质量状态*/
  qualityType?: string;
  /** 流水号*/
  serialNo?: string;
  /** 单据编号(交易单据)*/
  docNo?: string;
  /** 申请单号*/
  applyDocNo?: string;
  /** 履约单号*/
  performanceDocNo?: string;
  /** wms单号*/
  wmsNo?: string;
  /** 交易发生时间*/
  happenedTime?: string;
  /** 公司id*/
  companyId?: number;
  /** 客户公司*/
  companyName?: string;
  /** 租户code*/
  partnerCode?: string;
  /** 订单标签, 后端快照存值, 前端无需翻译 */
  orderTagList?: string[];
};
