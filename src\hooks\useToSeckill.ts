import Cookies from 'js-cookie';
import { sendInitJumpSeckillInfo } from '@/service/global';
import { session } from '@/utils/session';

export async function jumpSeckillPage() {
  const appEnv = session.getAppEnv();

  const response = await sendInitJumpSeckillInfo();

  if (response && response.success) {
    const url = window.location.hostname.includes('localhost')
      ? 'http://localhost:8001'
      : appEnv === 'pro'
      ? 'https://seckill.wwlcargo.com/seck/home'
      : // : `${window.location.origin}/seck/home`;
        'http://**************:13000/seck/home';

    window.open(`${url}?token=${Cookies.get('auth_website')}`);
  }
}
