import React from 'react';
import type { FormItemProps } from 'antd';
import { Form } from 'antd';
import type { NamePath } from 'antd/lib/form/interface';

/** 纯展示组件,用于FormItem只读展示, 后续考虑命名 FormItemText */
export default function TextValue(props: {
  value?: React.ReactNode;
  /** Form.Item参数 */
  name?: NamePath;
  /** formItemProps.label */
  label?: string;
  /** Form.Item参数 */
  noStyle?: boolean;
  /** 渲染空值, 传true, 则渲染- */
  empty?: true | string;
  formItemProps?: FormItemProps;
  children?: (params: {
    /** formItem 注入的value */
    value?: any;
    /** formItem 注入的onChange */
    onChange?: (...args: any[]) => void;
  }) => React.ReactNode;
}) {
  const { name, label, formItemProps, noStyle, ...fieldProps } = props;
  const newFormItemProps = {
    name,
    noStyle,
    label,
    ...props.formItemProps,
  };
  let node: JSX.Element | string;

  if (name || props.formItemProps) {
    /** 如果存在name或formItemProps, 则包装FormItem返回 */
    node = (
      <Form.Item {...newFormItemProps}>
        {/** 剔除formItem属性,并渲染自身 */}
        <TextValue {...fieldProps} />
      </Form.Item>
    );
    return node;
  }

  node =
    typeof props.children === 'function'
      ? (props.children(props) as JSX.Element)
      : (props.value as JSX.Element);

  if (props.empty && !node) {
    node = props.empty === true ? '-' : props.empty;
  }

  return <>{node}</>;
}
