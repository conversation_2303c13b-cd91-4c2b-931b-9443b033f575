import { Divider, message, Space } from 'antd';
import React, { useRef } from 'react';
import moment from 'moment';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProFormInstance, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';
import { PageContainer } from '@/components/PageContainer';
import { apiChargeWarehouseDetailExport, apiChargeWarehouseList } from './chargeApi';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import { OVERSEA_CHARGE_SETTLEMENT_INTERVAL } from '@/pages/overseaLocation/enum';
import { dateUtils } from '@/utils';
import { combineLinkPath } from '@/pages/overseaLocation/utils';
import { ModalChargeWarehouseDetail } from './components';
import {
  apiAccountCurrencyOptions,
  apiMapDictType,
  apiQueryWarehouseOptions,
} from '@/pages/overseaLocation/api';
import { AsyncExportButton, AmountMoney } from '@/views';
import { exportUtils } from '@/utils-business';

const { transformDate } = dateUtils;

/** 费用管理/仓租 */
function ChargeWarehouse() {
  const { config, getSearchData, detailModalRef } = useConfig();

  return (
    <PageContainer>
      <ModalChargeWarehouseDetail ref={detailModalRef} />
      <ProTable
        {...config}
        {...{
          headerTitle: (
            <AsyncExportButton
              {...{
                buttonProps: {
                  type: 'primary',
                  async onClick() {
                    const searchData = getSearchData();
                    const { startChargeDate, endChargeDate, startTime, endTime } = searchData;
                    const { isNotIncludeToday, isBatchAllowDaysLimit } = exportUtils;

                    // 仓租导出逻辑不包含当天, 因为还没结算, 仅有值时校验
                    // if (
                    //   endChargeDate &&
                    //   isNotIncludeToday(endChargeDate, { msgName: '日结日期' }) === false
                    // ) {
                    //   return Promise.reject();
                    // }

                    if (
                      isBatchAllowDaysLimit([
                        [startChargeDate, endChargeDate],
                        [startTime, endTime],
                      ]) === false
                    ) {
                      return Promise.reject();
                    }
                  },
                },
                async request({ form }) {
                  const { fileName } = form.getFieldsValue();
                  const searchData = getSearchData();

                  await apiChargeWarehouseDetailExport({ ...searchData, fileName });
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.exportDetails}
            </AsyncExportButton>
          ),
        }}
      />
    </PageContainer>
  );
}

export default ChargeWarehouse;

function useConfig() {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const { columns, detailModalRef } = useColumns();

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      /* 1 : 应收 */
      costType: 1,
      ...formData,
    } as Parameters<typeof apiChargeWarehouseDetailExport>[0];
  }
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    formRef,
    scroll: { x: 'max-content' },
    search: {
      labelWidth: 100,
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...getSearchData(),
          ...args,
        },
      } as Parameters<typeof apiChargeWarehouseList>[0];
      const { data } = await apiChargeWarehouseList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    getSearchData,
    detailModalRef,
  };
}

function useColumns() {
  const detailModalRef = useRef<React.ElementRef<typeof ModalChargeWarehouseDetail>>(null);
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.expenseSheet,
      dataIndex: 'id',
      search: false,
      hideInTable: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.expenseDocNo,
      dataIndex: 'chargeRecordBillNo',
      search: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.settlementCycle,
      dataIndex: 'settlementInterval',
      valueType: 'select',
      valueEnum: OVERSEA_CHARGE_SETTLEMENT_INTERVAL,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.warehouse,
      dataIndex: 'warehouseName',
      search: true,
      fieldProps: {
        showSearch: true,
      },
      formItemProps: {
        name: 'warehouseCode',
      },
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: (queryParam) =>
                apiQueryWarehouseOptions({ queryParam }, { valueName: 'code' }),
            }}
          />
        );
      },
      renderText: (text, record) => {
        return `${record.warehouseCode}`;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols1,
      dataIndex: 'chargeDate',
      valueType: 'dateRange',
      search: {
        transform(value) {
          return {
            startChargeDate: value[0],
            endChargeDate: value[1],
          };
        },
      },
      render: (_text, record) => {
        return dateUtils.dateFormatter(record.chargeDate) || '-';
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform(value) {
          return {
            startTime: transformDate(value[0], 'START'),
            endTime: transformDate(value[1], 'END'),
          };
        },
      },
      render: (_text, record) => {
        return record.createDate || '-';
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.actualVolume,
      dataIndex: 'goodsActualTotalVolume',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.quantity,
      dataIndex: 'goodsTotalQuantity',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.billingVolume,
      dataIndex: 'goodsTotalVolume',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.settlementAmount,
      dataIndex: 'settlementTotal',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.settlementCurrency,
      dataIndex: 'settlementCurrency',
      search: true,
      /** 产品要求字段放在最后 */
      order: -100,
      fieldProps: {
        mode: 'multiple',
        request: (currency: string) => apiAccountCurrencyOptions({ currency }),
      },
      formItemProps: {
        name: 'chargeCurrencyList',
      },
      renderFormItem() {
        return <ZSearchSelect />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.calculatedAmount,
      dataIndex: 'chargeTotal',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.calculationCurrency,
      dataIndex: 'chargeCurrency',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.expenseCategory,
      dataIndex: 'chargeType',
      search: false,
      valueType: 'select',
      request: apiMapDictType.chargeType,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.invoicingStatus,
      dataIndex: 'invoiceStatus',
      search: false,
      valueType: 'select',
      request: apiMapDictType.invoiceStatus,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.invoiceNo,
      dataIndex: 'invoiceNo',
      search: false,
      render: (dom, record) => {
        return record?.invoiceUrl ? (
          <a href={combineLinkPath(record.invoiceUrl)} target="_blank" rel="noreferrer">
            {record.invoiceNo}
          </a>
        ) : (
          record?.invoiceNo
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
      /** 团团定的,和费用清单是同一张表, 所以是这个名字 */
      dataIndex: 'createDeliveryWarehouseCode',
      search: false,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   valueType: 'option',
    //   search: false,
    //   width: 120,
    //   fixed: 'right',
    //   render: () => {
    //     return (
    //       <Space split={<Divider type="vertical" />} size={0}>
    //         <a onClick={() => detailModalRef.current?.showModal()}>查看明细</a>
    //       </Space>
    //     );
    //   },
    // },
  ] as ProColumns<TypezChargeWarehouseTB>[];

  return {
    columns,
    detailModalRef,
  };
}
