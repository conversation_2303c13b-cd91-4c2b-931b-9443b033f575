/** 响应数据声明 */
declare namespace NsApi {
  type TypeResponseData<T = any> = {
    code: string;
    traceId?: string;
    success: boolean;
    result: T;
    data: T;
    refresh?: boolean;
    message?: string;
  };

  /** List列表ResponseData */
  type TypeResponseList<T = any[]> = TypeResponseData<{
    records: T;
    totalSize: number;
  }>;

  /** request list query 基础结构 */
  type TypeRequestListQuery<T> = {
    /** 每页显示条数,示例值(10) */
    pageSize?: number;
    /** 当前页数,示例值(1) */
    currentPage?: number;
    /** 排序列表 */
    orders?: {
      /** 排序属性名称,示例值(createDate) */
      property?: string;
      /**
       * 排序类型:
       * 1.ASC - 升序 2.DESC - 倒序 ,可用值:ASC,DESC,示例值(ASC)
       */
      orderType?: string;
    }[];
  } & T;
}
// export = NsApi;
// export as namespace NsApi;
