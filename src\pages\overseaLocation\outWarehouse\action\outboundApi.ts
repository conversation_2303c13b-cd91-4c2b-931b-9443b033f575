import { request } from 'umi';

/** 出库商品选择 */
type TypeOutboundGoodsRecord = {
  /** 仓库id */
  warehouseId?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 仓库代码 */
  warehouseCode?: string;
  /** 仓库币种 */
  warehouseCurrency?: string;
  /** 仓库类型 */
  warehouseType?: string;
  /** 仓库系统, 只有物理仓有 */
  wmsSystemCode?: string;
  /** 商品id */
  goodsId?: number;
  /** 商品名称 */
  goodsName?: string;
  /** 商品英文名称 */
  goodsEnName?: string;
  /** 商品sku */
  sku?: string;
  /** 商品信息 */
  goods?: {
    /** 商品sku */
    sku?: string;
    /** 商品名称 */
    name?: string;
    /** 商品英文名 */
    enName?: string;
    /** 客商id */
    companyId?: string;
    /** 客商名称 */
    companyName?: string;
    /** 国际商代码 */
    upc?: string;
    /** 欧洲商编码 */
    ean?: string;
    /** 商品类型 1：商品 2：组合商品 */
    goodsType?: number;
    /** 其他编码 */
    otherCode?: string;
    /** 安全库存 */
    safeNums?: number;
    /** 商品图片名称 */
    serverPictureName?: string;
    /** 商品图片链接 */
    pictureUrl?: string;
    /** 商品描述 */
    comment?: string;
    /** 商品长度 */
    length?: number;
    /** 商品宽度 */
    width?: number;
    /** 商品高度 */
    height?: number;
    /** 商品计量单位 */
    measureUnit?: string;
    /** 商品重量 */
    weight?: number;
    /** 商品重量单位 */
    weightUnit?: string;
    /** 计量类型：1公制 g/cm 2英制oz.av/in */
    measureType?: number;
    /** 商品品牌 */
    brand?: string;
    /** 商品材料 */
    material?: string;
    /** 商品规格 */
    specifications?: string;
    /** 商品用途 */
    purpose?: string;
    /** 是否需要签收 1：是 2：否 */
    sign?: boolean;
    /** 是否需要投保 1：是2：否 */
    insure?: boolean;
    /** 是否需要序列号 1：是 2：否 */
    serialNumber?: boolean;
    /** 包装类型 1：无包装2：自带包装3：特殊包装 */
    packingType?: number;
    /** 是否带电 1：是 2否 */
    electrified?: boolean;
    /** 打包类型1：无设置2：独立打包3：合并打包 */
    baleType?: number;
    /** 是否包含电池 1：是 2否 */
    containBattery?: boolean;
    /** 电池类型 1：锂离子电池2：锂金属电池3：其他 */
    batteryType?: number;
    /** 复核状态1：待复核2：已复核3：重新复核 */
    examinedStatus?: number;
    /** 复核描述 */
    examinedRemark?: string;
    /** 主键 id */
    id?: number;
    /** 合作者 code */
    partnerCode?: string;
  };
  /** 好件库存量 */
  goodQuantity?: number;
  /** 坏件库存量 */
  badQuantity?: number;
  /** 异常件库存量 */
  errorQuantity?: number;
  /** 仓库商品的投保信息 */
  portalGoodsCustomValueResp?: {
    /** 目的国名称 */
    countryName?: string;
    /** 目的国hscode */
    destinationHscode?: string;
    /** 商品货值 */
    goodsValue?: number;
    /** 历史名称: 保险价值  | 货品价值 */
    goodsInsuranceValue?: number;
    /** 历史名称: 申报价值 | 现在对应 投保金额 */
    goodsDeclaredValue?: number;
    /** 货币 */
    currency?: string;
    /** 税率 */
    taxRate?: number;
  };
};

/** 出库商品拉下接口
 * @deprecated 已废弃, 迁移至 src\pages\overseaLocation\outWarehouse\components\FormGoodsChoose\FormGoodsChooseApi.ts
 */
export function apiOutboundGoodsList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 仓库id */
      warehouseIds?: string[];
      /** 商品sku */
      sku?: string;
      /** 仓库类型 1：实际仓 2：虚拟仓 */
      warehouseType?: number;
      /** 订单类型（标准出库，TOB出库） */
      businessType?: number;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypeOutboundGoodsRecord[]>>(
    '/zouwu-oms-stock/portal/inventory/getInventoryGoodsList',
    { data, method: 'POST' },
  ).then((res) => {
    return {
      ...res,
      data: {
        ...res.data,
        records: convertSingleGoodsRecord(res?.data?.records),
      },
    };
  });
}

function convertSingleGoodsRecord(records: TypeOutboundGoodsRecord[]) {
  return records.map((o) => ({
    sku: o.sku,
    warehouseIdSku: `${o.warehouseId}-${o.sku}`,
    warehouseId: o.warehouseId,
    warehouseName: o.warehouseName,
    warehouseCode: o.warehouseCode,
    wmsSystemCode: o.wmsSystemCode,
    goodsPicture: o.goods?.pictureUrl,
    goodsName: o.goodsName,
    goodsEnName: o.goodsEnName,
    weightUnit: o.goods?.weightUnit,
    goodsWeight: o.goods?.weight,
    goodsLength: o.goods?.length,
    goodsWidth: o.goods?.width,
    goodsHeight: o.goods?.height,
    /** 商品计量单位 */
    volumeUnit: o.goods?.measureUnit,
    goodQuantity: o.goodQuantity,
    sign: o.goods?.sign,
    insure: o.goods?.insure,
    electrified: o.goods?.electrified,
    warehouseCurrency: o.warehouseCurrency,
    warehouseType: o.warehouseType,
    portalGoodsCustomValueResp: o?.portalGoodsCustomValueResp,
  }));
}

/** 出库商品数据结构 */
export type TypeOutboundGoodsTB = Awaited<
  ReturnType<typeof apiOutboundGoodsList>
>['data']['records'][0];

/** 是否存在虚拟仓
 * @deprecated 已废弃, 迁移至 src\pages\overseaLocation\outWarehouse\components\FormGoodsChoose\FormGoodsChooseApi.ts
 */
export function apiOutboundHasVirtualWarehouse() {
  return request<NsApi.TypeResponseData<boolean>>(
    '/zouwu-oms-system/portal/warehouse/isRelationVirtualWarehouse',
    {
      method: 'POST',
    },
  );
}
