import { request } from 'umi';

/** 物流拦截列表 */
export function apiLogisticsInterceptList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 出库单号 */
      podOrderNo?: string;
      /** 客户销售单号 */
      customerSalesNo?: string;
      /** 客户关联单号 */
      customerRelatedNo?: string;
      /** 物流追踪单号 */
      trackingNo?: string;
      /** 拦截状态(pending:待拦截,intercepting:拦截中,succeeded:拦截成功,failed:拦截失败) */
      interceptStatusList?: string[];
      /** 仓库id */
      warehouseId?: number;
      /** 创建时间开始时间 */
      startCreateDate?: string;
      /** 创建时间结束时间 */
      endCreateDate?: string;
      /** 公司唯一标识 */
      companyId?: string;
    };
  }>,
) {
  return request('/zouwu-oms-order/portal/order/logistics/intercept/list', {
    method: 'POST',
    data,
  });
}

/** 退货面单请 */
export function apiReturnFaceSheetApply(data: { orderId: string }) {
  return request('/zouwu-oms-order/portal/order/returningGoodsLabel/create', {
    method: 'POST',
    data,
  });
}
