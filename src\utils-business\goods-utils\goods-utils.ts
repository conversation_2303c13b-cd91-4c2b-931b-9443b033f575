import { eR, emptyRenderArrayJoin, typeUtils } from '@/utils';

const { isNumOrNumString } = typeUtils;

/** 商品尺寸排序: 降序排序, 最长边 * 次长边 * 最短边 */
export function goodsSizeSortStr(arr: (number | null | undefined)[]) {
  const newArr = arr
    .sort(function (a, b) {
      /** 降序排序, 最长边 * 次长边 * 最短边 */
      if (!isNumOrNumString(b)) {
        return -1;
      }
      if (!isNumOrNumString(a)) {
        return -1;
      }
      return b - a;
    })
    .map((val) => eR(val));

  return emptyRenderArrayJoin(newArr, ' * ');
}
