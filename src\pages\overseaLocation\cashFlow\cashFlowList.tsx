import React, { useRef, useState } from 'react';
import { Link } from '@portal/hive-sdk';
import { message } from 'antd';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps, ProFormInstance } from '@/common-import';
import { PageContainer, ProTable } from '@/common-import';
import { dateUtils } from '@/utils';
import { apiCashFlowExport, apiCashFlowList, apiCustomerCashFlowCount } from './cashFlowApi';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import {
  apiChargeItemOptions,
  apiMapDictType,
  useAccountCurrencyOptions,
} from '@/pages/overseaLocation/api';
import { historyCreateLocationForHive, historyGetQuery } from '@/pages/overseaLocation/utils';
import { CashFlowBoard } from './components';
import { AsyncExportButton, AmountMoney } from '@/views';
import { exportUtils } from '@/utils-business';

const { isAllowDaysLimit } = exportUtils;

function CashFLowList() {
  const { config, getSearchData } = useConfig();

  return (
    <PageContainer>
      <ProTable
        {...config}
        {...{
          headerTitle: (
            <AsyncExportButton
              {...{
                buttonProps: {
                  type: 'primary',
                  async onClick() {
                    const searchData = getSearchData();
                    const { startCreateDate, endCreateDate, currency } = searchData;

                    if (isAllowDaysLimit(startCreateDate, endCreateDate) === false) {
                      return Promise.reject();
                    }

                    if (!currency) {
                      message.error(
                        I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.chineseSymbols,
                      );
                      return Promise.reject();
                    }
                  },
                },
                async request({ form }) {
                  const { fileName } = form.getFieldsValue();
                  const searchData = getSearchData();

                  await apiCashFlowExport({ ...searchData, fileName });
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.export}
            </AsyncExportButton>
          ),
        }}
      />
    </PageContainer>
  );
}

export default CashFLowList;

function useConfig() {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  /** 存储表单接口值 */
  const tableDataRef = useRef<any>();
  const { columns, asyncAccountCurrencyOptions } = useColumns({ formRef });

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      /* 1 : 应收 */
      costType: 1,
      ...formData,
    } as Parameters<typeof apiCashFlowExport>[0];
  }
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    formRef,
    form: {
      ignoreRules: false,
    },
    scroll: { x: 'max-content' },
    search: {
      labelWidth: 100,
      defaultCollapsed: false,
    },
    tableExtraRender: (props, dataSource) => {
      return <CashFlowBoard dataSource={tableDataRef.current} />;
    },
    request: async (params) => {
      const { defaultCurrency } = await asyncAccountCurrencyOptions;
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          currency: defaultCurrency,
          ...args,
        },
      } as Parameters<typeof apiCashFlowList>[0];

      const [{ data }, countRes] = await Promise.all([
        apiCashFlowList(query),
        /**  根据传入条件统计资金流水 */
        apiCustomerCashFlowCount(query.condition),
      ]);

      tableDataRef.current = countRes?.data;
      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    getSearchData,
  };
}

function useColumns({ formRef }: { formRef: React.MutableRefObject<undefined | ProFormInstance> }) {
  const { currency } = historyGetQuery();
  const { currencyOptions, defaultCurrency, asyncAccountCurrencyOptions } =
    useAccountCurrencyOptions({
      defaultCurrency: currency,
      callback: (params) => formRef.current?.setFieldsValue({ currency: params.defaultCurrency }),
    });
  const required = true;
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.capitalFlow,
      dataIndex: 'cashFlowNo',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.expenseDocNo,
      dataIndex: 'chargeRecordBillNo',
      search: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.businessAssociation,
      dataIndex: 'podOrderNo',
      search: true,
      renderText: (text, record) => {
        const { businessType, podOrderNo } = record;
        const pathnameMap = {
          1: '/oversea-location/enter-warehouse/detail',
          2: '/oversea-location/out-warehouse/detail',
        };
        const pathname = pathnameMap[businessType as keyof typeof pathnameMap];

        if (!pathname) {
          return text;
        }

        return (
          text && (
            <Link
              to={
                historyCreateLocationForHive({
                  pathname,
                  query: { id: text, podOrderNo: podOrderNo! },
                })
                /* `${pathname}?id=${text}&podOrderNo=${podOrderNo}` */
              }
              rel="noreferrer"
            >
              {text}
            </Link>
          )
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.actualAmount,
      dataIndex: 'chargeAmount',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.flowType,
      dataIndex: 'flowType',
      valueType: 'select',
      search: true,
      request: apiMapDictType.cashFlowType,
      fieldProps: {
        mode: 'multiple',
        showSearch: false,
      },
      formItemProps: {
        name: 'flowTypeList',
      },
    },
    {
      title: I18N.Src__Pages__Freight__Lcl.List.expenseName,
      dataIndex: 'gmChargeName',
      search: true,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'gmChargeCodeList',
      },
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: (gmChargeName) => apiChargeItemOptions({ gmChargeName }),
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.businessScenario,
      dataIndex: 'businessType',
      valueType: 'select',
      search: true,
      request: apiMapDictType.cashFlowBusinessType,
      fieldProps: {
        mode: 'multiple',
        showSearch: false,
      },
      formItemProps: {
        name: 'businessTypeList',
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.accountBalance,
      dataIndex: 'accountBalance',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__Home__Components.AccountFunding.availableBalance,
      dataIndex: 'availableBalance',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.frozenAmount,
      dataIndex: 'frozenAmount',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.customerCurrency,
      dataIndex: 'customerCurrency',
      search: true,
      initialValue: defaultCurrency,
      valueType: 'select',
      /** 产品要求 客户币种排第一 */
      order: 100,
      fieldProps: {
        options: currencyOptions,
        allowClear: false,
      },
      formItemProps: {
        name: 'currency',
        rules: [{ required }],
      },
      renderFormItem() {
        return <ZSearchSelect />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.timeOfOccurrence,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform(value) {
          const { transformDate } = dateUtils;

          return {
            startCreateDate: transformDate(value[0], 'START'),
            endCreateDate: transformDate(value[1], 'END'),
          };
        },
      },
      render: (dom, record) => {
        return `${record.createDate}`;
      },
    },
  ] as ProColumns<TypezCashFlowTB>[];

  return { columns, asyncAccountCurrencyOptions };
}
