/** 批量出库列表 */
type TypezBatchOutWarehouseTB = {
  /** 主键id */
  id?: number;
  /** 业务类型(201=标准出库) */
  businessType?: string;
  /** 批次号 */
  batchNo?: string;
  /** 导入数 */
  importNum?: number;
  /** 下发数 */
  submitNum?: number;
  /** 待下发数 */
  draftNum?: number;
  /** 异常数 */
  exceptionNum?: number;
  /** 状态（1=解析中，2=待下发，3=部分下发，4=全部下发） */
  state?: number;
  /** 创建时间 */
  createDate?: number;
  /** 原文件地址 */
  originalFilePath?: string;
};

/** 导入订单数据量 */
type TypezOrderImportRecord = {
  /** 批次号 */
  batchNo?: string;
  /** 导入数 */
  importNum?: number;
  /** 下发数 */
  submitNum?: number;
  /** 待下发数 */
  draftNum?: number;
  /** 异常数 */
  exceptionNum?: number;
  /** 状态（1=解析中，2=待下发，3=部分下发，4=全部下发) */
  state?: number;
};

/** 批量出库详情 */
type TypezBatchOutWarehouseOrderTB = {
  /** 主键id */
  id?: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 订单类型1-入库单，2-出库单 */
  orderType?: number;
  /** 业务类型（201=标准出库） */
  businessType?: number;
  /** 订单来源平台名称（出库） */
  orderSourcePlatformName?: string;
  /** 平台卖家id（出库） */
  platformSellerId?: string;
  /** 状态（1=未下发，2=已下发，3=异常） */
  state: number;
  /** 订单状态 */
  orderStatus?: number;
  /** 异常信息 */
  exceptionMsg: string;
  /** 子单信息 */
  outboundChildList?: {
    /** 主键id */
    id?: number;
    /** 履约单号 */
    complianceNo?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 发货仓库名称 */
    deliveryWarehouseName?: string;
    /** 发货仓库code */
    deliveryWarehouseCode?: string;
    /** 商品信息 */
    goodsList?: {
      /** 主键id */
      id?: number;
      /** 申请单号 */
      podOrderNo?: string;
      /** 履约单号 */
      complianceNo?: string;
      /** SKU */
      sku?: string;
      /** 商品名称 */
      goodsName?: string;
      /** 总件数 */
      totalQuantity?: number;
    }[];
    /** 快递信息 */
    express?: {
      /** 主键id */
      id?: number;
      /** 订单号 */
      podOrderNo?: string;
      /** 收件人姓名 */
      recipientName?: string;
      /** 收件人国家名称 */
      recipientCountryName?: string;
      /** 收件人省/州名称 */
      recipientProvinceName?: string;
      /** 收件人城市名称 */
      recipientCityName?: string;
      /** 收件人详细地址1 */
      recipientAddress1?: string;
      /** 收件人联系电话1 */
      recipientPhoneNumber1?: string;
      /** 收件人邮编 */
      recipientPostcode?: string;
    };
    /** 配送信息 */
    delivery?: {
      /** 主键id */
      id?: number;
      /** 申请单号 */
      podOrderNo?: string;
      /** 快递信息id */
      expressId?: number;
      /** 派送服务类型 */
      dispatchServiceType?: number;
      /** 派送服务名称 */
      dispatchServiceName?: number;
      /** 客户渠道Code, 系统流转和客户使用都是按照code */
      customerChannelCode?: string;
      /** 客户渠道名称 */
      customerChannelName?: string;
      /** 追踪单号 */
      trackingNo?: string;
      /** 面单地址 */
      orderFile?: string;
      /** 文件名称 */
      orderFileName?: string;
    };
  }[];
};
