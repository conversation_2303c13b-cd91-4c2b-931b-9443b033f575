import { Button, Divider, Modal, ModalProps, Space } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';

type TypeDetailRef = React.ForwardedRef<{
  showModal: (id: string) => void;
}>;

/** 查看仓租明细详情 */
const ModalChargeWarehouseDetail = React.forwardRef((props, ref: TypeDetailRef) => {
  const { config, actionRef } = useConfig();
  const modalControl = useModal();

  useImperativeHandle(ref, () => ({
    showModal: () => {
      modalControl.show();
      actionRef.current?.reload();
    },
  }));

  return (
    <Modal {...modalControl.config}>
      <ProTable {...config} />
    </Modal>
  );
});

export default ModalChargeWarehouseDetail;

function useModal() {
  const [visible, setVisible] = useState(false);
  const show = () => setVisible(true);
  const config = {
    title:
      I18N.Src__Pages__OverseaLocation__ChargeManage__Components.ModalChargeWarehouseDetail
        .warehouseRentDetails,
    visible,
    destroyOnClose: true,
    onCancel: () => setVisible(false),
    onOk: () => setVisible(false),
  } as ModalProps;

  return {
    config,
    show,
  };
}

function useConfig() {
  const actionRef = useRef<ActionType>();
  const columns = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: false,
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof api>[0];
      const { result } = await api(query);

      return {
        data: result.records,
        success: true,
        total: result.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    actionRef,
  };
}

function useColumns() {
  const columns = [
    {
      title: '',
      dataIndex: '',
      search: false,
    },
  ] as ProColumns<any>[];

  return columns;
}
