import I18N from '@/utils/I18N';
import React, { useState } from 'react';
import { Steps } from 'antd';
import moment from 'moment';
import styles from './index.less';

const { Step } = Steps;

type PortStepsType = {
  data: any[];
  onModeToggleChange: (type: string) => void;
};

const PortSteps = ({ data = [], onModeToggleChange }: PortStepsType) => {
  const [startFlag, toggleStartFlag] = useState(true);
  const polList = data.filter((o) => o.type === 'POL');
  const podList = data.filter((o) => o.type === 'POD');

  const handleTypeChange = (type: string, flag: boolean) => {
    toggleStartFlag(flag);

    onModeToggleChange(type);
  };

  const getCurrentStep = () => {
    const activeRank = data.filter((o) => o.active).map((o) => o.rank);
    const maxRank = Math.max(...activeRank);

    // 当前最大rank是否在pol中
    const isMaxRankInPol = polList.find((o) => o.rank === maxRank);

    if (startFlag) {
      // 如果是存在pol中
      if (isMaxRankInPol) {
        return polList.findIndex((o) => o.rank === maxRank);
      }
      return polList.length;
    }
    if (isMaxRankInPol) {
      return -1;
    }
    return podList.findIndex((o) => o.rank === maxRank) + 1;
  };

  const getCurrentTitle = (record: any) => {
    const activeRank = data.filter((o) => o.active).map((o) => o.rank);
    const maxRank = Math.max(...activeRank);

    return record.rank > maxRank ? record.notReadyStatus : record.readyStatus;
  };

  return (
    <Steps progressDot size="small" className={styles.wrap} current={getCurrentStep()}>
      {!startFlag ? (
        <Step
          title={
            <span className={styles.action} onClick={() => handleTypeChange('start_port', true)}>
              {I18N.Src__Components__PortSteps.Index.seeDetailsOfStartingPort}
            </span>
          }
        />
      ) : null}
      {(startFlag ? polList : podList).map((item: any) => (
        <Step
          key={item.code}
          title={<span className={styles.title}>{getCurrentTitle(item)}</span>}
          description={
            <span className={styles.desc}>
              {item.time ? moment(item.time).format('YYYY-MM-DD HH:mm') : ''}
            </span>
          }
        />
      ))}
      {startFlag ? (
        <Step
          title={
            <span className={styles.action} onClick={() => handleTypeChange('end_port', false)}>
              {I18N.Src__Components__PortSteps.Index.detailedPortOfDestination}
            </span>
          }
        />
      ) : null}
    </Steps>
  );
};

export default PortSteps;
