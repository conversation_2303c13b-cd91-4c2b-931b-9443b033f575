// kiwi-disable
/**
 * formItem rules规则复用
 * 请务必加上注释, 因为可能会有国际化 */

import type { ValidatorRule } from 'rc-field-form/lib/interface';

const ruleUtils = {
  /** 请输入大于等于 0 的整数或小数,小数最多支持 4 位,整数不超过 10 位 */
  ruleNumAndFloatFourZero: {
    pattern: /^(0|[1-9][0-9]{0,9})(\.[0-9]{1,4})?$/g,
    message: '请输入大于等于 0 的整数或小数,小数最多支持 4 位,整数不超过 10 位',
  },
  /** 请输入大于等于 0 的整数或小数,小数最多支持 2 位,整数不超过 10 位 */
  ruleNumAndFloatTwoZero: {
    pattern: /^(0|[1-9][0-9]{0,9})(\.[0-9]{1,2})?$/g,
    message: '请输入大于等于 0 的整数或小数,小数最多支持 2 位,整数不超过 10 位',
  },
  /** 请输入非 0 整数或小数,小数最多支持 2 位 ,正整数位不超过 10 位,负整数位不超过 9 位 */
  ruleNumAndNegativeAndFloatTwo: calcRuleNumPositiveAndNegative({
    positive: 10,
    negative: 9,
    decimal: 2,
  }),
  /** 请输入 0 ~ 100 之间的数值,小数最多支持 2 位, 不包含 0 */
  ruleNumPercentageAndLeftF2: calcRuleNumClosedIntervalAndFloat({
    min: 0,
    max: 100,
    decimal: 2,
    closedInterval: [false, true],
  }),
  /** 请输入 0 ~ 100 之间的数值,小数最多支持 2 位 */
  ruleNumPercentageAndFloatTwo: calcRuleNumClosedIntervalAndFloat({ min: 0, max: 100, decimal: 2 }),
  /** 请输入 0 ~ 100 之间的数值,小数最多支持 1 位 */
  ruleNumPercentageAndFloatOne: calcRuleNumClosedIntervalAndFloat({ min: 0, max: 100, decimal: 1 }),
  /** 请输入非 0 整数或小数,小数最多支持 4 位 ,正整数位不超过 10 位,负整数位不超过 9 位 */
  ruleNumAndNegativeAndFloatFour: {
    pattern:
      /^((-?0\.[0-9]{1,4})|(([1-9][0-9]{0,9})(\.[0-9]{1,4})?)|-(([1-9][0-9]{0,8})(\.[0-9]{1,4})?))$/g,
    message: '请输入非 0 整数或小数,小数最多支持 4 位 ,正整数位不超过 10 位,负整数位不超过 9 位',
  },
  /** 请输入大于 0 的整数或小数,小数最多支持 4 位,整数不超过 10 位 */
  ruleNumAndFloatFour: {
    pattern: /^((0\.[0-9]{1,4})|(([1-9][0-9]{0,9})(\.[0-9]{1,4})?))$/g,
    message: '请输入大于 0 的整数或小数,小数最多支持 4 位,整数不超过 10 位',
  },
  ruleNumAndFloatThree: {
    /** 请输入大于 0 的整数或小数,小数最多支持 3 位,整数不超过 10 位 */
    pattern: /^((0\.[0-9]{1,3})|(([1-9][0-9]{0,9})(\.[0-9]{1,3})?))$/g,
    message: '请输入大于 0 的整数或小数,小数最多支持 3 位,整数不超过 10 位',
  },
  /** 请输入大于 0 的整数或小数,小数最多支持 2 位,整数不超过 10 位 */
  ruleNumAndFloatTwo: {
    pattern: /^((0\.[0-9]{1,2})|(([1-9][0-9]{0,9})(\.[0-9]{1,2})?))$/g,
    message: '请输入大于 0 的整数或小数,小数最多支持 2 位,整数不超过 10 位',
  },
  /** 请输入正整数 */
  ruleNoZeroPositiveInteger: {
    pattern: /^[1-9]\d*$/,
    message: '请输入正整数',
  },
  /** 请输入0或正整数 */
  rulePositiveIntZero: {
    pattern: /^((0)|([1-9]\d*))$/,
    message: '请输入0或正整数',
  },
  /** 请输入数字或英文字符 */
  ruleNumLetterHyphen: {
    pattern: /^[0-9a-zA-Z-]+$/,
    message: '仅支持数字、英文字符或短横杠',
  },
  /** 请输入大写英文子母, 不超过2位
   * @ 用于校验excel列名称规则
   */
  ruleExcelColumn: {
    pattern: /^[A-Z]{1,2}$/,
    message: '请输入大写英文字母, 不超过2位',
  },
  /** 仅支持中英文、数字、中英文横杠
   * @ 用于约束名称
   */
  ruleDefineName: {
    pattern: /^[-—a-zA-Z0-9\u4e00-\u9fa5]+$/,
    message: '仅支持中英文、数字、中英文横杠',
  },
  /** 仅允许 大写A~Z字母数字及英文横杆- */
  ruleDefineUpperCode: {
    pattern: /^[-A-Z0-9]+$/,
    message: '仅允许大写A~Z字母数字及英文横杆-',
  },
  /**
   * @description 校验规则保持和门户一致
   * 电话 英文、数字、字符、空格 +-
   * */
  SPECIAL_TEL_SYMBOL_SPACE: /^[ A-Za-z0-9+-.]+$/,
  /**
   * @description 校验规则保持和门户一致
   * 邮箱
   */
  EMAIL: /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,
  /** 校验整数范围 */
  calcRuleInteger,
} as const;

export { ruleUtils };

/** 不含0, 支持正数,负数,小数位数校验
 * @ 请输入非 0 整数或小数,小数最多支持 ${decimal} 位 ,正整数位不超过 ${positive} 位,负整数位不超过 ${negative} 位
 */
function calcRuleNumPositiveAndNegative({
  positive,
  negative,
  decimal,
}: {
  /** 整数位数, >=1 */
  positive: number;
  /** 负数位数, >=1 */
  negative: number;
  /** 小数位数, >=1 */
  decimal: number;
}) {
  return {
    /** 注意字符串转义和正则转义之间的转换 */
    pattern: new RegExp(
      `^((-?0\\.[0-9]{1,${decimal}})|(([1-9][0-9]{0,${
        positive - 1
      }})(\\.[0-9]{1,${decimal}})?)|-(([1-9][0-9]{0,${negative - 1}})(\\.[0-9]{1,${decimal}})?))$`,
    ),
    message: `请输入非 0 整数或小数,小数最多支持 ${decimal} 位 ,正整数位不超过 ${positive} 位,负整数位不超过 ${negative} 位`,
  };
}

/** 开闭区间校验, 支持小数位 [min,max], 默认闭区间 */
function calcRuleNumClosedIntervalAndFloat(params: {
  min: number;
  max: number;
  decimal: number;
  /** 默认闭区间 [true, true] */
  closedInterval?: [boolean, boolean];
}) {
  const { min, max, decimal, closedInterval = [true, true] } = params;

  return {
    async validator(rule, inVal) {
      const value = inVal?.toString?.().trim();

      /** null  undefined, 空串都不校验 */
      if (!value) {
        return;
      }
      const num = Number(value);
      const leftRes = closedInterval[0] === false ? num <= min : num < min;
      const rightRes = closedInterval[1] === false ? num >= max : num > max;
      /** 开区间提示语 */
      const openIntervalMsg = closedInterval.includes(false)
        ? `,不包含${[
            closedInterval[0] === false ? ` ${min} ` : undefined,
            closedInterval[1] === false ? ` ${max} ` : undefined,
          ]
            .filter(Boolean)
            .join(',')}`
        : '';

      if (
        leftRes ||
        rightRes ||
        new RegExp(`^[0-9]*(\\.[0-9]{1,${decimal}})?$`).test(value) === false
      ) {
        throw new Error(
          `请输入 ${min}~${max} 之间的数值,小数最多支持 ${decimal} 位${openIntervalMsg}`,
        );
      }
    },
  } as ValidatorRule;
}

/** 校验整数范围 */
function calcRuleInteger(params: {
  /** 含最小值 */
  min: number;
  /** 含最大值 */
  max: number;
}) {
  const { min, max } = params;

  return {
    async validator(rule, inVal) {
      const value = inVal?.toString?.().trim();

      /** null  undefined, 空串都不校验 */
      if (!value) {
        return;
      }
      const num = Number(value);

      if (Number.isInteger(num) === false || num > max || num < min) {
        throw new Error(`请输入 ${min} - ${max} 之间的整数`);
      }
    },
  } as ValidatorRule;
}
