declare module '*.css';
declare module '*.less';
declare module '*.png';
declare module '*.gif';
declare module '*.scss';
declare module '@dzg/sentry-utils';

declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement;
}

/**
 * @param key 事件名
 * @param data 数据
 */
declare const sensorsTrack: (key: string, data: any, callbacl?: Function) => void;

interface Window {
  '@@protalConfig': any;
  // sensorsTrack?: typeof sensorsTrack;
  WWL_PORTAL_autoOrderTracker?: boolean;
}

declare module 'js-md5';

declare const setGa: (p1: boolean, p2: any) => void;

declare const ga: (p1: string, p2: string) => void;

declare const doGa: (p1: string) => void;

declare const doMyGa: (p1: string) => void;

declare const setSensors: (params: any, otherGlobalParams: Function) => void;

declare module 'react-amap-plugin-geolocation';

declare namespace JSX {
  interface IntrinsicElements {
    'dzg-logistic-map': any;
  }
}

interface ResponseResultType<T = any> {
  result: T; // 返回结果
  success: boolean; // 请求是否成功
  errorCode?: number; // 错误码
  errorMsg?: string; // 错误信息
}

interface Loading {
  global: boolean;
  effects: Record<string, boolean | undefined>;
  models: Record<string, any>;
}
