import common from './common';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery} from './Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery';
import { default as $Src__Views__AsyncImportButton} from './Src__Views__AsyncImportButton';
import { default as $Src__Pages__OverseaLocation__ImportManage__ImportList} from './Src__Pages__OverseaLocation__ImportManage__ImportList';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel} from './Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel';
import { default as $Src__Pages__OverseaLocation__Components__SearchSelect} from './Src__Pages__OverseaLocation__Components__SearchSelect';
import { default as $Src__Views__ModalLogisticsTrack} from './Src__Views__ModalLogisticsTrack';
import { default as $Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet} from './Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet';
import { default as $Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept} from './Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply} from './Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics} from './Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate} from './Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord} from './Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord';
import { default as $Src__Pages__OverseaLocation__Components__SimpleUpload} from './Src__Pages__OverseaLocation__Components__SimpleUpload';
import { default as $Src__Views__AsyncExportButton } from './Src__Views__AsyncExportButton';
import { default as $Src__Pages__OverseaLocation__FundsManage__FundsAccount } from './Src__Pages__OverseaLocation__FundsManage__FundsAccount';
import { default as $Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components } from './Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components';
import { default as $Src__Pages__OverseaLocation__ExportManage__ExportList } from './Src__Pages__OverseaLocation__ExportManage__ExportList';
import { default as $Src__Routes } from './Src__Routes';
import { default as $Src__Pages__OverseaLocation__Goods__Components__GoodsImport } from './Src__Pages__OverseaLocation__Goods__Components__GoodsImport';
import { default as $Config__Routes } from './Config__Routes';
import { default as $Config } from './Config';
import { default as $Src__Utils } from './Src__Utils';
import { default as $Src } from './Src';
import { default as $Src__Pages__User__Register } from './Src__Pages__User__Register';
import { default as $Src__Pages__User__Login } from './Src__Pages__User__Login';
import { default as $Src__Pages__User__Forgetpw } from './Src__Pages__User__Forgetpw';
import { default as $Src__Pages__User } from './Src__Pages__User';
import { default as $Src__Pages__User__Components } from './Src__Pages__User__Components';
import { default as $Src__Pages__User__BindAccount } from './Src__Pages__User__BindAccount';
import { default as $Src__Pages__TransportationTrack } from './Src__Pages__TransportationTrack';
import { default as $Src__Pages__System__Security } from './Src__Pages__System__Security';
import { default as $Src__Pages__System__Account } from './Src__Pages__System__Account';
import { default as $Src__Pages__Settle__Overseas } from './Src__Pages__Settle__Overseas';
import { default as $Src__Pages__Settle__Invoice } from './Src__Pages__Settle__Invoice';
import { default as $Src__Pages__Settle__Domestic } from './Src__Pages__Settle__Domestic';
import { default as $Src__Pages__Settle__Ant } from './Src__Pages__Settle__Ant';
import { default as $Src__Pages__Seckill__Contract } from './Src__Pages__Seckill__Contract';
import { default as $Src__Pages__OverseaLocation__Utils } from './Src__Pages__OverseaLocation__Utils';
import { default as $Src__Pages__OverseaLocation__PostingKey__Components } from './Src__Pages__OverseaLocation__PostingKey__Components';
import { default as $Src__Pages__OverseaLocation__PostingKey } from './Src__Pages__OverseaLocation__PostingKey';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__List } from './Src__Pages__OverseaLocation__OutWarehouse__List';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Detail } from './Src__Pages__OverseaLocation__OutWarehouse__Detail';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Components } from './Src__Pages__OverseaLocation__OutWarehouse__Components';
import { default as $Src__Pages__OverseaLocation__OutWarehouse__Action } from './Src__Pages__OverseaLocation__OutWarehouse__Action';
import { default as $Src__Pages__OverseaLocation__OutWarehouse } from './Src__Pages__OverseaLocation__OutWarehouse';
import { default as $Src__Pages__OverseaLocation__Inventory__List } from './Src__Pages__OverseaLocation__Inventory__List';
import { default as $Src__Pages__OverseaLocation__Inventory__List__Components } from './Src__Pages__OverseaLocation__Inventory__List__Components';
import { default as $Src__Pages__OverseaLocation__Inventory__Flow__List } from './Src__Pages__OverseaLocation__Inventory__Flow__List';
import { default as $Src__Pages__OverseaLocation__Inventory__Detail__List } from './Src__Pages__OverseaLocation__Inventory__Detail__List';
import { default as $Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse } from './Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse';
import { default as $Src__Pages__OverseaLocation__Goods__List } from './Src__Pages__OverseaLocation__Goods__List';
import { default as $Src__Pages__OverseaLocation__Goods__Group__List } from './Src__Pages__OverseaLocation__Goods__Group__List';
import { default as $Src__Pages__OverseaLocation__Goods__Group__Action } from './Src__Pages__OverseaLocation__Goods__Group__Action';
import { default as $Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel } from './Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel';
import { default as $Src__Pages__OverseaLocation__Goods__Components } from './Src__Pages__OverseaLocation__Goods__Components';
import { default as $Src__Pages__OverseaLocation__Goods__Action } from './Src__Pages__OverseaLocation__Goods__Action';
import { default as $Src__Pages__OverseaLocation } from './Src__Pages__OverseaLocation';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse__List } from './Src__Pages__OverseaLocation__EnterWarehouse__List';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse__Detail } from './Src__Pages__OverseaLocation__EnterWarehouse__Detail';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse__Action } from './Src__Pages__OverseaLocation__EnterWarehouse__Action';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse__Action__Components } from './Src__Pages__OverseaLocation__EnterWarehouse__Action__Components';
import { default as $Src__Pages__OverseaLocation__EnterWarehouse } from './Src__Pages__OverseaLocation__EnterWarehouse';
import { default as $Src__Pages__OverseaLocation__Components__SingleUpload } from './Src__Pages__OverseaLocation__Components__SingleUpload';
import { default as $Src__Pages__OverseaLocation__Components__ModalUpload } from './Src__Pages__OverseaLocation__Components__ModalUpload';
import { default as $Src__Pages__OverseaLocation__Components__ListItemShowMore } from './Src__Pages__OverseaLocation__Components__ListItemShowMore';
import { default as $Src__Pages__OverseaLocation__ChargeManage__Components } from './Src__Pages__OverseaLocation__ChargeManage__Components';
import { default as $Src__Pages__OverseaLocation__ChargeManage } from './Src__Pages__OverseaLocation__ChargeManage';
import { default as $Src__Pages__OverseaLocation__CashFlow__Components } from './Src__Pages__OverseaLocation__CashFlow__Components';
import { default as $Src__Pages__OverseaLocation__CashFlow } from './Src__Pages__OverseaLocation__CashFlow';
import { default as $Src__Pages__Order__TrailerOrder } from './Src__Pages__Order__TrailerOrder';
import { default as $Src__Pages__Order__So } from './Src__Pages__Order__So';
import { default as $Src__Pages__Order__Si__TableForm } from './Src__Pages__Order__Si__TableForm';
import { default as $Src__Pages__Order__Si } from './Src__Pages__Order__Si';
import { default as $Src__Pages__Order__Si__Component__ShareModals } from './Src__Pages__Order__Si__Component__ShareModals';
import { default as $Src__Pages__Order__Si__Component } from './Src__Pages__Order__Si__Component';
import { default as $Src__Pages__Order__Ro } from './Src__Pages__Order__Ro';
import { default as $Src__Pages__Order__List } from './Src__Pages__Order__List';
import { default as $Src__Pages__Order__LclBooking } from './Src__Pages__Order__LclBooking';
import { default as $Src__Pages__Order__Import } from './Src__Pages__Order__Import';
import { default as $Src__Pages__Order__Detail } from './Src__Pages__Order__Detail';
import { default as $Src__Pages__Order__Components__Popovers } from './Src__Pages__Order__Components__Popovers';
import { default as $Src__Pages__Order__Components__HsCodeForm } from './Src__Pages__Order__Components__HsCodeForm';
import { default as $Src__Pages__Order__Components__CoustomTable } from './Src__Pages__Order__Components__CoustomTable';
import { default as $Src__Pages__Order__Components } from './Src__Pages__Order__Components';
import { default as $Src__Pages__Order__Components__AddressModal } from './Src__Pages__Order__Components__AddressModal';
import { default as $Src__Pages__Order__BookingTracking } from './Src__Pages__Order__BookingTracking';
import { default as $Src__Pages__Message__Todo } from './Src__Pages__Message__Todo';
import { default as $Src__Pages__Message__Notice } from './Src__Pages__Message__Notice';
import { default as $Src__Pages__Message__Config } from './Src__Pages__Message__Config';
import { default as $Src__Pages__Home } from './Src__Pages__Home';
import { default as $Src__Pages__Home__Components } from './Src__Pages__Home__Components';
import { default as $Src__Pages__Freight__YjtFcl } from './Src__Pages__Freight__YjtFcl';
import { default as $Src__Pages__Freight__Trailer } from './Src__Pages__Freight__Trailer';
import { default as $Src__Pages__Freight__Quotation } from './Src__Pages__Freight__Quotation';
import { default as $Src__Pages__Freight__Lcl } from './Src__Pages__Freight__Lcl';
import { default as $Src__Pages__Freight__Component__TagSelect } from './Src__Pages__Freight__Component__TagSelect';
import { default as $Src__Pages__Frame } from './Src__Pages__Frame';
import { default as $Src__Pages__Enterprise__Index } from './Src__Pages__Enterprise__Index';
import { default as $Src__Pages__Enterprise__Account } from './Src__Pages__Enterprise__Account';
import { default as $Src__Pages__Dynamic__Statistics } from './Src__Pages__Dynamic__Statistics';
import { default as $Src__Pages__Customer__CustomerManage } from './Src__Pages__Customer__CustomerManage';
import { default as $Src__Pages__Company__UserManager } from './Src__Pages__Company__UserManager';
import { default as $Src__Pages__Company__DepManager } from './Src__Pages__Company__DepManager';
import { default as $Src__Pages__Common__Template } from './Src__Pages__Common__Template';
import { default as $Src__Layout } from './Src__Layout';
import { default as $Src__Hooks } from './Src__Hooks';
import { default as $Src__Components__UploadFileModal } from './Src__Components__UploadFileModal';
import { default as $Src__Components__StandardTable } from './Src__Components__StandardTable';
import { default as $Src__Components__ShipiTrackDrawer } from './Src__Components__ShipiTrackDrawer';
import { default as $Src__Components__SelectLang } from './Src__Components__SelectLang';
import { default as $Src__Components__SearchSelect } from './Src__Components__SearchSelect';
import { default as $Src__Components__PortSteps } from './Src__Components__PortSteps';
import { default as $Src__Components__Im } from './Src__Components__Im';
import { default as $Src__Components__HeaderRightContent } from './Src__Components__HeaderRightContent';
import { default as $Src__Components__Header } from './Src__Components__Header';

export default Object.assign({}, {
  Common: common,
  Config__Routes: $Config__Routes,
  Config: $Config,
  Src__Routes: $Src__Routes,
  Src__Views__AsyncExportButton: $Src__Views__AsyncExportButton,
  Src__Pages__OverseaLocation__FundsManage__FundsAccount:
    $Src__Pages__OverseaLocation__FundsManage__FundsAccount,
  Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components:
    $Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components,
  Src__Pages__OverseaLocation__ExportManage__ExportList:
    $Src__Pages__OverseaLocation__ExportManage__ExportList,
  Src__Pages__OverseaLocation__Goods__Components__GoodsImport:
    $Src__Pages__OverseaLocation__Goods__Components__GoodsImport,
  Src__Components__Header: $Src__Components__Header,
  Src__Components__HeaderRightContent: $Src__Components__HeaderRightContent,
  Src__Components__Im: $Src__Components__Im,
  Src__Components__PortSteps: $Src__Components__PortSteps,
  Src__Components__SearchSelect: $Src__Components__SearchSelect,
  Src__Components__SelectLang: $Src__Components__SelectLang,
  Src__Components__ShipiTrackDrawer: $Src__Components__ShipiTrackDrawer,
  Src__Components__StandardTable: $Src__Components__StandardTable,
  Src__Components__UploadFileModal: $Src__Components__UploadFileModal,
  Src__Hooks: $Src__Hooks,
  Src__Layout: $Src__Layout,
  Src__Pages__Common__Template: $Src__Pages__Common__Template,
  Src__Pages__Company__DepManager: $Src__Pages__Company__DepManager,
  Src__Pages__Company__UserManager: $Src__Pages__Company__UserManager,
  Src__Pages__Customer__CustomerManage: $Src__Pages__Customer__CustomerManage,
  Src__Pages__Dynamic__Statistics: $Src__Pages__Dynamic__Statistics,
  Src__Pages__Enterprise__Account: $Src__Pages__Enterprise__Account,
  Src__Pages__Enterprise__Index: $Src__Pages__Enterprise__Index,
  Src__Pages__Frame: $Src__Pages__Frame,
  Src__Pages__Freight__Component__TagSelect: $Src__Pages__Freight__Component__TagSelect,
  Src__Pages__Freight__Lcl: $Src__Pages__Freight__Lcl,
  Src__Pages__Freight__Quotation: $Src__Pages__Freight__Quotation,
  Src__Pages__Freight__Trailer: $Src__Pages__Freight__Trailer,
  Src__Pages__Freight__YjtFcl: $Src__Pages__Freight__YjtFcl,
  Src__Pages__Home__Components: $Src__Pages__Home__Components,
  Src__Pages__Home: $Src__Pages__Home,
  Src__Pages__Message__Config: $Src__Pages__Message__Config,
  Src__Pages__Message__Notice: $Src__Pages__Message__Notice,
  Src__Pages__Message__Todo: $Src__Pages__Message__Todo,
  Src__Pages__Order__BookingTracking: $Src__Pages__Order__BookingTracking,
  Src__Pages__Order__Components__AddressModal: $Src__Pages__Order__Components__AddressModal,
  Src__Pages__Order__Components: $Src__Pages__Order__Components,
  Src__Pages__Order__Components__CoustomTable: $Src__Pages__Order__Components__CoustomTable,
  Src__Pages__Order__Components__HsCodeForm: $Src__Pages__Order__Components__HsCodeForm,
  Src__Pages__Order__Components__Popovers: $Src__Pages__Order__Components__Popovers,
  Src__Pages__Order__Detail: $Src__Pages__Order__Detail,
  Src__Pages__Order__Import: $Src__Pages__Order__Import,
  Src__Pages__Order__LclBooking: $Src__Pages__Order__LclBooking,
  Src__Pages__Order__List: $Src__Pages__Order__List,
  Src__Pages__Order__Ro: $Src__Pages__Order__Ro,
  Src__Pages__Order__Si__Component: $Src__Pages__Order__Si__Component,
  Src__Pages__Order__Si__Component__ShareModals: $Src__Pages__Order__Si__Component__ShareModals,
  Src__Pages__Order__Si: $Src__Pages__Order__Si,
  Src__Pages__Order__Si__TableForm: $Src__Pages__Order__Si__TableForm,
  Src__Pages__Order__So: $Src__Pages__Order__So,
  Src__Pages__Order__TrailerOrder: $Src__Pages__Order__TrailerOrder,
  Src__Pages__OverseaLocation__CashFlow: $Src__Pages__OverseaLocation__CashFlow,
  Src__Pages__OverseaLocation__CashFlow__Components:
    $Src__Pages__OverseaLocation__CashFlow__Components,
  Src__Pages__OverseaLocation__ChargeManage: $Src__Pages__OverseaLocation__ChargeManage,
  Src__Pages__OverseaLocation__ChargeManage__Components:
    $Src__Pages__OverseaLocation__ChargeManage__Components,
  Src__Pages__OverseaLocation__Components__ListItemShowMore:
    $Src__Pages__OverseaLocation__Components__ListItemShowMore,
  Src__Pages__OverseaLocation__Components__ModalUpload:
    $Src__Pages__OverseaLocation__Components__ModalUpload,
  Src__Pages__OverseaLocation__Components__SingleUpload:
    $Src__Pages__OverseaLocation__Components__SingleUpload,
  Src__Pages__OverseaLocation__EnterWarehouse: $Src__Pages__OverseaLocation__EnterWarehouse,
  Src__Pages__OverseaLocation__EnterWarehouse__Action__Components:
    $Src__Pages__OverseaLocation__EnterWarehouse__Action__Components,
  Src__Pages__OverseaLocation__EnterWarehouse__Action:
    $Src__Pages__OverseaLocation__EnterWarehouse__Action,
  Src__Pages__OverseaLocation__EnterWarehouse__Detail:
    $Src__Pages__OverseaLocation__EnterWarehouse__Detail,
  Src__Pages__OverseaLocation__EnterWarehouse__List:
    $Src__Pages__OverseaLocation__EnterWarehouse__List,
  Src__Pages__OverseaLocation: $Src__Pages__OverseaLocation,
  Src__Pages__OverseaLocation__Goods__Action: $Src__Pages__OverseaLocation__Goods__Action,
  Src__Pages__OverseaLocation__Goods__Components: $Src__Pages__OverseaLocation__Goods__Components,
  Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel:
    $Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel,
  Src__Pages__OverseaLocation__Goods__Group__Action:
    $Src__Pages__OverseaLocation__Goods__Group__Action,
  Src__Pages__OverseaLocation__Goods__Group__List:
    $Src__Pages__OverseaLocation__Goods__Group__List,
  Src__Pages__OverseaLocation__Goods__List: $Src__Pages__OverseaLocation__Goods__List,
  Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse:
    $Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse,
  Src__Pages__OverseaLocation__Inventory__Detail__List:
    $Src__Pages__OverseaLocation__Inventory__Detail__List,
  Src__Pages__OverseaLocation__Inventory__Flow__List:
    $Src__Pages__OverseaLocation__Inventory__Flow__List,
  Src__Pages__OverseaLocation__Inventory__List__Components:
    $Src__Pages__OverseaLocation__Inventory__List__Components,
  Src__Pages__OverseaLocation__Inventory__List: $Src__Pages__OverseaLocation__Inventory__List,
  Src__Pages__OverseaLocation__OutWarehouse: $Src__Pages__OverseaLocation__OutWarehouse,
  Src__Pages__OverseaLocation__OutWarehouse__Action:
    $Src__Pages__OverseaLocation__OutWarehouse__Action,
  Src__Pages__OverseaLocation__OutWarehouse__Components:
    $Src__Pages__OverseaLocation__OutWarehouse__Components,
  Src__Pages__OverseaLocation__OutWarehouse__Detail:
    $Src__Pages__OverseaLocation__OutWarehouse__Detail,
  Src__Pages__OverseaLocation__OutWarehouse__List:
    $Src__Pages__OverseaLocation__OutWarehouse__List,
  Src__Pages__OverseaLocation__PostingKey: $Src__Pages__OverseaLocation__PostingKey,
  Src__Pages__OverseaLocation__PostingKey__Components:
    $Src__Pages__OverseaLocation__PostingKey__Components,
  Src__Pages__OverseaLocation__Utils: $Src__Pages__OverseaLocation__Utils,
  Src__Pages__Seckill__Contract: $Src__Pages__Seckill__Contract,
  Src__Pages__Settle__Ant: $Src__Pages__Settle__Ant,
  Src__Pages__Settle__Domestic: $Src__Pages__Settle__Domestic,
  Src__Pages__Settle__Invoice: $Src__Pages__Settle__Invoice,
  Src__Pages__Settle__Overseas: $Src__Pages__Settle__Overseas,
  Src__Pages__System__Account: $Src__Pages__System__Account,
  Src__Pages__System__Security: $Src__Pages__System__Security,
  Src__Pages__TransportationTrack: $Src__Pages__TransportationTrack,
  Src__Pages__User__BindAccount: $Src__Pages__User__BindAccount,
  Src__Pages__User__Components: $Src__Pages__User__Components,
  Src__Pages__User: $Src__Pages__User,
  Src__Pages__User__Forgetpw: $Src__Pages__User__Forgetpw,
  Src__Pages__User__Login: $Src__Pages__User__Login,
  Src__Pages__User__Register: $Src__Pages__User__Register,
  Src: $Src,
  Src__Utils: $Src__Utils,
  Src__Pages__OverseaLocation__Components__SimpleUpload: $Src__Pages__OverseaLocation__Components__SimpleUpload,
  Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord: $Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord,
  Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate: $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate,
  Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics: $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics,
  Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply: $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply,
  Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept: $Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept,
  Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet: $Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet,
  Src__Views__ModalLogisticsTrack: $Src__Views__ModalLogisticsTrack,
  Src__Pages__OverseaLocation__Components__SearchSelect: $Src__Pages__OverseaLocation__Components__SearchSelect,
  Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel: $Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel,
  Src__Pages__OverseaLocation__ImportManage__ImportList: $Src__Pages__OverseaLocation__ImportManage__ImportList,
  Src__Views__AsyncImportButton: $Src__Views__AsyncImportButton,
  Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery: $Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery,
});
