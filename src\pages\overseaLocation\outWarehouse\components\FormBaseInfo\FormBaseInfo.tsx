import React from 'react';
import { apiMapDictType } from '@/api';
import { SearchSelect } from '@/components';
import { RowLayout, TextValue } from '@/views';
import { Checkbox, Form, Space, Typography } from 'antd';
import type { FormInstance } from 'antd';
import { textMap } from './text-map';
import { apiCustomerOmsSelect } from '../../OutboundDiffImport';
import { isOMS, isPortal } from '@/oms-portal-diff-import';
import { EnumOutboundBusinessType } from '../../OutboundEnum';

const { Item } = Form;

type TypeProps = {
  form: FormInstance;
  isCreate: boolean;
  onChangeCompany: (option?: any) => void;
};

/** 出库表单基础信息 */
export default function FormBaseInfo(props: TypeProps) {
  const { isCreate, form } = props;
  const businessType = Form.useWatch('businessType', form);
  const isTOB = Number(businessType) === EnumOutboundBusinessType.tob;

  return (
    <div>
      <RowLayout {...{ columnNum: 3, style: { marginBottom: 20 } }}>
        <Typography.Title
          {...{
            level: 5,
            children: (
              <Space>
                <span>{textMap.txtApplicationNo}</span>
                <span style={{ fontSize: 14, color: '#333' }}>
                  <Item {...{ name: 'podOrderNo', noStyle: true, children: <TextValue /> }} />
                </span>
              </Space>
            ),
          }}
        />
        {isOMS && isTOB && (
          <Item
            {...{
              noStyle: true,
              name: 'isCalculateCosts',
              valuePropName: 'checked',
            }}
          >
            <Checkbox>{textMap.txtCalculateCosts}</Checkbox>
          </Item>
        )}
      </RowLayout>
      <RowLayout>
        <Item
          {...{
            label: textMap.txtOrderType,
            name: 'businessType',
            rules: [{ required: true }],
          }}
        >
          <SearchSelect
            {...{
              allowClear: false,
              showSearch: false,
              include: ['201' /** 标准出库 */, '203' /** TOB出库 */],
              /** 只有完全新增才允许修改, 复制单和修改单都不能改 */
              disabled: isCreate === false,
              request: apiMapDictType.outboundBusinessType,
            }}
          />
        </Item>
        {isPortal && (
          <Item
            {...{
              label: textMap.txtCompanyName,
              name: 'companyName',
              children: <TextValue />,
            }}
          />
        )}
        {isOMS && (
          <Item
            {...{
              label: textMap.txtCompanyName,
              name: 'companyInfo',
              rules: [{ required: true }],
              children: (
                <SearchSelect
                  {...{
                    labelInValue: true,
                    onChange(val, option) {
                      props.onChangeCompany?.(option);
                      form.setFieldsValue({
                        companyName: option?.companyName,
                        companyId: option?.companyId,
                      });
                    },
                    request: async (name: string) =>
                      apiCustomerOmsSelect({ name, enabled: true }, 'companyId'),
                  }}
                />
              ),
            }}
          />
        )}
        <Item
          {...{
            label: textMap.txtApplicant,
            name: 'userName',
            children: <TextValue />,
          }}
        />
      </RowLayout>
      <Item noStyle hidden>
        <TextValue {...{ label: textMap.txtOrderId, name: 'id' }} />
        <TextValue {...{ label: textMap.txtCompanyId, name: 'companyId' }} />
        <TextValue {...{ label: textMap.txtCompanyName, name: 'companyName' }} />
        <TextValue {...{ label: textMap.txtOrderSource, name: 'orderSource' }} />
      </Item>
    </div>
  );
}
