const REGEX_UTIL = {
  MOBILE: /^(13|14|15|16|17|18|19)\d{9}$/,
  EMAIL: /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,
  EMAILS: /(([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4}),?)+/,
  // PASSWORD: /^[a-zA-Z\d_]{6,20}$/,
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d)[^]{6,20}$/,
  // PASSWORD: /^(?=.*[a-zA-Z])(\d)[^]{6,20}$/,

  VCODE: /^\d{4,6}$/,
  PHOTO_TYPES: /(gif|jpe?g|png|GIF|JPG|PNG)$/,
  FILE_TYPES: /(xls|xlsx)$/,
  SPECIAL_SYMBOL: /^[A-Za-z0-9\u4e00-\u9fa5]+$/,
  LETTER_AND_NUM: /^[A-Za-z0-9]+$/,
  POSITIVE_FLOAT: /^[0-9]+.?[0-9]*$/,
  POSITIVE_INGETER: /^([1-9]\d*|[0]{1,1})$/,
  IDENTITY_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  NAME: /^[\u4e00-\u9fa5]{2,4}$/,
  /** 匹配中文和英文带英文空格 */
  NAMECNEN: /^[\u4e00-\u9fa5a-zA-Z ]+$/,
  NUMBER: /^\d+$/,
  SPACE: /[^\s+]/,
  NO_SPACE: /^[^\s]*$/,
  // 只匹配字母中文
  SPECIALCHART: /[\a-\z\A-\Z\u4E00-\u9FA5]/,
  // 只匹配英文状态
  ENGLIST_STATUS: /^([^\u4e00-\u9fa5\uff00-\uffff。])*$/,
  // 箱号规则，11位，前4位为大写字母后7位为数字
  CONTAINERNO_RULE: /^[A-Z]{4}[0-9]{7}$/,
  HS_CODE_RULE: /^\d{6,}$/,
  // 只能为数字和字母
  NUMBER_AND_ENGLISH: /^[0-9a-zA-Z--]+$/,
  TEL: /^((\d{3,4})|\d{3,4}-|s)?\d{7,14}$/,
  QQ: /^[1-9][0-9]{4,9}$/,
  /** 网址正则 */
  HREF_RE: /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/,
  /** 零和非零开头的数字正则 */
  REGX_NUM: /^(0|[1-9][0-9]*)$/,
  /** 数字、最多二位小数 正则 */
  REGX_NUM_AND_FLOAT_ZERO: /^(([0-9]?|([1-9]+[0-9]*))(\.[0-9]{1,2})?)$/g,
  /** 非0、数字、最多二位小数 正则 */
  REGX_NUM_AND_FLOAT: /^((0.\d?[1-9]{1})|(([1-9]+[0-9]*)(\.[0-9]{1,2})?))$/g,
  /** 非0、数字、最多二位小数 正则 */
  REGX_NUM_AND_FLOAT_TWO: /^((0.\d?[1-9]{1})|(([1-9]+[0-9]*)(\.[0-9]{1,2})?))$/g,
  /** 非0、 数字、最多3位小数,10位整数 正则 */
  REGX_NUM_AND_FLOAT_THREE:
    /^((0.(((?!0{3})[0-9]{3})|((?!0{2})[0-9]{2})|((?!0{1})[0-9]{1})))|(([1-9]{1}[0-9]{0,9})(\.[0-9]{1,3})?))$/g,
  /** 非0、 数字、最多4位小数,10位整数 正则 */
  REGX_NUM_AND_FLOAT_FOUR:
    /^((0.(((?!0{4})[0-9]{4})|((?!0{3})[0-9]{3})|((?!0{2})[0-9]{2})|((?!0{1})[0-9]{1})))|(([1-9]{1}[0-9]{0,9})(\.[0-9]{1,4})?))$/g,
  /** 非零开头的数字正则 */
  REGX_NUM_NOT_ZERO: /^[1-9][0-9]*$/,
  /** 英文、数字、字符 */
  // eslint-disable-next-line no-control-regex
  NO_CHINESE: /^[0-9a-zA-Z\u0000-\u00FF]+$/,
  /** 英文、数字、字符、空格 */
  SPECIAL_SYMBOL_SPACE: /^[ A-Za-z0-9\u4e00-\u9fa5]+$/,
  /** 禁止输入# & */
  NO_SOME_SPECIAL: /^[^#&\x22]*$/,
  /** 英文、数字、字符、空格 (=+-.:/*&< >)_, */
  SOME_SPECIAL_SYMBOL_SPACE: /^[ A-Za-z0-9(=+-.:/*&<>)_,]+$/,
  /** 海外仓电话 英文、数字、字符、空格 +-*/
  SPECIAL_TEL_SYMBOL_SPACE: /^[ A-Za-z0-9+-.]+$/,
};

export default REGEX_UTIL;
