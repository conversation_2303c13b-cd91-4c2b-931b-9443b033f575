import React from 'react';
import { FormProDescriptions } from '@/views';
import { emptyRenderArrayJoin } from '@/utils';
import { useDictTypeValueEnum } from '@/api';
import { isPortal } from '@/oms-portal-diff-import';

type TypeProps = {
  detailData?: TypeOutboundDetail;
};

/** 收件人信息 */
export default function DetailRecipientInfo(props: TypeProps) {
  const { dictTypeMap } = useDictTypeValueEnum(['certificateType']);

  return (
    <FormProDescriptions<NonNullable<TypeOutboundDetail['orderExpressDTO']>>
      {...{
        title: isPortal ? '收件人信息' : undefined,
        dataSource: props.detailData?.orderExpressDTO || {},
        columns: [
          { title: '姓名', dataIndex: 'recipientName' },
          { title: '国家', dataIndex: 'recipientCountry' },
          { title: '省/州', dataIndex: 'recipientProvince' },
          { title: '城市', dataIndex: 'recipientCity' },
          { title: '电话1', dataIndex: ['recipientPhoneNumberList', 0] },
          { title: '电话2', dataIndex: ['recipientPhoneNumberList', 1] },
          { title: '详细地址1', dataIndex: ['recipientAddressList', 0] },
          { title: '详细地址2', dataIndex: ['recipientAddressList', 1] },
          { title: '详细地址3', dataIndex: ['recipientAddressList', 2] },
          { title: '门牌号', dataIndex: 'recipientHouseNumber' },
          { title: '邮编', dataIndex: 'recipientPostcode' },
          { title: '分邮编', dataIndex: 'recipientBranchPostcode' },
          { title: '邮箱', dataIndex: 'recipientEmail' },
          { title: '公司名称', dataIndex: 'recipientCompanyName' },
          { title: '收件人EORI号', dataIndex: 'recipientEoriNo' },
          {
            title: '收件人证件号及类型',
            dataIndex: 'recipientIdNo',
            render(val, record) {
              const { recipientIdType, recipientIdNo } = record;

              return emptyRenderArrayJoin(
                [dictTypeMap.certificateType[recipientIdType!], recipientIdNo],
                '-',
              );
            },
          },
        ],
      }}
    />
  );
}
