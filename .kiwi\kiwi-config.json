{"kiwiDir": "./.kiwi", "configFile": "./.kiwi/kiwi-config.json", "srcLang": "zh-CN", "distLangs": ["en-US"], "translateOptions": {"concurrentLimit": 100, "requestOptions": {}, "eachTranslateAwaitTime": 0}, "baiduApiAppId": "20211012000971140", "baiduApiKey": "cCkAZe1NZ5OVckUdqRG4", "importI18N": "import I18N from '@/utils/I18N';", "ignoreDir": ".umi,.umi-production,service,locales", "ignoreFile": "", "langMap": {"en-US": "en"}, "uploadIgnoreIdentifiers": [], "resolveAt": false, "requestHost": "http://admin-dev.200jit.com", "projectName": "", "useStandard": false, "suggestion": "full", "routeMap": false, "includes": ["./"]}