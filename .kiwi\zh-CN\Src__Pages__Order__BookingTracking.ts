export default {
  Index: {
    boxType: '箱型',
    pleaseInputBox: '{val1}{val2}',
    boxDynamics: '箱动态',
    portOfDestination: '目的港港口：',
    portOfOrigin: '起始港港口：',
    shipDynamics: '船舶动态',
    pleaseEnterTheCar: '请输入车牌号',
    trailerDynamics: '拖车动态',
    expectedSailing: '预计开航时间',
    cutOffTime: '截港时间',
    closingTime: '截关时间',
    placeOfDelivery: '交货地',
    placeOfReceipt: '收货地',
    voyageNumber: '航次',
    viewVessel: '查看船舶轨迹',
    shipName: '船名',
    orderNumber: '订单号',
    orderDetails: '订单详情',
    orderProgress: '订单进程跟踪',
    expenseStatus: '费用状态',
    sailingTime: '开船时间',
    billOfLadingStatus: '提单状态',
    preConfiguredManifest: '预配舱单状态',
    state: 'VGM状态',
    documentStatus: '单证状态',
    classConfirmation: '舱位确认',
    freightStart: '货运开始',
    noLicensePlateNumber: '车牌号不能为空',
    actualArrival: '实际到港',
    expectedArrival: '预计到港',
    actualSailing: '实际开航',
    estimatedDeparture: '预计离港',
    actualBerthing: '实际靠港',
    estimatedPortCall: '预计靠港',
    returnTime: '还箱时间',
    heavyBoxExit: '重箱出场时间',
    seaOfDestination: '目的港海关放行时间',
    dischargeAtDestinationPort: '目的港卸船时间',
    dischargeAtTransitPort: '中转港卸船时间',
    transshipmentPortLoading: '中转港装船时间',
    startingPortLoading: '起始港装船时间',
    dockRelease: '码头放行时间',
    customsClearance: '海关放行时间',
    timeOfArrival: '进港时间',
    warehousingTime: '入库时间',
    departureWarehouse: '离厂/仓库时间',
    arrivalWarehouse: '到厂/仓库时间',
    carryingTime: '提箱时间',
    invoicingCompleted: '开票完成',
    confirmCompletion: '确认完成',
    sent: '已寄出',
    confirmed: '已确认',
    supplementedMaterials: '已补料',
    applicationMaterials: '申报材料已发送海关',
    submittedApplication: '已提交申报材料',
    declarationConfirmed: '申报已确认',
    materialsHaveBeenApplied: '材料已申报船公司',
    caseNo: '箱号',
    pleaseEnter: '请输入',
  },
};
