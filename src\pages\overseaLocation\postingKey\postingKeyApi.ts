import { request } from 'umi';
import { dateUtils } from '@/utils';

/** 获取记账码 */
export function apiGetListPostingKey(data: NsApi.TypeRequestListQuery<TypeQueryData>) {
  /** 历史接口 '/api/website/web/postingKeyFacade/listPostingKey.do' */
  return request<NsApi.TypeResponseList<TypePostingKeyRecord[]>>(
    '/zouwu-oms-order/portal/posting/list',
    // '/api/website/web/postingKeyFacade/listPostingKey.do',
    {
      data,
      method: 'POST',
    },
  ); /* .then((res) => dateUtils.listDateFormatter(res, ['updateDate'])); */
}

/** 新增记账码参数 */
type TypeFormData = {
  id?: string;
  /** 电商平台code */
  orderSourcePlatformCode?: string;
  /** 电商平台名称 */
  orderSourcePlatformName?: string;
  /** 仓库ids */
  warehouseIds?: string[];
  /** 快递服务商 */
  dispatchServiceProvider?: string;
  /** 启用状态 0-停用，1-启用 */
  enable?: number;
  /** 是否指定店铺 0-否，1-是 */
  appointStore?: number;
  /** 店铺id */
  platformStoreId?: string;
  /** 记账码 */
  postingKey?: string;
};

/** 新增记账码 */
export function apiAddPostingKey(data: TypeFormData) {
  /** 历史接口 '/api/website/web/postingKeyFacade/addPostingKey.do' */
  return request('/zouwu-oms-order/portal/posting/create', {
    data,
    method: 'POST',
  });
}

/** 编辑记账码 */
export function apiUpdatePostingKey(data: TypeFormData) {
  /** 历史接口 '/api/website/web/postingKeyFacade/editPostingKey.do' */
  return request('/zouwu-oms-order/portal/posting/edit', {
    data,
    method: 'POST',
  });
}

/** 记账码停启用
 * @ 这个接口比较特殊, 启用的时候需要传表单数据, 后端设计的, 当初前端是周金华
 */
export function apiPostingKeyEnable(data: TypeFormData) {
  /** 历史接口 '/api/website/web/postingKeyFacade/enableOrDisable.do' */
  return request('/zouwu-oms-order/portal/posting/enableOrDisable', {
    data,
    method: 'POST',
  });
}
