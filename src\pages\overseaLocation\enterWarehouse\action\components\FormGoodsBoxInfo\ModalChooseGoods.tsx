import type { ProColumns } from '@/common-import';
import { ProTable } from '@/common-import';
import type { TypeZouModalRef } from '@/components';
import { ZouModal } from '@/components';
import GoodsImage from '@/components/GoodsImage';
import { apiGoodsListForGoodsChoose } from '@/pages/overseaLocation/goods/goodsApi';
import { strUtils } from '@/utils';
import I18N from '@/utils/I18N';
import { Button, Col, Input, Row, Space } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';

type TypeProps = {
  /** 组件Props定义 */
};

type TypeOpenParams = {
  /** 组件打开参数定义 */
  initSkuArr?: string[];
};

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: (newRows: NsInbound.TypeBoxGoodsInfo[]) => void;
};

type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

/** 选择添加商品 */
export const ModalChooseGoods = React.forwardRef(function ModalChooseGoods(
  props,
  ref: React.Ref<TypeModalRef | undefined>,
) {
  const {
    columnsForChoose,
    asyncInit,
    asyncClear,
    asyncSubmit,
    modalRef,
    detailData,
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRows,
  } = useConfig();

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams, operate?: TypeOperateMap) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title: '添加商品',
          width: '80%',
          okText:
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.confirmAdd,
          afterClose() {
            asyncClear();
          },
        },
        async onOk() {
          await asyncSubmit();
        },
        children: (
          <ProTable
            {...{
              rowKey: 'sku',
              tableAlertRender: false,
              scroll: { x: 'max-content' },
              columns: columnsForChoose,
              bordered: true,
              cardProps: false,
              form: {
                isKeyPressSubmit: false,
              },
              options: false,
              size: 'small',
              pagination: {
                pageSize: 10,
                showSizeChanger: false,
              },
              rowSelection: {
                preserveSelectedRowKeys: true,
                type: 'checkbox',
                selectedRowKeys,
                onChange(keys, rows: NsInbound.TypeBoxGoodsInfo[]) {
                  setSelectedRowKeys(keys as string[]);
                  setSelectedRows(rows);
                },
                getCheckboxProps: (record) => {
                  const isExist =
                    (detailData.initSkuArr || []).find((sku) => sku === record.sku) !== undefined;

                  return {
                    disabled: isExist,
                  };
                },
              },
              async request(params) {
                const { pageSize, current: currentPage, ...condition } = params;
                const { sku = '' } = condition;
                const skuList = strUtils.strSplitForSku(sku);

                const { data } = await apiGoodsListForGoodsChoose({
                  currentPage,
                  pageSize,
                  condition: {
                    skuList: skuList.length > 1 ? skuList : undefined,
                    sku: skuList.length === 1 ? skuList[0] : undefined,
                  },
                });

                return {
                  data: data.records,
                  success: true,
                  total: data.totalSize,
                };
              },
            }}
          />
        ),
      }}
    />
  );
});

function useConfig() {
  const [detailData, setDetailData] = useState<TypeOpenParams>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<NsInbound.TypeBoxGoodsInfo[]>([]);
  const modalRef = useRef<TypeZouModalRef>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    setDetailData(params);
    setOperateMap(operate || {});
    setSelectedRowKeys(params.initSkuArr || []);
  }

  async function asyncClear() {
    setOperateMap({});
    setDetailData({});
    setSelectedRowKeys([]);
  }

  async function asyncSubmit() {
    const newRows = selectedRows.filter(
      (row) => (detailData.initSkuArr || []).includes(row.sku!) === false,
    );

    operateMap?.submitSuccessCB?.(newRows);
  }

  const columnsForChoose = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
      render(dom, record) {
        return dom;
      },
      renderFormItem() {
        return <Input.TextArea />;
      },
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      dataIndex: 'goodsName',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'goodsWeight',
      search: false,
      render(_, record) {
        return record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-';
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'goodsVolume',
      search: false,
      render(_, record) {
        return `${record.volumeUnit}: ${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      search: false,
      render(src, record) {
        return record.goodsPicture ? <GoodsImage src={record.goodsPicture} /> : '-';
      },
      width: 100,
    },
  ] as ProColumns<NsInbound.TypeBoxGoodsInfo>[];

  return {
    columnsForChoose,
    operateMap,
    asyncInit,
    asyncClear,
    asyncSubmit,
    modalRef,
    detailData,
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRows,
  };
}
