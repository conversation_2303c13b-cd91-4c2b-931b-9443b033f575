import React, { useState, useEffect } from 'react';
import { connect, useDispatch } from 'umi';
import { FooterToolbar } from '@ant-design/pro-layout';
import {
  Card,
  Spin,
  Descriptions,
  Steps,
  Typography,
  Button,
  Tabs,
  Row,
  Col,
  Table,
  Space,
  Modal,
  message,
} from 'antd';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import {
  historyGetQuery,
  historyGoPrev,
  includesInArray,
  ossPathCombine,
} from '@/pages/overseaLocation/utils';
import {
  DetailAdditionalInfo,
  DetailChargeChecklist,
  DetailLogisticsService,
  DetailOrderGoodsInfo,
  DetailRecipientInfo,
  OrderCancelStatus,
  fileListDeconStruct,
} from '../components';
import { useDictTypeValueEnum } from '@/pages/overseaLocation/api';
import { OUT_WAREHOUSE_LINK_OPTIONS } from '@/pages/overseaLocation/enum';
import { GetPageQuery } from '@/utils/util';
import { dateUtils, eR } from '@/utils';
import { apiOutboundCancel, apiOutboundTOBCannel, apiOutWarehouseDetail } from '../OutboundApi';
import { handleEditOrCopy } from '../OutWarehouseList';
import { LoadButton } from '../../components';

const { Item } = Descriptions;
const { Step } = Steps;
const { Title } = Typography;
const { dateTimeFormatter } = dateUtils;

/** 出库详情页面 */
function OutWarehouseDetailPage() {
  const { asyncOutOrderCancel } = useOutOrderCancel();
  const { id } = historyGetQuery();
  const [data, setData] = useState<TypeOutboundDetail>({} as any);
  const [pageLoading, setPageLoading] = useState(false);
  const { dictTypeMap } = useDictTypeValueEnum([
    'warehouseType',
    'outboundBusinessType',
    'outboundSignatureTypes',
    'DcDispatchServiceType',
  ]);

  useEffect(() => {
    asyncRefreshDetail();
  }, []);

  async function asyncInit() {
    const { podOrderNo } = GetPageQuery();
    const { data: result } = await apiOutWarehouseDetail({
      id,
      podOrderNo,
    });

    if (result) {
      setData(result);
      return result;
    }
  }

  async function asyncRefreshDetail() {
    setPageLoading(true);
    const res = asyncInit();

    res.finally(() => setPageLoading(false));
    return res;
  }

  const deliveryColumns = [
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.deliveryService,
      dataIndex: 'customerChannelCode',
      render: (text: any, record: any) =>
        record?.customerChannelCode || record?.dispatchServiceNameValue,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.distributionType,
      dataIndex: 'dispatchServiceType',
      render: (text: any) => dictTypeMap.DcDispatchServiceType[text],
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
          .logisticsTracking,
      dataIndex: 'trackingNo',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.enclosure,
      dataIndex: 'labelFile',
      render: (_: any, record: any) => (
        <ul>
          {record.fileList
            .filter((o: any) => o.fileType === 1)
            .map((o: any) => (
              <li key={o.id}>
                <a
                  href={ossPathCombine(o.filePath as string, record.dispatchServiceType === 4)}
                  target="_blank"
                  rel="noreferrer"
                >
                  {o.fileName}
                </a>
              </li>
            ))}
        </ul>
      ),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.otherAccessories,
      dataIndex: 'otherFile',
      render: (_: any, record: any) => (
        <ul>
          {record.fileList
            .filter((o: any) => o.fileType === 99)
            .map((o: any) => (
              <li key={o.id}>
                <a
                  href={ossPathCombine(o.filePath as string, record.dispatchServiceType === 4)}
                  target="_blank"
                  rel="noreferrer"
                >
                  {o.fileName}
                </a>
              </li>
            ))}
        </ul>
      ),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.numberOfPallets1,
      dataIndex: 'palletQuantity',
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.creationTime,
      dataIndex: 'createDate',
      render: (date: any) => dateTimeFormatter(date, 'YYYY-MM-DD HH:mm'),
    },
  ];

  const goodsColumns = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_: any, record: any) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName1,
      dataIndex: 'goodsName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName,
      dataIndex: 'goodsEnName',
    },
    // { title: '平台编码', dataIndex: 'platformCode' },
    // { title: '商品申报价值', dataIndex: 'goodsValue' },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'goodsWeight',
      render: (_: string, record: any) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'volumeUnit',
      render: (_: any, record: any) => (
        <span>
          {record.volumeUnit}: {`${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`}
        </span>
      ),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.total,
      dataIndex: 'totalQuantity',
    },
  ];

  const column2 = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_: any, record: any) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName1,
      dataIndex: 'goodsName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName,
      dataIndex: 'goodsEnName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.customerApplication,
      dataIndex: 'totalQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.numberToBeExecuted,
      dataIndex: 'notExecutedQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.executed1,
      dataIndex: 'executedQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.warehousePerformance1,
      dataIndex: 'complianceNo',
    },
    { title: I18N.Src__Pages__Order__Detail.SiItem.remarks, dataIndex: 'remark' },
  ];

  /** 根据后端返回的数据进行一一查找，如果后端提供了对应的节点，则点亮，如果是取消状态则展示错误的信息 */
  const getStepStatus = (
    step: typeof OUT_WAREHOUSE_LINK_OPTIONS[number],
    orderStatusList: any[],
  ) => {
    let status = 'wait' as 'wait' | 'error' | 'process';

    if (!step || !orderStatusList) return { status };
    const current = orderStatusList.find((o) => o.orderStatusMapping === step.tabKey);

    if (current) {
      status = step.tabKey === 'ORDER_CANCEL' /** 已取消 */ ? 'error' : 'process';
    }
    return {
      status,
      description: dateTimeFormatter(current?.createDate, 'YYYY-MM-DD HH:mm'),
    };
  };

  // const handleCopyRecord = () => {
  //   sensorsTrack('WWL_PORTAL_OUT_WAREHOUSE_OPTION_BUTTON_CLICK', {
  //     option_type: 'edit',
  //     current_click_page: I18N.Src__Pages__Order__Components__CoustomTable.Index.details,
  //     order_status: data.orderStatus,
  //     oversea_pod_no: data.podOrderNo,
  //   });

  //   HiveModule.history.push(
  //     `/oversea-location/out-warehouse/outbound-create?id=${id}&orderStatus=${data.orderStatus}`,
  //   );
  // };

  const renderActionButton = () => {
    const { orderStatusMapping } = data;
    // 新增异常单（210）允许复制、申请整单取消

    /** 仅非草稿且门户侧创建允许复制, 不包含门户导入 */
    const hasBtnCopy =
      orderStatusMapping !== 'DRAFT' /** 非草稿单 */ && data.orderSource === 1; /** 门户创建 */

    return (
      <Space>
        {hasBtnCopy && (
          <Button onClick={() => handleEditOrCopy(data, { optionType: 'copy' })}>
            {I18N.Src__Pages__Order__Si__Component__ShareModals.Alert.copy}
          </Button>
        )}
        {renderOutboundCancelBtn(
          {
            orderStatus: data.orderStatus,
            businessType: data.businessType,
            orderStatusMapping: data.orderStatusMapping,
          },
          <LoadButton
            type="primary"
            onClick={async () => {
              await asyncOutOrderCancel(data);
              return asyncRefreshDetail();
            }}
          >
            {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.applyForWholeDocument}
          </LoadButton>,
        )}
      </Space>
    );
  };

  // if (!data || isEmpty(data)) return null;

  const express = data?.orderExpressDTO || {};
  const orderStatusList = data?.orderStatusList || [];

  return (
    <PageContainer>
      <Spin spinning={pageLoading === true}>
        <Card
          title={
            <Space>
              {I18N.Src__Pages__Order__Detail.Index.orderInformation}
              <OrderCancelStatus {...data.orderCancelRecord} />
            </Space>
          }
          extra={renderActionButton()}
        >
          <Row>
            <Col span={4}>
              <Title level={5}>
                {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.documentProcess}
              </Title>
              <Steps progressDot size="small" direction="vertical">
                {OUT_WAREHOUSE_LINK_OPTIONS.map((o) => (
                  <Step title={o.label} key={o.value} {...getStepStatus(o, orderStatusList)} />
                ))}
              </Steps>
            </Col>
            <Col span={20}>
              <Descriptions>
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                      .applicationNo
                  }
                >
                  {data.podOrderNo}
                </Item>
                <Item label={I18N.Src__Pages__Order__TrailerOrder.Index.orderStatus}>
                  {data.orderStatusName}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.orderType}
                >
                  {dictTypeMap.outboundBusinessType[data.businessType!]}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.applicant}
                >
                  {data.companyName}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.applicant}
                >
                  {data.createByName}
                </Item>
              </Descriptions>
              <Descriptions title="From/To">
                {/* 后端接口没有返回仓库CODE,产品说先隐藏 */}
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.shipmentInventory
                  }
                >
                  {data.createDeliveryWarehouseCode}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.targetType}
                  span={2}
                >
                  {eR(dictTypeMap.warehouseType[data.createDeliveryWarehouseType!])}
                </Item>
              </Descriptions>
              <DetailAdditionalInfo detailData={data} />
            </Col>
          </Row>
        </Card>
        <Card
          title={
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.logisticsInformation
          }
          style={{ marginTop: 20 }}
        >
          {/* <Descriptions title="发件人信息">
            <Item label="国家">{express.senderCountry}</Item>
            <Item label="省/州">{express.senderProvince}</Item>
            <Item label="城市">{express.senderCity}</Item>
            {outArr(express.senderAddressList).map((o: any, i: number) => (
              <Item label={`详细地址${i + 1}`}>{o}</Item>
            ))}
            <Item label="姓名">{express.senderName}</Item>
            <Item label="电话">{express.senderPhoneNumber}</Item>
            <Item label="公司">{express.senderCompanyName}</Item>
          </Descriptions> */}
          {/* 收件人信息 */}
          <DetailRecipientInfo detailData={data} />
          {/** 关联物流单 */}
          <DetailLogisticsService detailData={data} />
          {
            //   <Descriptions
            //   title={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.recipientsLetter}
            // >
            //   <Item label={I18N.Src__Pages__Enterprise__Account.Index.fullName}>
            //     {express.recipientName}
            //   </Item>
            //   {outArr(express.recipientPhoneNumberList).map((o: any, i: number) => (
            //     <Item
            //       key={i}
            //       label={I18N.template(
            //         I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.contactNumber,
            //         { val1: i + 1 },
            //       )}
            //     >
            //       {o}
            //     </Item>
            //   ))}
            //   <Item label={I18N.Src__Pages__Order__Detail.PrePane.country}>
            //     {express.recipientCountry}
            //   </Item>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index
            //         .provincialAndStateCapitals
            //     }
            //   >
            //     {express.recipientProvince}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.urbanArea}>
            //     {express.recipientCity}
            //   </Item>
            //   <>
            //     {outArr(express?.recipientAddressList).map((o: any, i: number) => (
            //       <Item
            //         label={I18N.template(
            //           I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.detailedAddress,
            //           { val1: i + 1 },
            //         )}
            //         key={i}
            //       >
            //         {o}
            //       </Item>
            //     ))}
            //   </>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Components.RecipientAddress
            //         .houseNumber
            //     }
            //   >
            //     {express.recipientHouseNumber}
            //   </Item>
            //   <Item label={I18N.Src__Pages__Enterprise__Account.Index.mailbox}>
            //     {express.recipientEmail}
            //   </Item>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
            //         .postalCode
            //     }
            //   >
            //     {express.recipientPostcode}
            //   </Item>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Components.RecipientAddress
            //         .postalCode1
            //     }
            //   >
            //     {express.recipientBranchPostcode}
            //   </Item>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.recipientCertificate
            //     }
            //   >
            //     {CERTIFICATE_TYPE_DESC[express.recipientIdType!]}
            //   </Item>
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.identificationNumber
            //     }
            //   >
            //     {express.recipientIdNo}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.recipientNo}>
            //     {express.recipientEoriNo}
            //   </Item>
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.corporateName}
            //   >
            //     {express.recipientCompanyName}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.whether1}>
            //     {express.recipientIsFba
            //       ? I18N.Src__Pages__OverseaLocation.Enum.yes
            //       : I18N.Src__Pages__OverseaLocation.Enum.no}
            //   </Item>
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.residentialOrNot}
            //   >
            //     {express.residential
            //       ? I18N.Src__Pages__OverseaLocation.Enum.yes
            //       : I18N.Src__Pages__OverseaLocation.Enum.no}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.remoteOrNot}>
            //     {express.remoteName}
            //   </Item>
            //   <Item label="ZONE">{express.zone}</Item>
            // </Descriptions>
            // <Descriptions
            //   title={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.associatedLogistics}
            // >
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.billingWeight}
            //   >
            //     {express.billingWeight}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.actualWeight}>
            //     {express.actualWeight ? express.actualWeight + express.actualWeightUnit! : ''}
            //   </Item>
            //   <Item label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.actualVolume}>
            //     {express.actualVolume ? express.actualVolume + express.actualVolumeUnit! : ''}
            //   </Item>
            //   {/* 删除签名服务、新增客户记账码 */}
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.customerBookkeeping
            //     }
            //   >
            //     {express.accountNumber}
            //   </Item>
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.signingServices}
            //   >
            //     {findConstEnum(dictTypeMap.outboundSignatureTypes, express.signatureType!) ??
            //       express.signatureType}
            //   </Item>
            //   {/* <Item label="是否带电">{express.electric ? '是' : '否'}</Item> */}
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.chineseSymbols3}
            //   >
            //     {express.insured
            //       ? I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.yes
            //       : I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.no}
            //   </Item>
            //   {/* <Item label="预计交付日期">{express.senderPhoneNumber}</Item> */}
            //   <Item
            //     label={
            //       I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsServiceSetTableForm
            //         .chineseSymbols
            //     }
            //   >
            //     {express.insuredAmount}
            //   </Item>
            //   <Item
            //     label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.chineseSymbols2}
            //   >
            //     {express.insuredAmountCurrency}
            //   </Item>
            // </Descriptions>
            // <Table
            //   rowKey="id"
            //   size="small"
            //   pagination={false}
            //   dataSource={data.deliveryList}
            //   columns={deliveryColumns}
            //   bordered
            // />
          }
        </Card>

        <Card
          title={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.documentDetails}
          style={{ marginTop: 20 }}
        >
          {/* <Tabs defaultActiveKey="1" type="card">
            <Tabs.TabPane
              tab={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.commodityList}
              key="1"
            >
              <Table
                rowKey={(record: any) => `${record.sku}-${record.shippingMarkNo}`}
                dataSource={data.goodsList}
                columns={goodsColumns}
                pagination={false}
                bordered
                scroll={{ x: 'max-content' }}
                size="small"
              />
            </Tabs.TabPane>
            <Tabs.TabPane
              tab={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.warehousePerformance
              }
              key="2"
            >
              <Table
                rowKey={(record: any) => `${record.sku}-${record.shippingMarkNo}`}
                dataSource={data.performanceList}
                columns={column2}
                pagination={false}
                bordered
                scroll={{ x: 'max-content' }}
                size="small"
              />
            </Tabs.TabPane>
          </Tabs> */}
          <DetailOrderGoodsInfo detailData={data} />
        </Card>

        <div style={{ marginTop: 20 }}>
          <DetailChargeChecklist detailData={data} />
        </div>
      </Spin>

      <FooterToolbar
        style={{ width: '100%', textAlign: 'center' }}
        extra={
          <Button type="primary" onClick={() => historyGoPrev()}>
            {I18N.Src__Pages__Order__Detail.Index.return}
          </Button>
        }
      />
    </PageContainer>
  );
}

/**
 * @description 针对返回字段数据存在不返回或者内容不存在的情况，手动返回一个默认数组，数组内容是‘-’，用于在内容为空默认展示一条空数据
 * @param arr 需要处理的数据
 */
function outArr<T>(arr?: T[] | null) {
  console.log(I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.chineseSymbols, arr);
  if (Array.isArray(arr) && arr.length > 0) {
    return arr;
  }
  return ['-'] as const;
}

export default OutWarehouseDetailPage;

/** 出库订单取消 */
export function useOutOrderCancel() {
  const dispatch = useDispatch();
  /** 取消订单 */
  const asyncOutOrderCancel = async (record: TypeOutboundDetail) => {
    return new Promise<void>((resolve, reject) => {
      Modal.confirm({
        title:
          I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.applyForWholeDocument,
        content:
          record.orderStatus === 203
            ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.thisOrder
            : I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.whetherToConfirm,
        async onOk() {
          sensorsTrack('WWL_PORTAL_OUT_WAREHOUSE_OPTION_BUTTON_CLICK', {
            option_type: 'delete',
            current_click_page: I18N.Src__Pages__Order__Components__CoustomTable.Index.details,
            order_status: record.orderStatus,
            oversea_pod_no: record.podOrderNo,
          });
          const { podOrderNo, businessType } = record;

          await (`${businessType}` === '203'
            ? apiOutboundTOBCannel({ podOrderNo })
            : apiOutboundCancel({ podOrderNo }));

          message.success('已发起取消');
          resolve();
          // ( apiOutboundTOBCannel: apiOutboundCancel)({});
          // const res = (await dispatch({
          //   type: 'out_warehouse/cancelOutBoundOrder',
          //   payload: { id: record.id },
          // })) as any as NsApi.TypeResponseData<number>;

          // const msgMap = {
          //   0: [
          //     'info',
          //     I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.wholeOrderCancellation2,
          //   ],
          //   10: [
          //     'success',
          //     I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.wholeOrderCancellation1,
          //   ],
          //   25: [
          //     'success',
          //     I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.operationFailed,
          //   ],
          //   20: [
          //     'error',
          //     I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.wholeOrderCancellation,
          //   ],
          // } as const;

          // if (res?.success && includesInArray([0, 10, 20, 25] as const, res.result)) {
          //   const msg = msgMap[res.result];

          //   if (res.result === 25) {
          //     return Modal.success({
          //       // title: '取消结果',
          //       content: msg[1],
          //       okText: I18N.Src__Pages__Order__Detail.SiPane.confirm,
          //       keyboard: false,
          //       onOk: () => {
          //         resolve(res);
          //       },
          //     });
          //   }

          //   message[msg[0]]?.(msg[1]);
          //   return resolve(res);
          // }
          // reject();
        },
        onCancel() {
          reject();
        },
      });
    });
  };

  return {
    asyncOutOrderCancel,
  };
}

/** 是否渲染出库申请整单取消 */
export function renderOutboundCancelBtn(
  params: {
    orderStatus: string | number | undefined;
    businessType: string | number | undefined;
    orderStatusMapping: string | undefined;
  },
  node: React.ReactNode,
) {
  let { orderStatus, businessType } = params;
  const { orderStatusMapping } = params;

  businessType += '';
  orderStatus += '';

  // tob2期改造后, 标准出库也有取消按钮
  // if (businessType === '203' /** TOB出库 */) {
  //   /* 产品更新逻辑, TOB 出库单不允许取消.
  //   起因是测试发现后端接口TOB草稿单不允许取消了
  //    */
  //   return null;
  // }
  /** 新增异常单状态(210)下可以取消, 资金不足 211 也可以取消  */
  return includesInArray(
    [
      'ORDER_CREATE' /** 待审核 */,
      'WAIT_UPLOAD_LABEL' /** 待补充面单 */,
      'ORDER_HANDLE' /** 处理中 */,
      'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR' /** 资金不足 */,
      'ORDER_ERROR' /** 异常单 */,
      'NOTIFY_WAREHOUSE' /** 待发货 */,
    ],
    orderStatusMapping,
  )
    ? node
    : null;

  /** 新增异常单状态(210)下可以取消, 资金不足 211 也可以取消  */
  // return includesInArray(
  //   [
  //     '201' /** 待审核 */,
  //     '203' /** 待发货 */,
  //     '209' /** 处理中 */,
  //     '210' /** 异常单 */,
  //     '211' /** 资金不足 */,
  //   ],
  //   orderStatus,
  // )
  //   ? node
  //   : null;
}
