// kiwi-disable
import { defineMaxConfig } from '@portal/umi-config';
import { execSync } from 'child_process';
import moment from 'moment';
import { SourceMapDevToolPlugin } from 'webpack';

const isDev = process.env.NODE_ENV === 'development';
const GIT_COMMIT_SHA = execSync('git rev-parse HEAD', { encoding: 'utf-8' }).trim();

export default defineMaxConfig({
  publicPath: '/wwl-zouwu-front/',
  // 注入变量
  define: {
    'process.env.PACK_TIME': moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
    'process.env.UMI_APP_VERSION': GIT_COMMIT_SHA,
    GIT_COMMIT_SHA,
  },
  hive: {
    moduleId: 'wwl-zouwu-front',
    // sdk: {
    //   url: 'https://xd-dev.oss-cn-hangzhou.aliyuncs.com/portal/portal-test/portal-hive-sdk.min.js',
    //   name: '@portal/hive-sdk',
    // },
    // publicResource: {
    //   '@hive/sdk-core': {
    //     js: 'https://xd-dev.oss-cn-hangzhou.aliyuncs.com/portal/standard/hive-core.umd.min.js',
    //   },
    // },
  },
  ...{ devtool: isDev ? 'eval-source-map' : 'source-map' },
  targets: {
    chrome: 68,
  },
  mfsu: false,
  locale: {
    default: 'zh-CN',
    antd: false,
    baseNavigator: true,
  },
  history: {
    type: 'hash',
  },
  dva: {},
  antd: {},
  initialState: {},
  model: {},
  request: {
    dataField: '',
  },
  hash: true,
  lessLoader: {
    modifyVars: {
      // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
      hack: `true; @import "~@/styles/common/variable.less";`,
    },
  },
  // chainWebpack(memo) {
  //   memo.plugin('SourceMapDevToolPlugin').use(SourceMapDevToolPlugin, [
  //     {
  //       append: '\n//# 111222',
  //       filename: '[name].js',
  //     },
  //   ]);
  // },
});
