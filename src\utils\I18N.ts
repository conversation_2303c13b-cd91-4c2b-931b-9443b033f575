// I18N代码简单例子
import kiwiIntl from 'kiwi-intl';
import _ from 'lodash';
import cookie from 'js-cookie';
import zhCNLangs from '../../.kiwi/zh-CN';

export enum LangEnum {
  'zh-CN' = 'zh-CN',
  'en-US' = 'en-US',
}

export const langKey = 'dzg_language';

const zhCNLangsOSS = zhCNLangs;

const langs = {
  'zh-CN': zhCNLangsOSS,
  'en-US': _.merge({}, zhCNLangsOSS, _.get(window, 'wwl_zouwu_front__en', {})),
};

/**
 * 获取当前语言的Cookie
 */
export function getCurrentLang(): LangEnum {
  const urlLang = new URL(window.location.href).searchParams.get('lang');
  const lang = getLangFormStorage();

  if (Object.keys(LangEnum).includes(urlLang as string)) {
    return urlLang as LangEnum;
  }
  return lang as LangEnum;
}

/**
 *
 */
export function setLangFormStorage(value: LangEnum) {
  cookie.set(langKey, value);
}

/**
 *
 */
export function getLangFormStorage() {
  const lang = cookie.get(langKey) || LangEnum['zh-CN'];

  return lang.replace('_', '-');
}

/**
 *
 */
function createI18N() {
  const defaultLang = getCurrentLang();
  const curLang = defaultLang;

  (window as any)[langKey] = curLang;

  const I18N = kiwiIntl.init(curLang, langs);

  return I18N as Required<typeof I18N>;
}

export default createI18N();
