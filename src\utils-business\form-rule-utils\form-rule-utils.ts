import type { Rule } from 'antd/lib/form';
import { textMap } from './form-rule-text';

function trim<T>(value: T) {
  return typeof value === 'string' ? value.trim() : value;
}

/*
 * formItem rules规则复用
 * 请务必加上注释, 因为可能会有国际化
 */
export const overseaRulesMap = {
  /** 海外仓SKU规则, 只允许输入数字、点、英文、英文横杠 */
  overseaSKU: [
    {
      type: 'string',
      required: true,
      max: 30,
      transform(value: any) {
        return trim(value);
      },
    },
    {
      type: 'string',
      message: textMap.skuMsg,
      pattern: /^[A-Za-z0-9-_\+\.]+$/,
      transform(value: string) {
        return trim(value);
      },
    },
  ],
  /** 海外仓UPC规则, 只允许输入数字、英文、英文横杠 */
  overseaUPC: [
    {
      type: 'string',
      max: 25,
      transform(value: any) {
        return trim(value);
      },
    },
    {
      type: 'string',
      message: textMap.upcMsg,
      pattern: /^[A-Za-z0-9-]+$/,
      transform(value: string) {
        return trim(value);
      },
    },
  ],
  /** 【箱唛号】：字母英文横杆下划线，15个字符，不必填 */
  shippingMarkNo: [
    {
      type: 'string',
      max: 15,
      pattern: /^[A-Za-z-_\+\.]+$/,
      message: textMap.shippingMarkNoMsg,
      transform(value: any) {
        return trim(value);
      },
    },
  ],
} as const;
