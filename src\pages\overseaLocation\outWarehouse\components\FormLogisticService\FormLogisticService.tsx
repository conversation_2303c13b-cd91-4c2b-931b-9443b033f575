// kiwi-disable
import React, { useEffect, useMemo, useRef } from 'react';
import { RowLayout, TextValue } from '@/views';
import type { FormInstance } from 'antd';
import { DatePicker, Form, Input, InputNumber, Radio } from 'antd';
import { useRequest } from 'umi';
import type { TypeSearchSelectRef, TypeSimpleUploadProps } from '@/components';
import { LoadButton, SearchSelect, SimpleUpload } from '@/components';
import { EnumUploadFileType, getUploadPropsConfig, apiMapDictType } from '@/api';
import { ruleUtils, typeUtils } from '@/utils';
import { optionsYesOrNo } from '@/constants/enum';
import { textMap } from './text-map';
import { EnumOutboundBusinessType, EnumOutboundFileType } from '../../OutboundEnum';
import type { TypeOutboundGoodsTB } from '../FormGoodsChoose';
import type {
  EnumDispatchType,
  TypeCustomerChannelTreeNode,
  TypeDispatchChannelOption,
  TypeDispatchChannelTree,
} from '@/pages/pagesApi';
import { apiCustomerChannelTree, apiDispatchChannelTree, dispatchRuleDict } from '@/pages/pagesApi';
import { isOMS, isPortal } from '@/oms-portal-diff-import';
import { FormListPalletSize } from '../FormListPalletSize';

const { Item } = Form;
const { isNumOrNumString } = typeUtils;

type TypeProps = {
  form: FormInstance;
  columnNum?: 3;
  /** 弹窗修改展示, 有特殊逻辑, 默认 false */
  isModalModify?: boolean;
};

/** 仅前端使用,  配送渠道类型 */
export enum EnumFrontChannelType {
  /** 客户渠道 */
  customerChannel = 'customerChannel',
  /** 配送渠道 */
  dispatchChannel = 'dispatchChannel',
}

/** 物流服务 */
export default function FormLogisticService(props: TypeProps) {
  const { form, isModalModify = false } = props;
  const dispatchServiceTypeRef = useRef<TypeSearchSelectRef>();
  const isInsure = Form.useWatch(nPExpress('insured'), form);
  const isPack = Form.useWatch(nPExpress('isPack'), form);
  const goodsList = Form.useWatch(['goodsList'], form);
  const businessType = Form.useWatch('businessType', form);
  const supportSmall = Form.useWatch('supportSmall', form);
  const isTOB = Number(businessType) === EnumOutboundBusinessType.tob;
  const { dsControl, fieldShowMap, customerChannelOptions, dispatchChannelOptions } =
    useWatchDeliveryWarehouseId({ form, isModalModify });

  useEffect(() => {
    isModalModify !== true && refreshGoodsInsuredAmount({ form });
  }, [isInsure, goodsList, isModalModify]);

  return (
    <>
      <Item hidden>
        <TextValue {...{ label: '配送类型Value', name: nPDelivery('dispatchServiceType') }} />
        {/* 后端配送服务是list数组结构, 历史配送服务含有唯一id */}
        <TextValue {...{ label: '配送类型id', name: nPDelivery('id') }} />
        {/* 自带Form.useWatch依赖项, 方便独立在Modal中使用 */}
        <TextValue {...{ label: '订单业务类型', name: 'businessType' }} />
        <TextValue {...{ label: '已选中商品的仓库ID', name: 'createDeliveryWarehouseId' }} />
        <Form.List
          {...{
            label: '已选中商品',
            name: 'goodsList',
            children: () => {
              return null;
            },
          }}
        />
        <TextValue {...{ label: '申请单号', name: 'podOrderNo' }} />
        <TextValue {...{ label: '履约单号', name: 'complianceNo' }} />
        <TextValue {...{ label: '是否支持小件', name: 'supportSmall' }} />
        <Form.List
          {...{
            label: '已删除的文件缓存',
            name: 'cacheDeletedFileList',
            initialValue: [],
            children: () => {
              return null;
            },
          }}
        />
      </Item>
      <RowLayout columnNum={props.columnNum ?? 4}>
        <Item
          {...{
            label: '配送类型',
            name: nPDelivery('dispatchServiceTypeInfo'),
            rules: [{ required: true }],
          }}
        >
          <SearchSelect
            {...{
              ref: dispatchServiceTypeRef,
              labelInValue: true,
              searchByLocal: true,
              options: dsControl.data,
              onChange(val, option?: TypeCustomerChannelTreeNode) {
                form.setFieldsValue({
                  deliveryInfo: {
                    dispatchServiceType: option?.value,
                    dispatchServiceNameInfo: null,
                    dispatchServiceName: null,
                    customerChannelCode: null,
                    trackingNo: null,
                    fileList: [],
                  },
                });
              },
            }}
          />
        </Item>
        <Item
          {...{
            shouldUpdate: (preVal, nextVal) => preVal.frontChannelType !== nextVal.frontChannelType,
          }}
        >
          {function () {
            const frontChannelType = form.getFieldValue('frontChannelType');
            const customerChannelNode = (
              <Item
                {...{
                  noStyle: true,
                  label: '客户渠道',
                  name: nPDelivery('customerChannelCode'),
                  rules: [{ required: true }],
                }}
              >
                <SearchSelect
                  {...{
                    options: customerChannelOptions,
                  }}
                />
              </Item>
            );
            const dispatchChannelNode = (
              <div>
                <Item
                  {...{
                    noStyle: true,
                    label: '配送渠道',
                    name: nPDelivery('dispatchServiceNameInfo'),
                    rules: [{ required: true }],
                  }}
                >
                  <SearchSelect
                    {...{
                      labelInValue: true,
                      options: dispatchChannelOptions,
                      onChange(val, option?: TypeDispatchChannelOption) {
                        form.setFields([
                          {
                            name: nPDelivery('dispatchServiceName'),
                            value: option?.deliveryChannelCode,
                          },
                        ]);
                      },
                    }}
                  />
                </Item>
                <Item hidden>
                  <TextValue
                    {...{ label: '配送渠道value', name: nPDelivery('dispatchServiceName') }}
                  />
                </Item>
              </div>
            );

            if (isPortal) {
              /** 门户侧仅支持客户渠道选择 */
              return <Item label={'客户渠道'}>{customerChannelNode}</Item>;
            }

            return (
              <Item
                {...{
                  label: (
                    <Item
                      {...{
                        noStyle: true,
                        name: 'frontChannelType',
                        initialValue: 'customerChannel',
                      }}
                    >
                      <SearchSelect
                        {...{
                          allowClear: false,
                          showSearch: false,
                          disabled: isModalModify,
                          size: 'small',
                          options: [
                            { label: '客户渠道', value: 'customerChannel' },
                            { label: '配送渠道', value: 'dispatchChannel' },
                          ],
                          onChange() {
                            form.setFields([
                              { name: nPDelivery('customerChannelCode'), errors: [] },
                            ]);
                          },
                        }}
                      />
                    </Item>
                  ),
                  required: true,
                }}
              >
                {frontChannelType === 'dispatchChannel' ? dispatchChannelNode : customerChannelNode}
              </Item>
            );
          }}
        </Item>
        {fieldShowMap.trackingNoShow && (
          <Item {...{ label: '物流跟踪单号', name: nPDelivery('trackingNo') }}>
            <Input {...{ placeholder: '请输入' }} />
          </Item>
        )}
        {fieldShowMap.labelFileShow && (
          <Item {...{ label: 'Label附件', name: nPDelivery('fileListLabel') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.label, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
        )}
        <Item
          {...{
            label: '签署服务',
            name: nPExpress('signatureType'),
            initialValue: 'NO_SIGNATURE' /** 默认: 无需签署 */,
          }}
        >
          <SearchSelect
            {...{
              request: apiMapDictType.outboundSignatureTypes,
              allowClear: false,
            }}
          />
        </Item>
        <Item {...{ label: '投保服务' }}>
          <div style={{ display: 'flex' }}>
            <Item
              {...{
                noStyle: true,
                label: '投保服务',
                name: nPExpress('insured'),
                initialValue: false /** 默认: 否 */,
                rules: [],
              }}
            >
              <Radio.Group
                {...{
                  style: { width: 120 },
                  options: optionsYesOrNo,
                  onChange(e) {
                    if (e.target.value === false) {
                      form.setFields([
                        { name: nPExpress('insuredAmount'), value: undefined, errors: [] },
                      ]);
                    }
                  },
                }}
              />
            </Item>
            <Item
              {...{
                noStyle: true,
                label: '投保金额',
                name: nPExpress('insuredAmount'),
              }}
            >
              <InputNumber
                {...{
                  style: { flex: 1 },
                  disabled: true,
                  stringMode: true,
                  addonAfter: (
                    <Item {...{ noStyle: true, name: nPExpress('insuredAmountCurrency') }}>
                      <TextValue />
                    </Item>
                  ),
                }}
              />
            </Item>
          </div>
        </Item>
        {supportSmall && (
          <Item
            {...{
              label: '是否打包',
              name: nPExpress('isPack'),
              initialValue: false /** 默认: 否 */,
            }}
          >
            <Radio.Group
              {...{
                style: { width: 120 },
                options: optionsYesOrNo,
              }}
            />
          </Item>
        )}
        {isPack && (
          <Item
            {...{
              label: '包材规格',
              name: npPackageMaterial('materialType'),
              initialValue: 'default',
            }}
          >
            <SearchSelect
              {...{
                request: apiMapDictType.outboundPackageMaterialTypeEnum,
                allowClear: false,
              }}
            />
          </Item>
        )}
        <RowLayout.RowInner {...{ ifShow: isTOB }}>
          <Item
            {...{
              label: '预计提货时间',
              name: nPDelivery('estimatedPickupDate'),
            }}
          >
            <DatePicker />
          </Item>
          <Item
            {...{
              label: '是否打托',
              shouldUpdate: (prevVal, nextVal) =>
                prevVal.deliveryInfo?.isPalletize !== nextVal.deliveryInfo?.isPalletize,
            }}
          >
            {function () {
              // const isPalletize = form.getFieldValue(nPDelivery('isPalletize'));

              return (
                <div style={{ display: 'flex' }}>
                  <Item
                    {...{
                      noStyle: true,
                      label: '是否打托',
                      name: nPDelivery('isPalletize'),
                      initialValue: false /** 默认: 否 */,
                    }}
                  >
                    <Radio.Group
                      {...{
                        style: { width: 120 },
                        options: optionsYesOrNo,
                        onChange(e) {
                          if (e.target.value === false) {
                            form.setFields([
                              { name: nPDelivery('palletQuantity'), value: null, errors: [] },
                            ]);
                          }
                        },
                      }}
                    />
                  </Item>
                  <Item
                    {...{
                      noStyle: true,
                      label: '打托数',
                      name: nPDelivery('palletQuantity'),
                      // initialValue: 0,
                      // rules: [
                      //   /** oms 侧校验必填, 门户不校验 */
                      //   { required: isOMS && isPalletize },
                      //   isPalletize && ruleUtils.rulePositiveIntZero,
                      // ].filter(Boolean),
                    }}
                  >
                    <InputNumber
                      {...{
                        style: { flex: 1 },
                        // disabled: isPalletize !== true,
                        /** 通过托盘尺寸计算 */
                        disabled: true,
                        stringMode: true,
                        addonAfter: '托',
                      }}
                    />
                  </Item>
                </div>
              );
            }}
          </Item>
          <Item
            {...{
              label: '托盘贴数',
              name: nPDelivery('palletNumber'),
              rules: [ruleUtils.rulePositiveIntZero],
            }}
          >
            <InputNumber
              {...{
                stringMode: true,
                addonAfter: '张/托',
                placeholder: '请输入',
              }}
            />
          </Item>
          <Item
            {...{
              label: '箱唛贴数',
              name: nPDelivery('markQuantity'),
              rules: [ruleUtils.rulePositiveIntZero],
            }}
          >
            <InputNumber
              {...{
                stringMode: true,
                addonAfter: '张/箱',
                placeholder: '请输入',
              }}
            />
          </Item>
          <Item
            {...{
              label: '产品贴数',
              name: nPDelivery('productQuantity'),
              rules: [ruleUtils.rulePositiveIntZero],
            }}
          >
            <InputNumber
              {...{
                stringMode: true,
                addonAfter: '张/箱',
                placeholder: '请输入',
              }}
            />
          </Item>
          <Item {...{ label: '箱唛贴', name: nPDelivery('fileListBoxMark') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.boxMark, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
          <Item {...{ label: '托盘贴', name: nPDelivery('fileListPallet') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.pallet, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
          <Item {...{ label: '产品贴', name: nPDelivery('fileListProduct') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.product, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
          <Item {...{ label: 'AMZX附件', name: nPDelivery('fileListAMZX') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.AMZX, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
          <Item {...{ label: 'BOL', name: nPDelivery('fileListBOL') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.BOL, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
        </RowLayout.RowInner>
        {fieldShowMap.fileShow && (
          <Item {...{ label: '其它附件', name: nPDelivery('fileListOther') }}>
            <SimpleUpload {...calcUploadProps(EnumOutboundFileType.other, props)}>
              <LoadButton>上传</LoadButton>
            </SimpleUpload>
          </Item>
        )}
      </RowLayout>
      <FormListPalletSize {...{ isModalModify }} />
    </>
  );
}

/** 监听派送仓库Id变化 */
function useWatchDeliveryWarehouseId({
  form,
  isModalModify,
}: {
  form: FormInstance;
  isModalModify: boolean;
}) {
  const createDeliveryWarehouseId = Form.useWatch(['createDeliveryWarehouseId'], form);
  const dispatchServiceType = Form.useWatch(nPDelivery('dispatchServiceType'), form);
  const frontChannelType = Form.useWatch('frontChannelType', form);
  /** 客户渠道接口 */
  const dsControl = useRequest<{ data: TypeCustomerChannelTreeNode[] }>(
    async function () {
      if (!createDeliveryWarehouseId) {
        return [];
      }
      return apiCustomerChannelTree({ warehouseId: createDeliveryWarehouseId });
    },
    { formatResult: (response) => response },
  );
  /** 配送渠道接口 */
  const dispatchControl = useRequest<{ data: TypeCustomerChannelTreeNode[] }>(
    async function () {
      if (!createDeliveryWarehouseId) {
        return [];
      }
      return apiDispatchChannelTree({
        warehouseId: createDeliveryWarehouseId,
      });
    },
    { formatResult: (response) => response },
  );
  const { fieldShowMap, customerChannelOptions, dispatchChannelOptions } = useMemo(
    function () {
      return {
        fieldShowMap:
          isOMS && isModalModify
            ? /** 仅OMS侧在弹窗修改时开放这三个选项操作 */
              { trackingNoShow: true, labelFileShow: true, fileShow: true }
            : dispatchRuleDict[dispatchServiceType as EnumDispatchType] || {},
        customerChannelOptions:
          ((dsControl.data as TypeCustomerChannelTreeNode[]) || []).find(
            (item) => item.deliveryType === dispatchServiceType,
          )?.children || [],
        dispatchChannelOptions:
          ((dispatchControl.data as TypeDispatchChannelTree[]) || []).find(
            (item) => item.deliveryType === dispatchServiceType,
          )?.children || [],
      };
    },
    [dsControl.data, dispatchControl.data, dispatchServiceType],
  );

  useEffect(() => {
    dsControl.run();
  }, [createDeliveryWarehouseId, frontChannelType]);

  useEffect(() => {
    dispatchControl.run();
  }, [createDeliveryWarehouseId, frontChannelType]);

  return {
    dsControl,
    fieldShowMap,
    customerChannelOptions,
    dispatchChannelOptions,
  };
}

/** NamePathPackageMaterial
 * @ 包材信息
 */
function npPackageMaterial<T>(x: T): ['packageMaterial', T] {
  return ['packageMaterial', x];
}

/** NamePathExpress
 * @ 物流快递信息
 */
export function nPExpress<T>(x: T): ['express', T] {
  return ['express', x];
}

/** NamePathDeliveryInfo
 * @ 物流服务信息
 */
function nPDelivery<T>(x: T): ['deliveryInfo', T] {
  return ['deliveryInfo', x];
}

/** 计算文件上传属性样式 */
function calcUploadProps(fileType: EnumOutboundFileType, props: TypeProps) {
  const defaultExtraProps = {
    multiple: true,
    maxCount: 10,
  };
  const extraPropsMap: Partial<Record<EnumOutboundFileType, TypeSimpleUploadProps>> = {
    /** 除label附件外不限制类型 */
    [EnumOutboundFileType.label]: {
      fileSuffix: ['pdf', 'png'],
      multiple: false,
      maxCount: 1,
    },
    [EnumOutboundFileType.AMZX]: {
      fileSuffix: ['pdf', 'png'],
      multiple: false,
      maxCount: 1,
    },
    [EnumOutboundFileType.BOL]: {
      fileSuffix: ['pdf', 'png'],
      multiple: false,
      maxCount: 1,
    },
  };
  const uploadProps = {
    ...getUploadPropsConfig(),
    ...defaultExtraProps,
    ...extraPropsMap[fileType],
    data: {
      pathType: EnumUploadFileType.temp,
    },
    allowManual: false,
    allowValidFileStatus: true,
    onRemove(file: NsOutbound.TypeFileItem) {
      if (file?.id) {
        const cacheDeletedFileList = props.form.getFieldValue('cacheDeletedFileList') || [];

        props.form.setFieldsValue({
          cacheDeletedFileList: [...cacheDeletedFileList, { ...file, deleted: true }],
        });
      }
    },
    onOriginChange(info) {
      getUploadPropsConfig.fileDoneProcess(info.file);
      info.file.fileType = fileType;
    },
  } as TypeSimpleUploadProps<{ fileType: EnumOutboundFileType }>;

  return uploadProps;
}

/** 物流服务文件列表解构 */
export function fileListDeconStruct(fileList: NsOutbound.TypeFileItem[]) {
  const fileTypeMap: Record<EnumOutboundFileType, NsOutbound.TypeFileItem[]> = {
    [EnumOutboundFileType.label]: [],
    [EnumOutboundFileType.other]: [],
    [EnumOutboundFileType.boxMark]: [],
    [EnumOutboundFileType.product]: [],
    [EnumOutboundFileType.pallet]: [],
    [EnumOutboundFileType.AMZX]: [],
    [EnumOutboundFileType.BOL]: [],
    [EnumOutboundFileType.POD]: [],
  };

  fileList.forEach((file) => {
    const item = Object.assign(file, {
      name: file.fileName,
      url: file.filePath,
      status: 'done',
    });

    fileTypeMap[item.fileType!]?.push(item);
  });

  return {
    fileListLabel: fileTypeMap[EnumOutboundFileType.label],
    fileListOther: fileTypeMap[EnumOutboundFileType.other],
    fileListBoxMark: fileTypeMap[EnumOutboundFileType.boxMark],
    fileListProduct: fileTypeMap[EnumOutboundFileType.product],
    fileListPallet: fileTypeMap[EnumOutboundFileType.pallet],
    fileListAMZX: fileTypeMap[EnumOutboundFileType.AMZX],
    fileListBOL: fileTypeMap[EnumOutboundFileType.BOL],
    fileListPOD: fileTypeMap[EnumOutboundFileType.POD],
  };
}

/** 物流服务文件合并上传 */
export function fileListMerge(data: TypeOutboundFormData) {
  const deliveryInfo = data?.deliveryInfo || {};
  const fileList = [
    ...(deliveryInfo?.fileListLabel || []),
    ...(deliveryInfo?.fileListOther || []),
    ...(deliveryInfo?.fileListBoxMark || []),
    ...(deliveryInfo?.fileListProduct || []),
    ...(deliveryInfo?.fileListPallet || []),
    ...(deliveryInfo?.fileListAMZX || []),
    ...(deliveryInfo?.fileListBOL || []),
    ...(deliveryInfo?.fileListPOD || []),
    ...(data?.cacheDeletedFileList || []),
  ];

  return {
    fileList,
  };
}

/** 刷新商品投保金额 */
function refreshGoodsInsuredAmount({ form }: { form: FormInstance }) {
  const isInsured: boolean = form?.getFieldValue(nPExpress('insured'));
  const goodsList: TypeOutboundGoodsTB[] = form.getFieldValue('goodsList') || [];

  if (isInsured) {
    let insuredAmount = 0;
    let isValid = true;

    goodsList.forEach((item) => {
      insuredAmount += (item.goodsInsuranceValue || 0) * (item.totalQuantity || 0);
      if (
        [isNumOrNumString(item.goodsInsuranceValue), isNumOrNumString(item.totalQuantity)].includes(
          false,
        )
      ) {
        /** 如果存在不合法的数据, 则保险金额无效 */
        isValid = false;
      }
    });

    form.setFields([{ name: nPExpress('insuredAmount'), value: isValid ? insuredAmount : null }]);
  }
}
