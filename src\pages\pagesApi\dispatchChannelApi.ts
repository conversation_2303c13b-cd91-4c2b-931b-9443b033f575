import { request } from 'umi';

/** 配送服务类型 | 物流类型
 * 客户面单1 指定快递2 默认仓配3 客户记账码4 LTL5
 */
export enum EnumDispatchType {
  /** 客户面单 */
  customerFaceSheet = 1,
  /** 指定快递 */
  specifyExpress = 2,
  /** 默认仓配 */
  defaultWarehouse = 3,
  /** 客户记账码 */
  customerAccountCode = 4,
  /** 卡车 */
  LTL = 5,
}

/** 客户渠道类型校验规则
 * @ 客户渠道上线以后,前端只控制显隐, 后端根据真实渠道配置做异常拦截
 */
export const dispatchRuleDict = {
  [EnumDispatchType.customerFaceSheet]: {
    trackingNoShow: true,
    labelFileShow: true,
    fileShow: false,
  },
  [EnumDispatchType.specifyExpress]: {
    trackingNoShow: false,
    labelFileShow: false,
    fileShow: false,
  },
  [EnumDispatchType.defaultWarehouse]: {} as {
    trackingNoShow?: boolean;
    labelFileShow?: boolean;
    fileShow?: boolean;
  },
  [EnumDispatchType.customerAccountCode]: {
    trackingNoShow: false,
    labelFileShow: false,
    fileShow: false,
  },
  [EnumDispatchType.LTL]: {
    trackingNoShow: true,
    labelFileShow: true,
    fileShow: true,
  },
};

/** 配送类型+渠道树形结构 */
export type TypeDispatchChannelTree = Awaited<ReturnType<typeof apiDispatchChannelTree>>[number];
/** 配送渠道单个Option类型, 里面包含配送服务 */
export type TypeDispatchChannelOption = Awaited<
  ReturnType<typeof apiDispatchChannelTree>
>[number]['children'][number];

/** 配送渠道树形结构 */
export function apiDispatchChannelTree(
  data: {
    /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
    deliveryType?: string;
    /** 仓库id */
    warehouseId?: string;
    /** 是否停启用, 默认查询启用 */
    enabled?: boolean;
  } = {},
) {
  return request<
    NsApi.TypeResponseData<
      {
        /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
        deliveryType?: number;
        /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
        deliveryTypeName?: string;
        /** 渠道列表 */
        channelList?: {
          /** 渠道商 */
          channelProvider?: string;
          /** 配送服务 */
          deliveryService?: string;
          /** 配送渠道编码 */
          deliveryChannelCode?: string;
          /** 配送渠道名称 */
          deliveryChannelName?: string;
          /** 区域值来源(1=数据库，2=API) */
          zoneSource?: number;
          /** 必填校验对象 */
          requiredRule?: {
            /** 快递单号是否必填 */
            trackingNo?: boolean;
            /** label附件是否必填 */
            labelFile?: boolean;
            /** 附件是否必填 */
            file?: boolean;
          };
          /** 关联仓库code列表 */
          warehouseRelaList?: string[];
        }[];
      }[]
    >
  >('/zouwu-oms-system/portal/channel/delivery/tree', {
    data: {
      enabled: true,
      ...data,
    },
    method: 'POST',
  }).then((res) => {
    return (res.data || []).map((itemDeliveryType) => {
      return {
        ...itemDeliveryType,
        label: itemDeliveryType.deliveryTypeName,
        value: itemDeliveryType.deliveryType,
        children: (itemDeliveryType.channelList || []).map((item) => {
          return {
            ...item,
            /** 物流追踪单号必填 */
            requiredLogistics: item?.requiredRule?.trackingNo,
            /** label附件必填 */
            requiredLabelFile: item?.requiredRule?.labelFile,
            /** 其它附件必填 */
            requiredEmployFile: item?.requiredRule?.file,
            label: item.deliveryChannelName,
            value: item.deliveryChannelCode,
          };
        }),
      };
    });
  });
}

/** 客户渠道服务TreeNode */
export type TypeCustomerChannelTreeNode = Awaited<
  ReturnType<typeof apiCustomerChannelTree>
>[number];

/** 出库客户渠道下拉选择 */
export function apiCustomerChannelTree(
  data: {
    /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
    deliveryType?: string;
    /** 仓库id */
    warehouseId?: string;
    /** 是否停启用, 默认查询启用 */
    enabled?: boolean;
  } = {},
) {
  return request<
    NsApi.TypeResponseData<
      {
        /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
        deliveryType?: number;
        /** 配送类型(1=客户面单，2=指定快递，3=默认仓配) */
        deliveryTypeName?: string;
        /** 客户渠道列表 */
        customerChannelList?: {
          /** 客户渠道id */
          customerChannelId: string;
          /** 客户渠道代码 */
          customerChannelCode?: string;
          /** 客户渠道名称 */
          customerChannelName?: string;
          /** 状态 | 是否启用 */
          enabled?: boolean;
          /** 配送渠道类型 */
          deliveryChannelType?: number;
        }[];
      }[]
    >
  >('/zouwu-oms-system/portal/customer/channel/order/tree', {
    data: {
      enabled: true,
      ...data,
    },
    method: 'POST',
  }).then((res) => {
    return (res.data || []).map((itemDeliveryType) => {
      return {
        ...itemDeliveryType,
        label: itemDeliveryType.deliveryTypeName,
        value: itemDeliveryType.deliveryType,
        children: (itemDeliveryType.customerChannelList || []).map((item) => {
          return {
            ...item,
            ...dispatchRuleDict[
              itemDeliveryType.deliveryType as EnumDispatchType.customerFaceSheet
            ],
            deliveryType: itemDeliveryType.deliveryType,
            // /** 物流追踪单号必填 */
            // requiredLogistics: item?.requiredRule?.trackingNo,
            // /** label附件必填 */
            // requiredLabelFile: item?.requiredRule?.labelFile,
            // /** 其它附件必填 */
            // requiredEmployFile: item?.requiredRule?.file,
            label: item.customerChannelCode,
            value: item.customerChannelCode,
          };
        }),
      };
    });
  });
}
