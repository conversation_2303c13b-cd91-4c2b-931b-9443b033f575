import type { ReactNode } from 'react';
import React from 'react';
import './index.less';

type TypeStatusShowProps = {
  /** 状态值 */
  value: string;
  /** 状态值对应的内容信息 */
  valueEnum: Record<string, TypeStatusValue>;
};
type TypeStatusValue = {
  /** 展示内容 */
  text: string | ReactNode;
  /** 状态值 */
  status?: 'success' | 'error' | 'default' | 'process' | 'warn';
  /** 自定义颜色 */
  color?: string;
};

/** 状态值 */
const colorStatus: Record<string, string> = {
  success: 'status-success',
  error: 'status-error',
  default: 'status-default',
  process: 'status-process',
  warn: 'status-warn',
};
/** 启用、停用，有、无等状态展示组件 */

export default function (props: TypeStatusShowProps) {
  const { value, valueEnum } = props;

  return (
    <span
      className={`${colorStatus[valueEnum[value]?.status || '']}`}
      style={{
        color: valueEnum[value]?.color ? valueEnum[value]?.color : '',
      }}
    >
      {valueEnum[value]?.text || '-'}
    </span>
  );
}
