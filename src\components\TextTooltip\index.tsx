import type { TooltipProps } from 'antd';
import { Tooltip } from 'antd';
import React from 'react';
import styles from './index.less';

type IProps = {
  width?: number;
} & TooltipProps;

/** @deprecated 计划废弃, 不适用, 限制死宽度容易造成业务想看数据不方便 */
export default function TextTooltip(props: IProps) {
  const { placement = 'top', title, width = 100, ...args } = props;

  return (
    <Tooltip title={title} placement={placement} {...args}>
      <span className={styles.text} style={{ width }}>
        {title}
      </span>
    </Tooltip>
  );
}
