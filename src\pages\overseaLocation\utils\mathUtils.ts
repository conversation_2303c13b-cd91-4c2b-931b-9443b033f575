/* eslint-disable @typescript-eslint/no-unused-expressions */
import { typeUtils } from './typeof';

/**
 * 保留小数（不四舍五入）
 * @param floatNum 输入值
 * @param decimal 保留浮点数精度位数,默认保留小数点后的0
 */
function toFixed(
  num: string | number,
  decimal: number,
  params: {
    /** 默认true */
    zeroFill?: boolean;
  } = {},
) {
  if (typeUtils.isNumOrNumString(num) === false) {
    // 判断是否为有效数字
    return num;
  }
  const { zeroFill = true } = params;

  let result = num.toString();
  const index = result.indexOf('.');
  let zeroLength = decimal;

  if (index !== -1) {
    zeroLength = result.length - index > decimal ? 0 : decimal + 1 - (result.length - index);
    result = result.substring(0, decimal + index + 1);
    zeroFill && (result += new Array(zeroLength + 1).join('0'));
  } else {
    // 处理整数
    result = result.substring(0);
    zeroFill && (result += `.${new Array(zeroLength + 1).join('0')}`);
  }
  return result;
}

/**
 * 四舍五入
 * 默认小数后的0不要
 */
function toFixedRound(
  num: string | number,
  decimal: number,
  params: {
    /** 默认true */
    zeroFill?: boolean;
  } = {},
) {
  if (typeUtils.isNumOrNumString(num) === false) {
    // 判断是否为有效数字
    return num;
  }
  const { zeroFill = false } = params;
  const result = Number(num).toFixed(decimal);

  return zeroFill ? result : Number(result);
}

export const mathUtils = {
  toFixed,
  toFixedRound,
};
