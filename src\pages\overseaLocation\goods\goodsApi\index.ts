import { request } from 'umi';
import { message } from 'antd';
import { downloadFileByFullUrl } from '@/pages/overseaLocation/utils';
import { arrayAddKey } from '@/utils';

/** 商品列表 */
export function apiGoodsList(
  data: NsApi.TypeRequestListQuery<{
    condition?: {
      /** 商品sku*/
      sku?: string;
      /** 商品查询sku集合 */
      skuList?: string[];
      /** 国际商代码 */
      upc?: string;
      /** 其他编码 */
      otherCode?: string;
      /** 客户sku */
      skuRemark?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypeGoodsTB[]>>('/zouwu-oms-goods/portal/goods/list', {
    method: 'POST',
    data,
  });
}

/** 商品详情 */
export function apiGoodsDetails(data: { id?: string }) {
  return request<NsApi.TypeResponseData<TypeGoodsTB>>('/zouwu-oms-goods/portal/goods/details', {
    method: 'POST',
    data,
  });
}

/** 商品新增 */
export function apiGoodsCreate(data: any) {
  return request('/zouwu-oms-goods/portal/goods/create', {
    method: 'POST',
    data,
  });
}

/** 商品编辑 */
export function apiGoodsUpdate(data: any) {
  return request('/zouwu-oms-goods/portal/goods/update', {
    method: 'POST',
    data,
  });
}

/** 获取商品列表, 用于入库商品选择 */
export function apiGoodsListForGoodsChoose(
  data: NsApi.TypeRequestListQuery<{
    condition?: {
      sku?: string;
      skuList?: string[];
      /** 当前仓库币种 */
      warehouseCurrency?: string;
    };
  }>,
) {
  return apiGoodsList(data).then((res) => {
    const records = res?.data?.records || [];
    const newRecords = records.map((o) => {
      // 暂时无视国家属性 countryName
      const info =
        o?.goodsCustomsInformationList?.find(
          (item) => item.currency === data?.condition?.warehouseCurrency,
        ) || {};

      return {
        sku: o.sku,
        goodsPicture: o.pictureUrl,
        goodsName: o.name,
        goodsWeight: o.weight,
        weightUnit: o.weightUnit,
        goodsLength: o.length,
        goodsWidth: o.width,
        goodsHeight: o.height,
        volumeUnit: o.measureUnit,
        goodsPallet: o.goodsPallet,
        goodsValue: info.goodsDeclaredValue,
        valueUnit: info.currency,
        goodsInsuranceValue: info.goodsInsuranceValue,
      };
    });

    return {
      ...res,
      data: {
        records: newRecords,
        totalSize: res.data?.totalSize,
      },
    };
  });
}

/** 商品目的仓库列表 */
export function apiDestinationWarehouseSelect(data?: { query: string }) {
  return request<
    NsApi.TypeResponseData<
      {
        /** 商品ID */
        id?: number;
        /** 仓库名称 */
        name?: string;
        /** 仓库code */
        code?: string;
      }[]
    >
  >('/api/website/web/zouwu/getWarehousesList.do', {
    data,
    method: 'POST',
  }).then((res) => {
    return res.result.map((item) => ({
      ...item,
      label: item.code,
      value: item.code,
    }));
  });
}

/** 商品打印sku标签图片预览 */
export function apiGoodsSkuLabelPreview(data: {
  /** 商品 List */
  printSkuList?: {
    /** 商品id */
    goodsId?: number;
    /** 种类分拣码 */
    categoryCode?: string;
    /** 打印个数 */
    printNum?: number;
  };
  /** 仓库code */
  warehouseCode?: number;
  /** 模板类型: SKU_100MM_150MM, SKU_30MM_40MM */
  templateType?: string;
}) {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-goods/portal/goods/previewSkuLabels', {
    data,
    method: 'POST',
  }).then((res) => ({ labelPictureBase64: res?.data }));
}

/** 商品打印sku标签下载类型 */
export type TypeSkuLabelDownload = {
  /** 仓库code, 不同仓库可能模板不同 */
  warehouseCode?: string;
  /** 商品 List */
  printSkuList?: {
    /** 商品id */
    goodsId?: number;
    /** 种类分拣码 */
    categoryCode?: string;
    /** 打印个数 */
    printNum?: number;
  }[];
  /** 模板类型: SKU_100MM_150MM, SKU_30MM_40MM */
  templateType?: string;
};

/** 商品打印sku标签下载 */
export function apiGoodsSkuLabelDownload(data: TypeSkuLabelDownload) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-goods/portal/goods/getBatchPrintSkuLabels',
    {
      data,
      method: 'POST',
    },
  ).then((res) => {
    return downloadFileByFullUrl(res.data);
  });
}

/** 商品导入模板下载 */
export async function apiGoodsImportTemplateDownload() {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-goods/portal/goods/getTemplate', {
    method: 'GET',
  })
    .then(zouwuOmsApiCatchProcess)
    .then(({ data }) => {
      // kiwi-disable
      downloadFileByFullUrl(data, '商品导入模板.xlsx');
      // kiwi-enable
    });
}

/** 商品批量创建导入 */
export async function apiGoodsBatchImport(data: { filePath: string }) {
  return request<
    NsApi.TypeResponseData<{
      records: {
        sku: string;
        recordLine: number;
        message: string;
      }[];
      totalSize: number;
    }>
  >('/zouwu-oms-goods/portal/goods/batchImportGoods', {
    data,
    method: 'POST',
  })
    .then(zouwuOmsApiCatchProcess)
    .then((res) => {
      return {
        ...res,
        data: {
          ...res.data,
          records: (res?.data?.records || []).map((item, i) => ({
            ...item,
            index: i + 1 /** 添加下标 */,
          })),
        },
      };
    });
}

/** 商品编辑批量导入 */
export async function apiGoodsEditBatchImport(data: { filePath: string }) {
  return request<
    NsApi.TypeResponseData<{
      records: {
        sku: string;
        recordLine: number;
        message: string;
        index: number;
      }[];
      totalSize: number;
    }>
  >('/zouwu-oms-goods/portal/goods/batchImportUpdateGoods', {
    data,
    method: 'POST',
  }).then((res) => {
    (res.data.records || []).forEach((item, i) => {
      item.index = i + 1;
    });
    return res;
  });
}

/** 商品批量导出 */
export async function apiGoodsBatchExport(data: {
  /** 商品查询sku列表 */
  skuList?: string[];
  /** 商品sku */
  sku?: string;
  /** 国际商代码 */
  upc?: string;
  /** 其他编码 */
  otherCode?: string;
  /** 客户sku */
  skuRemark?: string;
  /** 文件名 */
  fileName?: string;
}) {
  return request('/zouwu-oms-system/portal/goods/export/goods', {
    data,
    method: 'POST',
  });
}

/** zouwu-oms接口临时异常处理机制 */
async function zouwuOmsApiCatchProcess<T>(res: T): Promise<T> {
  const response = res as any;

  if (response?.code === '000000') {
    return res;
  }
  if (response?.message) {
    message.error(response?.message);
  }

  return Promise.reject(res);
}

/** 根据商品ID获取组合商品信息 */
export function apiGetCombinedGoodsByGoodsId(data: { id?: string }) {
  return request<
    NsApi.TypeResponseData<{
      exist?: boolean;
      combinedGoodsList?: TypeGoodsGroupRecord[];
    }>
  >('/zouwu-oms-goods/portal/goods/combined/getByGoodsId', {
    method: 'POST',
    data,
  });
}

/** 组合商品中, 下拉搜索商品 */
export async function apiSearchGoodsBySkuOrName(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 商品sku */
      sku?: string;
      /** 商品名称 */
      name?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypeGoodsTB[]>>(
    '/zouwu-oms-goods/portal/goods/listBySkuOrName',
    {
      method: 'POST',
      data,
    },
  );
}
