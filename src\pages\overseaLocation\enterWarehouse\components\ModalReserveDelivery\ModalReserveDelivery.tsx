import I18N from '@/utils/I18N';
import type { TypeZouModalRef } from '@/components';
import { ZouModal } from '@/components';
import { ElementCopy, TextValue } from '@/views';
import { dateUtils, strUtils, tipsUtils, useTimeSeries } from '@/utils';
import { Form, Input, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import TextArea from 'antd/lib/input/TextArea';
import { CopyOutlined } from '@ant-design/icons';
import css from './ModalReserveDelivery.less';
import type { TypeInboundReserveSendWarehouseDetail } from '../../enterWarehouseApi';
import { apiInboundReserveDetail, apiInboundReserveEmailSubmit } from '../../enterWarehouseApi';

const { dateFormatter } = dateUtils;

type TypeProps = {
  /** 组件Props定义 */
};

type TypeOpenParams = {
  /** 组件打开参数定义 */
  podOrderNo?: string;
};

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};

export type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

/** 预约送仓 */
export default React.forwardRef(function ModalReserveDelivery(
  props: TypeProps,
  ref: React.Ref<TypeModalRef | undefined>,
) {
  const { modalRef, asyncInit, asyncClear, asyncSubmit, form, detailData } = useConfig({ props });

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams, operate?: TypeOperateMap) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        fixedHeight: true,
        modalProps: {
          width: '60%',
          title:
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
              .ModalReserveDelivery.chineseSymbols15,
          afterClose() {
            asyncClear();
          },
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      <Form {...{ form, style: { flex: 1 } }}>
        <div>
          {
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
              .ModalReserveDelivery.chineseSymbols14
          }
        </div>
        <ElementCopy
          {...{
            className: css.copyBox,
            copyProps: {
              icon: (
                <span>
                  {
                    I18N
                      .Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                      .ModalReserveDelivery.chineseSymbols13
                  }
                  <CopyOutlined style={{ marginLeft: 5 }} />
                </span>
              ),
            },
          }}
        >
          <div>
            <div className={css.headerBox}>
              <div>
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                    .ModalReserveDelivery.chineseSymbols12
                }
                {detailData.podOrderNo}
              </div>
              <div>
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                    .ModalReserveDelivery.chineseSymbols11
                }
                {detailData.logisticsNo}
              </div>
              <div>
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                    .ModalReserveDelivery.chineseSymbols10
                }
                {detailData.containerNo}
              </div>
              <div>
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                    .ModalReserveDelivery.chineseSymbols9
                }
                {detailData.containerModel}
              </div>
              <div>ETD：{dateFormatter(detailData.expectSendTime)}</div>
              <div>ETA：{dateFormatter(detailData.expectTime)}</div>
            </div>
            <br />
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols8
              }
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols7
              }
              {detailData.warehouseCode}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols6
              }
              {detailData.contactPerson}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.telephone
              }
              {detailData.contactPersonTel}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.email
              }
              {detailData.contactPersonEmail}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols5
              }
              {detailData.informEmail}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols4
              }
              {detailData.address}
            </div>
            <br />
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols3
              }
            </div>
            <br />
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols2
              }
              <a target="_blank" href={detailData.doorTrailerLink}>
                {detailData.doorTrailerLink}
              </a>
            </div>
          </div>
        </ElementCopy>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                .ModalReserveDelivery.mailbox,
            name: 'emailString',
            extra: (
              <div style={{ color: '#F59A23' }}>
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                    .ModalReserveDelivery.chineseSymbols1
                }
              </div>
            ),
            rules: [
              {
                required: true,
              },
            ],
          }}
        >
          <Input.TextArea
            {...{
              placeholder:
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.chineseSymbols,
            }}
          />
        </Form.Item>
        <Form.Item hidden>
          <TextValue
            {...{
              label:
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                  .ModalReserveDelivery.orderNumber,
              name: 'podOrderNo',
            }}
          />
        </Form.Item>
      </Form>
    </ZouModal>
  );
});

function useConfig({ props }: { props: TypeProps }) {
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<TypeInboundReserveSendWarehouseDetail>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const modalRef = useRef<TypeZouModalRef>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    const initData = transDetail(params);

    setDetailData(initData);
    form.setFieldsValue(initData);
    setOperateMap(operate || {});
  }

  async function asyncClear() {
    setOperateMap({});
    setDetailData({});
    form.resetFields();
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();
    const emailList = strUtils.strSplitForEmail(formData.emailString);

    await apiInboundReserveEmailSubmit({
      ...formData,
      emailString: undefined,
      emailList,
    });
    message.success(tipsUtils.TIPS_FORM_SUBMIT_SUCCESS);
    operateMap?.submitSuccessCB?.();
  }

  return { detailData, asyncInit, asyncClear, asyncSubmit, form, modalRef };
}

/** 转换DetailData */
function transDetail(inData: TypeInboundReserveSendWarehouseDetail) {
  return {
    ...inData,
  };
}
