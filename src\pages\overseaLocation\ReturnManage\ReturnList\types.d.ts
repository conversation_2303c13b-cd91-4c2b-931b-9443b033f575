/** 退件订单Record */
type TypeReturnRecord = {
  /** 主键 id */
  id?: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 退件类型 300-未知退件, 301-物流退件, 302-客户退件 */
  businessType?: number;
  /** 处理状态 | 订单状态 301-待认领, 302-已认领, 330-标记入库, 390-标记销毁 */
  orderStatus?: number;
  /** 审核类型 1-待审核, 2-已审核 */
  orderReviewType?: number;
  /** 订单来源 1-门户, 2-erp, 3-电商系统, 4-FBA, 5-门户导入 */
  orderSource?: number;
  /** 退件追踪单号 */
  returnTrackingNo?: string;
  /** 目标仓库编码 */
  targetWarehouseCode?: string;
  /** 目标仓库名称 */
  targetWarehouseName?: string;
  /** 包裹状态 1-完好, 2-破损, 99-未知 */
  packageStatus?: number;
  /** 退件原因 */
  returnReason?: string;
  /** 客户备注 */
  customerRemark?: string;
  /** 预计销毁时间 */
  expectDestroyTime?: string;
  /** 创建时间 */
  createDate?: string;
  /** 创建人id */
  createById?: string;
  /** 创建人名称 */
  createByName?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 更新人id */
  updateById?: string;
  /** 更新人名称 */
  updateByName?: string;
  /** 客户id */
  companyId?: string;
  /** 客户名称 */
  companyName?: string;
  /** 删除标志 */
  deleted?: boolean;
  /** 附件信息 */
  orderFileList?: NsReturn.TypeOrderFileItem[];
  /** 商品信息 */
  orderGoodsList?: {
    /** 主键 id */
    id?: string;
    /** SKU */
    sku?: string;
    /** 换标SKU */
    changedSku?: string;
    /** 总件数 */
    totalQuantity?: number;
    /** 创建时间 */
    createDate?: string;
    /** 创建人id */
    createById?: string;
    /** 创建人名称 */
    createByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 更新人id */
    updateById?: string;
    /** 更新人名称 */
    updateByName?: string;
    /** 客户id */
    companyId?: string;
    /** 客户名称 */
    companyName?: string;
  }[];
};

/** 退件订单详情 */
type TypeReturnOrderDetail = TypeReturnRecord & {
  /** 履约信息 */
  orderChild?: {
    /** 主键 id */
    id?: string;
    /** 目标仓库id */
    targetWarehouseId?: number;
    /** 目标仓库编码 */
    targetWarehouseCode?: string;
    /** 目标仓库名称 */
    targetWarehouseName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人id */
    createById?: string;
    /** 创建人名称 */
    createByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 更新人id */
    updateById?: string;
    /** 更新人名称 */
    updateByName?: string;
    /** 客户id */
    companyId?: string;
    /** 客户名称 */
    companyName?: string;
  };
  /** 附件信息 */
  orderFileList?: NsReturn.TypeOrderFileItem[];
};

/** 退件订单表单数据 */
type TypeReturnOrderFormData = {
  /** 申请单号 */
  podOrderNo?: string;
  /** 退件追踪单号 */
  returnTrackingNo?: string;
  /** 包裹状态 1-完好, 2-破损, 99-未知 */
  packageStatus?: number;
  /** 退件原因 */
  returnReason?: string;
  /** 客户id */
  companyId?: string;
  /** 创建人 */
  createByName?: string;
  /** 履约信息 */
  orderChild?: {
    /** 目标仓库编码 */
    targetWarehouseCode?: string;
  };
  /** 商品信息 */
  orderGoodsList?: {
    /** SKU */
    sku?: string;
    /** 总件数 */
    totalQuantity?: number;
  }[];
  /** 附件信息 */
  orderFileList?: NsReturn.TypeOrderFileItem[];
  /** OMS订单文件信息 */
  fileListOms?: NsReturn.TypeOrderFileItem[];
  /** 门户订单文件信息 */
  fileListPortal?: NsReturn.TypeOrderFileItem[];
};

declare namespace NsReturn {
  /** 退件订单文件信息 */
  export type TypeOrderFileItem = {
    /** 附件名称 */
    fileName?: string;
    /** 附件类型 1-海外仓附件, 2-门户附件(客户附件) */
    fileType?: import('./ReturnOrderEnum').EnumReturnFileType;
    /** 附件路径 */
    filePath?: string;
    /** 文件id */
    id?: string;
  };
}
