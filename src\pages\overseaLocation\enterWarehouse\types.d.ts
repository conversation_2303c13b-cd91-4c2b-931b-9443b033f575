/** 入库订单列表数据 */
type TypeEnterWarehouseTB = {
  /** id */
  id: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 业务类型 */
  businessType?: number;
  /** 单据状态 */
  orderStatus?: number;
  /** 单据状态对应的tabKey名称 */
  orderStatusTabKey?: string;
  /** 单据状态后端翻译 */
  orderStatusName?: string;
  /** 目标仓库id */
  targetWarehouseId?: number;
  /** 目标仓库编码 */
  targetWarehouseCode?: string;
  /** 目标仓库名称 */
  targetWarehouseName?: string;
  /** 目标仓库类型 */
  targetWarehouseType?: number;
  /** 目标仓库类型后端翻译 */
  targetWarehouseTypeName?: string;
  /** 总箱数 */
  boxQuantity?: number;
  /** 总件数 */
  totalQuantity?: number;
  /** 待执行数量 */
  notReceivedQuantity?: number;
  /** 已执行数量 */
  receivedQuantity?: number;
  /** 物流渠道 */
  logisticsProvider?: number;
  /** 物流渠道(后端翻译) */
  logisticsProviderName?: string;
  /** 物流追踪单号 */
  logisticsNo?: string;
  /** 订单创建时间 */
  createDate?: string;
  /** 客户公司Id */
  companyId?: string;
  /** 客户公司 */
  companyName?: string;
  /** 预计到仓时间 */
  expectTime?: string;
  /** 预计发货时间 */
  expectSendTime?: string;
  /** 签收时间 */
  receivedTime?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 船公司 */
  shippingCompany?: string;
  /** 集装箱号（物流渠道=1时展示） */
  containerNo?: string;
  /** 集装箱型号（物流渠道=1时展示） */
  containerModel?: string;
  /** 预约状态(协同状态)，详见teamWorkStatus字典 */
  teamWorkStatus?: number;
  /** 头程标记(履约标记)，详见complianceType字典 */
  complianceType?: number;
  /** 头程标记(履约标记)，后端翻译 */
  complianceTypeName?: string;
  /** 是否可以预约送仓 */
  ableToDeliveryAppointment?: boolean;
  /** 预约单号(协同单号)，非空时展示【预约详情】按钮 */
  teamWorkNo?: string;
  /** 预约单状态(协同单状态) 1 :未生效 2 :生效 3 :失效 */
  teamWorkNoStatus?: number;
  /** 物流列表 */
  orderLogisticList?: {
    /** 物流跟踪单号 */
    logisticsNo?: string;
    /** 物流渠道商  1 :MWB(海运) 2 :AWB(空运) 3 :TRUCK(卡车) 4 :OTHER 5 :EXPRESS(快递) */
    logisticsProvider?: number;
    /** 总托盘数 */
    totalTrayNum?: number;
    /** 物流渠道商(后端翻译) */
    logisticsProviderName?: string;
    /** 物流渠道商 */
    logisticsChannelProvider?: number;
    /** 物流渠道商-value */
    logisticsChannelProviderValue?: string;
    /** 海运集装箱型号 */
    containerModel?: string;
    /** 物流当前状态 */
    currentStatus?: string;
    /** 物流渠当前节点类型POL, POD */
    currentType?: string;
    /** 物流箱动态节点 */
    nodeList?: {
      /** 节点代码 */
      code?: string;
      /** 节点顺序 */
      rank?: number;
      /** 节点名称 */
      name?: string;
      /** 节点准备好的状态描述 */
      readyStatus?: string;
      /** 节点未准备好的状态描述 */
      notReadyStatus?: string;
      /** 节点时间 */
      time?: string;
      /** 节点是否点亮 */
      active?: boolean;
      /** 是否预计时间 */
      isest?: boolean;
      /** 节点类型POL:头程，POD尾程 */
      type?: string;
    }[];
  }[];
};

/** 到仓数量详情 */
type TypezReachedRecords = {
  /** WMS签收单据号 */
  wmsReceivedNo?: string;
  /** 异常数量 */
  badQuantity?: number;
  /** 正常数量 */
  goodQuantity?: number;
  /** 异常件数量 */
  errorQuantity?: number;
  /** 订单商品id */
  orderGoodsId?: number;
  /** 履约单号 */
  complianceNo?: string;
  /** 签收记录状态 */
  status?: number;
  /** 库存单据号 */
  reserveNo?: string;
  /** 仓库签收时间 */
  remoteReceivedTime?: number;
  /** 签收时间 */
  receivedTime?: number;
};

/** 箱型号 */
type TypeModalItem = {
  cnName?: string;
  code: string;
  createTime: string;
  dzgId: string;
  enName: string;
  id: string;
  isEnable: number;
  limitWeight: number;
  modifier: string;
  modifierId: string;
  modifyTime: string;
  remark: string;
  sort: number;
  teu: string;
  uniqueCode: string;
  version: string;
};

/** 商品出入库表单结构, 入库和出库都会用到 */
type TypeGoodsTableForm = {
  /** SKU */
  sku?: string;
  /** 商品类型（1-商品，2-组合商品，3-子商品） */
  goodsType?: number;
  /** 关联组合商品sku（商品类型为子商品时必填） */
  combinedGoodsSku?: string;
  /** 商品图片地址 */
  goodsPicture?: string;
  /** 商品名称 */
  goodsName?: string;
  /** 商品英文名称 */
  goodsEnName?: string;
  /** 商品长度,示例值(1.55) */
  goodsLength?: number;
  /** 商品宽度,示例值(1.55) */
  goodsWidth?: number;
  /** 商品高度,示例值(1.55) */
  goodsHeight?: number;
  /** 体积单位, 即长宽高单位 */
  volumeUnit?: string;
  /** 商品重量,示例值(1.55) */
  goodsWeight?: number;
  /** 商品重量单位 */
  weightUnit?: 'KG' | 'G' | 'LB' | 'OZ';
  /** 总件数 */
  totalQuantity?: number;
  /** 总箱数 */
  boxQuantity?: number;
  /** 单箱包装数量, 一件一件货就是1 */
  quantityPerBox?: number;
  /** 1 整箱, 2 混箱 */
  boxType?: number;
  /** 混箱id, id一致表示在同箱里, 会生成一致的箱唛号, 由前端生成 */
  boxUniqueId?: string;
};

/** 入库单提交表单 */
type TypeInboundFormData = {
  /** 主订单id */
  orderId?: number;
  /** 履约单id */
  orderChildId?: number;
  /** 申请单号 */
  podOrderNo?: string;
  /** 业务类型;1-环世头程入库，2-代发头程入库，3-FBA中转入库，4-退货入库 */
  businessType?: number;
  /** 目标仓库id */
  warehouseId?: string;
  /** 仓库code */
  warehouseCode?: string;
  /** 目标仓库名称 */
  warehouseName?: string;
  /** 目标仓库类型;1-标准海外仓，2-海外中转仓 */
  warehouseType?: number;
  /** 预期交付时间 */
  expectTime?: string;
  /** 预计发货时间（入库） */
  expectSendTime?: string;
  /** 船公司 */
  shippingCompany?: string;
  /** 订单来源 1-门户，2-erp， 3-电商系统', 4-FBA ,5-门户导入 */
  orderSource?: number;
  /** 船公司code */
  shippingCompanyCode?: string;
  /** 备注 */
  remark?: string;
  /** 物流相关信息 */
  logisticsDTOS?: {
    /** id */
    logisticsId?: number;
    /** 是否删除 */
    deleted?: boolean;
    /** 物流渠道商类型 */
    logisticsProvider?: number;
    /** 物流渠道商 */
    logisticsChannelProvider?: number;
    /** 物流追踪单号 */
    logisticsNo?: string;
    /** 海运集装箱型号 */
    containerModel?: string;
    /** 海运集装箱号 */
    containerNo?: string;
    /** 总托盘数 */
    totalTrayNum?: number;
  }[];
  /** 商品信息
   * @ 表单提交使用
   */
  goodsDTOS?: {
    /** id */
    orderGoodsId?: number;
    /** 是否删除 true-是，false-否 */
    deleted?: boolean;
    /** 履约单号 */
    complianceNo?: string;
    /** SKU */
    sku?: string;
    /** 产品图片 */
    goodsPicture?: string;
    /** 产品描述 */
    goodsName?: string;
    /** 产品价值 */
    goodsValue?: number;
    /** 价值单位 */
    valueUnit?: string;
    /** 产品净重 */
    goodsWeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 体积单位 */
    volumeUnit?: string;
    /** 保质期 */
    shelfLife?: string;
    /** 单箱数量 */
    quantityPerBox?: number;
    /** 总件数 */
    totalQuantity?: number;
    /** 总箱数 */
    boxQuantity?: number;
    /** 箱唛号 */
    shippingMarkNo?: string;
    /** 装箱类型 */
    boxType?: number;
    /** 混箱id */
    boxUniqueId?: string;
  }[];
  /** 物流箱子信息 */
  boxList?: NsInbound.TypeLogisticsBoxInfo[];
};

/** 入库相关数据类型 */
declare namespace NsInbound {
  /** 箱子内商品信息 */
  type TypeBoxGoodsInfo = {
    /** id */
    orderGoodsId?: number;
    /** 是否删除 true-是，false-否 */
    deleted?: boolean;
    /** 履约单号 */
    complianceNo?: string;
    /** SKU */
    sku?: string;
    /** 产品图片 */
    goodsPicture?: string;
    /** 产品描述 */
    goodsName?: string;
    /** 产品价值 */
    goodsValue?: number;
    /** 价值单位 */
    valueUnit?: string;
    /** 产品净重 */
    goodsWeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 体积单位 */
    volumeUnit?: string;
    /** 保质期 */
    shelfLife?: string;
    /** 单箱数量 */
    quantityPerBox?: number;
    /** 总件数 */
    totalQuantity?: number;
    /** 总箱数 */
    boxQuantity?: number;
    /** 箱唛号 */
    shippingMarkNo?: string;
    /** 装箱类型 */
    boxType?: number;
    /** 混箱id */
    boxUniqueId?: string;
  };
  /** 物流箱子信息 */
  type TypeLogisticsBoxInfo = {
    /** 箱id */
    boxId?: number;
    /** 出库单号 */
    podOrderNo?: string;
    /** 履约单号 */
    complianceNo?: string;
    /** 自定义箱条码 */
    boxCode?: string;
    /** 箱重 */
    boxWeight?: number;
    /** 箱长 */
    boxLength?: number;
    /** 箱宽 */
    boxWidth?: number;
    /** 箱高 */
    boxHeight?: number;
    /** 重量单位 */
    weightUnit?: number;
    /** 体积单位 */
    volumeUnit?: number;
    /** 箱子数量 */
    boxQuantity?: number;
    /** 箱唛号 */
    shippingMarkNo?: string;
    /** 是否删除 true-是，false-否 */
    deleted?: boolean;
    /** 入库商品信息 */
    orderGoodsList?: TypeBoxGoodsInfo[];
  };
}
