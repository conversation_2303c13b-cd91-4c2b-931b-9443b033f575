/** 退货管理页面 */
import I18N from '@/utils/I18N';

export const returnManage = [
  {
    path: 'return-manage',
    redirect: './logistics-intercept-list',
  },
  {
    path: 'return-manage',
    title: I18N.Src__Routes.Return_manage.chineseSymbols1,
    access: 'OVERSEA_RETURN_MANAGE',
    locale: false,
    routes: [
      {
        path: 'logistics-intercept-list',
        title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols1,
        access: 'OVERSEA_RETURN_MANAGE_LOGISTICS_INTERCEPT',
        locale: false,
        component: '@/pages/overseaLocation/ReturnManage/LogisticsIntercept/LogisticsInterceptList',
      },
      {
        path: 'return-face-sheet-list',
        title: I18N.Src__Routes.Return_manage.chineseSymbols,
        access: 'OVERSEA_RETURN_MANAGE_RETURN_FACE_SHEET',
        locale: false,
        component: '@/pages/overseaLocation/ReturnManage/ReturnFaceSheet/ReturnFaceSheetList',
      },
      {
        path: 'return-list',
        title: '退件列表',
        access: 'OVERSEA_RETURN_MANAGE_RETURN_LIST',
        locale: false,
        hideChildrenInMenu: true,
        routes: [
          {
            path: '',
            title: '退件列表',
            component: '@/pages/overseaLocation/ReturnManage/ReturnList/ReturnList',
          },
          {
            path: 'return-detail',
            title: '退件详情',
            locale: false,
            component: '@/pages/overseaLocation/ReturnManage/ReturnList/ReturnDetail',
          },
          {
            path: 'return-edit',
            title: '退件编辑',
            locale: false,
            component: '@/pages/overseaLocation/ReturnManage/ReturnList/ReturnEdit',
          },
        ],
      },
    ],
  },
];
