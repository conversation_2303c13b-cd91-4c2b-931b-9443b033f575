import React from 'react';
import { isPortal } from '@/oms-portal-diff-import';
import { FormProDescriptions } from '@/views';

type TypeProps = {
  detailData?: TypeOutboundDetail;
};

/** 附加信息 */
export default function DetailAdditionalInfo(props: TypeProps) {
  return (
    <FormProDescriptions
      {...{
        title: isPortal ? '附加信息' : undefined,
        dataSource: props.detailData || {},
        columns: [
          { title: '订单来源平台', dataIndex: 'orderSourcePlatformName' },
          { title: '客户销售单', dataIndex: 'customerSalesNo' },
          { title: '客户关联单', dataIndex: 'customerRelatedNo' },
          { title: '平台卖家ID', dataIndex: 'platformSellerId' },
          { title: '平台店铺ID', dataIndex: 'platformStoreId' },
          { title: 'Ebay交易ID号', dataIndex: 'ebaySalesId' },
          { title: 'Ebay平台交易单号', dataIndex: 'ebayPlatformSalesNo' },
          { title: '订单备注', dataIndex: 'remark' },
        ],
      }}
    />
  );
}
