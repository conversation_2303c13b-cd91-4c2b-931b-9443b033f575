import React, { useEffect, useState } from 'react';
import { Form, Space, Select, Input, Button, Popconfirm, Table } from 'antd';
import { connect, ConnectRC, getLocale, useIntl } from 'umi';
import type { ValidatorRule } from 'rc-field-form/lib/interface';
import I18N from '@/utils/I18N';
import styles from './GoodsServiceSetTableForm.less';
import { CommonRulesMap } from '@/utils/rules';
import { CountrySelect, TextValue } from '@/views';
import { SearchSelect } from '../../components';
import { apiCustomerCurrencyOptions } from '@/pages/pagesApi';

interface IProps {
  name: string;
  value?: any;
  rules?: ValidatorRule[];
  /** 客户币种 */
  customerCurrency: string;
}

/** 投保服务设置 */
function GoodsServiceSetTableForm({ name: prefix, rules, customerCurrency }: IProps) {
  return (
    <Form.List name={prefix} rules={rules}>
      {(fields, { add, remove }, { errors }) => {
        const columns = [
          {
            title: I18N.Src__Pages__Order__Detail.PrePane.country,
            width: '20%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'countryName']}
                  fieldKey={[field.fieldKey, 'countryName']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                  ]}
                >
                  <CountrySelect />
                </Form.Item>
              );
            },
          },
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsServiceSetTableForm
                .chineseSymbols1,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'goodsInsuranceValue']}
                  fieldKey={[field.fieldKey, 'goodsInsuranceValue']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatFour,
                  ]}
                >
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              );
            },
          },
          /** 产品要求隐藏  历史名称: 申报价值 | 现在对应 投保金额 */
          /* {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsServiceSetTableForm
                .chineseSymbols,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'goodsDeclaredValue']}
                  fieldKey={[field.fieldKey, 'goodsDeclaredValue']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatFour,
                  ]}
                >
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              );
            },
          }, */
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsServiceSetTableForm.currency,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...{
                    name: [field.name, 'currency'],
                  }}
                >
                  <SearchSelect
                    {...{
                      request: (keyword: string) => apiCustomerCurrencyOptions({ keyword }),
                    }}
                  />
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Common__Template.Index.operation,
            width: '10%',
            render: (_: any, field: any) => {
              return (
                <Form.Item {...field} fieldKey={[field.fieldKey, 'action']}>
                  <span>
                    <Popconfirm
                      title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                      onConfirm={() => {
                        remove(field.name);
                      }}
                    >
                      <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                    </Popconfirm>
                  </span>
                </Form.Item>
              );
            },
          },
        ];

        return (
          <>
            <Table
              title={() => (
                <Space>
                  <Button type="primary" onClick={() => add({})}>
                    {
                      I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsServiceSetTableForm
                        .increaseInsurance
                    }
                  </Button>
                </Space>
              )}
              className={styles['goods-service-table']}
              columns={columns}
              dataSource={fields}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content', y: 300 }}
            />
            <Form.ErrorList errors={errors} />
          </>
        );
      }}
    </Form.List>
  );
}

export default connect(({ global }: { global: any }) => ({ ...global }), null, null, {
  forwardRef: true,
})(GoodsServiceSetTableForm);
