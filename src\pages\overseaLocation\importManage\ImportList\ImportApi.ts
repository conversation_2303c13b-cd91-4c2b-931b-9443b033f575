import { request } from 'umi';

/** 导入列表 */
export function apiImportList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 导入类型 */
      importType?: string;
      /** 导入状态 */
      importStatus?: string;
      /** 创建时间开始 */
      createDateStart?: string;
      /** 创建时间结束 */
      createDateEnd?: string;
    };
  }>,
) {
  return request('/zouwu-oms-system/portal/common/imports/list', { data, method: 'POST' });
}
