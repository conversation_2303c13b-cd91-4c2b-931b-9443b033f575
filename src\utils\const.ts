import I18N from '@/utils/I18N';
import { BASE_API_PREFIX } from '@/constants';

export const BASE_DOMAIN =
  process.env.NODE_ENV === 'development' ? 'http://192.168.50.3' : window.location.origin;

export const DOWNLOAD_FORM_ACTION_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/website/web/downloadfile?alifileName`;

export const SI_DOWNLOAD_TEMPLATE_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/api/website/web/orderSi/downloadTemplate.do`;
export const SI_DOWNLOAD_CONTAINER_TEMPLATE_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/api/website/web/vgm/downloadTemplate.do?fileDic=si&fileName=wwl_si_container_Template.xlsx`;
export const ZHONGLIAN_SI_DOWNLOAD_CONTAINER_TEMPLATE_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/api/website/web/vgm/downloadTemplate.do?fileDic=si&fileName=ul_si_container_Template.xlsx`;

export const SI_DOWNLOAD_VGM_TEMPLATE_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/api/website/web/vgm/downloadTemplate.do?fileDic=vgm&fileName=WWL-VGM-Template.xlsx`;
export const ZHONGLIAN_SI_DOWNLOAD_VGM_TEMPLATE_URL = `${BASE_DOMAIN}/${BASE_API_PREFIX}/api/website/web/vgm/downloadTemplate.do?fileDic=vgm&fileName=UL-VGM-Template.xlsx`;

export const SI_TEMPLATE_PATH =
  'https://eshipping.oss-cn-shanghai.aliyuncs.com/eshipping/other/template/si/wwl_si_Template.xlsx';

export const UL_SI_TEMPLATE_PATH =
  'https://eshipping.oss-cn-shanghai.aliyuncs.com/eshipping/other/template/si/wwl_si_Template.xlsx';

// 船名轨迹查询
export const SHIP_TRACK_PATH =
  'https://www.freightower.com/#/vessel/iframe/tIuUI06pqIVy-E9BTz66apXa9THnVMPlzy_qDiPbpCtbf-DDT5VP9PcYJGd1Ywjb/';

export const PRINT_DOCUMENT_URL = 'https://eshipping.oss-cn-shanghai.aliyuncs.com/upload_public/';

export const ORDER_TAB_STATUS = [
  {
    label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole,
    labelEn: 'ALL',
    value: 'all',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted,
    labelEn: 'TO BE SUBMITE',
    value: 10,
  },
  { label: I18N.Src__Pages__Home.Index.received, labelEn: 'RECEIVED', value: 7 },
  // { cnlabel: '待订舱', labelEn: 'BKG PENDING', value: 1 },
  { label: I18N.Src__Pages__Home.Index.booking, labelEn: 'BKG TO CARRIER', value: '1,2' },
  { label: I18N.Src__Pages__Home.Index.bookedSpace, labelEn: 'BKG CONFIRMED', value: 3 },
  { label: I18N.Src__Pages__Home.Index.sld, labelEn: 'ONBOARD', value: 4 },
  { label: I18N.Src__Pages__Home.Index.arrived, labelEn: 'ARRIVAL', value: 6 },
  { label: I18N.Src__Utils.Const.customsCleared, labelEn: 'CLEARANCE', value: 8 },
  { label: I18N.Src__Utils.Const.shipped, labelEn: 'DELIVERED', value: 9 },
  { label: I18N.Src__Pages__Home.Index.chargeback, labelEn: 'BKG CANCELLED', value: 5 },
];

export const DEBITNOTE_TAB_STATUS = [
  { label: I18N.Src__Utils.Const.toBeConfirmed, labelEn: 'TO PAID', value: '1' },
  {
    label: I18N.Src__Pages__Order__BookingTracking.Index.confirmed,
    labelEn: 'CFMD CONFIRMED',
    value: '2',
  },
  { label: I18N.Src__Utils.Const.paid, labelEn: 'PAID', value: '3' },
];

export const ENTERPRISE_TAB_STATUS = [
  { label: I18N.Src__Pages__Enterprise__Account.Index.joined, labelEn: 'HAS JOINED', value: '1' },
  { label: I18N.Src__Utils.Const.toBeAdded, labelEn: 'TO JOIN THE', value: '0' },
];

export const ENTERPRISEACCOUNT_TAB_STATUS = [
  { label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole, labelEn: 'ALL', value: '' },
  { label: I18N.Src__Pages__Enterprise__Account.Index.toBeReviewed, labelEn: 'TO AUDIT', value: 0 },
  { label: I18N.Src__Pages__Enterprise__Account.Index.joined, labelEn: 'HAS JOINED', value: 1 },
  {
    label: I18N.Src__Pages__Enterprise__Account.Index.rejected,
    labelEn: 'HAS REFUSED TO',
    value: 2,
  },
];

export const BILL_TAB_STATUS = [
  { label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole, labelEn: 'ALL', value: '' },
  // { label: '审核中', labelEn: 'Under review', value: '4' },
  { label: I18N.Src__Utils.Const.toBeConfirmed, labelEn: 'TO PAID', value: '1' },
  {
    label: I18N.Src__Pages__Order__BookingTracking.Index.confirmed,
    labelEn: 'CFMD CONFIRMED',
    value: '2',
  },
  { label: I18N.Src__Utils.Const.paid, labelEn: 'PAID', value: '3' },
];

export enum BlType {
  'MB/L' = '1',
  'HB/L' = '2',
}

export enum Dictionary {
  'MBL_QIANDAN' = '502',
  'HBL_QIANDAN' = '302',
  'MBL_YUNSHU' = '503',
  'HBL_YUNSHU' = '304',
  'MBL_PAY_TYPE' = '504',
  'HBL_PAY_TYPE' = '303',
  'ORDER_REQUIRE' = '532',
  'ZUOXIANG_TYPE' = '533',
  'BAOGUAN_TYPE' = '534',
  'ZHUANGXIANG_TYPE' = '526',
  'MAO_YI_TIAOKUANG' = '535',
  'XIANG_XING_SIZE' = '507',
  'HUOWU_TYPE' = '505',
  'BAOZHUANG_TYPE' = '506',
  'DAN_ZHENG_TYPE' = 'order_so_file',
  'DAI_BAN_SHI_XIANG' = '37',
  'MESSAGE_TYPE' = '26',
  'READ_STATUS' = '25',
  'ORDER_TYPE' = '1001',
  'SHI_FOU' = '1',
}

export const SPACE_STATUS_MAP: any = {
  1: {
    name: I18N.Src__Utils.Const.tightSpace,
    nameEn: 'TIGHT',
    color: '#e4d41f',
  },
  2: {
    name: I18N.Src__Utils.Const.sufficientSpace,
    nameEn: 'AVAILABLE',
    color: '#73c88b',
  },
  3: {
    name: I18N.Src__Utils.Const.explosionChamber,
    nameEn: 'FULL',
    color: '#c87373',
  },
};

export const WAREHOUSE_BUSINESS_TYPE = [
  { label: I18N.Src__Utils.Const.theFirstJourneyOfTheWorld, value: 101 },
  { label: I18N.Src__Utils.Const.issuingTheFirstCourse, value: 102 },
  { label: I18N.Src__Utils.Const.returnReceipt, value: 104 },
  { label: I18N.Src__Utils.Const.warehousing1, value: 105 },
];

export const OVERSEA_LOCATION_BATCH_OUT_STATUS = [
  { label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole, value: 'ALL' },
  { label: I18N.Src__Pages__Order__So.Tuoshu.parsing, value: 1 },
  { label: I18N.Src__Utils.Const.toBeIssued, value: 2 },
  { label: I18N.Src__Utils.Const.partialDistribution, value: 3 },
  { label: I18N.Src__Utils.Const.distributeAll, value: 4 },
];

export const OVERSEA_LOCATION_BATCH_OUT_STATUS_ENUM = {
  1: I18N.Src__Pages__Order__So.Tuoshu.parsing,
  2: I18N.Src__Utils.Const.toBeIssued,
  3: I18N.Src__Utils.Const.partialDistribution,
  4: I18N.Src__Utils.Const.distributeAll,
} as const;

export const OVERSEA_LOCATION_BATCH_OUT_DETAIL_STATUS_ENUM = {
  1: I18N.Src__Utils.Const.toBeIssued,
  2: I18N.Src__Utils.Const.issued,
  3: I18N.Src__Utils.Const.abnormal,
};

export const LOGISTICS_PROVIDER_TYPE = [
  { label: I18N.Src__Utils.Const.oceanShipping, value: 1 },
  //  目前不支持
  // { label: 'AWB(空运)', value: 2 },
  { label: I18N.Src__Utils.Const.truck, value: 3 },
  // { label: 'OTHER', value: 4 },
  { label: I18N.Src__Utils.Const.express, value: 5 },
];

/** @deprecated 弃用,改后端翻译 */
export const LOGISTICS_PROVIDER_TYPE_DESC = {
  1: 'MWB',
  //  目前不支持
  // 2: 'AWB',
  3: 'TRUCK',
  // 4: 'OTHER',
  5: 'EXPRESS',
};

/**
 * @deprecated 废弃的出库订单状态翻译
 */
export const ENTER_WAREHOUSE_TYPE: any = {
  100: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted,
  102: I18N.Src__Utils.Const.customerCreatesOrder,
  103: I18N.Src__Utils.Const.supplementTheFirstStep,
  104: I18N.Src__Utils.Const.inboundForecast,
  105: I18N.Src__Utils.Const.inTransit,
  106: I18N.Src__Utils.Const.signingIn,
  107: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1,
  108: I18N.Src__Utils.Const.expenseConfirmation,
  109: I18N.Src__Utils.Const.statement,
  110: I18N.Src__Pages__Order__TrailerOrder.Config.canceled,
};

/** 新增状态,新增对应颜色
 * @deprecated 作废了, 由 INBOUND_TAB_OPTIONS 统一维护
 */
export const ENTER_WAREHOUSE_OPTION_DESC: any = {
  100: { name: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted, color: '#FFA940' },
  102: { name: I18N.Src__Utils.Const.headToBeSupplemented, color: '#FFA940' },
  103: { name: I18N.Src__Utils.Const.headToBeSupplemented, color: '#FFA940' },
  104: { name: I18N.Src__Utils.Const.inTransit, color: '#00C853' },
  105: { name: I18N.Src__Utils.Const.inTransit, color: '#00C853' },
  130: { name: I18N.Src__Utils.Const.inTransit, color: '#00C853' },
  106: { name: I18N.Src__Utils.Const.signingIn, color: '#00C853' },
  107: { name: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1, color: '#00AAFF' },
  108: { name: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1, color: '#00AAFF' },
  // 109: { name: '已结单', color: '#00AAFF' },
  110: { name: I18N.Src__Pages__Order__TrailerOrder.Config.canceled, color: '#999' },
};

/** @deprecated 作废了, 由OUT_WAREHOUSE_OPTIONS 统一维护 */
export const OUT_WAREHOUSE_OPTION_DESC: any = {
  200: { name: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted, color: '#FFA940' },
  201: { name: I18N.Src__Pages__Enterprise__Account.Index.toBeReviewed, color: '#FFA940' },
  202: { name: I18N.Src__Pages__OverseaLocation.Enum.toBeShipped, color: '#00C853' },
  203: { name: I18N.Src__Pages__OverseaLocation.Enum.toBeShipped, color: '#00C853' },
  204: { name: I18N.Src__Pages__OverseaLocation.Enum.shipped, color: '#00C853' },
  205: { name: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1, color: '#00AAFF' },
  206: { name: I18N.Src__Pages__OverseaLocation.Enum.toBeDetermined, color: '#00AAFF' },
  207: { name: I18N.Src__Pages__OverseaLocation.Enum.settledBill, color: '#00AAFF' },
  208: { name: I18N.Src__Pages__Order__TrailerOrder.Config.canceled, color: '#999' },
  209: { name: I18N.Src__Pages__OverseaLocation.Enum.processing, color: '#00C853' },
  //  新增异常单
  210: { name: I18N.Src__Pages__OverseaLocation.Enum.exceptionList, color: '#fe0013' },
};

export const ORDERTYPE = {
  '1': I18N.Src__Utils.Const.receiptDoc,
  '2': I18N.Src__Utils.Const.outboundOrder,
};

/** 包装类型 */
export const PACKING_TYPE_OPTIONS = [
  { label: I18N.Src__Utils.Const.unpacked, value: 1 },
  { label: I18N.Src__Utils.Const.selfPacking, value: 2 },
  { label: I18N.Src__Utils.Const.specialPackaging, value: 3 },
];

/* 打包设置 */
export const BALE_TYPE_OPTIONS = [
  { label: I18N.Src__Utils.Const.doNotSet, value: 1 },
  { label: I18N.Src__Utils.Const.independentPackaging, value: 2 },
  { label: I18N.Src__Utils.Const.mergePackaging, value: 3 },
];

/* 复核状态类型 */
export const EXAMINED_STATUS_OPTIONS = [
  { label: I18N.Src__Utils.Const.toBeReviewed, value: 1 },
  { label: I18N.Src__Utils.Const.reviewed, value: 2 },
];

export const CONTAIN_BATTERY_OPTIONS = [
  { label: I18N.Src__Utils.Const.batteryMatching, value: false },
  { label: I18N.Src__Utils.Const.batteryInstallation, value: true },
];

export const YES_OR_NO_OPTIONS = [
  { label: I18N.Src__Pages__OverseaLocation.Enum.yes, value: true },
  { label: I18N.Src__Pages__OverseaLocation.Enum.no, value: false },
];

export const YES_OR_NO_TYPES = [
  { label: I18N.Src__Pages__OverseaLocation.Enum.yes, value: 1 },
  { label: I18N.Src__Pages__OverseaLocation.Enum.no, value: 0 },
];

export const BATTERY_TYPE_OPTIONS = [
  { label: I18N.Src__Utils.Const.lithiumIonElectricity, value: 1 },
  { label: I18N.Src__Utils.Const.lithiumMetalElectricity, value: 2 },
  { label: I18N.Src__Utils.Const.other, value: 3 },
];

/** 度量方式 */
export const MEASURE_TYPE_OPTIONS = [
  { label: I18N.Src__Utils.Const.metric, value: 1 },
  { label: I18N.Src__Utils.Const.imperial, value: 2 },
];

/** 英制重量单位 */
export const IMPERIAL_WEIGHT_UNIT_OPTIONS = [
  { label: 'LB', value: 'LB' },
  { label: 'OZ', value: 'OZ' },
];

/** 公制重量单位 */
export const METRIC_WEIGHT_UNIT_OPTIONS = [
  { label: 'KG', value: 'KG' },
  { label: 'G', value: 'G' },
];

export enum EnumActionType {
  'ADD' = 'ADD',
  'EDIT' = 'EDIT',
  'COPY' = 'COPY',
}

// /** 配送服务类型 */
// export const WAREHOUSE_BUSINESS_OPTIONS = [
//   { label: '环世头程入库', value: 1 },
//   { label: '代发头程入库', value: 2 },
//   // { label: 'FBA中转入库', value: 3 },
//   // { label: '退货入库', value: 4 },
// ];

/** 配送服务类型 */
// export const DISPATCH_SERVICE_OPTIONS = [
//   { label: '指定快递', value: 1 },
//   { label: '仓配服务', value: 2 },
//   { label: '客户自供面单', value: 3 },
// ];

/** 质量类型 */
export const QUALITYTYPE: any = {
  GOOD: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece1,
  BAD: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts1,
  ERROR: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.abnormalParts1,
};

/** 入库详情流程节点 */
export const WAREHOUSE_DOCUMENT_STATUS = [
  { label: I18N.Src__Utils.Const.customerCreatesOrder, value: 101 },
  { label: I18N.Src__Utils.Const.submitForReview, value: 102 },
  { label: I18N.Src__Utils.Const.supplementTheFirstStep, value: 103, hide: true },
  {
    label: I18N.Src__Utils.Const.inboundForecast,
    value: [104 /** 入库预报通知 */, 130 /** 待下发 */],
  },
  { label: I18N.Src__Utils.Const.inTransit, value: 105 },
  { label: I18N.Src__Utils.Const.signingIn, value: 106 },
  { label: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1, value: 107 },
  /** 优化移除已废弃入库流程的节点 */
  // { label: '费用确认', value: 108 },
  // { label: '结单', value: 109 },
  { label: I18N.Src__Pages__Order__TrailerOrder.Config.canceled, value: 110 },
];

export const WAREHOUSE_DOCUMENT_STATUS_DESC: any = {
  // 100: '待提交',
  '101': I18N.Src__Utils.Const.customerCreatesOrder,
  '102': I18N.Src__Utils.Const.submitForReview,
  '103': I18N.Src__Utils.Const.supplementTheFirstStep,
  '104': I18N.Src__Utils.Const.inboundForecast,
  '105': I18N.Src__Utils.Const.inTransit,
  '106': I18N.Src__Utils.Const.signingIn,
  '107': I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1,
  '108': I18N.Src__Utils.Const.expenseConfirmation,
  '109': I18N.Src__Utils.Const.statement,
};
/** 订单来源平台
 * @deprecated 作废,改为字典取值
 */
export const ORDER_SOURCE_PLATFORM = [
  { label: 'Amazon', value: 1 },
  { label: 'Ebay', value: 2 },
  { label: 'Wayfair', value: 3 },
  { label: 'overstock', value: 4 },
  { label: 'lowes', value: 5 },
  { label: 'home depot', value: 6 },
  { label: I18N.Src__Utils.Const.otherECommerce, value: 99 },
];

/** @deprecated 废弃, 改用 apiMapDictType.certificateType */
export const CERTIFICATE_TYPE = [
  { label: I18N.Src__Utils.Const.id, value: 1 },
  { label: I18N.Src__Utils.Const.passport, value: 2 },
  { label: I18N.Src__Utils.Const.other, value: 99 },
];

/** @deprecated 废弃 改用字段 apiMapDictType.certificateType */
export const CERTIFICATE_TYPE_DESC: any = {
  1: I18N.Src__Utils.Const.id,
  2: I18N.Src__Utils.Const.passport,
  99: I18N.Src__Pages__OverseaLocation.Enum.other,
};

export const DISPATCH_SERVICES_TYPES = [
  { label: I18N.Src__Utils.Const.enclosure, value: 1, disabledKey: 'requiredLabelFile' },
  {
    label: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.otherAccessories,
    value: 2,
    disabledKey: 'requiredEmployFile',
  },
];

export const OUT_WAREHOUSE_ORDER_TYPE = [
  { label: I18N.Src__Utils.Const.standardDelivery, value: 201 },
  { label: I18N.Src__Utils.Const.exitFromWarehouse, value: 202 },
];

/** @deprecated 海外仓常量定义之后会逐步作废 */
export const OUT_WAREHOUSE_ORDER_TYPE_DESC: any = {
  201: I18N.Src__Utils.Const.standardDelivery,
  202: I18N.Src__Utils.Const.exitFromWarehouse,
};

/** 客户记账码快递服务商
 * @deprecated 废弃
 */
export const DISPATCH_SERVICE_PROVIDER_OPTIONS: any = [
  { label: 'FedEx', value: 'FedEx' },
  { label: 'UPS', value: 'UPS' },
];
