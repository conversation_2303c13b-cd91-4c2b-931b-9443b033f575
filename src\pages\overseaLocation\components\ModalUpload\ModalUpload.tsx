import type { ModalProps, FormInstance } from 'antd';
import { Button, Form, message, Modal } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import classnames from 'classnames';
import I18N from '@/utils/I18N';
import { useSafeStateUpdate } from '@/pages/overseaLocation/utils';
import './ModalUpload.less';
import type { TypeSimpleUploadProps, TypeUploadFile } from '../SimpleUpload';
import { SimpleUpload } from '../SimpleUpload';

export type TypezModalUploadAction = {
  /** loading状态 */
  loading: boolean;
  /** 修改loading状态 */
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  /** 打开弹窗 */
  open: () => void;
  /** 关闭弹窗 */
  close: () => void;
  /** 初始化 */
  init: () => void;
};
/** 上传完后返回格式 */
export type TypezModalUploadResponse = {
  result?: React.ReactNode;
};
export type TypezModalUploadProps = {
  /** 标题 */
  title?: string;
  /** open按钮,自动绑定点击开启弹窗
   * @效果等同挂ref.open
   */
  children?: React.ReactElement;
  /** 上传限制说明 */
  limitDesc?: React.ReactNode[];
  /** 文件后缀 */
  fileSuffix: string[];
  /** 文件大小限制, 默认100mb */
  maxFileSizeMb?: number;
  /** 单次选择不少于有效数量, 默认1 */
  validCount?: number;
  /** 文件数量限制, 默认1
   * @为1时多次选择会自动替换上一个 */
  maxCount?: number;
  /** 上传请求, return不为void时,按成功处理 */
  asyncSubmit?: (params: {
    form: FormInstance;
    fileList: TypeUploadFile[];
    modalControl: TypezModalUploadAction;
  }) => Promise<void | TypezModalUploadResponse>;
  /** 如果发生过upload,关闭时会触发回调
   * @否则不触发
   */
  onCancelAfterUpload?: () => void;
  /** 弹窗属性, 优先级高于独立属性 */
  modalProps?: ModalProps;
  className?: string;
  /** 渲染附件下载按钮 */
  btnExtra?: () => React.ReactNode;
};

/** 通用弹窗上传 */
export default React.forwardRef(function ModalUpload(
  props: TypezModalUploadProps,
  ref: React.ForwardedRef<TypezModalUploadAction>,
) {
  const { limitDesc = [] } = props;
  const modalControl = useModal(props);
  const { loading, clickRef, initUploadRef, isUploadRef } = modalControl;
  const uploadControl = useUpload({ props, modalControl, isUploadRef });
  const { fileLimitNode, innerRequest, initUpload, uploadResponse, form } = uploadControl;
  const openNode = props.children ? (
    <span onClick={() => modalControl.open()}> {props.children}</span>
  ) : null;

  clickRef.current = innerRequest;
  initUploadRef.current = initUpload;

  useImperativeHandle(ref, () => modalControl);

  return (
    <span className={classnames('modal-upload', props.className)}>
      {openNode}
      <Modal {...modalControl.config}>
        <Form {...{ form }}>
          <Form.Item {...{ label: '上传文件', name: 'fileList' }}>
            <SimpleUpload {...uploadControl.config}>
              <Button type="default" disabled={loading}>
                {I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.selectFile1}
              </Button>
            </SimpleUpload>
          </Form.Item>
        </Form>
        <div className="modal-upload__file-limit-box">
          {fileLimitNode}
          <div>
            {limitDesc.length > 0 ? (
              <ol className="limit-desc-box">
                {limitDesc.map((item, i) => (
                  <li className="li" key={i}>
                    <span className="serial-number">{i + 1}.</span>
                    {item}
                  </li>
                ))}
              </ol>
            ) : null}
          </div>
        </div>
        {uploadResponse && (
          <div className="modal-upload__result-box">
            <div>
              {
                I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload
                  .chineseSymbols4
              }
            </div>
            <div>{uploadResponse.result}</div>
          </div>
        )}
      </Modal>
    </span>
  );
});

function useUpload({
  props,
  modalControl,
  isUploadRef,
}: {
  props: TypezModalUploadProps;
  modalControl: TypezModalUploadAction;
  isUploadRef: React.MutableRefObject<boolean>;
}) {
  const [form] = Form.useForm();
  const { maxCount = 1, validCount = 1, fileSuffix, maxFileSizeMb = 10, btnExtra } = props;
  const [uploadResponse, setUploadResponse] = useState<TypezModalUploadResponse>();
  const { loading, setLoading } = modalControl;
  const { safeUpdateFn } = useSafeStateUpdate();
  const config = {
    disabled: loading,
    maxCount,
    multiple: true,
    fileSuffix,
    maxFileSizeMb,
    btnExtra,
  } as TypeSimpleUploadProps;

  const fileLimitNode = I18N.template(
    I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.chineseSymbols1,
    { val1: fileSuffix.join(', '), val2: maxFileSizeMb },
  );

  function initUpload() {
    form.resetFields();
    setUploadResponse(undefined);
  }

  function innerRequest() {
    if (!props?.asyncSubmit) return;
    const formData = form.getFieldsValue();
    const fileList = formData.fileList || [];

    if (fileList.length < validCount) {
      message.error(
        I18N.template(
          I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.chineseSymbols,
          { val1: validCount },
        ),
      );
      return;
    }
    setLoading(true);
    const res = props.asyncSubmit({ fileList, modalControl, form });

    res.then((response) => {
      if (response !== undefined) {
        initUpload();
        setUploadResponse(response);
      }
    });
    isUploadRef.current = true;
    res.finally?.(safeUpdateFn(() => setLoading(false)));
  }

  return {
    form,
    config,
    fileLimitNode,
    innerRequest,
    initUpload,
    uploadResponse,
  };
}

function useModal(props: TypezModalUploadProps) {
  const initUploadRef = useRef<() => void>();
  /** 是否发生过上传 */
  const isUploadRef = useRef(false);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const open = () => {
    init();
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
  };
  const clickRef = useRef<() => void>();
  const config = {
    title: props.title,
    visible,
    width: 600,
    okButtonProps: {
      loading,
      onClick() {
        clickRef.current?.();
      },
    },
    cancelButtonProps: {
      disabled: loading,
    },
    okText: I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.confirmUpload1,
    cancelText: I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.close,
    closable: loading === false,
    destroyOnClose: true,
    maskClosable: false,
    keyboard: false,
    ...props.modalProps,
    className: classnames('modal-upload', props?.modalProps?.className, props.className),
    onCancel(e) {
      if (isUploadRef.current) {
        props.onCancelAfterUpload?.();
        isUploadRef.current = false;
      }
      props?.modalProps?.onCancel?.(e);
      close();
    },
  } as ModalProps;

  function init() {
    initUploadRef.current?.();
  }

  return {
    config,
    open,
    close,
    loading,
    setLoading,
    init,
    initUploadRef,
    clickRef,
    isUploadRef,
  };
}
