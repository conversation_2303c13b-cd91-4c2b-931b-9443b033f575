/* eslint-disable react/jsx-key */
import React from 'react';
import { request } from 'umi';
import type { apiCommonImport } from './importApi';
import { Form } from 'antd';
import type { TypeSimpleUploadProps } from '@/components';
import { SearchSelect } from '@/components';
import { apiActualWarehouseOptions } from '@/pages/pagesApi';
import type { NamePath } from 'antd/lib/form/interface';
import { aliOssUtils } from '@/utils-business';

/** 自定义弹窗表单内容渲染 */
export type TypeModalChildren = (params: {
  nodeMap: {
    /** 单据识别对象 */
    nodeIdentityNoType: React.ReactNode;
    /** 导入类型 */
    nodeImportType: React.ReactNode;
    /** 文件上传 */
    nodeSimpleUpload: React.ReactNode;
  };
  npExtParams: (name: string) => NamePath;
}) => React.ReactNode;

/** enum 导入类型 */
export enum EnumImportType {
  /** 门户入库单 */
  inbound = 'INBOUND_ORDER_CREATE',
  /** 出库tob物流补充 */
  outboundTobExpress = 'TOB_OUTBOUND_EXPRESS_IMPORT',
  /** 出库单BOL补充 */
  outboundBOL = 'OUTBOUND_BOL_BATCH_IMPORT',
  /** 批量人工出库, TOB, 标准都支持 */
  outboundManual = 'TOB_OUTBOUND_FULFILL_IMPORT',
  /** 批量导入退件 */
  returnOrderCreate = 'RETURN_ORDER_CREATE',
  /** 批量导入退件附件 */
  returnOrderFile = 'RETURN_ORDER_FILE_BATCH_IMPORT',
}

/** 根据导入类型约定不同的参数, 业务逻辑 */
export const importTypeParamsDict = {
  [EnumImportType.inbound]: { fileSuffix: ['xls', 'xlsx'] },
  [EnumImportType.outboundTobExpress]: {
    fileSuffix: ['xls', 'xlsx'],
    labelColSpan: 6,
    showExtParams: { identityNoType: 'POD_ORDER_NO' },
    apiCustomizeImport(data) {
      return request('/zouwu-oms-system/oms/order/tob/outbound/express/import', {
        data,
        method: 'POST',
      });
    },
  },
  [EnumImportType.outboundBOL]: {
    fileSuffix: ['zip'],
    maxFileSizeMb: 50,
    labelColSpan: 6,
    allowDownloadTemp: false,
    showExtParams: { identityNoType: 'TRACKING_NO' },
    showFileLimitTip: true,
    limitDesc: [
      <span>文件夹和文件名都只能使用英文和数字，{renderTextRed('不支持汉字；')}</span>,
      <span>
        <span style={{ color: 'red' }}>附件名称请对准匹配规则，</span>附件支持pdf和png格式，且不超过
        20 M；
      </span>,
      <span>
        根据附件名称与单据识别对象匹配，
        <span style={{ color: 'red' }}>若未匹配上或者匹配多条订单，则该条附件无法上传；</span>
      </span>,
      '若附件对应的单号上已上传过附件，则导入时更新原来附件；',
    ],
    apiCustomizeImport(data) {
      return request('/zouwu-oms-system/oms/order/tob/outbound/bol/import', {
        data,
        method: 'POST',
      });
    },
  },
  [EnumImportType.outboundManual]: {
    fileSuffix: ['xls', 'xlsx'],
    labelColSpan: 6,
    showExtParams: { identityNoType: 'POD_ORDER_NO' },
    apiCustomizeImport(data) {
      return request('/zouwu-oms-system/oms/order/tob/outbound/fulfill/import', {
        data,
        method: 'POST',
      });
    },
  },
  [EnumImportType.returnOrderCreate]: {
    fileSuffix: ['xls', 'xlsx'],
    apiCustomizeImport(data) {
      return request('/zouwu-oms-system/oms/return/imports', {
        data,
        method: 'POST',
      });
    },
    modalChildren({ nodeMap, npExtParams }) {
      return (
        <>
          {nodeMap.nodeImportType}
          <Form.Item
            {...{
              label: '仓库组织',
              name: npExtParams('targetWarehouseCode'),
              rules: [{ required: true }],
              children: (
                <SearchSelect
                  {...{
                    request: (queryParam) =>
                      apiActualWarehouseOptions({ queryParam }, { valueName: 'code' }),
                  }}
                />
              ),
            }}
          />
          {nodeMap.nodeSimpleUpload}
        </>
      );
    },
  },
  [EnumImportType.returnOrderFile]: {
    fileSuffix: ['zip'],
    maxFileSizeMb: 500,
    allowDownloadTemp: false,
    showFileLimitTip: true,
    simpleUploadProps: {
      ...aliOssUtils.calcMultipartUploadProps({}, { expirationType: 'month' }),
    },
    limitDesc: [
      <span>文件夹和文件名都只能使用英文和数字，{renderTextRed('不支持汉字；')}</span>,
      <span>
        {renderTextRed('文件夹名称必须与退件跟踪单号完全一致才能匹配，')}
        附件支持jpeg，jpg和png格式，且不超过 20 M；
      </span>,
    ],
    apiCustomizeImport(data) {
      return request('/zouwu-oms-system/oms/return/imports', {
        data,
        method: 'POST',
      });
    },
    modalChildren({ nodeMap, npExtParams }) {
      return (
        <>
          {nodeMap.nodeImportType}
          <Form.Item
            {...{
              label: '仓库组织',
              name: npExtParams('targetWarehouseCode'),
              rules: [{ required: true }],
              children: (
                <SearchSelect
                  {...{
                    request: (queryParam) =>
                      apiActualWarehouseOptions({ queryParam }, { valueName: 'code' }),
                  }}
                />
              ),
            }}
          />
          {nodeMap.nodeSimpleUpload}
        </>
      );
    },
  },
} as Record<
  EnumImportType,
  {
    /** 文件后缀限制 */
    fileSuffix?: string[];
    /** 文件大小限制, 默认跟随SimpleUpload, 默认10MB */
    maxFileSizeMb?: number;
    /** labelCol: {span: 默认4 } */
    labelColSpan?: number;
    /** 上传限制说明 */
    limitDesc?: React.ReactNode[];
    /** 是否允许下载模板, 默认true */
    allowDownloadTemp?: boolean;
    /** 是否展示文件限制提示 */
    showFileLimitTip?: boolean;
    /** 是否展示额外参数, true时无默认值, 否则string作为默认值 */
    showExtParams?: Record<'identityNoType', true | string>;
    /** 后端为了实现按钮级权限控制, 每个导入接口都单独实现, 金家伟的方案 */
    apiCustomizeImport?: (...args: Parameters<typeof apiCommonImport>) => Promise<any>;
    /** 自定义弹窗表单内容渲染 */
    modalChildren?: TypeModalChildren;
    /** 上传组件参数 */
    simpleUploadProps?: TypeSimpleUploadProps;
  }
>;

function renderTextRed(txt: string) {
  return <span style={{ color: 'red' }}>{txt}</span>;
}
