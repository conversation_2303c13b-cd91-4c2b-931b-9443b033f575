/** Demo 功能验证 */
import React, { useRef } from 'react';
import { Button, Form, Space } from 'antd';
import { PageContainer } from '@/common-import';
import type { TypeSimpleUploadRef } from '@/components';
import { LoadButton, SimpleUpload } from '@/components';
import { aliOssUtils } from '@/utils-business';
import { FormProCard } from '@/views';

/** 断点续传demo, 功能验证 */
export default function AliOssDemo() {
  const simpleUploadRef = useRef<TypeSimpleUploadRef>();
  const [form] = Form.useForm();

  console.log('=======> form:', (window.form = form));

  return (
    <PageContainer>
      <FormProCard>
        <Form {...{ form, layout: 'vertical' }}>
          <Form.Item {...{ label: '文件列表A', name: 'fileList' }}>
            <SimpleUpload
              {...{
                ...aliOssUtils.calcMultipartUploadProps(
                  {
                    ref: simpleUploadRef,
                    allowValidFileStatus: true,
                    multiple: true,
                    maxCount: 2,
                    onOriginChange(info) {
                      console.log('=======> onOriginChange info:', info);
                    },
                  },
                  { expirationType: 'month' },
                ),
              }}
            >
              <LoadButton>上传文件</LoadButton>
            </SimpleUpload>
          </Form.Item>
          <Form.Item {...{ label: '文件列表B', name: 'fileList2' }}>
            <SimpleUpload
              {...{
                ...aliOssUtils.calcMultipartUploadProps({
                  ref: simpleUploadRef,
                  multiple: true,
                  maxCount: 2,
                  allowValidFileStatus: true,
                  onOriginChange(info) {
                    console.log('=======> onOriginChange info:', info);
                  },
                }),
              }}
            >
              <LoadButton>上传文件</LoadButton>
            </SimpleUpload>
          </Form.Item>
        </Form>
        <Space>
          <Button
            {...{
              onClick() {
                form.validateFields();
              },
            }}
          >
            校验
          </Button>
          <Button
            {...{
              onClick() {
                form.resetFields();
              },
            }}
          >
            重置
          </Button>
        </Space>
      </FormProCard>
    </PageContainer>
  );
}
