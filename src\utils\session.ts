import { getAliyunPath } from '@/hooks/useAliyunPath';

export type TypeSessionInfo = {
  appEnv: 'dev' | 'test' | 'pro' | null;
  userInfo: {
    companyName: string;
    companyId: string;
    userName: string;
  } | null;
};

export type ChangeHandler<T> = (value: T) => void;

/** 需要在umi getInitialState 中初始化需要用到的属性
 * @ getInitialState会保障时序优先于页面组件
 */
class Session {
  private info: TypeSessionInfo = {
    appEnv: null,
    userInfo: null,
  };

  private property = ['appEnv', 'userInfo'] as const;

  get = <T extends Session['property'][number]>(key: T) => {
    return this.info[key]!; // 处理为不存在null值, null在初始化的时候保证
  };

  set = <T extends Session['property'][number]>(key: T, value: TypeSessionInfo[T]) => {
    this.info[key] = value;
  };

  getAppEnv = () => {
    return this.info.appEnv;
  };

  getAliyunPath() {
    return getAliyunPath(this.info.appEnv!);
  }
}

export const session = new Session();
