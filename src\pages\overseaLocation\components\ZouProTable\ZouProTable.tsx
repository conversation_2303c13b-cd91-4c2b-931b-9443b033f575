import type { ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ParamsType } from '@ant-design/pro-provider';
import RcResizeObserver from 'rc-resize-observer';
import { compUtils, tableSchemaAide } from '@/utils';
import { getSpanConfig } from './pro-table-utils';
import React, { useEffect, useMemo, useState } from 'react';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import type { TypeProColumns } from './types';

/** props属性传 undefined 会取default配置
 * @例如 search: undefined
 */
export type TypeZouProTableProps<T = any, U = any> = {
  /** 区分列表类型 默认list
   * @simple 默认会关闭一些属性, 如 search: false, cardProps: false
   * @ 外部传入属性依然优先级最高,额外会标记样式,处理不同场景
   */
  tableType?: 'simple' | 'list';
  search?:
    | ({
        /** 折叠时展示行数, 默认2*/
        defaultRowsNumber?: number;
      } & SearchConfig)
    | false;
  columns?: TypeProColumns<T>[];
} & Omit<ProTableProps<T, U>, 'columns' | 'search'>;

const defaultColumn = {
  search: false as const,
};

/**
 * ProTable 组件升级版简化样板代码;
 * @ 修改了一些column默认值;
 */
function ZouProTable<T, U extends ParamsType>(props: TypeZouProTableProps<T, U>) {
  const { searchConfig, updateWidth, fillItem } = useSearchDefaultConfig(props);
  const { tableTypeCalcProps } = useTableTypeCalcProps(props);
  const propsMerge = compUtils.propsMerge;

  const defaultProps = propsMerge(
    {
      tableLayout: 'auto',
      pagination: {
        showSizeChanger: true,
        position: ['bottomLeft'],
      },
      search: {
        ...searchConfig,
      },
    } as ProTableProps<any, any>,
    tableTypeCalcProps,
  );

  const newProps = propsMerge(defaultProps, {
    ...props,
    search: propsMerge(defaultProps.search as any, props.search),
    pagination: propsMerge(defaultProps.pagination as any, props.pagination),
    columns: tableSchemaAide
      .tableColumnsIntercept(props.columns || [], defaultColumn)
      .concat(fillItem),
  });

  return (
    <RcResizeObserver
      key="resize-observer"
      onResize={(size) => {
        updateWidth(size.width);
      }}
    >
      <ProTable {...newProps} />
    </RcResizeObserver>
  );
}

export default ZouProTable;
ZouProTable.defaultColumn = defaultColumn;

/** 根据tableType默认一些属性 */
function useTableTypeCalcProps(props: TypeZouProTableProps) {
  const { tableType = 'list' } = props;
  const propsMap: Record<typeof tableType, typeof props> = {
    simple: {
      search: false,
      cardProps: false,
      toolBarRender: false,
      pagination: false,
      className: 'zou-pro-table__type-simple',
    },
    list: {
      className: 'zou-pro-table__type-list',
    },
  };

  const tableTypeCalcProps = propsMap[tableType];

  return { tableTypeCalcProps };
}

/** 获取默认search 配置 */
function useSearchDefaultConfig(props: TypeZouProTableProps) {
  const [width, setWidth] = useState<number>(0);

  const {
    layout,
    span,
    /** 定义默认展示行数 */
    defaultRowsNumber = 2,
  } = props.search || {};
  const totalSize = (props.columns || [])
    .filter((item) => item && item.search)
    /** 需要考虑colSize占了多格的情况 */
    .reduce((sum, item) => sum + ((item as ProColumns).colSize || 1), 0);
  /** 计算span大小 */
  const { spanConfig, defaultColsNumber } = useMemo(() => {
    /** search form 在protable中的padding宽度 24 * 2*/
    const searchFormPadding = 48;
    const res = getSpanConfig(layout, width + 16 - searchFormPadding, span);

    return {
      spanConfig: res,
      /** proTable从2.63.7 升到2.79.0, 内部的defaultColsNumber逻辑判断变化了, 需要+1判断
       * 有一个很扯淡的ProTable bug, 它在计算是否展示折叠时, 少算了查询的占位, 因此当defaultColsNumber和总搜索栏数量刚好相等时, 查询展开按钮会消失
       * 解决办法是补一个隐形数据fillItem
       */
      defaultColsNumber: (24 / res.span) * defaultRowsNumber + 1,
    };
  }, [width, layout, span]);
  /** 补位用的Item */
  const fillItem =
    defaultColsNumber - 1 === totalSize
      ? [
          {
            formItemProps: { hidden: true },
            /** order必须存在,为了保证这个占位是垫底的, 其它formItem使用order的定义不要小于这个数字 */
            order: -999999,
            hideInTable: true,
            search: true,
          } as any,
        ]
      : [];

  const updateWidth = (newWidth: number) => {
    newWidth !== width && newWidth > 17 && setWidth(newWidth);
  };
  const searchConfig = {
    /** 默认不折叠 */
    defaultCollapsed: false,
    defaultColsNumber: undefined,
  } as ProTableProps<any, any>['search'];

  useEffect(() => {
    if (props.search && props.search.defaultColsNumber) {
      console.warn(
        // kiwi-disable-next-line
        '存在从外部传入的search.defaultColsNumber, 特定情况下会吞掉搜索条件,参考122行注释',
      );
    }
  }, []);

  return {
    searchConfig,
    updateWidth,
    defaultColsNumber,
    fillItem,
  };
}
