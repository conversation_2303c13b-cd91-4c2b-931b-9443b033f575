/** 中台接口文件 */

import { request } from '@umijs/max';
import { queryOverseaContainerBox } from '@/service/global';
import { promiseCacheApi } from '../utils';

/** 中台费用项接口, 后端搜索不生效的, 历史原因 */
export function apiChargeItemOptions(data?: {
  /** 限制数量 */
  limit?: number;
  /** 1-启动，2-禁用，-1-未设置 */
  isEnable?: number;
  /** 中台费用项code */
  gmChargeCode?: string;
  /** 中台费用项中文名 */
  gmChargeName?: string;
  /** 中台费用项英文名 */
  gmChargeEnName?: string;
  /** oms费用code */
  chargeCode?: string;
  /** oms费用名称 */
  chargeName?: string;
}) {
  return request<NsApi.TypeResponseData<{ chargeItems: TypezChargeItem[] }>>(
    // '/api/website/web/chargeFacade/getChargeItemList.do',
    '/zouwu-oms-charge/portal/charge/item/list',
    {
      method: 'POST',
      data: {
        limit: 1000,
        ...data,
      },
    },
  ).then((res) =>
    (res?.data?.chargeItems || []).map((record) => {
      return {
        ...record,
        label: record.gmChargeName,
        value: record.gmChargeCode,
      };
    }),
  );
}

/**
 * 中台提供, 箱子型号
 * @ 由于历史代码已存在, 调取之前预设置接口
 * @ 已做 promiseCacheApi 缓存
 *  */
export const apiQueryOverseaContainerBox = promiseCacheApi(async (data?: any) => {
  return queryOverseaContainerBox({ limit: 999, ...data }).then((res) => {
    return (res?.result || []).map((item) => {
      return {
        ...item,
        label: item.enName,
        value: item.code,
      };
    });
  });
});

/**
 * 中台数据,国家省市列表数据（下拉框）
 * @ 原接口名queryGlobalCountryCityList
 * @ 文档链接: http://wiki.900jit.com/pages/viewpage.action?pageId=129045652
 */
export const apiCountryCityOptions = promiseCacheApi(
  async function apiCountryCityOptions(
    data: {
      /** 省市必填, 父级code编码(国家不需要传) */
      parentCode?: string;
      /** 检索条件（中英文名、code） */
      searchValue?: string;
      /** 是否包含code查询结果, 默认true */
      containCode?: boolean;
    } = {},
  ) {
    return request<
      NsApi.TypeResponseData<
        {
          displayName: string;
          code: string;
          /** 返回的标准二子码 */
          standardCode: string;
        }[]
      >
    >('/api/website/web/rich/queryLinkage.do', {
      method: 'POST',
      data: {
        containCode: true,
        ...data,
      },
    }).then(({ result }) => {
      return (result || []).map((item) => ({
        ...item,
        label: item.displayName,
        value: item.code,
      }));
    });
  },
  { expired: 30 * 1000 },
);
