/** 资金流水 */
type TypezCashFlowTB = {
  /** 主键id */
  id?: number;
  /** 账户id */
  accountId?: number;
  /** 客户id */
  customerId?: number;
  /** 公司id */
  companyId?: string;
  /** 公司名 */
  companyName?: string;
  /** 资金流水ID */
  cashFlowNo?: string;
  /** 费用单号 */
  chargeRecordBillNo?: string;
  /** 业务关联单据 */
  podOrderNo?: string;
  /** 实际金额 */
  chargeAmount?: number;
  /** 流水类型1: 充值2: 冻结3: 解冻4: 扣款5: 退款 */
  flowType?: number;
  /** 中台费用项code */
  gmChargeCode?: string;
  /** 中台费用名 */
  gmChargeName?: string;
  /** 系统费用项code */
  chargeCode?: string;
  /** 系统费用名 */
  chargeName?: string;
  /** 业务场景
    1: 入库订单
    2: 出库订单
    3: 仓租
    4: 充值
 */
  businessType?: number;
  /** 账户余额 */
  accountBalance?: number;
  /** 可用余额 */
  availableBalance?: number;
  /** 冻结金额 */
  frozenAmount?: number;
  /** 信用额度 */
  creditLimit?: number;
  /** 客户币种 */
  customerCurrency?: number;
  /** 发生时间 */
  createDate?: number;
};

/** 金额统计 */
type TypeCashFlowCount = {
  /** 充值总金额 */
  rechargeAmount?: number;
  /** 扣款总金额 */
  deductAmount?: number;
  /** 退款总金额 */
  refundAmount?: number;
  /** 冻结总金额 */
  freezeAmount?: number;
};
