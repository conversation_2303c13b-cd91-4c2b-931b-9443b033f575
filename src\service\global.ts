import { request } from '@@/plugin-request';

/**
 * 获取国家列表（下拉框）
 * @param data
 * @deprecated 废弃, 改用 apiCountryCityOptions
 */
export const queryGlobalCountryList = async (data: { name: string }) => {
  return request('/api/website/country/queryPage.do', {
    method: 'POST',
    data,
  });
};

/**
 * 国家省市列表数据（下拉框）
 * @param data
 * @deprecated 出库单创建时使用, 已废弃
 */
export const queryGlobalCountryCityList = async (data: { name: string }) => {
  return request('/api/website/web/rich/queryLinkage.do', {
    method: 'POST',
    data,
  });
};

/**
 * 起始港/目的港
 * @param data
 */
export const queryGlobalPortList = async (data: { type: number; portName: string }) => {
  return request('/api/website/web/freight/queryPortList.do', {
    params: data,
  });
};

/**
 * 船公司
 * @param data
 */
export const queryGlobalShippingList = async (data: { searchName: string }) => {
  return request('/api/website/web/shipper/queryShippingListByPage', {
    params: data,
  });
};

/**
 * 字典项数据
 * @param data
 */
// export const queryGlobalDictionaryList = async (data: {type: number|string}) => {
//   return request('/api/website/system/dict/getDictionaryListByType', {
//     params: data
//   });
// };

/**
 * 上传
 * @param data
 * @deprecated 门户原上传接口, 计划报废
 */
export const sendGlobalUploadFile = async (data: any) => {
  const formData = new FormData();

  Object.keys(data).forEach((key) => {
    formData.append(key, data[key]);
  });

  return request<
    NsApi.TypeResponseData<{
      /** 非完整路径 */
      fileName: string;
      /** 文件名称 */
      nameUrl: string;
      /** 完整路径 */
      fileUrl: string;
    }>
  >('/api/website/upload/uploadOne', {
    data: formData,
    method: 'POST',
    headers: {
      'Content-type': 'multipart/form-data',
    },
  });
};

/**
 * 通过Type查询字典数据列表
 * @param data
 */
export const queryDictionaryListByType = async (data: any) => {
  return request('/api/website/system/dict/listByType.do', {
    params: data,
  });
};

/**
 * 创建蚂蚁账号
 * @param data
 */
export const sendRegisterAntAccount = async () => {
  return request('/api/website/antfin/register/register');
};

/**
 * 获取json文件地址
 * @param data
 */
export const queryWebIndex = async (data: any) => {
  return request('/api/website/web/webIndex/getIndexFilePath.do', {
    method: 'POST',
    data,
  });
};

/**
 * 初始化用户信息到抢舱项目中
 * @param data
 */
export const sendInitJumpSeckillInfo = async () => {
  return request('/api/website/web/seckill/initJumpPage', {
    method: 'POST',
  });
};

/**
 * 获取提醒列表
 * @param data
 */
export const queryNotReadNotice = async () => {
  return request('/api/website/web/seckillNotice/queryNotReadNotice', {
    method: 'POST',
  });
};

/**
 * 获取提醒列表
 * @param data
 */
export const sendReadNotice = async (data: any) => {
  return request('/api/website/web/seckillNotice/readNotice', {
    method: 'POST',
    data,
  });
};

/**
 * 获取电商平台基础数据
 * @param code
 * @deprecated 已废弃, 改用字典orderSourcePlatform
 */
export const queryCommercePaltformRichSelectData = async (data: any) => {
  return request('/api/website/web/rich/richSelect.do', {
    method: 'POST',
    data,
  });
};

/**
 * 环世海外仓箱型下拉
 * @param code
 */
export const queryOverseaContainerBox = async (data: any) => {
  return request<NsApi.TypeResponseData<TypeModalItem[]>>('/api/website/web/zouwu/boxSearch.do', {
    method: 'POST',
    data,
  });
};

export const queryStreamData = async (url: string, option: RequestInit) => {
  return request(url, {
    ...option,
    getResponse: true,
    responseType: 'blob',
  });
};
