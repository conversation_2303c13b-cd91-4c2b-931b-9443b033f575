import React, { useEffect, useRef, useState } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import moment from 'moment';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { QueryFilter } from '@ant-design/pro-form';
import { Button, Card, Tooltip, Form, Input, Select } from 'antd';
import { cloneDeep, forIn } from 'lodash';
import { HiveModule } from '@portal/hive-sdk';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { QUALITYTYPE } from '@/utils/const';
import { HumpToUnderline, GetPageQuery } from '@/utils/util';
import styles from './index.less';
import { ProTable } from '@/common-import';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import {
  apiMapDictType,
  apiQueryWarehouseOptions,
  useDictTypeValueEnum,
} from '@/pages/overseaLocation/api';
import type { TypeInventoryFlowQuery } from '../../inventoryApi';
import { apiInventoryFlowExport, apiInventoryFlowList } from '../../inventoryApi';
import { apiVirtualWarehouseOptions } from '@/pages/pagesApi';
import { emptyRenderArray } from '@/utils';
import { AsyncExportButton } from '@/views';

const { Item } = ProForm;

/** 库存交易流水列表 */
function OverseaInventoryFlowListPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<any>(1);
  const tableRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [searchValues, setSearchValues] = useState<any>({});
  const { sku, warehouseId, warehouseCode, warehouseName } = GetPageQuery();
  const { dictTypeMap } = useDictTypeValueEnum(['orderTag']);

  useEffect(() => {
    if (sku) {
      init();
    }
  }, [sku]);

  const init = () => {
    if (sku) {
      formRef.current?.setFieldsValue({ sku, warehouseIds: [warehouseId] });
      setSearchValues({ sku, warehouseIds: [warehouseId] });
    }
  };

  const columns: ProColumns<TypeTransactionFlowListTD>[] = [
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.reservoirArea,
      key: 'warehouseAreaName',
      dataIndex: 'warehouseAreaName',
      // width: 150,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.transactionScenario,
      key: 'businessType',
      dataIndex: 'businessType',
      valueType: 'select',
      request: apiMapDictType.allBusinessType,
      // width: 150,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.warehouseOrganization,
      key: 'warehouseCode',
      dataIndex: 'warehouseCode',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.operationType,
      key: 'operationType',
      dataIndex: 'operationType',
      valueType: 'select',
      request: apiMapDictType.inventoryOperationType,
      // width: 120,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.inventoryFlow,
      key: 'serialNo',
      dataIndex: 'serialNo',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.transactionNote,
      key: 'docNo',
      dataIndex: 'docNo',
      // width: 220,
      search: false,
      // render: (_: any) => {
      //   return <span style={{ color: '#1890FF' }}>{_}</span>;
      // },
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      key: 'goodsName',
      dataIndex: 'goodsName',
      // ellipsis: true,
      width: 200,
      render: (_: any, record: any) => {
        return (
          <div>
            <div>{record.goodsName}</div>
            <div>{record.goodsEnName}</div>
            {/* <Tooltip title={record.goodsName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsName}</span>
            </Tooltip>
            <Tooltip title={record.goodsEnName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsEnName}</span>
            </Tooltip> */}
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      key: 'sku',
      dataIndex: 'sku',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.commodityQualityAssurance,
      key: 'shelfLife',
      dataIndex: 'shelfLife',
      search: false,
      width: 150,
      valueType: 'date',
      // render: (_: any, record: any) =>  moment(record.shelfLife).format('YYYY-MM-DD'),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.receiptBatch,
      key: 'batchNo',
      dataIndex: 'batchNo',
      width: 150,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.transactionQuantity,
      key: 'quantityWithSymbol',
      dataIndex: 'quantityWithSymbol',
      width: 120,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.qualityStatus,
      key: 'qualityType',
      dataIndex: 'qualityType',
      width: 120,
      search: false,
      render: (_: any, record: any) => {
        return <span>{QUALITYTYPE[record.qualityType]}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.transactionTime,
      key: 'happenedTime',
      dataIndex: 'happenedTime',
      search: false,
      // width: 150,
      render: (_: any, record: any) => {
        return <span>{moment(_).format('YYYY-MM-DD HH:mm')}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.associatedUpstream,
      key: 'associatedDocNo',
      dataIndex: 'associatedDocNo',
      // width: 250,
      render: (_: any, record: any) => {
        return (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.applicationForm}
              {record.applyDocNo}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.performanceOrder}
              {record.performanceDocNo}
            </div>
            {/** 后端取值错误,前端暂时隐藏 */}
            {/* <div>WMS: {record.wmsNo}</div> */}
          </>
        );
      },
    },
    {
      title: '订单标签',
      dataIndex: 'orderTagList',
      search: false,
      render(dom, record) {
        return emptyRenderArray(record.orderTagList, (item) => {
          return <div key={item}>{item}</div>;
        });
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
      dataIndex: 'virtualWarehouseCode',
      search: false,
    },
  ];

  const handleSearchList = async (params: any) => {
    const { pageSize, current: currentPage } = params;

    const values = cloneDeep(searchValues);

    if (sku) {
      values.sku = sku;
      values.warehouseIds = [warehouseId];
      HiveModule.history.replace('/oversea-location/inventory/trade/flow');
    }

    const { data } = await apiInventoryFlowList({
      currentPage,
      pageSize,
      condition: {
        ...values,
      },
    });
    const result = data || {};

    const sensorsData: any = {};

    forIn(params, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    // 海外仓库存交易流水列表查询埋点
    sensorsTrack('WWL_PORTAL_OVERSEA_INVENTORY_FLOW_LIST_SEARCH', sensorsData);

    return {
      data: result.records,
      total: result.totalSize ? Number(result.totalSize) : 0,
    };
  };

  const handleTableSearch = async (values: any) => {
    setSearchValues(values);
    setPageIndex(1);
    tableRef.current?.reload();
  };

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      ...formData,
    } as TypeInventoryFlowQuery;
  }

  return (
    <PageContainer>
      <Card className={styles['inventory-flow-search-form']}>
        <QueryFilter
          defaultCollapsed={false}
          onFinish={handleTableSearch}
          onReset={() => handleTableSearch({})}
          formRef={formRef}
          submitter={{
            submitButtonProps: {
              loading,
            },
          }}
        >
          <Item
            {...{
              name: 'warehouseIds',
              label:
                I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index
                  .warehouseOrganization,
            }}
          >
            <ZSearchSelect
              {...{
                request: async (queryParam: string) =>
                  apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
                mode: 'multiple',
              }}
            />
          </Item>
          <Item
            name="goodsName"
            label={I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName}
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="sku"
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1
            }
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="businessTypes"
            label={
              I18N.Src__Pages__OverseaLocation__Inventory__Flow__List.Index.transactionScenario
            }
          >
            <ZSearchSelect
              showSearch
              mode="multiple"
              allowClear
              placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
              request={apiMapDictType.allBusinessType}
              // options={WAREHOUSE_BUSINESS_TYPE_OPTION}
            />
          </Item>
          <Item
            name="docNo"
            label={I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.transactionNote}
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="associatedDocNo"
            label={
              I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.associatedUpstream
            }
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            {...{
              name: 'virtualWarehouseList',
              label: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
            }}
          >
            <ZSearchSelect
              {...{
                mode: 'multiple',
                request: async (queryParam: string) =>
                  apiVirtualWarehouseOptions({ queryParam }, { valueName: 'id' }),
              }}
            />
          </Item>
        </QueryFilter>
      </Card>
      <ProTable
        className={styles['inventory-flow-list']}
        request={async (params) => {
          setLoading(true);
          const res = handleSearchList(params);

          res?.finally?.(() => setLoading(false));
          return res;
        }}
        toolBarRender={() => [
          <AsyncExportButton
            key="export"
            {...{
              buttonProps: {
                type: 'primary',
                async onClick(e, params) {
                  params.form.setFieldsValue({
                    // kiwi-disable-next-line
                    fileName: `库存交易流水${moment().format('YYYYMMDD')}`,
                  });
                },
              },
              async request({ form }) {
                const searchData = getSearchData();
                const { fileName } = form.getFieldsValue();

                await apiInventoryFlowExport({
                  ...searchData,
                  fileName,
                });
              },
            }}
          />,
        ]}
        columns={columns}
        rowKey="id"
        actionRef={tableRef}
        search={false}
        scroll={{ x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          current: pageIndex,
          defaultPageSize: 10,
          position: ['bottomLeft'],
        }}
        onChange={(pagination) => {
          setPageIndex(pagination.current);
        }}
      />
    </PageContainer>
  );
}

export default OverseaInventoryFlowListPage;
