import { useEffect } from 'react';

/** table sticky hook, 用于解决header头宽度无法自适应的问题 */
function useTableSticky() {
  const cssTableSticky = 'zw-table-sticky';
  const cssTableStickyHasTab = 'zw-table-sticky zw-table-sticky__has-tab';

  useEffect(() => {
    const tableDom = document.querySelector(`.${cssTableSticky}`);
    const getNode = () => {
      return tableDom?.querySelector('.ant-table-sticky-holder thead');
    };
    /** header dom */
    let node = getNode();
    /** 深度克隆一份 */
    let cloneNode: undefined | Node | null;
    /** 刷新或者插入节点, 通过top 负值隐藏于sticky header之下 */
    const refreshNode = () => {
      node = getNode();
      const offsetHeight = (node as HTMLElement)?.offsetHeight;

      if (cloneNode) {
        (cloneNode as HTMLElement)?.remove();
      }
      cloneNode = node?.cloneNode(true);
      if (cloneNode) {
        const tableBodyDom = tableDom?.querySelector('.ant-table-body') as HTMLElement | null;

        tableDom
          ?.querySelector('.ant-table-body table')
          ?.insertBefore(cloneNode, tableDom?.querySelector('.ant-table-tbody'));

        tableBodyDom?.style.setProperty('top', `-${offsetHeight}px`);
        tableBodyDom?.style.setProperty('position', 'relative');
      }
    };

    if (!node || !tableDom) {
      return;
    }
    const observer = new MutationObserver((mutationsList, observer) => {
      // console.log('====>触发监听, 刷新table thead');
      refreshNode();
    });

    node && observer.observe(node, { childList: true, attributes: true, subtree: true });
    refreshNode();

    return () => {
      (cloneNode as HTMLElement)?.remove();
      observer?.disconnect();
    };
  }, []);

  return {
    /** table需要绑定这个样式名称作为标记
     * 配合table的sticky 开启头固定功能
     */
    cssTableSticky,
    /* 需要固定tab时, 使用这个 */
    cssTableStickyHasTab,
  };
}

export const tableUtils = {
  useTableSticky,
};
