import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';

/** 异步Modal.confirm弹窗确认 */
export function asyncModalConfirm(
  props: Omit<ModalFuncProps, 'onOk'> & { onOk?: () => Promise<any> },
) {
  let isResole = false;

  return new Promise<void>(function (resolve, reject) {
    Modal.confirm({
      ...props,
      async onOk() {
        if (isResole) {
          /** onOk 的 loading 取消在 弹窗关闭之前, 会导致最后时刻有机会触发连点 */
          console.info('=======> 成功防止连点请求:');
          return;
        }
        await props?.onOk?.();
        resolve();
        isResole = true;
      },
      onCancel() {
        reject();
      },
    });
  });
}
