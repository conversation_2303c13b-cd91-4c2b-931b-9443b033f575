export default {
  DeleteGoodsModal: {
    items: '件商品',
    etc: '等',
    theCommodityIsClosed:
      '该商品关联了以下组合商品，删除该商品将同时删除所有关联的组合商品！',
    thisProductHas: '该商品已下单，暂无法删除！',
    okToDelete: '确定删除',
    deletePrompt: '删除提示',
  },
  GoodsBelongSkuTableForm: {
    increase: '增加SKU',
    mostRemarks: '备注最多255个字符',
    commodity: '商品',
    englishName: '英文名称',
    nameInEnglish: 'SKU/名称/英文名称',
  },
  GoodsBoxTableForm: {
    addSinglePiece: '增加单件箱规',
    addBoxGauge: '增加箱规',
    pictureOfOuterBox: '外箱图片',
    quantityOfSingleBox: '单箱数量',
  },
  GoodsImageUpload: {
    uploadFiles: '上传文件大小不能超过20M',
    onlyLatticesAreSupported: '只支持{val1}格式',
    unknownOn: '未知的上传类型',
    uploadPhotos: '上传照片',
  },
  GoodsPlatformSkuTableForm: {
    addPlatform: '增加平台SKU',
    mostSold: '销售URL最多255个字符',
    sale: '销售URL',
    upToCharacters: 'SKU最多50个字符',
    platform: '平台',
  },
  GoodsServiceSetTableForm: {
    increaseInsurance: '增加保险设置',
    declaredValue: '申报价值',
    insurableValue: '保险价值',
    currency: '币种',
    chineseSymbols: '投保金额',
    chineseSymbols1: '货品价值',
  },
  SKUEditModal: {
    reasonForModification: '修改原因',
    new: '新SKU',
    primary: '原SKU',
    canOnlyApply: 'SKU 只能申请修改一次',
    applyForModification: '申请修改SKU',
  },
} as const;
