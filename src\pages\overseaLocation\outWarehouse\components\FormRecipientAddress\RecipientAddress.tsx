// kiwi-disable
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Col, Form, Input, Row, Space } from 'antd';
import React, { useImperativeHandle, useRef } from 'react';
import type { TypeCityAutoCompleteRef } from '../CityAutoComplete';
import styles from './RecipientAddress.less';
import type { TypeSearchSelectProps, TypeSearchSelectRef } from './RecipientAddressImport';
import {
  ZSearchSelect,
  trimUtils,
  REGEX,
  apiCountryCityOptions,
  SearchAutoComplete,
} from './RecipientAddressImport';
import { TextValue } from '@/views';

const { Item } = Form;

type TypeProps = {
  /** 表单实例 */
  form: FormInstance;
  /** col 布局，基于原有布局，实现兼容弹窗布局 */
  colSpan?: number;
  /** 大表单模式, 从form获取额外字段信息, 否则额外详情从props.detailData入参获取 */
  mode?: 'createOrEdit';
  detailData?: TypeOutboundFormData;
};

type TypeValidateCode = '000001' | '000002' | '000003' | '000004' | '000005' | '999999';

/** 德国二字码 */
const germanyCode = 'DE';
/** 判断德国仓库 */
const germanyWmsArr = ['YI_CANG_GERMANY'];
/** 仓库系统对照校验规则 */
const wmsSysCodeMap = {
  /** SAT */
  SAT: '000001',
  /** 魔方云->天马 */
  MFY: '000001',
  /** 魔方云miss */
  MFY_MISS: '000001',
  /** 易仓 */
  YI_CANG: '000002',
  /** 澳洲仓 本期不做, 默认走000001 */
  // ECOF: '000001',
  /** 德国仓 */
  YI_CANG_GERMANY: '000003',
  /** 易仓英国 */
  YI_CANG_BRITAIN: '000004',
  /** 科劳斯(德国)-汉堡仓 */
  CROSS: '000005',
  /** 特殊值, 用于费用试算校验  */
  COST_CALC: '999999',
} as Record<string, TypeValidateCode>;

/** 收件人地址相关信息字段 */
enum EnumOutWarehouseExpress {
  /** 收件联系人 */
  recipientName = 'recipientName',
  /** 收件联系人电话 */
  recipientPhoneNumber = 'recipientPhoneNumber',
  /** 收件联系人邮箱 */
  recipientEmail = 'recipientEmail',
  recipientCountry = 'recipientCountry',
  recipientProvince = 'recipientProvince',
  recipientCity = 'recipientCity',
  /** 邮编 */
  recipientPostcode = 'recipientPostcode',
  /** 分邮编 */
  recipientBranchPostcode = 'recipientBranchPostcode',
  'recipientAddress.0' = 'recipientAddress.0',
  'recipientAddress.1' = 'recipientAddress.1',
  'recipientAddress.2' = 'recipientAddress.2',
  /** 门牌号 */
  recipientHouseNumber = 'recipientHouseNumber',
}

export default React.forwardRef((props: TypeProps, ref) => {
  const { form, colSpan = 1 } = props;
  const recipientCountry = Form.useWatch(['express', 'recipientCountry'], form);
  /** 国家为美国时, 门牌号禁止添加 */
  const recipientHouseNumberDisabled = recipientCountry === 'US';
  const {
    addressPropsMap,
    addressSelectRefMap,
    addressInit,
    isNeedSearchAutoComplete,
    addressAutoRef,
    isGermanyAndWms,
    wmsSystemCode,
  } = useExpress({
    form,
    props,
  });

  useImperativeHandle(ref, () => ({
    addressInit,
  }));

  return (
    <Row gutter={20}>
      <Item hidden>
        <TextValue {...{ label: '仓库系统CODE', name: 'wmsSystemCode' }} />
      </Item>
      <Col span={outSpan(colSpan, 12)}>
        <Item label="收件国家/省/市" required>
          <Input.Group compact>
            <Item
              name={['express', 'recipientCountry']}
              noStyle
              rules={[
                // {
                //   required: true,
                //   message: '请选择收件国家',
                // },
                ({ getFieldsValue }) => {
                  return {
                    validator(_, value) {
                      return asyncValidate({
                        wmsSystemCode,
                        fieldKey: EnumOutWarehouseExpress.recipientCountry,
                        fieldData: value,
                        props,
                      });
                    },
                  };
                },
              ]}
            >
              <ZSearchSelect {...addressPropsMap.recipientCountry} />
            </Item>
            <Item
              noStyle
              shouldUpdate={(prev, cur) => {
                return prev.express.recipientCountry !== cur.express.recipientCountry;
              }}
            >
              {() => {
                return (
                  <Item
                    name={['express', 'recipientProvince']}
                    noStyle
                    rules={[
                      // {
                      //   required: true,
                      //   message: '请选择收件省份',
                      // },
                      ({ getFieldsValue }) => {
                        return {
                          validator(_, value) {
                            return asyncValidate({
                              wmsSystemCode,
                              fieldKey: EnumOutWarehouseExpress.recipientProvince,
                              fieldData: value,
                              props,
                            });
                          },
                        };
                      },
                    ]}
                  >
                    {isNeedSearchAutoComplete('recipientProvince') ? (
                      <SearchAutoComplete {...addressPropsMap.recipientProvince} />
                    ) : (
                      <ZSearchSelect {...addressPropsMap.recipientProvince} />
                    )}
                  </Item>
                );
              }}
            </Item>
            <Item
              name={['express', 'recipientCity']}
              noStyle
              rules={[
                ({ getFieldsValue }) => {
                  return {
                    validator(_, value) {
                      return asyncValidate({
                        wmsSystemCode,
                        fieldKey: EnumOutWarehouseExpress.recipientCity,
                        fieldData: value,
                        props,
                      });
                    },
                  };
                },
              ]}
            >
              <SearchAutoComplete {...addressPropsMap.recipientCity} />
            </Item>
          </Input.Group>
        </Item>
      </Col>
      <Col span={outSpan(colSpan, 6)}>
        <Item
          name={['express', 'recipientName']}
          label="收件联系人"
          required
          rules={[
            ({ getFieldsValue }) => {
              return {
                async validator(_, value) {
                  if (/&/.test(value)) {
                    throw new Error('${label}不能包含特殊字符 &');
                  }

                  return asyncValidate({
                    wmsSystemCode,
                    fieldKey: EnumOutWarehouseExpress.recipientName,
                    fieldData: value,
                    props,
                  });
                },
              };
            },
          ]}
        >
          <Input placeholder="请输入收件联系人姓名" />
        </Item>
      </Col>
      <Form.List {...{ name: ['express', 'recipientPhoneNumberList'], initialValue: [''] }}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, fieldIndex) => (
              <Col span={outSpan(colSpan, 6)} key={field.key}>
                <Item label="收件联系人电话" required={fieldIndex === 0} key={field.key}>
                  <div style={{ display: 'flex' }}>
                    <Item
                      {...field}
                      noStyle
                      label="收件联系人电话"
                      validateTrigger={['onChange', 'onBlur']}
                      rules={[
                        ({ getFieldsValue }) => {
                          return {
                            validator(_, value) {
                              return asyncValidate({
                                wmsSystemCode,
                                fieldKey: EnumOutWarehouseExpress.recipientPhoneNumber,
                                fieldData: value,
                                props,
                                fieldIndex,
                              });
                            },
                          };
                        },
                      ]}
                    >
                      <Input placeholder="请输入收件联系人电话" />
                    </Item>
                    <Space style={{ marginLeft: 10 }}>
                      {fields.length > 1 ? (
                        <MinusCircleOutlined onClick={() => remove(field.name)} />
                      ) : null}
                      {fields.length < 2 && fieldIndex === 0 ? (
                        <PlusOutlined onClick={() => add('')} />
                      ) : null}
                    </Space>
                  </div>
                </Item>
              </Col>
            ))}
          </>
        )}
      </Form.List>
      <Form.List {...{ name: ['express', 'recipientAddressList'], initialValue: ['', ''] }}>
        {(fields, { add, remove }) => (
          <>
            {(fields?.length > 2
              ? fields
              : [
                  /** 保证至少有2个地址输入框 */
                  { isListField: true, name: 0, key: 0 },
                  { isListField: true, name: 1, key: 1 },
                ]
            ).map((field, index) => {
              /** 是否展示详细地址的必填标识 */
              const showRequired = index === 0;
              // const showRequired = index === 0 || index === 2;
              /** 德国境内禁填 德国的二字码是 DE */
              const { express } = form.getFieldsValue();
              /** 收件国家是否是德国 */
              const isGermany = isGermanyAndWms(express?.recipientCountry);
              /** 德国仓地址1必填, 地址2, 3禁止填写 */
              const disabled = isGermany && index !== 0;
              const labelMap = {
                0: '详细地址一',
                1: '详细地址二',
                2: '详细地址三',
              };

              return (
                <Col span={outSpan(colSpan, 6)} key={field.key}>
                  <Item
                    label={labelMap[index as keyof typeof labelMap]}
                    required={showRequired}
                    key={field.key}
                  >
                    <div style={{ display: 'flex' }}>
                      <Item
                        {...field}
                        label="收件人详细地址"
                        noStyle
                        validateTrigger={['onChange', 'onBlur']}
                        rules={[
                          {
                            // required: true,
                            /** 产品后期规划空格问题, 先算入字符 */
                            // whitespace: true,
                          },
                          // { max: 35, min: 2 },
                          ({ getFieldsValue }) => {
                            return {
                              validator(_, value) {
                                return asyncValidate({
                                  wmsSystemCode,

                                  fieldKey: `recipientAddress.${index}` as EnumOutWarehouseExpress,
                                  fieldData: value,
                                  props,
                                });
                              },
                            };
                          },
                        ]}
                      >
                        <Input placeholder="请输入收件人详细地址" disabled={disabled} />
                      </Item>
                      <Space style={{ marginLeft: 10 }}>
                        {fields.length > 2 ? (
                          <MinusCircleOutlined onClick={() => remove(field.name)} />
                        ) : null}
                        {fields.length < 3 && index === 1 ? (
                          <PlusOutlined
                            onClick={() => {
                              const recipientAddressList =
                                form.getFieldValue(['express', 'recipientAddressList']) || [];

                              if (recipientAddressList.length === 1) {
                                /** 如果长度为1, 说明传入数据不足, 需要多进行一次add */
                                add('');
                              }
                              add('');
                            }}
                          />
                        ) : null}
                      </Space>
                    </div>
                  </Item>
                </Col>
              );
            })}
          </>
        )}
      </Form.List>
      <Col span={outSpan(colSpan, 6)}>
        <Item
          name={['express', 'recipientHouseNumber']}
          label="门牌号"
          rules={[
            ({ getFieldsValue }) => {
              return {
                validator(_, value) {
                  return asyncValidate({
                    wmsSystemCode,

                    fieldKey: EnumOutWarehouseExpress.recipientHouseNumber,
                    fieldData: value,
                    props,
                  });
                },
              };
            },
          ]}
        >
          <Input
            {...{
              placeholder: recipientHouseNumberDisabled ? '' : '请输入门牌号',
              disabled: recipientHouseNumberDisabled,
            }}
          />
        </Item>
      </Col>
      <Col span={outSpan(colSpan, 6)}>
        <Item
          name={['express', 'recipientPostcode']}
          label="邮编"
          required
          rules={[
            // {
            //   pattern: /^\d{5}$/,
            //   message: '邮编仅允许5位数字',
            // },
            ({ getFieldsValue }) => {
              return {
                validator(_, value) {
                  return asyncValidate({
                    wmsSystemCode,

                    fieldKey: EnumOutWarehouseExpress.recipientPostcode,
                    fieldData: value,
                    props,
                  });
                },
              };
            },
          ]}
        >
          <Input placeholder="请输入邮编" />
        </Item>
      </Col>
      <Col span={outSpan(colSpan, 6)}>
        <Item
          name={['express', 'recipientBranchPostcode']}
          label="分邮编"
          rules={[
            // { type: 'string' }, { max: 20, message: '长度不能超过20位' },
            ({ getFieldsValue }) => {
              return {
                validator(_, value) {
                  return asyncValidate({
                    wmsSystemCode,

                    fieldKey: EnumOutWarehouseExpress.recipientBranchPostcode,
                    fieldData: value,
                    props,
                  });
                },
              };
            },
          ]}
        >
          <Input placeholder="请输入分邮编" />
        </Item>
      </Col>
      <Col span={outSpan(colSpan, 6)}>
        <Item
          name={['express', 'recipientEmail']}
          label="收件联系人邮箱"
          rules={[
            // { required: true, message: '请输入收件联系人邮箱' },
            // { pattern: REGEX.EMAIL, message: '请输入正确格式的邮箱' },
            ({ getFieldsValue }) => {
              return {
                validator(_, value) {
                  return asyncValidate({
                    wmsSystemCode,
                    fieldKey: EnumOutWarehouseExpress.recipientEmail,
                    fieldData: value,
                    props,
                  });
                },
              };
            },
          ]}
        >
          <Input placeholder="请输入收件联系人邮箱" />
        </Item>
      </Col>
    </Row>
  );
});

function useExpress({ form, props }: { form: FormInstance<any>; props: TypeProps }) {
  const nameArr = ['recipientCountry', 'recipientProvince', 'recipientCity'] as const;
  const wmsSystemCode = Form.useWatch(['wmsSystemCode'], form);
  const addressSelectRefMap = {
    recipientCountry: useRef<TypeSearchSelectRef>(),
    recipientProvince: useRef<TypeSearchSelectRef>(),
    recipientCity: useRef<TypeSearchSelectRef>(),
  } as Record<typeof nameArr[number], React.MutableRefObject<TypeSearchSelectRef | undefined>>;
  const addressAutoRef = useRef<TypeCityAutoCompleteRef>();
  /** 国家必定是下拉框, 省州动态切换, 城市目前必定是文本框输入 */
  const isNeedSearchAutoComplete = (propName?: string) => {
    if (propName === nameArr[0]) {
      return false;
    }
    return (
      propName === nameArr[2] ||
      (propName === nameArr[1] &&
        ['US' /** 美国 */, 'AU' /** 澳洲 */, 'CA' /** 加拿大 */, undefined, null].includes(
          form.getFieldValue(['express', 'recipientCountry']) as any,
        )) === false
    );
  };
  /** 收件地址在德国并且是德国仓发货 */
  const isGermanyAndWms = (country: string) => {
    return country === germanyCode && germanyWmsArr.includes(wmsSystemCode);
  };
  const apiOptions = async (searchValue: string, parentCode: string, allowCustomOption = true) => {
    const res = apiCountryCityOptions({ searchValue, parentCode });

    if (allowCustomOption === false) {
      return res;
    }

    return res.then((options) =>
      options.map((item) => {
        return {
          ...item,
          // 拼接label，用于展示 code-名称
          label: [item.standardCode, item.label].filter(Boolean).join('-'),
          optionNode:
            allowCustomOption !== true ? undefined : (
              <div className={styles.selectLabel}>
                <div>{item.standardCode}</div>
                <div className="label-right">{item.label}</div>
              </div>
            ),
        };
      }),
    );
  };
  const addressPropsMap: Record<
    keyof typeof addressSelectRefMap,
    TypeSearchSelectProps<any>
  > = {} as any;

  nameArr.forEach((name) => {
    addressPropsMap[name] = calcProps(name);
  });

  /** 计算select props属性 */
  function calcProps(name: keyof typeof addressPropsMap) {
    const nameIndex = nameArr.indexOf(name);
    /** 只有城市不自定义option渲染 */
    const hasCustomOptionVNode = name !== 'recipientCity';
    /** 公共props */
    const selectProps = {
      ref: addressSelectRefMap[name],
      style: { width: '33%' },
      /** 只有第一个拉下框会自动加载 */
      autoLoad: name === nameArr[0],
      allowFocusLoad: true,
      onChange: (value) => {
        const recipientAddressList = form.getFieldValue([
          'express',
          'recipientAddressList',
        ]) as string[];

        switch (nameIndex) {
          case 0: {
            /** 发货地是否是德国境内, 并且为德国仓 */
            const isGermany = isGermanyAndWms(value);
            /** 切换德国时, 清空详细地址二三的值 */

            form.setFieldsValue({
              express: {
                [nameArr[1]]: undefined,
                [nameArr[2]]: undefined,
                recipientAddressList: isGermany
                  ? recipientAddressList?.length > 1
                    ? recipientAddressList.map((item, index) => {
                        return index !== 0 ? '' : item;
                      })
                    : recipientAddressList
                  : recipientAddressList,
              },
            });

            if (value === 'US') {
              /**   收件人为美国时，门牌号置灰，不可填写； */
              form.setFieldsValue({ express: { recipientHouseNumber: '' } });
            }

            addressSelectRefMap[nameArr[nameIndex + 1]].current?.fetchOptionsForCascade();
            break;
          }
          case 1:
            form.setFieldsValue({
              express: {
                [nameArr[2]]: undefined,
              },
            });
            addressSelectRefMap[nameArr[nameIndex + 1]].current?.fetchOptionsForCascade();
            break;
          case 2:
            break;
          default:
            console.error(`未定义名称-->${name}`);
            break;
        }
      },
      optionLabelProp: 'label',
      /** 完全按照接口返回作为下拉搜索 */
      filterOption: false,
      request: async (searchValue: string) => {
        const parentCode =
          nameIndex === 0 ? undefined : form.getFieldValue(['express', nameArr[nameIndex - 1]]);

        /** 需要parentCode但是不存在 */
        if (name !== nameArr[0] && !parentCode) {
          return [];
        }
        return apiOptions(searchValue, parentCode, !isNeedSearchAutoComplete(name));
      },
    } as TypeSearchSelectProps<any>;

    return {
      ...selectProps,
    };
  }
  /** 用于手动初始化  1、页面初始化 2、页面详情 */
  function addressInit() {
    addressSelectRefMap.recipientProvince.current?.fetchOptions();
    addressSelectRefMap.recipientCity.current?.fetchOptions();
  }

  return {
    addressPropsMap,
    addressSelectRefMap,
    addressInit,
    addressAutoRef,
    isNeedSearchAutoComplete,
    isGermanyAndWms,
    wmsSystemCode,
  };
}

async function asyncValidate<T>({
  wmsSystemCode,
  fieldKey,
  fieldData,
  props,
  fieldIndex = 0,
}: {
  /** 仓库系统标识 */
  wmsSystemCode: string;
  /** 表单字段 */
  fieldKey: EnumOutWarehouseExpress;
  /** 表单字段值 */
  fieldData?: any;
  props: TypeProps;
  /** 用于数组校验 */
  fieldIndex?: number;
}) {
  // console.log('=========537', fieldKey, fieldData, wmsSystemCode);
  const { mode, form, detailData } = props;
  /** 表单取值是labelInValue结构 */
  const dispatchServiceType = (
    mode === 'createOrEdit'
      ? form.getFieldsValue()?.deliveryList?.[0]
      : detailData?.express?.deliveryList?.[0]
  )?.dispatchServiceType;
  /** 判断客户面单 */
  const isCustomerFaceSheet =
    Number(dispatchServiceType?.value ?? dispatchServiceType) === 1; /** 客户面单 */

  // console.log('dispatchServiceType-->', dispatchServiceType, isCustomerFaceSheet);

  /**
   * 说明
   * tm开头的是 天马、miss、SAT（美国）
   * cy开头的是 菜鸟-易仓（美国）
   * lzh开头的是 LZH-德国仓
   */
  /** 2-35 个字符 */
  const charLenRegex = /^.{2,35}$/;
  /** 不能超过30个字符 */
  const charNotRegex = /^.{0,30}$/;
  /** 断言判断合集 */
  const assertMap = {
    /** 断言: 必填 */
    required: (title?: string) => {
      if (!fieldData) {
        throw new Error(`请输入${title || '必填项'}`);
      }
    },
    /** 断言: 只支持英文字母、数字、逗号、点、横杠 、空格 */
    char_YI_CANG_BRITAIN: () => {
      if (/^[ a-zA-Z\d,\._·\-]*$/g.test(fieldData) === false) {
        throw new Error('只支持英文字母、数字、逗号、点、横杠 、空格');
      }
    },
  };

  const fieldRuleMap = {
    recipientName: {
      /**
       * 必填 2-35 个字符 闭区间
       * 这个校验规则适用于 菜鸟-易仓（美国）、天马、miss（美国）
       */
      tmCharLength: async () => {
        if (!fieldData) return Promise.reject(new Error('收件联系人必填'));

        if (charLenRegex.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('收件联系人须在2-35个字符之间'));
        }
        return Promise.resolve();
      },
      /** LZH-德国仓 必填 */
      lzhRequired: () => {
        if (!fieldData) return Promise.reject(new Error('收件联系人必填'));
        return Promise.resolve();
      },
      /** 易仓英国 */
      YI_CANG_BRITAIN: async () => {
        if (!fieldData) {
          throw new Error('收件联系人必填');
        }

        if (fieldData?.length > 30) {
          throw new Error('收件联系人不超过30个字符');
        }

        assertMap.char_YI_CANG_BRITAIN();
      },
    },
    recipientPhoneNumber: {
      async allRequired() {
        if (!fieldData) {
          throw new Error('电话必填');
        }
      },
      /**
       * 天马、miss（美国）
       * 菜鸟-易仓（美国
       * LZH-德国仓
       * 必填；最大30位，支持数字0-9、大小写英文字母、特殊字符（支持“空格”+-.）且数字必须大于9位。 */
      allCount: () => {
        if (!fieldData) return Promise.reject(new Error('电话必填'));

        const countNum = fieldData.split('').filter((char: string) => /\d/.test(char)).length;

        if (countNum <= 9) {
          return Promise.reject(new Error('电话必须包含数字,而且包含数字必须大于9位'));
        }
        if (charNotRegex.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('电话长度不能超过30位'));
        }
        if (REGEX.SPECIAL_TEL_SYMBOL_SPACE.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('只能输入数字、英文、空格、特定字符包含(+ - .)'));
        }
        return Promise.resolve();
      },
      YI_CANG_GERMANY: async () => {
        if (!trimUtils.trim(fieldData)) return Promise.reject(new Error('电话必填'));

        if (charNotRegex.test(trimUtils.trim(fieldData)) === false) {
          throw new Error('电话长度不能超过30位');
        }
        if (REGEX.SPECIAL_TEL_SYMBOL_SPACE.test(trimUtils.trim(fieldData)) === false) {
          throw new Error('只能输入数字、英文、空格、特定字符包含(+ - .)');
        }
      },
      YI_CANG_BRITAIN: async () => {
        if (!fieldData) {
          throw new Error('电话必填');
        }

        if (/^\d{0,15}$/.test(fieldData) === false) {
          throw new Error('电话长度不超过15位,仅支持数字');
        }
      },
      CROSS: async () => {
        /** 第一个电话必填, 后面不必填 */
        if (!fieldData && fieldIndex === 0) {
          throw new Error('电话必填');
        }
        /** 中英文括号都支持 */
        if (/^[\d+\-\(\)（）]{0,20}$/.test(fieldData) === false) {
          throw new Error('电话长度不超过20位,仅支持数字+-()');
        }
      },
    },
    recipientEmail: {
      /** 非必填 输入了需要保证正确的邮箱格式 */
      allEmail: async () => {
        if (!fieldData) return Promise.resolve();
        if (REGEX.EMAIL.test(fieldData) === false) {
          return Promise.reject(new Error('请输入正确格式的邮箱'));
        }
        return Promise.resolve();
      },
      async nothing() {},
    },
    recipientCountry: {
      /** 国家：必填、二字码 */
      allRequired: () => {
        if (!fieldData) return Promise.reject(new Error('请选择收件国家'));
        return Promise.resolve();
      },
    },
    recipientProvince: {
      /**
       * 天马、miss（美国）
       * 菜鸟-易仓（美国） */
      /** 省：必填  */
      async allRequired() {
        if (!fieldData) {
          throw new Error('请选择收件省份');
        }
      },
      /** LZH-德国仓 省：非必填、二字码 */
      lzhRecipientProvince: () => {
        return Promise.resolve();
      },
    },
    recipientCity: {
      /** 必填 */
      allRequired: () => {
        if (!fieldData) return Promise.reject(new Error('请输入收件城市'));

        return Promise.resolve();
      },
      YI_CANG_BRITAIN: async () => {
        if (!fieldData) {
          throw new Error('请输入收件城市');
        }
        assertMap.char_YI_CANG_BRITAIN();
      },
    },
    /** 邮编 */
    recipientPostcode: {
      /** 天马、miss（美国）菜鸟-易仓（美国）  */
      /** 邮编 必填，仅5位数字 */
      tmCharNum: () => {
        if (!fieldData) return Promise.reject(new Error('请输入邮编'));
        if (/^\d{5}$/.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('邮编仅允许5位数字'));
        }
        return Promise.resolve();
      },
      /** LZH-德国仓 */
      /** 必填； */
      lzhRequired: () => {
        if (!fieldData) return Promise.reject(new Error('请输入邮编'));
        return Promise.resolve();
      },
    },
    recipientBranchPostcode: {
      /** 天马、miss（美国）菜鸟-易仓（美国）  */
      /** 分邮编 非必填，长度不能超过20位数字 */
      tmCharNum: () => {
        if (trimUtils.trim(fieldData)) {
          if (/^\d{0,20}$/.test(trimUtils.trim(fieldData)) === false) {
            return Promise.reject(new Error('分邮编长度不能超过20位数字'));
          }
        }

        return Promise.resolve();
      },
      /** LZH-德国仓 */
      /** 非必填； */
      notRequired: () => {
        return Promise.resolve();
      },
      /** 不做限制 */
      async nothing() {},
    },
    'recipientAddress.0': {
      /** 天马、miss（美国）  */
      /** 必填，收货人地址2-35个字符（2-35，闭区间） */
      tmCharNum: () => {
        if (!fieldData) return Promise.reject(new Error('请输入收件人详细地址'));
        if (charLenRegex.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('收件人详细地址须在2-35字符之间'));
        }
        return Promise.resolve();
      },
      /** 菜鸟-易仓（美国） */
      /** 必填； */
      cyRequired: () => {
        if (!fieldData) return Promise.reject(new Error('请输入收件人详细地址'));

        return Promise.resolve();
      },
      /** 必填，不能超过30个字符; */
      lzhCharNum: () => {
        if (!fieldData) return Promise.reject(new Error('请输入收件人详细地址'));
        if (charNotRegex.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('收件人详细地址不能超过30位'));
        }
        return Promise.resolve();
      },
      YI_CANG_BRITAIN: async () => {
        assertMap.required('收件人详细地址');

        if (/^.{0,30}$/.test(fieldData) === false) {
          throw new Error('收件人详细地址不能超过30位');
        }

        assertMap.char_YI_CANG_BRITAIN();
      },
      /** 必填, 不超过35个字符 */
      async CROSS() {
        if (!fieldData) {
          throw new Error('请输入收件人详细地址');
        }
        if (/^.{0,35}$/.test(fieldData) === false) {
          throw new Error('收件人详细地址不能超过35位');
        }
      },
    },
    'recipientAddress.1': {
      /** 天马、miss（美国）  */
      /** 非必填，收货人地址2-35个字符（2-35，闭区间） */
      tmCharNum: async () => {
        if (trimUtils.trim(fieldData)) {
          /** 客户面单地址2, 3不校验 */
          if (isCustomerFaceSheet) {
            return;
          }
          if (charLenRegex.test(trimUtils.trim(fieldData)) === false) {
            return Promise.reject(new Error('收件人详细地址须在2-35字符之间'));
          }
        }

        return Promise.resolve();
      },
      /** 菜鸟-易仓（美国） */
      /** 非必填 */
      notRequired: () => {
        return Promise.resolve();
      },
      /** 非必填，不能超过30个字符;德国境内禁填; */
      lzhCharNum: () => {
        if (isCustomerFaceSheet) {
          return;
        }
        if (charNotRegex.test(trimUtils.trim(fieldData)) === false) {
          return Promise.reject(new Error('收件人详细地址不能超过30位'));
        }
        return Promise.resolve();
      },
      YI_CANG_BRITAIN: async () => {
        if (/^.{0,30}$/.test(fieldData) === false) {
          throw new Error('收件人详细地址不能超过30位');
        }

        assertMap.char_YI_CANG_BRITAIN();
      },
      /** 非必填, 不超过35位 */
      async CROSS() {
        if (/^.{0,35}$/.test(fieldData) === false) {
          throw new Error('收件人详细地址不能超过35位');
        }
      },
    },
    'recipientAddress.2': {
      /** 天马、miss（美国）  */
      /** 非必填, 收货人地址2-35个字符（2-35，闭区间） */
      tmCharNum: async () => {
        return fieldRuleMap['recipientAddress.1'].tmCharNum();
      },
      /** 菜鸟-易仓（美国） */
      /** 非必填； */
      notRequired: () => {
        // if (!fieldData) return Promise.reject(new Error('请输入收件人详细地址'));
        return Promise.resolve();
      },
      /** 非必填，不能超过30个字符; 德国境内禁填; */
      lzhCharNum: async () => {
        return fieldRuleMap['recipientAddress.1'].lzhCharNum();
      },
      async CROSS() {
        return fieldRuleMap['recipientAddress.1'].CROSS();
      },
      /** 没有限制 */
      nothing: async () => {},
    },
    recipientHouseNumber: {
      /** 天马、miss（美国 菜鸟-易仓（美国） */
      /** 非必填 */
      notRequired: () => {
        return Promise.resolve();
      },
      /** 非必填，客户未填时，后端默认为“.”（英文的句号）传到仓库；不能超过8个字符；  */
      lzhCharNum: () => {
        if (trimUtils.trim(fieldData)) {
          if (/^.{0,8}$/.test(trimUtils.trim(fieldData)) === false) {
            return Promise.reject(new Error('门牌号不能超过8位'));
          }
        }
        return Promise.resolve();
      },
      nothing: async () => {},
    },
  } as const;
  /** SAT、天马（魔方云），miss 美国区域公共校验规则 */
  const amCommonRule = {
    recipientName: 'tmCharLength',
    recipientPhoneNumber: 'allCount',
    recipientEmail: 'allEmail',
    recipientCountry: 'allRequired',
    recipientProvince: 'allRequired',
    recipientCity: 'allRequired',
    recipientPostcode: 'tmCharNum',
    recipientBranchPostcode: 'tmCharNum',
  };
  /** SAT 天马、miss（美国） */
  const tmRule = {
    ...amCommonRule,
    'recipientAddress.0': 'tmCharNum',
    'recipientAddress.1': 'tmCharNum',
    'recipientAddress.2': 'tmCharNum',
    recipientHouseNumber: 'notRequired',
  };
  /** 菜鸟-易仓（美国） */
  const cyRule = {
    ...amCommonRule,
    'recipientAddress.0': 'cyRequired',
    'recipientAddress.1': 'notRequired',
    'recipientAddress.2': 'notRequired',
    recipientHouseNumber: 'notRequired',
  };
  const validateMap = {
    /** 天马、miss（美国） */
    '000001': {
      ...tmRule,
    },
    /** 菜鸟-易仓（美国 */
    '000002': {
      ...cyRule,
    },
    /** LZH-德国仓 */
    '000003': {
      recipientName: 'lzhRequired',
      recipientPhoneNumber: 'YI_CANG_GERMANY',
      recipientEmail: 'allEmail',
      recipientCountry: 'allRequired',
      recipientProvince: 'lzhRecipientProvince',
      recipientCity: 'allRequired',
      recipientPostcode: 'lzhRequired',
      recipientBranchPostcode: 'notRequired',
      'recipientAddress.0': 'lzhCharNum',
      'recipientAddress.1': 'lzhCharNum',
      'recipientAddress.2': 'lzhCharNum',
      recipientHouseNumber: 'lzhCharNum',
    },
    /** 易仓英国 */
    '000004': {
      recipientName: 'YI_CANG_BRITAIN',
      recipientPhoneNumber: 'YI_CANG_BRITAIN',
      recipientEmail: 'allEmail',
      recipientCountry: 'allRequired',
      recipientProvince: 'lzhRecipientProvince',
      recipientCity: 'YI_CANG_BRITAIN',
      recipientPostcode: 'lzhRequired',
      recipientBranchPostcode: 'nothing',
      'recipientAddress.0': 'YI_CANG_BRITAIN',
      'recipientAddress.1': 'YI_CANG_BRITAIN',
      'recipientAddress.2': 'nothing',
      recipientHouseNumber: 'nothing',
    },
    /** 科劳斯(德国)-汉堡仓 */
    '000005': {
      recipientName: 'lzhRequired',
      recipientPhoneNumber: 'CROSS',
      recipientEmail: 'allEmail',
      recipientCountry: 'allRequired',
      recipientProvince: 'allRequired',
      recipientCity: 'allRequired',
      recipientPostcode: 'lzhRequired',
      recipientBranchPostcode: 'nothing',
      'recipientAddress.0': 'CROSS',
      'recipientAddress.1': 'CROSS',
      'recipientAddress.2': 'CROSS',
      recipientHouseNumber: 'nothing',
    },
    '999999': {
      recipientName: 'lzhRequired',
      recipientPhoneNumber: 'allRequired',
      recipientCountry: 'allRequired',
      'recipientAddress.0': 'cyRequired',
      recipientPostcode: 'lzhRequired',
    },
  } as Record<TypeValidateCode, Record<EnumOutWarehouseExpress, string>>;

  const getRule = (ruleKey: EnumOutWarehouseExpress, ruleName: string) => {
    return (fieldRuleMap[ruleKey] as Record<string, (() => Promise<any>) | undefined>)?.[ruleName];
  };
  const validateCode = wmsSysCodeMap[wmsSystemCode];
  // const validateCode = '000005' as const;

  if (!wmsSysCodeMap[wmsSystemCode]) {
    console.warn('未匹配到对应的校验规则, 默认不校验', 'wmsSystemCode--->', wmsSystemCode);
  }

  /** 在没有匹配到对应的规则时候返回undefined,SAT 作为例子 */
  return getRule(fieldKey, validateMap[validateCode]?.[fieldKey])?.();
}

/** 输出布局 */
function outSpan(/** 需要处理的span */ colSpan: number, /** 当前默认span */ normal: number) {
  return colSpan * normal;
}
