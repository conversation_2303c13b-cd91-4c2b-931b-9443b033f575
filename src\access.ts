export const ShiroMap = {
  FREIGHT_MODULE: 'dir_1001',
  FOB_URL: '/web/freight/freightfclweb/queryFreightFclListForFob.html',
  CIF_URL: '/web/freight/freightfclweb/queryFreightFclListForPlat.html',
  YJT_FCL: 'YJT_FCL_LIST',
  FREIGHT_TRAILER: 'freight.efcl.trailer.query.contact',

  OVERSEA_MODULE: 'dir_1020',
  OVERSEA_GOODS_MANAGER: 'OVERSEA_GOODS_MANAGER',
  OVERSEA_GOODS_LIST: 'OVERSEA_GOODS_LIST',
  OVERSEA_COMPGOODS_LIST: 'OVERSEA_COMPGOODS_LIST',
  OVERSEA_IN_STOCK_MANAGER: 'OVERSEA_IN_STOCK_MANAGER',
  OVERSEA_OUT_STOCK_MANAGER: 'OVERSEA_OUT_STOCK_MANAGER',
  OVERSEA_STOCK_MANAGER: 'OVERSEA_STOCK_MANAGER',
  OVERSEA_STOCK_MANAGER_LIST: 'OVERSEA_STOCK_MANAGER_LIST',
  OVERSEA_STOCK_TRADE_FLOW: 'OVERSEA_STOCK_TRADE_FLOW',
  OVERSEA_STOCK_AGE_MANAGER: 'OVERSEA_STOCK_AGE_MANAGER',
  OVERSEA_IMPORT_MANAGER: 'OVERSEA_IMPORT_MANAGER',
  OVERSEA_IMPORT_MANAGER_BATCH_OUT_WAREHOUSE: 'OVERSEA_IMPORT_MANAGER_BATCH_OUT_WAREHOUSE',
  OVERSEA_CUSTOMER_ACCOUNT_CODE: 'OVERSEA_CUSTOMER_ACCOUNT_CODE',
  OVERSEA_CASH_CASHFLOWLIST: 'OVERSEA_CASH_CASHFLOWLIST',
  OVERSEA_CHARGE_MANAGE_CHARGEBILLLIST: 'OVERSEA_CHARGE_MANAGE_CHARGEBILLLIST',
  OVERSEA_CHARGE_MANAGE_CHARGEWAREHOUSE: 'OVERSEA_CHARGE_MANAGE_CHARGEWAREHOUSE',
  /** 报表 */
  OVERSEA_DTC_REPORT: 'OVERSEA_DTC_REPORT',
  OVERSEA_DTC_REPORT_BASIC: 'OVERSEA_DTC_REPORT_BASIC',
  OVERSEA_DTC_REPORT_IN_ORDER: 'OVERSEA_DTC_REPORT_IN_ORDER',
  OVERSEA_DTC_REPORT_OUT_ORDER: 'OVERSEA_DTC_REPORT_OUT_ORDER',
  OVERSEA_DTC_REPORT_IN_STOCK: 'OVERSEA_DTC_REPORT_IN_STOCK',
  OVERSEA_DTC_REPORT_LIBRARY_AGE: 'OVERSEA_DTC_REPORT_LIBRARY_AGE',
  /** 出库操作费 */
  OVERSEA_DTC_REPORT_OUTBOUND_OPERATION_FEE: 'OVERSEA_DTC_REPORT_OUTBOUND_OPERATION_FEE',
  /** 卸货附加费 */
  OVERSEA_DTC_REPORT_WAREHOUSE_LOADING_FEE: 'OVERSEA_DTC_REPORT_WAREHOUSE_LOADING_FEE',
  /** 理货上架费  */
  OVERSEA_DTC_REPORT_TALLY_LISTING_FEE: 'OVERSEA_DTC_REPORT_TALLY_LISTING_FEE',
  OVERSEA_DTC_REPORT_WAREHOUSE_STOCKPILING: 'OVERSEA_DTC_REPORT_WAREHOUSE_STOCKPILING',
  OVERSEA_DTC_REPORT_FINAL_DELIVERY_FEE: 'OVERSEA_DTC_REPORT_FINAL_DELIVERY_FEE',
  /** 独立结算费用 */
  OVERSEA_DTC_REPORT_INDEPENDENT_SETTLEMENT: 'OVERSEA_DTC_REPORT_INDEPENDENT_SETTLEMENT',

  RO_MANAGE: '/web/webro/ro.html',
  ORDER_MODULE: 'dir_1004',
  RO_MODULE: '/web/webro/ro.html',
  SETTLE_MODULE: 'dir_1007',
  OVERSEAS_BILLS: '/web/webforeigninvoice/foreigninvoice.html',
  DOMESTICE_BILLS: '/web/webchargeconfirm/chargeconfirm.html',
  ININVOICE: '/web/ininvoice/queryInInvoicelList.html',
  COUNT_MODULE: 'dir_1006',
  CUSTOMER_VIEW: '/web/webFineReport/total_web_customer_view.html',
  PORT_VIEW: '/web/webFineReport/total_web_port_view.html',
  MT_SHIPPING_PLAN_REPORT: 'MT_SHIPPING_PLAN_REPORT',
  CUSTOMER_MODULE: 'dir_1005',
  COMPANY_MANAGER: 'dir_1009',
  GENERAL_INFO: 'dir_1003', // 常用信息 模块
  SFT_MODULE: '/web/webconsignee/consignee.html',
  USER_INFO: '/web/webUserManager/userinfo.html',
  EDIT_USER_INFO: '/web/webUserManager/toEditUserPwd.html',
  CERTIFICATE: '/web/webUserManager/certificate.html',
  IMPORT_SI_EXCEL: '/wwl/api/website/web/orderSi/importSiExcel',
  WEB_BOOKING: '/web/webbooking/booking.html',
  WEB_LCL: '/web/api/website/*/lcl/queryWebLclPage.do',
  ENTRUST_COMMODITY_MANAGER: 'ENTRUST_COMMODITY_MANAGER',

  // 委托关联
  ENTRUST_MANAGER: 'ENTRUST_MANAGER',
  INTERNATIONAL_TRANSPORTATION: 'INTERNATIONAL_TRANSPORTATION',
  INTERNATIONAL_LINE: 'INTERNATIONAL_LINE',

  // 出运计划
  SHIPPING_PLAN_MANAGER: 'SHIPPING_PLAN_MANAGER',
  // 销售单
  SALES_ORDER: 'SALES_ORDER',
  // 采购单
  PURCHASE_ORDER: 'PURCHASE_ORDER',

  IM_MODULE: '/im/webchat/index.html',
  TEMPLATE_MODULE: '/web/webbooking/template/queryPage.html',

  // 收货地址
  ADDRESS_MANAGER: 'ADDRESS_MANAGER',
  BOOKING_SO_ADD: '/web/webbooking/toOpenView',
  BOOKING_SO_UPDATE: '/web/webbooking/toUpdate',
  BOOKING_SO_COPY: '/web/webbooking/toCopy',
  BOOKING_SI_ADD: '/web/websi/tosiadd.html',
  BOOKING_SI_UPDATE: '/web/websi/tosiupdate.html',
  BOOKING_SI_IMPORT: '/web/WebSiImport/importViewSi.html',
  BOOKING_SO_UPLOAD: '/web/webbooking/orderFileAdd',
  BOOKING_VGM_LOAN: '/web/webvgm/importOpenVgm.html',
  BOOKING_PREW_IMPORT: '/web/webprewiredimport/importPrewiredView',
  BOOKING_UPLOAD_FILE: '/wwl/api/website/web/orderTrack/addFileToBooking',

  ANT_ORDER_MODULE: '/wwl/api/website/antfin/antorder/queryAntOrderByPage',

  MESSAGE_MODULE: 'dir_1010',
  MESSAGE_CONFIG: '/wwl/api/website/web/webuserSubscribe/*',
  MESSAGE_TODO_PAGE: '/web/todo/queryToDoPage.html',
  MESSAGE_NOTICE: '/web/message/queryMessageNotice.html',
  ORDER_PRARSING: '/wwl/api/website/web/orderParsing/createOrderParsing.do', // 托书
  SHOW_SI_CONTAINER_AGENT: 'showSiContainerAgent',
  MERGE_SI: '/wwl/api/website/web/orderSi/submitMergeSiInfo',
  SHARE_SI: '/wwl/api/website/web/OBookingSiHref/shareLink',

  SECKILL_MODULE: 'dir_1011',
  SECKILL_CONTRACT: '/wwl/api/website/web/seckill/initJumpPage',
  SECKILL_SPACE: '/wwl/api/website/web/seckill/getCustomerAppointListByPage',

  // 拖车服务
  LOGITICS_DYNAMIC: 'dir_1012',
  TRUCK_ORDER_LIST: 'TRUCK_ORDER_LIST',
  TRUCK_TRACK: 'TRUCK_TRACK',
  TRUCK_BUSSI_AVG: 'TRUCK_BUSSI_AVG',

  INQUIRY_QUOTATION_LIST: 'INQUIRY_QUOTATION_LIST',
  // 企业管理
  ENTERPRISE_MANAGER_INDEX: 'COMPANY_MANAGER',
};

export type ShiroKeyType = typeof ShiroMap;

export default (initialState: any): ShiroKeyType => {
  if (!initialState || !initialState.webUser || !initialState.webUser.permissionsList) {
    const failResult: any = {};

    Object.keys(ShiroMap).forEach((key) => {
      failResult[key] = false;
    });

    return failResult;
  }

  const { permissionsList = [] } = initialState.webUser;
  const regStr = permissionsList.join('|').replace(/\//gim, '\\/');
  const checkPermiss = (params: string): boolean => new RegExp(regStr, 'gim').test(params);
  const result: any = {};

  Object.entries(ShiroMap).forEach((item: any[]) => {
    result[item[0]] = checkPermiss(item[1]);
  });
  result.ENTERPRISE_ACCOUNT_MANAGER = initialState.webUser.memCompanyManager === 1;

  return result;
};
