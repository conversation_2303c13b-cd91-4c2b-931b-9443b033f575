export default {
  Index: {
    notSubmitted: '未提交',
    preConfiguredManifest: '预配舱单：',
    documents: '单证文件：',
    bill: '账单：',
    cargoTracking: '货物跟踪',
    consignee: '收货人',
    consignor: '发货人',
    details: '详情',
    oceanBillOfLading: '海运提单:&nbsp;',
    oceanBillOfLading1: '海运提单',
    enclosure: '附件',
    uploadTime: '上传时间',
    fileName: '文件名',
    fileType: '文件类型',
    orderOperation: '订单操作',
    bookingNo: '订舱号',
    statusBooking: '{val1}/{val2}',
    consignor1: '发货人',
    shippersReceipt: '{val1}-{val2}',
    shipNameAndVoyage: '船名/航次',
    carrier: '承运人',
    carrierShip: '{val1}-{val2}',
    huanshiOrderNo: '环世单号',
    zhonglianDocumentNo: '中联单号',
    customerNo: '客户编号',
    state: '状态',
  },
};
