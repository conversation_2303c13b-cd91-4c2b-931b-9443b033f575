import { InputNumber, Form, Input, Space, Table } from 'antd';
import React from 'react';
import classnames from 'classnames';
import { apiMapDictType } from '@/api';
import { SearchSelect, LoadButton } from '@/components';
import { ruleUtils } from '@/utils';
import { cssFormListTable } from '@/styles';
import { RowLayout } from '@/views';

const listName = 'goodsPallet';

/** 打托设设置 */
function npGoodsPallet(path: string) {
  return [listName, path];
}

/** 打托设置 */
export default function FormPalletConfig() {
  return (
    <div>
      <RowLayout>
        <Form.Item {...{ label: '托盘尺寸', name: npGoodsPallet('palletType') }}>
          <SearchSelect {...{ request: apiMapDictType.palletSizeType }} />
        </Form.Item>
        <Form.Item
          {...{
            label: '每托件数',
            name: npGoodsPallet('goodsNum'),
            rules: [ruleUtils.ruleNoZeroPositiveInteger],
          }}
        >
          <InputNumber stringMode />
        </Form.Item>
        <Form.Item {...{ label: '打托备注', name: npGoodsPallet('remark'), rules: [{ max: 255 }] }}>
          <Input />
        </Form.Item>
      </RowLayout>
    </div>
  );
}

/** 打托设置, FormList版本, 以后可能会用到 */
function FormPalletConfig2() {
  const form = Form.useFormInstance();

  return (
    <div className={classnames(cssFormListTable.FormListTable /* cssFormListTable.plainTable */)}>
      <Form.List {...{ name: listName, initialValue: [] }}>
        {function (fieldList, action, { errors }) {
          // const dataSource = form.getFieldValue(listName) || [];
          // console.log('fieldList-->', fieldList);
          return (
            <>
              <Table
                {...{
                  // dataSource: fieldList.map((item) => ({ key: item.key })),
                  dataSource: [{}],
                  pagination: false,
                  columns: [
                    {
                      title: '托盘尺寸',
                      dataIndex: 'palletType',
                      render(val, record, index) {
                        return (
                          <Form.Item {...{ label: '托盘尺寸', name: [index, 'palletType'] }}>
                            <SearchSelect {...{ request: apiMapDictType.palletSizeType }} />
                          </Form.Item>
                        );
                      },
                    },
                    {
                      title: '每托件数',
                      dataIndex: 'goodsNum',
                      render(val, record, index) {
                        return (
                          <Form.Item
                            {...{
                              label: '每托件数',
                              name: [index, 'goodsNum'],
                              rules: [ruleUtils.ruleNoZeroPositiveInteger],
                            }}
                          >
                            <InputNumber stringMode />
                          </Form.Item>
                        );
                      },
                    },
                    {
                      title: '打托备注',
                      dataIndex: 'remark',
                      render(val, record, index) {
                        return (
                          <Form.Item
                            {...{
                              label: '打托备注',
                              name: [index, 'remark'],
                              rules: [{ max: 255 }],
                            }}
                          >
                            <Input />
                          </Form.Item>
                        );
                      },
                    },
                    // {
                    //   title: '操作',
                    //   dataIndex: 'operate',
                    //   width: 100,
                    //   render(val, record, index) {
                    //     return (
                    //       <Space size={20} align={'start'}>
                    //         <LoadButton
                    //           {...{
                    //             popconfirmProps: {
                    //               title: '确认删除吗？',
                    //             },
                    //             onClick() {
                    //               action.remove(index);
                    //             },
                    //           }}
                    //         >
                    //           删除
                    //         </LoadButton>
                    //       </Space>
                    //     );
                    //   },
                    // },
                  ],
                }}
              />
              {/* <div
              className={cssFormListTable.btnAdd}
              onClick={() => {
                action.add({});
              }}
            >
              + 新增
            </div> */}
              {/* <Form.ErrorList errors={errors}></Form.ErrorList> */}
            </>
          );
        }}
      </Form.List>
    </div>
  );
}
