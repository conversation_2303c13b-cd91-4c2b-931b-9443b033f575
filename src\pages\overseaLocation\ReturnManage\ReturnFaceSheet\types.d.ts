/** 退货面单列表结构 */
type TypeReturnFaceSheetTB = {
  /** 主键 */
  id: string;
  /** 出库订单id */
  orderId?: number;
  /** 申请单号 */
  podOrderNo?: string;
  /** 客户销售单号（出库） */
  customerSalesNo?: string;
  /** 客户关联单号（出库） */
  customerRelatedNo?: string;
  /** 平台卖家id */
  platformSellerId?: string;
  /** 平台店铺id */
  platformStoreId?: string;
  /** 仓库code */
  warehouseCode?: string;
  /** 仓库币种 */
  currency?: string;
  /** 状态 10:待上传面单 20:已上传面单 35:取消 */
  returnStatus?: number;
  /** 状态 | 后端翻译 */
  returnStatusName?: string;
  /** 退货物流追踪单号 */
  returnTrackingNo?: string;
  /** 退货label附件文件 */
  returnLabelFile?: string;
  /** 退货label附件文件名称 */
  returnLabelFileName?: string;
  /** 物流渠道商 */
  registerCarrier?: string;
  /** 公司唯一标识 */
  companyId?: string;
  /** 公司名称 */
  companyName?: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createDate?: string;
  /** 创建人id */
  createById?: string;
  /** 创建人名称 */
  createByName?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 更新人id */
  updateById?: string;
  /** 更新人名称 */
  updateByName?: string;
  /** sku列表 */
  skuList?: string[];
  /** 物流可视化id */
  orderLogisticsId?: string;
  /** 物流进展 */
  latestNode?: string;
  /** 物流节点状态描述, 查询异常QUERY_ERROR  */
  trackNode?: string;
  /** 应收费用(只允许小数点两位) */
  receivableFee?: number;
  /** 应付费用(只允许小数点两位) */
  payableFee?: number;
  /** 收件人信息 */
  recipientDetail?: {
    /** 收件人姓名 */
    recipientName?: string;
    /** 收件人国家 */
    recipientCountry?: string;
    /** 收件人省/州 */
    recipientProvince?: string;
    /** 收件人城市 */
    recipientCity?: string;
    /** 收件人邮编 */
    recipientPostcode?: string;
    /** 收件人电话，若有两个，逗号拼接 */
    recipientPhoneNumberList?: string[];
    /** 收件人详细地址1 */
    recipientAddress1?: string;
    /** 收件人详细地址2 */
    recipientAddress2?: string;
  };
  /** 销售信息 */
  outboundDelivery?: {
    /** 派送物流单号 | 销售运单 */
    trackingNo?: string;
    /** 派送服务渠道 */
    dispatchServiceChannel?: string;
    /** 派送服务渠道: 后端翻译 */
    dispatchServiceChannelName?: string;
    /** 派送服务名称 | 物流渠道 */
    dispatchServiceName?: string;
    /** 派送服务名称 | 物流渠道 : 后端翻译*/
    dispatchServiceNameName?: string;
    /** 客户渠道code */
    customerChannelCode?: string;
    /** 客户渠道名称 : 后端翻译 */
    customerChannelName?: string;
    /** 派送服务商 | 渠道商 */
    dispatchServiceProvider?: string;
    /** 派送服务商: 后端翻译 */
    dispatchServiceProviderName?: string;
  };
  /** 退货sku信息 */
  returnSkuList?: {
    /** SKU */
    sku?: string;
    /** 商品净重 */
    goodsWeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 体积单位 */
    volumeUnit?: string;
    /** 删除标志 */
    deleted?: boolean;
  }[];
};
