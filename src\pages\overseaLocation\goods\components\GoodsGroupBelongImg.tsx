import React from 'react';
import './GoodsGroupBelongImg.less';
import GoodsImage from '@/components/GoodsImage';

type TypeProps = {
  goodsList: { id?: string; pictureUrl?: string; goodsId?: string }[];
};
// eslint-disable-next-line func-names
export default function(props: TypeProps) {
  const { goodsList = [] } = props;
  // 图片数据分组 2张为一组
  const threePicList = goodsList.slice(0, 2) || [];
  const onePicList = goodsList.slice(2, 3) || [];
  const twoPicList = goodsList.slice(3, 5) || [];

  return (
    <div className="collpase-box">
      {threePicList.map((item) => {
        return <GoodsImage className="image-cover" src={item.pictureUrl} key={item.id} />;
      })}
      <div className="collpase-hover">
        <div className="collpase-top ">
          {onePicList.map((item) => {
            return <GoodsImage className="image-cover" src={item.pictureUrl} key={item.id} />;
          })}
        </div>

        <div className="collpase-item-group">
          {twoPicList.map((item, index: number) => {
            return (
              <div
                className="collpase-item"
                style={{ left: `${(index + 1) * 22}px`, zIndex: 8 - index }}
                key={item.id}
              >
                <GoodsImage className="image-cover" src={item.pictureUrl} key={item.id} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
