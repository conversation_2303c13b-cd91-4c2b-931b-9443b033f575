import React from 'react';
import type { DividerProps } from 'antd';
import { Divider } from 'antd';
import classnames from 'classnames';
import './CustomizeDivider.less';
import { compUtils } from '@/utils';

type TypeCustomizeDividerProps = {
  /** 标题名称 */
  title?: React.ReactNode;
  /** 颜色类型 */
  colorType?: 'blue';
  /** title 风格 */
  titleStyle?: 'line-blue';
} & DividerProps;

/** 自定义分割线 */
function CustomizeDivider(inProps: TypeCustomizeDividerProps) {
  const { title, colorType = 'blue', titleStyle, ...dividerProps } = inProps;
  const titleStyleMap = {
    'line-blue': {
      orientation: 'left',
      orientationMargin: 0,
      className: 'customize-divider__line-blue',
    },
  } as Record<string, DividerProps>;
  const titleStyleProps = titleStyleMap[titleStyle!];
  const defaultDividerProps = {
    /** 默认title 靠左 */
    orientation: 'left',
    ...titleStyleProps,
    className: classnames(
      'customize-divider',
      colorType && `customize-divider__${colorType}`,
      titleStyleProps?.className,
    ),
  } as DividerProps;
  const props = compUtils.propsMerge(defaultDividerProps, dividerProps);

  return <Divider {...props}>{title}</Divider>;
}

export default CustomizeDivider;
