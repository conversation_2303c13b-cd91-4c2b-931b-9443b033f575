import React, { useState, useEffect, useRef } from 'react';
import { history } from 'umi';
import { FooterToolbar } from '@ant-design/pro-layout';
import { Spin, Card, Button, Form, Input, Space, message } from 'antd';
import { uniqBy } from '@/utils/utils-three';
import {
  dateFormatter,
  historyGetQuery,
  historyGoPrev,
  ruleUtils,
  session,
  transString,
} from '@/utils';
import { CustomizeDivider, RowLayout } from '@/views';
import { LoadButton, SearchSelect } from '@/components';
import { apiMapDictType, useDictTypeValueEnum } from '@/api';
import {
  apiOutWarehouseGetFormData,
  apiOutWarehouseSaveDraft,
  apiOutWarehouseSubmit,
  apiOutboundPodNoCreate,
} from './OutboundApi';
import { apiMixWarehouseOptions } from '@/pages/pagesApi';
import { EnumOutboundBusinessType, EnumOutOrderSource } from './OutboundEnum';
import {
  FormAdditionalInfo,
  FormBaseInfo,
  FormGoodsChoose,
  FormLogisticService,
  FormRecipientAddress,
  fileListMerge,
} from './components';
import type { TypeOutboundGoodsTB } from './components';
import { isOMS, isPortal } from '@/oms-portal-diff-import';
import { asyncGoodsInfoRefresh, PageContainer, useCostCalc } from './OutboundDiffImport';
import type { FormInstance } from 'antd/es/form';

const { Item } = Form;

/** 出库新增编辑页面 */
function OutboundEdit() {
  const [newPageLoading, setNewPageLoading] = useState(false);
  const { warehouseList, asyncGetWarehouseList } = useOutWarehouseOptions();
  const { isCreate, isCopy, isUpdate, isDraft, id } = useUpdate();
  const [form] = Form.useForm();
  const [allowBatchGoods, setAllowBatchGoods] = useState(true);
  const addressRef = useRef<any>();
  const { costCalcModalNode, asyncCostCalc } = useCostCalc({ form });
  const { dictTypeMap } = useDictTypeValueEnum(['orderSourcePlatform']);
  /** 已删除数据缓存, 后端需要deleted: true, 会在接口改造中逐步移除 */
  const cacheDeleteMapRef = useRef<{
    cacheGoodsList: TypeOutboundGoodsTB[];
  }>({
    cacheGoodsList: [],
  });

  useEffect(() => {
    setNewPageLoading(true);
    asyncInit().finally(() => {
      setNewPageLoading(false);
    });
  }, []);
  async function asyncInit() {
    const userInfo = session.get('userInfo') as {
      companyName?: string;
      companyId?: string;
      userName?: string;
    };

    form.setFieldsValue({
      userName: userInfo?.userName,
      ...(isPortal
        ? {
            /** 仅门户侧初始化数据 */
            companyName: userInfo?.companyName,
            companyInfo: { label: userInfo?.companyName, value: userInfo?.companyId },
            companyId: userInfo?.companyId,
          }
        : undefined),
    });

    if (isUpdate === false) {
      const { data } = await apiOutboundPodNoCreate();

      form.setFieldsValue({ podOrderNo: data });
    }

    if (id) {
      const { data: detail } = await apiOutWarehouseGetFormData({ id });

      await asyncFullDetailInfo(detail);
    }

    /** 获取地址选择初始数据 */
    addressRef.current?.addressInit?.();
    form.setFieldsValue({ frontIsInit: true });
  }

  /** 处理出库单回显数据 */
  async function asyncFullDetailInfo(data: TypeOutboundFormData) {
    if (isUpdate === false) {
      /** 非编辑页面需要额外加工处理数据 */
      delete data.id;
      delete data.podOrderNo;
      delete data.orderSource;
      delete data.express?.deliveryList?.[0]?.id;
      delete data.deliveryInfo?.id;
      delete data.userName;
      (data.goodsList || []).forEach((item) => {
        /** 移除商品列表id */
        delete item.id;
      });
      (data.deliveryInfo?.palletSizeList || []).forEach((item) => {
        /** 托盘尺寸非编辑页面移除主键索引数据 */
        delete item.palletId;
        delete item.deliveryId;
      });
      (data.express?.deliveryList?.[0]?.fileList || []).forEach((item) => {
        /** 移除上传文件的id */
        delete item.id;
      });
    }

    const { express, ...rest } = data || {};
    const goodsList = data?.goodsList || [];
    let nextGoodsList: any[] = [];

    const nextExpress = {
      ...express,
      recipientIdType: transString(express?.recipientIdType),
    };

    /** 设置国家、省、城市的内容,无论接口是否返回国家数据都需要设置，否则会使用表单设置的初始值 */
    if (express?.recipientAddressList?.length === 0) {
      nextExpress.recipientAddressList = [''];
    }
    if (express?.recipientPhoneNumberList?.length === 0) {
      nextExpress.recipientPhoneNumberList = [''];
    }
    // 商品列表传值时是没有仓库信息，由于商品可能要和订单的仓库信息一致，所以手动set
    /** 很难理解, 门户商品的仓库信息需要前端去组合拼接 */
    if (goodsList.length && data.createDeliveryWarehouseId) {
      nextGoodsList = goodsList.map((o) => ({
        ...o,
        warehouseIdSku: `${data.createDeliveryWarehouseId}-${o.sku}`,
        warehouseId: data.createDeliveryWarehouseId,
        warehouseName: data.createDeliveryWarehouseName,
        warehouseCode: data.createDeliveryWarehouseCode,
        warehouseType: data.createDeliveryWarehouseType,
      }));
    }

    /** 草稿单需要更新商品信息 */
    if (isCopy || isDraft) {
      nextGoodsList = await asyncGoodsInfoRefresh(nextGoodsList, express?.insuredAmountCurrency);
    }

    setAllowBatchGoods(isAllowBatchGoods(data?.businessType));
    form.setFieldsValue({
      ...rest,
      goodsList: nextGoodsList,
      express: nextExpress,
    });

    asyncGetWarehouseList(data?.companyId);
  }

  /** 添加商品功能 */
  const handleGoodsSelect = (records: TypeOutboundGoodsTB[]) => {
    const { goodsList = [], businessType } = form.getFieldsValue();
    const isFirstGoodsItem = goodsList.length === 0;
    const newRecords = (records || [])?.map((item) => ({
      ...item,
      totalQuantity: 1 /** 默认1件 */,
    }));

    /** 是否支持批量多商品 */
    const allowBatch = isAllowBatchGoods(businessType);
    const newGoodsList = uniqBy(
      [...(allowBatch ? goodsList : []), ...newRecords],
      'warehouseIdSku',
    );
    const record = records[0] || {};
    const supportSmall = newGoodsList.some((item) => item.supportSmall === true);

    form.setFieldsValue({
      goodsList: newGoodsList,
      salesCurrency: record?.warehouseCurrency,
      express: {
        insuredAmountCurrency:
          record?.portalGoodsCustomValueResp?.currency || record?.warehouseCurrency,
      },
      wmsSystemCode: record.wmsSystemCode,
      createDeliveryWarehouseId: record.warehouseId,
      supportSmall,
      ...findWarehouseInfo(record.warehouseId, warehouseList),
    });
    isFirstGoodsItem && goodsRelationFieldClear(form);
  };

  // 删除已选择商品，如果删除的是最后一条，并且存在物流信息，提醒
  function handleGoodsRemove(record: TypeOutboundGoodsTB) {
    const goodsList: TypeOutboundGoodsTB[] = form.getFieldValue('goodsList') || [];
    const newGoodsList = goodsList.filter((item) => record.warehouseIdSku !== item.warehouseIdSku);
    const supportSmall = newGoodsList.some((item) => item.supportSmall === true);
    const isLastGoodsItem = goodsList.length === 1;

    /** 无商品则需要清除仓库信息 */
    function clearWarehouseInfo() {
      form.setFieldsValue({
        createDeliveryWarehouseId: undefined,
        /** 清除仓库关联币种 */
        salesCurrency: undefined,
        /** 投保服务金额和币种 */
        express: {
          insuredAmount: undefined,
          insuredAmountCurrency: undefined,
        },
      });
    }

    function addCacheGoodsList(item: TypeOutboundGoodsTB) {
      item.id && cacheDeleteMapRef.current.cacheGoodsList.push({ ...item, deleted: true });
    }

    if (isLastGoodsItem) {
      clearWarehouseInfo();
      goodsRelationFieldClear(form);
    }
    addCacheGoodsList(record);

    form.setFieldsValue({
      goodsList: newGoodsList,
      supportSmall,
    });
    form.validateFields([['express', 'insured'], ['goodsList']]);
  }

  const rewriteData = (data: TypeOutboundFormData) => {
    const { cacheGoodsList } = cacheDeleteMapRef.current;
    const { express, goodsList, deliveryInfo, ...rest } = data;

    const newGoodsList: TypeOutboundFormData['goodsList'] = (goodsList || []).map((o) => ({
      ...o,
      totalQuantity: allowBatchGoods ? o.totalQuantity || undefined : 1,
      goodsType: 1 /** 商品 */,
    }));

    const result = {
      ...data,
      express: {
        ...express,
        deliveryList: [
          {
            ...deliveryInfo,
            ...fileListMerge(data),
            estimatedPickupDate: dateFormatter(deliveryInfo?.estimatedPickupDate),
          },
        ] as [NsOutbound.TypeDeliveryInfo],
        recipientAddressList: express?.recipientAddressList?.filter(Boolean),
        recipientPhoneNumberList: express?.recipientPhoneNumberList?.filter(Boolean),
      },
      goodsList: newGoodsList.concat(cacheGoodsList),
      orderSourcePlatformName: dictTypeMap.orderSourcePlatform[data.orderSourcePlatformCode!],
      ...findWarehouseInfo(data.createDeliveryWarehouseId, warehouseList),
    };

    return result;
  };

  const onFinish = async (values: any) => {
    const data = rewriteData(values);

    try {
      setNewPageLoading(true);
      await apiOutWarehouseSubmit({
        ...data,
      });
    } finally {
      setNewPageLoading(false);
    }

    message.success('新增出库单成功');
    historyGoPrev();
  };

  const onFinishFailed = (errors: any) => {
    if (!errors) return null;

    const { name } = errors.errorFields[0];
    const keys = name.join('_');

    const labelNode = document.querySelector(`label[for="${keys}"]`);

    if (labelNode) {
      labelNode.scrollIntoView({ block: 'center' });
    } else {
      form.scrollToField(name);
    }

    message.error('请检查表单并修正红色框内信息');

    return true;
  };

  const handleSave = async () => {
    const values = form.getFieldsValue();

    try {
      setNewPageLoading(true);
      await apiOutWarehouseSaveDraft(rewriteData(values));
    } finally {
      setNewPageLoading(false);
    }

    message.success('暂存出库单成功');
    historyGoPrev();
  };

  return (
    <PageContainer title={false}>
      <Spin spinning={newPageLoading === true}>
        <Card>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              express: {
                recipientPhoneNumberList: [''],
                recipientAddressList: ['', ''],
                /** 既不是copy单也不是编辑单, 纯新建单默认美国 */
                recipientCountry: isCreate ? 'US' : undefined,
              },
              businessType: '201' /** 标准出库 */,
              // TODO OMS是否需要定义新的订单来源, 产品说暂时不用, 就当它来源门户
              orderSource: EnumOutOrderSource.portal,
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            onValuesChange={(changedValues, values) => {
              if (changedValues.businessType) {
                setAllowBatchGoods(isAllowBatchGoods(changedValues.businessType));
                if (values.goodsList?.length > 0) {
                  form.validateFields(['goodsList']);
                }
              }
            }}
          >
            {/* 出库单基础信息 */}
            <FormBaseInfo
              {...{
                form,
                isCreate,
                onChangeCompany(option) {
                  const goodsList: TypeOutboundGoodsTB[] = form.getFieldValue('goodsList') || [];

                  goodsList.forEach((item) => handleGoodsRemove(item));
                  asyncGetWarehouseList(option?.companyId);
                },
              }}
            />
            <CustomizeDivider title={'商品清单'} />
            <Item
              name="goodsList"
              rules={[
                {
                  validator: async (rule, value?: TypeOutboundGoodsTB[]) => {
                    if (!Array.isArray(value) || !value.length) {
                      throw new Error('商品清单必填');
                    }
                    if (allowBatchGoods === false && value?.length > 1) {
                      throw new Error('当前订单类型仅允许单件商品');
                    }
                    const businessType = form.getFieldValue('businessType');
                    const num = value.reduce((prev, item) => {
                      return prev + (item.totalQuantity || 0);
                    }, 0);

                    if (num > 40 && Number(businessType) === EnumOutboundBusinessType.normal) {
                      throw new Error('标准出库的商品总件数限制40及以内');
                    }
                  },
                },
              ]}
            >
              {/* 商品清单 */}
              <FormGoodsChoose
                onGoodsSelect={handleGoodsSelect}
                onGoodsRemove={handleGoodsRemove}
                warehouseList={warehouseList}
                allowBatch={allowBatchGoods}
                formIns={form}
              />
            </Item>
            <CustomizeDivider title={'物流服务'} />
            {/** 物流服务 */}
            <FormLogisticService
              {...{
                form,
              }}
            />
            <CustomizeDivider title={'收件人信息'} />
            <FormRecipientAddress form={form} ref={addressRef} mode="createOrEdit" />
            <RowLayout columnNum={4}>
              <Item label={'收货人证件类型及证件号'}>
                <div style={{ display: 'flex' }}>
                  <Item name={['express', 'recipientIdType']} noStyle>
                    <SearchSelect
                      {...{
                        allowClear: false,
                        style: { width: 100 },
                        request: apiMapDictType.certificateType,
                      }}
                    />
                  </Item>
                  <Item
                    name={['express', 'recipientIdNo']}
                    noStyle
                    rules={[
                      {
                        max: 30,
                        message: '长度不能超过30位',
                      },
                      ruleUtils.ruleNumLetterHyphen,
                    ]}
                  >
                    <Input placeholder={'请输入证件号'} />
                  </Item>
                </div>
              </Item>
              <Item
                name={['express', 'recipientEoriNo']}
                label={'收件人EORI号'}
                rules={[
                  {
                    max: 30,
                  },
                ]}
              >
                <Input placeholder={'请输入收件人EORI号'} />
              </Item>
              <Item
                name={['express', 'recipientCompanyName']}
                label={'收件人公司名称'}
                rules={[
                  {
                    max: 50,
                  },
                ]}
              >
                <Input placeholder={'请输入收件人公司名称'} />
              </Item>
            </RowLayout>
            {/* 附加信息 */}
            <CustomizeDivider title={'附加信息'} />
            <FormAdditionalInfo {...{ form }} />
          </Form>
        </Card>

        {costCalcModalNode}
        <FooterToolbar
          style={{ textAlign: 'center' }}
          extra={
            <Space>
              <Button onClick={() => historyGoPrev()}>{'返回'}</Button>
              {
                // 新增和待提交的订单可以进行暂存（新增的暂存走草稿接口、暂存订单走编辑接口）
                isPortal ? (
                  <LoadButton type="default" loading={newPageLoading} onClick={handleSave}>
                    {'暂存'}
                  </LoadButton>
                ) : null
              }
              <Button type="primary" loading={newPageLoading} onClick={form.submit}>
                {'提交出库'}
              </Button>
              {isPortal && (
                <LoadButton
                  {...{
                    type: 'default',
                    loading: newPageLoading,
                    async onClick() {
                      setNewPageLoading(true);
                      await asyncCostCalc().finally(() => setNewPageLoading(false));
                    },
                  }}
                >
                  费用试算
                </LoadButton>
              )}
            </Space>
          }
        />
      </Spin>
    </PageContainer>
  );
}

export default OutboundEdit;

/** 编辑出库相关hook */
function useUpdate() {
  const query = historyGetQuery() as { id: string; orderStatus: string | number };
  const { id } = query;
  const orderStatus = Number(query.orderStatus);
  const { pathname } = history.location;
  const [isUpdate] = useState(pathname.endsWith('-edit'));
  /** 是否为草稿单 */
  const isDraft = isUpdate && orderStatus === 200;
  /** 复制单判断逻辑:  不为编辑单, 且id存在 */
  const isCopy = isUpdate === false && Boolean(id);
  /** 判断纯新建单子 */
  const isCreate = isUpdate === false && id === undefined;

  return {
    isUpdate,
    isDraft,
    isCopy,
    isCreate,
    id,
  };
}

/** 查找指定仓库信息 */
function findWarehouseInfo(warehouseId: string | undefined, warehouseList: any[]) {
  if (!warehouseId) {
    return {
      deliveryWarehouseId: undefined,
      deliveryWarehouseName: undefined,
      deliveryWarehouseCode: undefined,
      deliveryWarehouseType: undefined,
    };
  }
  const target = warehouseList.find((o) => o.id === warehouseId) || undefined;

  if (!target) {
    console.error(
      `仓库列表找不到此仓库${warehouseId}--->`,
      warehouseList,
      '-->可能是后端存在脏数据',
    );
    return undefined;
  }

  return {
    deliveryWarehouseId: target.id,
    deliveryWarehouseName: target.name,
    deliveryWarehouseCode: target.code,
    deliveryWarehouseType: target.warehouseType,
  };
}

/** 商品变化时关联清除字段 */
export function goodsRelationFieldClear(form: FormInstance) {
  form.setFieldsValue({
    deliveryInfo: {
      dispatchServiceTypeInfo: undefined,
      dispatchServiceType: undefined,
      customerChannelCode: undefined,
      dispatchServiceNameInfo: null,
      dispatchServiceName: null,
    },
  });
}

/** 是否允许批量商品 */
function isAllowBatchGoods(businessType?: any) {
  // return `${businessType}` === '203'; /** TOB出库订单支持 */
  return true; /** 所有类型订单都支持多商品 */
}

/** 获取仓库Options */
function useOutWarehouseOptions() {
  const [warehouseList, setWarehouseList] = useState<any[]>([]);

  function asyncGetWarehouseList(companyId?: string) {
    if (!companyId && isOMS) {
      setWarehouseList([]);
      return [];
    }
    return apiMixWarehouseOptions({ warehouseEnable: true, companyId }, { valueName: 'id' }).then(
      (records) => {
        setWarehouseList(records);
        return records;
      },
    );
  }

  useEffect(() => {
    asyncGetWarehouseList();
  }, []);
  return { warehouseList, asyncGetWarehouseList };
}
