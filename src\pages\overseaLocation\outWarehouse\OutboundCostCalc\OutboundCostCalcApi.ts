import { request } from 'umi';
import { transOutboundFormData } from '../OutboundApi';

export type TypeCostCalcResult = {
  /** 总金额 */
  total?: number;
  /** 结算币种 */
  currency?: string;
  /** 试算费用项集合 */
  chargeList?: {
    /** 费用code */
    chargeCode?: string;
    /** 费用名称 */
    chargeName?: string;
    /** 结算金额 */
    amount?: number;
    /** 结算币种 */
    currency?: string;
  }[];
};

/** 出库尾程费用试算 */
export function apiOutboundCostCalc(data: TypeOutboundFormData) {
  return request<NsApi.TypeResponseData<TypeCostCalcResult>>(
    '/zouwu-oms-order/portal/outbound/order/charge/express/quotation',
    {
      data: transOutboundFormData(data),
      method: 'POST',
    },
  );
}
