module.exports = () => {
  return {
    /** 域名：优先取工作区缓存的域名(登录成功的域名) */
    host: 'http://*************:3000',
    /** banner 头部内容，可以填写导入的请求实例等 */
    banner: '',
    /** 生成 res 包含的属性，默认 all, 可指定为 data、custom
     * 'all' | 'data' | 'custom'
     */
    responseKey: 'all',
    /** 生成 res 指定的属性值，仅当 responseKey 选择 custom 是有效，默认 data, 可指定为任意 key(支持链式：data.result) */
    responseCustomKey: 'data',
    /** resDataTypeContent 放置的位置是外层的 Promise<T> 还是作为请求方法的泛型 post<T>
     * 'outerFunction' | 'fetchMethodGeneric'
     */
    responseTypePosition: 'null',
    /** 开启自动格式化 */
    format: false,
    /** 缩进使用 tab，或者 双空格 */
    useTab: false,
    /** 自定义生成 request 方法 */
    genRequest(inParams, yApiData) {
      const { comment, fnName, IReqTypeName, IResTypeName, requestMethod, apiPath } = inParams;
      const { title } = yApiData;
      const params = IReqTypeName ? `data: ${IReqTypeName}` : '';
      const dataContent = IResTypeName ? 'data,' : '';
      const newFnName = `${fnName[0].toUpperCase()}${fnName.slice(1)}`;
      return `
/** ${title} */
export async function api${newFnName}(${params}) {
  return request('${apiPath}', {method: '${requestMethod}', ${dataContent} });
}`;
    },
  };
};
