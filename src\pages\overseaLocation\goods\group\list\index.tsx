import React, { useRef } from 'react';
import type { ConnectRC, Dispatch } from 'umi';
import { connect } from 'umi';
import { Button, Divider, message, Popconfirm } from 'antd';
import { forIn } from 'lodash';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { HiveModule } from '@portal/hive-sdk';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import GoodsGroupBlongImg from '../../components/GoodsGroupBelongImg';
import styles from './index.less';
import GoodsExpandedTable from '../../components/GoodsExpandedTable';
import GoodsImage from '@/components/GoodsImage';
import { HumpToUnderline } from '@/utils/util';
import { useSensors } from '@/hooks/useSensors';
import { apiGoodsCombineDelete, apiGoodsCombineList } from '../groupApi';

type Iprops = {
  dispatch: Dispatch;
};

const OverseaGoodsGroupListPage: ConnectRC<Iprops> = ({ dispatch }) => {
  const tableRef = useRef<any>();
  const formRef = useRef<any>();
  const { trackerSend } = useSensors({
    midfix: 'OL_GOODS_GROUP',
  });

  const columns: ProColumns[] = [
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__Group__List.Index.mainDrawing,
      key: 'pictureUrl',
      dataIndex: 'pictureUrl',
      render: (_: any, record: any) => {
        return <GoodsImage src={record.pictureUrl} />;
      },
      width: 90,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__Group__Action.Index.belongingTo,
      key: 'goodsList',
      dataIndex: 'goodsList',
      className: 'column-label-blong-sku',
      search: false,
      width: 220,
      render: (_: any, record: any) => {
        return <GoodsGroupBlongImg goodsList={record?.goodsList || []} />;
      },
    },
    {
      title: I18N.Src__Pages__Freight__Lcl.List.name,
      key: 'name',
      dataIndex: 'name',
      ellipsis: true,
      width: 240,
      render: (_: any) => {
        return <div className="goods-name-box">{_}</div>;
      },
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      width: 220,
      render: (_: any) => <span style={{ color: '#1890FF' }}>{_}</span>,
    },
    {
      title: I18N.Src__Pages__Order__Detail.SiItem.remarks,
      key: 'comment',
      dataIndex: 'comment',
      search: false,
      ellipsis: true,
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      key: 'option',
      width: 120,
      fixed: 'right',
      render: (_: any, record: any) => (
        <>
          <a
            onClick={() =>
              HiveModule.history.push({
                path: '/oversea-location/goods/group/action',
                search: { id: record.id },
              })
            }
          >
            {I18N.Src__Pages__Common__Template.Index.edit}
          </a>
          <Divider type="vertical" />
          {/* 因下游无法删除商品, 隐藏该功能 */}
          {/* <Popconfirm
            title="确定删除？"
            onConfirm={() => handleDelete(record.id)}
          >
            <a>删除</a>
          </Popconfirm> */}
        </>
      ),
      search: false,
    },
  ];

  const handleDelete = async (id: string) => {
    // const result = await dispatch({
    //   type: 'goodsGroup/deleteOverseaLocationGoodsGroup',
    //   payload: { id },
    // });

    // if (result) {
    //   tableRef.current.reload();
    //   message.success(I18N.Src__Pages__Common__Template.Index.deletionSucceeded);
    // }

    await apiGoodsCombineDelete({ id });

    tableRef.current.reload();
    message.success(I18N.Src__Pages__Common__Template.Index.deletionSucceeded);
  };

  const expandedRowRender = (record: any, index: number, indent: any, expanded: any) => {
    return (
      <div className="custom-expand-box">
        <GoodsExpandedTable goodsList={record?.goodsList} />
      </div>
    );
  };

  return (
    <PageContainer>
      <ProTable
        request={async (params) => {
          const { pageSize, current: currentPage, ...args } = params;
          const query = {
            currentPage,
            pageSize,
            condition: {
              ...args,
            },
          } as Parameters<typeof apiGoodsCombineList>[0];

          //   const result = await dispatch({
          //     type: 'goodsGroup/getOverseaLocationGoodsGroupList',
          //     payload: {
          //       currentPage,
          //       pageSize,
          //       condition: {
          //         ...args,
          //       },
          //     },
          //   });

          const { data } = await apiGoodsCombineList(query);

          const sensorsData: any = {};

          forIn(params, (value, key) => {
            sensorsData[HumpToUnderline(key)] = value;
          });

          trackerSend({
            name: 'SEARCH_LIST',
            data: sensorsData,
          });

          //   return result;
          return {
            data: data.records,
            success: true,
            total: data.totalSize,
          };
        }}
        columns={columns}
        rowKey="id"
        actionRef={tableRef}
        formRef={formRef}
        // scroll={{ x: 'max-content' }}
        expandable={{ expandedRowRender }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          position: ['bottomLeft'],
        }}
        tableClassName={styles['goods-group-list-table']}
        headerTitle={
          <Button
            type="primary"
            onClick={() => {
              trackerSend({
                name: 'CLICK_ADD',
              });

              HiveModule.history.push('/oversea-location/goods/group/action');
            }}
          >
            {I18N.Src__Pages__OverseaLocation__Goods__Group__List.Index.newlyAdded}
          </Button>
        }
      />
    </PageContainer>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.models.goodsGroup,
}))(OverseaGoodsGroupListPage);
