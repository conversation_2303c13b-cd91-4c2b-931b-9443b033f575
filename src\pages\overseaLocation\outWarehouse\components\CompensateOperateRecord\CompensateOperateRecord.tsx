import I18N from '@/utils/I18N';
import React, { useRef } from 'react';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';
import { AmountMoney, FormProCard } from '@/views';
import { includesInArray } from '@/utils';

type TypeProps = {
  detailData?: TypeOutboundCompensateApplyDetail;
};

/** 索赔记录 */
function CompensateOperateRecord(props: TypeProps) {
  const { config } = useConfig({ props });

  return (
    <FormProCard
      title={
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols5
      }
    >
      <ProTable {...config} />
    </FormProCard>
  );
}

export default CompensateOperateRecord;

function useConfig({ props }: { props: TypeProps }) {
  const actionRef = useRef<ActionType>();
  const { columns } = useColumns();
  const config = {
    rowKey: 'logId',
    dataSource: props.detailData?.compensateRecord,
    columns,
    actionRef,
    search: false,
    cardProps: false,
    toolBarRender: false,
    pagination: false,
    scroll: { x: 'max-content' },
  } as ProTableProps<any, any>;

  return {
    config,
  };
}

type TypeOutboundCompensateRecord = NonNullable<
  TypeOutboundCompensateApplyDetail['compensateRecord']
>[number];

function useColumns() {
  const columns = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols4,
      dataIndex: 'compensateStatusName',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols3,
      dataIndex: 'applyCompensateAmount',
      renderText(text, record) {
        return moneyFormatter(text, record.currency);
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols2,
      dataIndex: 'actualCompensateAmount',
      renderText(text, record) {
        return moneyFormatter(text, record.currency);
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols1,
      dataIndex: 'remark',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols,
      dataIndex: 'fileList',
      render(dom, record) {
        const fileList = record.fileList || [];
        const node = (fileList.length > 0 ? fileList : [undefined]).map((file) => {
          if (file === undefined) {
            return '-';
          }

          return (
            <div key={file.filePath}>
              {file.filePath && (
                <a target="_blank" download href={file.filePath}>
                  {file.fileName}
                </a>
              )}
            </div>
          );
        });

        return <div>{node}</div>;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.operationTime,
      dataIndex: 'operateTime',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.operator,
      dataIndex: 'operatorName',
    },
  ] as ProColumns<TypeOutboundCompensateRecord>[];

  return { columns };
}

function moneyFormatter(money: number | undefined, currency: string | undefined) {
  const fMoney = includesInArray([undefined, null, ''], money)
    ? '-'
    : AmountMoney.formatMoney(money);

  return `${fMoney} ${currency || '-'}`;
}
CompensateOperateRecord.moneyFormatter = moneyFormatter;
