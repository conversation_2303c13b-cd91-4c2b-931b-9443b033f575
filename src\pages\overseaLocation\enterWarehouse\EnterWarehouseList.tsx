import React, { useState, useEffect, useRef } from 'react';
import type { Dispatch } from '@umijs/max';
import { history, connect } from '@umijs/max';
import ProForm, {
  QueryFilter,
  ProFormText,
  ProFormSelect,
  ProFormDateRangePicker,
} from '@ant-design/pro-form';
import moment from 'moment';
import classnames from 'classnames';
import { HiveModule, Link } from '@portal/hive-sdk';
import {
  Spin,
  Card,
  Button,
  Pagination,
  Tabs,
  Row,
  Col,
  Empty,
  Modal,
  message,
  Space,
  Form,
  Divider,
  Tooltip,
} from 'antd';
import { cloneDeep, forIn } from 'lodash';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import {
  apiMapDictType,
  apiQueryOverseaContainerBox,
  apiQueryWarehouseOptions,
} from '@/pages/overseaLocation/api';

import {
  ENTER_WAREHOUSE_OPTION_DESC,
  WAREHOUSE_BUSINESS_TYPE,
  LOGISTICS_PROVIDER_TYPE,
} from '@/utils/const';
import PortSteps from '@/components/PortSteps';
import { HumpToUnderline } from '@/utils/util';
import styles from './EnterWarehouseList.less';
import { dateUtils, eR, tableUtils } from '@/utils';
import type { StateType } from './model';
import { AsyncImportButton, EnumImportType } from '@/views';
import type { SorterResult } from 'antd/es/table/interface';
import { TypeChannel } from './action/components/LogisticsInfo';
import { historyGoChild, includesInArray, useModalSet } from '@/pages/overseaLocation/utils';
import { LoadButton, ZSearchSelect } from '@/pages/overseaLocation/components';
import { useSensors } from '@/hooks/useSensors';
import {
  apiEnterWarehouseList,
  apiInboundBackDraft,
  apiInboundBoxMarkDownload,
  apiInboundDraftDelete,
  apiInboundGetTeamWorkCode,
  apiInboundOrderCancel,
  apiInboundReserveDetail,
} from './enterWarehouseApi';
import { ModalReserveDelivery } from './components';
import {
  EnumInboundBusinessType,
  EnumInboundTabKey,
  INBOUND_TAB_OPTIONS,
  inboundOrderStatusItemMap,
} from './inboundEnum';
import { hasBtnCancel } from './detail';

interface IProps extends StateType {
  dispatch: Dispatch;
  pageLoading: boolean;
}

const TO_BE_SUBMIT_STATUS = 100;

const EnterWarehouseListPage: React.FC<IProps> = ({ dispatch }) => {
  const [pageLoading, setPageLoading] = useState(false);
  const [operateLoading, setOperateLoading] = useState(false);
  const [
    { entryWarehouseList, entryWarehouseTotal, entryWarehouseStatusCount },
    setEntryWarehouseData,
  ] = useState<{
    entryWarehouseList: TypeEnterWarehouseTB[];
    entryWarehouseTotal: number;
    entryWarehouseStatusCount: Record<string, number>;
  }>({
    entryWarehouseList: [],
    entryWarehouseTotal: 0,
    entryWarehouseStatusCount: {},
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [searchValues, setSearchValues] = useState({});
  const [activeKey, setActiveKey] = useState('TOTAL');
  const [searchTrigger, setSearchTrigger] = useState(1);
  const { sorterRef, setSorter } = useTableSorter();
  const { modalActions, modalNodeList } = useModalSet({
    reserveDelivery: ModalReserveDelivery,
  });
  const { trackerSend } = useSensors({
    midfix: 'OL_ENTER',
  });
  const { cssTableStickyHasTab } = tableUtils.useTableSticky();

  useEffect(() => {
    asyncHandleSearchList();
  }, [activeKey, searchTrigger]);

  /** 查询入库列表 */
  async function asyncHandleSearchList() {
    setPageLoading(true);
    try {
      const params = {
        currentPage,
        pageSize,
        condition: {
          ...searchValues,
          ...sorterRef.current,
          tabKey: activeKey,
        },
      };

      // if (activeKey !== '-1') {
      //   searchValues.orderStatus = activeKey.includes(',') ? activeKey.split(',') : activeKey;
      // }

      console.log(activeKey, 'activeKey');
      const { data } = await apiEnterWarehouseList(params);
      const { records, countStatus } = data;

      setEntryWarehouseData({
        entryWarehouseList: records,
        entryWarehouseTotal: Number(countStatus[activeKey]),
        entryWarehouseStatusCount: countStatus,
      });

      trackerSend({
        name: 'SEARCH_LIST',
        data: params,
      });
    } finally {
      setPageLoading(false);
    }
  }

  const handleCancelRecord = async (record: TypeEnterWarehouseTB) => {
    const title =
      record.orderStatus === TO_BE_SUBMIT_STATUS
        ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.deleteOrder
        : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
            .wholeOrderCancellation;
    const defaultContent = I18N.template(
      I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.whetherToConfirm,
      {
        val1:
          record.orderStatus === TO_BE_SUBMIT_STATUS
            ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.delete
            : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.cancel,
      },
    );
    /** 预约送仓取消 */
    const reserveCannelContent =
      Number(record.teamWorkNoStatus) === 2 /** 预约单生效 */
        ? '车队已预约送仓，该操作将失效预约单，请线下通知车队和仓库。'
        : undefined;
    const content =
      record.orderStatus === 203
        ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.thisOrder
        : reserveCannelContent || defaultContent;

    Modal.confirm({
      title,
      content,
      onOk: async () => {
        // const result = await dispatch({
        //   type:
        //     record.orderStatus === TO_BE_SUBMIT_STATUS
        //       ? 'entry_warehouse/deleteEnterBoundOrder'
        //       : 'entry_warehouse/cancelEnterBoundOrder',
        //   payload: { orderId: record.id },
        // });

        const result = await (record.orderStatus === TO_BE_SUBMIT_STATUS
          ? apiInboundDraftDelete
          : apiInboundOrderCancel)({ orderId: record.id });

        if (result) {
          sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_OPTION_BUTTON_CLICK', {
            option_type: 'delete',
            current_click_page:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.list,
            order_status: record.orderStatus,
            oversea_pod_no: record.podOrderNo,
          });

          message.success(
            I18N.template(
              I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.success,
              { val1: title },
            ),
          );
          asyncHandleSearchList();
        }
      },
    });
  };

  const handleEditRecord = (record: any) => {
    // 入库列表操作栏点击事件
    sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_OPTION_BUTTON_CLICK', {
      option_type: 'edit',
      current_click_page: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.list,
      order_status: record.orderStatus,
      oversea_pod_no: record.podOrderNo,
    });

    historyGoChild({
      newTab: true,
      pathname: './action',
      query: {
        id: record.id,
      },
    });
    // HiveModule.history.open({
    //   path: '/oversea-location/enter-warehouse/action',
    //   search: {
    //     id: record.id,
    //   },
    // });
  };

  const handleTableSearch = async (values: any) => {
    const nextValues = cloneDeep(values);

    // ETA
    if (nextValues.expectTime && nextValues.expectTime.length) {
      nextValues.startExpectTime = dateUtils.transformDate(nextValues.expectTime[0], 'START');
      nextValues.endExpectTime = dateUtils.transformDate(nextValues.expectTime[1], 'END');

      delete nextValues.expectTime;
    }
    // 创建日期
    if (nextValues.createDate && nextValues.createDate.length) {
      nextValues.startCreateDate = dateUtils.transformDate(nextValues.createDate[0], 'START');
      nextValues.endCreateDate = dateUtils.transformDate(nextValues.createDate[1], 'END');

      delete nextValues.createDate;
    }
    // ETD
    if (nextValues.expectSendTime && nextValues.expectSendTime.length) {
      nextValues.startExpectSendTime = dateUtils.transformDate(
        nextValues.expectSendTime[0],
        'START',
      );
      nextValues.endExpectSendTime = dateUtils.transformDate(nextValues.expectSendTime[1], 'END');

      delete nextValues.expectSendTime;
    }
    // 签收时间
    if (nextValues.receivedTime && nextValues.receivedTime.length) {
      nextValues.startReceivedTime = dateUtils.transformDate(nextValues.receivedTime[0], 'START');
      nextValues.endReceivedTime = dateUtils.transformDate(nextValues.receivedTime[1], 'END');

      delete nextValues.receivedTime;
    }

    setCurrentPage(1);
    setSearchValues(nextValues);
    setSearchTrigger(searchTrigger + 1);

    const sensorsData: any = {};

    forIn(nextValues, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    trackerSend({
      name: 'LIST_SEARCH',
      data: sensorsData,
    });
  };

  const handleStepModeChange = (type: string, record: any) => {
    sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_LOGISTIC_CLICK', {
      current_click_page: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.list,
      order_status: record.orderStatus,
      oversea_pod_no: record.podOrderNo,
      logistic_type: type,
    });
  };

  const renderRecordActionButtons = (record: TypeEnterWarehouseTB) => {
    /** 复制功能, 所有状态都可以复制 */
    const hasCopy = (() => true)();

    /** 编辑按钮 */
    const hasEdit = (item: TypeEnterWarehouseTB) => {
      const currentStatus = Number(item.orderStatus);

      /** 草稿阶段, 仅草稿允许编辑 */
      if ([100, 101].includes(currentStatus)) {
        return true;
      }
      // /** 产品暂时定义tob入库非草稿单禁止编辑, 也禁止取消 */
      // if (
      //   [102, 103, 105].includes(currentStatus) &&
      //   item.businessType?.toString() !== '105' /** TOB入库  */
      // ) {
      //   return true;
      // }

      return false;
    };
    /** 删除 */
    const hasDelete = (item: TypeEnterWarehouseTB) => {
      const currentStatus = Number(item.orderStatus);

      if ([100].includes(currentStatus)) {
        return true;
      }
      return false;
    };

    // /** 整单取消 */
    // const hasCancel = (item: TypeEnterWarehouseTB) => {
    //   const currentStatus = Number(item.orderStatus);
    //   const { orderStatusTabKey } = item;

    //   if ([101].includes(currentStatus)) {
    //     return true;
    //   }
    //   if (
    //     // [102, 103, 105].includes(currentStatus) &&
    //     //  && item.businessType?.toString?.() !== '105' /** TOB入库 */
    //     includesInArray([EnumInboundTabKey.SHIPPING], orderStatusTabKey)
    //   ) {
    //     return true;
    //   }

    //   return false;
    // };
    /** 是否可以预约送仓 */
    const hasBtnReserveDelivery = record?.ableToDeliveryAppointment === true;
    /** 是否有预约送仓详情,  运输中和有单号时始终展示 */
    const hasBtnReserveDeliveryDetail =
      record.orderStatusTabKey === 'SHIPPING' || record.teamWorkNo;
    /** 是否有退回草稿按钮 */
    const hasBtnBackDraft =
      includesInArray(
        [EnumInboundBusinessType.FIRST_LEG, EnumInboundBusinessType.DROP_SHIPPING_FIRST_LEG],
        record.businessType,
      ) && record.orderStatusTabKey === EnumInboundTabKey.SHIPPING;

    return (
      <Space
        {...{
          wrap: true,
          split: <Divider type="vertical" />,
          size: [0, 12],
          style: { maxWidth: 120 },
        }}
      >
        <LoadButton
          {...{
            async onClick() {
              await apiInboundBoxMarkDownload({ podOrderNo: record?.podOrderNo });
            },
          }}
        >
          {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.chineseSymbols7}
        </LoadButton>
        {hasBtnReserveDelivery && (
          <a
            onClick={async () => {
              setOperateLoading(true);
              try {
                const { data } = await apiInboundReserveDetail({ podOrderNo: record?.podOrderNo });

                if (data?.isWWLCompliance) {
                  Modal.info({
                    title:
                      I18N
                        .Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                        .ModalReserveDelivery.chineseSymbols15,
                    content:
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                        .chineseSymbols5,
                    onOk() {
                      asyncHandleSearchList();
                    },
                  });
                  return;
                }

                modalActions.reserveDelivery?.open(data, {
                  submitSuccessCB() {
                    asyncHandleSearchList();
                  },
                });
              } finally {
                setOperateLoading(false);
              }
            }}
          >
            {
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Components__ModalReserveDelivery
                .ModalReserveDelivery.chineseSymbols15
            }
          </a>
        )}
        {hasBtnReserveDeliveryDetail && (
          <Tooltip
            {...{
              placement: 'topRight',
              title:
                !record.teamWorkNo &&
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.chineseSymbols4,
            }}
          >
            {/* button外包一层span可以避免disabled对Tooltip产生影响 */}
            <span>
              <LoadButton
                disabled={!record.teamWorkNo}
                onClick={async () => {
                  const { data: code } = await apiInboundGetTeamWorkCode({
                    teamWorkNo: record.teamWorkNo,
                  });
                  const a = document.createElement('a');

                  a.target = '_blank';
                  a.href = `/wwl-oms-front/#/supplier-collaboration/oversea-warehouse/sso?code=${code}&mode=detail`;
                  a.click();
                }}
              >
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                    .chineseSymbols3
                }
              </LoadButton>
            </span>
          </Tooltip>
        )}
        {hasCopy && (
          <a
            // to={`/oversea-location/enter-warehouse/action-create?id=${item.id}`}
            onClick={() => {
              historyGoChild({
                newTab: true,
                pathname: './action-create',
                query: { id: record.id },
              });
            }}
          >
            {I18N.Src__Pages__Order__Si__Component__ShareModals.Alert.copy}
          </a>
        )}
        {/* 编辑 */}
        {hasEdit(record) ? (
          <a onClick={() => handleEditRecord(record)}>
            {I18N.Src__Pages__Common__Template.Index.edit}
          </a>
        ) : null}
        {/* 删除 */}
        {hasDelete(record) ? (
          <a onClick={() => handleCancelRecord(record)}>
            {I18N.Src__Pages__Common__Template.Index.delete}
          </a>
        ) : null}
        {/* 取消 */}
        {hasBtnCancel(record) ? (
          <a onClick={() => handleCancelRecord(record)}>
            {
              I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                .wholeOrderCancellation
            }
          </a>
        ) : null}
        {hasBtnBackDraft && (
          <LoadButton
            {...{
              popconfirmProps: {
                title: '确定要撤回到草稿吗？',
              },
              async onClick() {
                await apiInboundBackDraft({ podOrderNo: record.podOrderNo });
                message.success('撤回成功');
                asyncHandleSearchList();
              },
              children: '撤回到草稿',
            }}
          />
        )}
      </Space>
    );
  };
  const { columns } = useColumns({ renderRecordActionButtons });

  return (
    <PageContainer
      title={false}
      content={
        <Card bordered={false} bodyStyle={{ padding: '20px 0 0 0' }} className={styles.header}>
          <QueryFilter
            // span={6}
            labelWidth={100}
            defaultCollapsed={false}
            onFinish={handleTableSearch}
            onReset={() => {
              setSearchValues({});
              setActiveKey('TOTAL');
              setCurrentPage(1);
              setSearchTrigger(searchTrigger + 1);
            }}
          >
            <ProFormText
              name="sku"
              label={I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.commodity}
              placeholder={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                  .pleaseEnterTheSupplier
              }
            />
            <ProFormText
              name="fuzzyNo"
              label={I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.oddNumbers}
              placeholder={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                  .pleaseEnterTheForm
              }
            />
            <ProFormSelect
              name="businessType"
              label={I18N.Src__Pages__Home.Index.businessType}
              options={WAREHOUSE_BUSINESS_TYPE}
              mode="multiple"
              showSearch={false}
            />
            <ProForm.Item
              {...{
                name: 'warehouseId',
                label:
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                    .targetOrganization,
              }}
            >
              <ZSearchSelect
                style={{ width: '100%' }}
                {...{
                  request: async (queryParam: string) =>
                    apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
                  mode: 'multiple',
                }}
              />
            </ProForm.Item>
            {/* <ProFormText name="shippingMarkNo" label="箱唛号" placeholder="请输入箱唛号" /> */}

            <ProFormSelect
              name="logisticsProviders"
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.logisticsChannel
              }
              options={LOGISTICS_PROVIDER_TYPE}
              mode="multiple"
              showSearch={false}
            />
            <ProFormDateRangePicker
              name="createDate"
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.creationDate
              }
            />
            <ProFormDateRangePicker
              name="receivedTime"
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.signingTime
              }
            />
            {/* ETD */}
            <ProFormDateRangePicker name="expectSendTime" label="ETD" />
            {/* ETA */}
            <ProFormDateRangePicker name="expectTime" label="ETA" />
            <ProForm.Item
              {...{
                name: 'complianceTypeList',
                label:
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                    .chineseSymbols2,
              }}
            >
              <ZSearchSelect
                {...{
                  request: apiMapDictType.complianceType,
                  mode: 'multiple',
                }}
              />
            </ProForm.Item>
            <ProForm.Item
              {...{
                name: 'teamWorkStatusList',
                label:
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                    .chineseSymbols1,
              }}
            >
              <ZSearchSelect
                {...{
                  request: apiMapDictType.teamWorkStatus,
                  mode: 'multiple',
                }}
              />
            </ProForm.Item>
          </QueryFilter>
        </Card>
      }
    >
      <Spin spinning={pageLoading === true || operateLoading === true}>
        <div className={classnames(cssTableStickyHasTab, styles.container)}>
          <Space>
            <Button
              type="primary"
              onClick={() => {
                // 入库新增按钮点击埋点
                trackerSend({
                  name: 'CREATE_BUTTON_CLICK',
                  data: {},
                });

                historyGoChild({ newTab: true, pathname: './action-create' });
              }}
            >
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.createReceipt}
            </Button>
            <AsyncImportButton
              {...{
                importType: EnumImportType.inbound,
              }}
            >
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.chineseSymbols}
            </AsyncImportButton>
          </Space>
          <Tabs
            size="small"
            tabBarStyle={{
              padding: '0 20px',
              margin: 0,
            }}
            activeKey={activeKey}
            onChange={(key) => {
              setCurrentPage(1);
              setActiveKey(key);
              setSearchTrigger(searchTrigger + 1);
              trackerSend({
                name: 'TAB_CHANGE',
                data: {
                  toggle_tab: key,
                },
              });
            }}
          >
            {INBOUND_TAB_OPTIONS.filter((item) => item.hidden !== true).map((item) => (
              <Tabs.TabPane
                tab={
                  <div className={styles.tab}>
                    <span>
                      {item.label}
                      {entryWarehouseStatusCount[item.tabKey] > 0
                        ? `(${entryWarehouseStatusCount[item.tabKey]})`
                        : null}
                    </span>
                  </div>
                }
                key={item.tabKey}
              />
            ))}
          </Tabs>
          {modalNodeList}
          <ProTable<TypeEnterWarehouseTB>
            {...{
              tableLayout: 'auto',
              rowKey: 'id',
              search: false,
              options: false,
              cardProps: false,
              columnEmptyText: false,
              sticky: true,
              onChange(pagination, filter, sorter, extra) {
                if (extra.action === 'sort') {
                  setSorter(sorter as SorterResult<any>);
                  asyncHandleSearchList();
                }
              },
              pagination: {
                showTotal: undefined,
                position: ['bottomCenter'],
                current: currentPage,
                pageSize,
                showSizeChanger: true,
                total: entryWarehouseTotal,
                onChange: (page: number, size: number) => {
                  setCurrentPage((prevPage) => {
                    if (prevPage !== page) {
                      /** ProTable 有bug, 分页onChange会触发2次,改造为仅数字有变化在触发刷新 */
                      setSearchTrigger((prevSearchTrigger) => prevSearchTrigger + 1);
                    }
                    return page;
                  });
                  setPageSize((prevSize) => {
                    if (prevSize !== size) {
                      setSearchTrigger((prevSearchTrigger) => prevSearchTrigger + 1);
                    }
                    return size;
                  });
                  // setSearchTrigger(searchTrigger + 1);
                },
              },
              scroll: { x: 'max-content' },
              dataSource: entryWarehouseList,
              columns,
              expandable: {
                /** 只在初始化使用 */
                defaultExpandAllRows: true,
                expandIcon: () => null,
                expandedRowKeys: entryWarehouseList.map((item) => item.id),
                showExpandColumn: false,
                rowExpandable: (item) => (item.orderLogisticList || []).length > 0,
                expandedRowRender: (item) => {
                  return (
                    <ul className={styles.expand}>
                      {(item?.orderLogisticList || []).map((o, i) => (
                        <li className={styles.expandItem} key={`${o.logisticsNo}--${i.toString()}`}>
                          <div className={styles.row1}>
                            <span className={styles.no}>{o.logisticsNo}</span>
                            <span className={styles.type}>{o.logisticsProviderName}</span>
                          </div>
                          {/* {o.currentStatus ? (
                            <div className={styles.row2}>
                              <span className={styles.logisticsStatus}>{o.currentStatus}</span>
                            </div>
                          ) : null} */}
                          {o.nodeList && o.nodeList.length ? (
                            <div className={styles.stepWrap}>
                              <PortSteps
                                data={o.nodeList}
                                onModeToggleChange={(type) => handleStepModeChange(type, item)}
                              />
                            </div>
                          ) : null}
                        </li>
                      ))}
                    </ul>
                  );
                },
              },
            }}
          />
        </div>
      </Spin>
    </PageContainer>
  );
};

/** 手动获取箱型数据 */
function useGetBoxModal() {
  const [modalInfo, setModalInfo] = useState<Record<any, any>>([]);

  useEffect(() => {
    apiQueryOverseaContainerBox().then((res) => {
      const newInfo: Record<any, any> = {};

      res.forEach((item) => {
        newInfo[item.value] = item;
      });
      setModalInfo(newInfo);
    });
  }, []);

  return { modalInfo };
}

function useColumns({
  renderRecordActionButtons,
}: {
  renderRecordActionButtons: (item: any) => JSX.Element | null | undefined;
}) {
  const handleToDetailPage = (item: any) => {
    if (![100].includes(item.orderStatus)) {
      sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_OPTION_BUTTON_CLICK', {
        option_type: 'detail',
        order_status: item.orderStatus,
        oversea_pod_no: item.podOrderNo,
      });

      historyGoChild({ newTab: true, pathname: './detail', query: { id: item.id } });
      // HiveModule.history.push(`/oversea-location/enter-warehouse/detail?id=${item.id}`);
    }
  };
  const { modalInfo } = useGetBoxModal();

  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.applicationNo,
      dataIndex: 'podOrderId',
      render: (dom, record) => {
        return (
          <>
            <div
              className={styles.podNo}
              style={{ color: record.orderStatus === 100 ? '#999' : '#1890ff' }}
              onClick={() => handleToDetailPage(record)}
            >
              {record.podOrderNo}
            </div>
            <div className={styles.createDate}>
              {record.createDate ? moment(record.createDate).format('YYYY-MM-DD HH:mm') : null}
            </div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.state,
      dataIndex: 'orderStatus',
      align: 'center',
      render: (dom, record) => {
        return (
          <div
            className={styles.status}
            style={{
              backgroundColor: inboundOrderStatusItemMap[record.orderStatus!]?.color,
            }}
          >
            <span>{record.orderStatusName}</span>
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__Home.Index.businessType,
      dataIndex: 'businessTypeName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.logisticsChannel,
      dataIndex: 'logisticsProvider',
      render(dom, record) {
        return (
          <>
            <div>{record.logisticsProviderName}</div>
            <div>
              {record.logisticsProvider === 1 /** 海运 */ ? (
                <div>
                  <div>{record.containerNo}</div>
                  <div>{record.containerModel}</div>
                </div>
              ) : (
                record.logisticsNo
              )}
            </div>
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.receivingOrganization,
      dataIndex: 'warehouseId',
      render: (dom, record) => {
        return (
          <>
            <div>{record.targetWarehouseCode}</div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.requestedQuantity,
      dataIndex: 'totalQuantity',
      render: (dom, record) => {
        return (
          <>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                  .totalNumberOfBoxes
              }
              {record.boxQuantity}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.total}
              {record.totalQuantity}
            </div>
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.performanceQuantity,
      dataIndex: 'receivedQuantity',
      render: (dom, item) => {
        return (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.toBeImplemented}
              {item.notReceivedQuantity}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.executed}
              {item.receivedQuantity}
            </div>
          </>
        );
      },
    },
    {
      title: 'ETD',
      dataIndex: 'startExpectSendTime',
      render: (dom, item) => {
        return (
          <span>{item.expectSendTime ? moment(item.expectSendTime).format('YYYY-MM-DD') : ''}</span>
        );
      },
    },
    {
      title: 'ETA',
      dataIndex: 'expectTime',
      render: (dom, item) => {
        return <span>{item.expectTime ? moment(item.expectTime).format('YYYY-MM-DD') : ''}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.signingTime,
      dataIndex: 'receivedTime',
      sorter: true,
      render: (dom, item) => {
        return (
          <span>
            {item.receivedTime ? moment(item.receivedTime).format('YYYY-MM-DD HH:mm:ss') : ''}
          </span>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.chineseSymbols2,
      dataIndex: 'complianceTypeName',
      renderText(text) {
        return eR(text);
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.chineseSymbols1,
      dataIndex: 'teamWorkStatusName',
      renderText(text) {
        return eR(text);
      },
    },
    {
      title: I18N.Src__Pages__Order__Components__CoustomTable.Index.orderOperation,
      dataIndex: 'operate',
      fixed: 'right',
      align: 'left',
      width: 'auto',
      render: (dom, item) => renderRecordActionButtons(item),
    },
  ] as ProColumns<TypeEnterWarehouseTB>[];

  return { columns };
}

export default connect(
  ({ entry_warehouse, loading }: { entry_warehouse: StateType; loading: any }) => ({
    entryWarehouseList: entry_warehouse.entryWarehouseList,
    entryWarehouseTotal: entry_warehouse.entryWarehouseTotal,
    entryWarehouseStatusCount: entry_warehouse.entryWarehouseStatusCount,
    pageLoading: loading.models.entry_warehouse,
  }),
)(EnterWarehouseListPage);

/** 使用排序功能 */
function useTableSorter() {
  const sorterRef = useRef<Record<string, string | undefined>>({});
  const orderMap = {
    ascend: 'asc',
    descend: 'desc',
  } as const;

  function setSorter({ field, order }: SorterResult<any>) {
    sorterRef.current[`${field}OrderMark`] = orderMap[order!];
  }

  return {
    sorterRef,
    setSorter,
  };
}
