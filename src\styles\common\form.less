.ant-form-item {
  /* 表单中的日期框自适应 */
  .ant-picker {
    width: 100%;
  }

  /* 表单中的InputNumber框自适应 */
  .ant-input-number-in-form-item,
  .ant-input-number-affix-wrapper,
  .ant-input-number-group-wrapper {
    width: 100%;
  }
}

/* QueryFilter 样式调整 */
.ant-pro-form-query-filter {
  .ant-input-group {
    /** 修改 input.addonBefore 底色 */
    .ant-input-group-addon:first-child {
      background-color: initial;
    }
  }
}
