{"private": true, "name": "wwl-zouwu-front", "scripts": {"dev": "npm run start", "start": "cross-env PORT=7035 SOCKET_SERVER=http://localhost:7035 max dev", "build": "cross-env max build && sentry-util-build", "analyze": "cross-env ANALYZE=1 max build", "postinstall": "max setup", "commit": "git-cz", "test": "umi-test", "test:coverage": "umi-test --coverage", "prepare": "husky install", "precommit": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,less,sass,md}": ["pretty-quick --staged"], "*.{js,jsx,ts,tsx}": ["eslint --fix"], "**/*.less": "stylelint --fix"}, "engines": {"node": ">=10.13"}, "config": {"commitizen": {"path": "cz-conventional-changelog-for-jira-smart-commits"}}, "dependencies": {"@ant-design/icons": "^4.2.1", "@ant-design/pro-descriptions": "1.11.6", "@ant-design/pro-form": "1.66.0", "@ant-design/pro-table": "^2.73.0", "@dzg/sentry-utils": "^2.0.16", "ali-oss": "^6.22.0", "antd": "4.24.1", "classnames": "^2.2.6", "debounce-promise": "^3.1.2", "dnd-core": "^16.0.1", "history-with-query": "^4.10.4", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "kiwi-intl": "^1.2.6-beta.0", "lodash": "^4.17.21", "moment": "^2.29.1", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "spark-md5": "^3.0.2"}, "devDependencies": {"@commitlint/cli": "^13", "@commitlint/config-conventional": "^13", "@portal/hive-sdk": "1.0.0", "@portal/umi-config": "1.0.0", "@types/ali-oss": "^6.16.11", "@types/classnames": "^2.2.10", "@types/crypto-js": "^4.0.1", "@types/debounce-promise": "^3.1.4", "@types/js-cookie": "^2.2.7", "@types/lodash": "^4.17.14", "@types/qs": "^6.9.7", "@types/react": "^16.9.56", "@types/react-resizable": "^1.7.2", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.1.0", "@umijs/fabric": "2.8.1", "@umijs/max": "^4.0.28", "babel-plugin-transform-remove-console": "^6.9.4", "commitizen": "^4.1.2", "cross-env": "^7.0.2", "cz-conventional-changelog-for-jira-smart-commits": "^1.0.5", "eslint": "^7.4.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jest": "^25.2.2", "husky": "^7.0.1", "lint-staged": "^10.0.7", "prettier": "^2.6.1", "pretty-quick": "^3.0.0", "stylelint": "^13.7.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "thread-loader": "^3.0.4"}, "resolutions": {"@types/react": "^18", "@umijs/test": "^3.1.3", "@ant-design/pro-layout": "7.1.11"}}