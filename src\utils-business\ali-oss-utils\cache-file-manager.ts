import type { UploadRequestOption as RcUploadRequestOption } from 'rc-upload/lib/interface';
import type { default as OSSAli } from 'ali-oss';
import type { UploadFile } from 'antd';
import SparkMD5 from 'spark-md5';
import type { RcFile } from 'antd/lib/upload';

type TypeCacheFile = RcFile & {
  url?: string;
  md5?: string;
  status?: 'uploading' | 'done' | 'error' | 'paused';
};
type TypeRequestOption = {
  /** 感觉antd.Upload 这部分类型定义有问题,和rc-upload的定义不一致, 所以他屏蔽了onProgress */
  onProgress?: (percent: { percent: number }, file: File) => void;
  file: RcFile & { url: string; md5: string };
} & Omit<RcUploadRequestOption, 'onProgress' | 'file'>;
/** 缓存信息 */
export type TypeCacheInfo = {
  /** 断点续传信息, 上传完成时为null */
  checkpoint?: OSSAli.Checkpoint | null;
  /** 完整可访问oss链接 */
  url?: string;
  file?: RcFile & { url: string; md5: string };
  md5?: string;
  /** 文件上传状态 */
  status?: 'uploading' | 'done' | 'error' | 'paused';
  /** data会写入file.response */
  data?: any;
  /** upload.customRequest 的请求参数 */
  requestOptionMap?: Record<
    RcFile['uid'],
    TypeRequestOption & {
      /** 根据文件uid标记状态 */
      requestStatus?: 'uploading';
    }
  >;
  /** 统一的上传控制器 */
  control?: ReturnType<typeof createControl>;
  /** 暂停上传, 可以通过checkpoint恢复 */
  cancel?: () => void;
  /** 中止上传, 无法恢复, 会发送delete请求 */
  abort?: () => void;
  /** 恢复上传 */
  resume?: () => void;
};

/** 缓存文件管理器, 通过md5值实现秒传,断点续传 */
class CacheFileManager {
  /** 根据文件名和md5缓存文件信息, 用于处理重复文件
   * 重复的定义: 文件名和md5都相同
   */
  cacheMap: Record<string, TypeCacheInfo | undefined> = {};

  /** cacheKey 相同说明是同源文件, 但是Upload支持上传 */
  getCacheKey(file: TypeCacheFile) {
    return `${file.name}_${file.md5}`;
  }

  getCacheInfo(file: TypeCacheFile) {
    if (!file.md5) {
      throw new Error('file md5 is not exist');
    }
    const cacheInfo = this.cacheMap[this.getCacheKey(file)];
    const { status } = cacheInfo || {};

    if (status === 'error' && file.uid !== cacheInfo?.file?.uid) {
      /** 上传错误文件只允许uid相同文件读取, 避免再次上传时读取缓存 */
      return;
    }

    return cacheInfo;
  }

  setCacheInfo(file: TypeCacheFile, value: TypeCacheInfo) {
    let cacheInfo = this.getCacheInfo(file);
    const key = this.getCacheKey(file);

    if (!cacheInfo) {
      cacheInfo = this.cacheMap[key] = {};
    }
    file.status = value.status ?? cacheInfo.status;

    Object.assign(cacheInfo, {
      file,
      md5: file.md5,
      ...value,
    });
    return cacheInfo;
  }

  clearCacheInfo(file: TypeCacheFile) {
    delete this.cacheMap[this.getCacheKey(file)];
  }

  /** 注册上传请求参数, 用于统一调度上传请求 */
  registerRequestOption(file: TypeCacheFile, requestOption: TypeRequestOption) {
    let cacheInfo = this.getCacheInfo(file);

    if (!cacheInfo || !cacheInfo.requestOptionMap) {
      cacheInfo = this.setCacheInfo(file, {
        requestOptionMap: {},
      }) as TypeCacheInfo;
      cacheInfo.control = createControl(cacheInfo || {});
    }
    this.setCacheInfo(file, {});

    cacheInfo.requestOptionMap![file.uid] = requestOption;

    if (cacheInfo.status === 'done') {
      file.url = cacheInfo.url;
      cacheInfo.control?.onSuccess?.(cacheInfo.data);
    } else if (cacheInfo.status === 'paused') {
      cacheInfo?.resume?.();
      console.info(`=======> 恢复上传: ${file.name}`);
    }
    return {
      control: cacheInfo.control,
      cacheInfo,
    };
  }

  /** 监听文件变化, 用于更新文件状态, 暂停, 恢复等 */
  listenFileChange = (fileList: UploadFile[], prevFileList: UploadFile[]) => {
    const addFileList = fileList.filter(
      (file) => !prevFileList.some((prevFile) => prevFile.uid === file.uid),
    );
    const removeFileList = prevFileList.filter(
      (prevFile) => !fileList.some((file) => file.uid === prevFile.uid),
    );

    /** 新增文件暂无监听逻辑, 保持统一入口 asyncAliOSSUpload 上传中处理 */
    /*   addFileList.forEach((file) => {
      this.autoUpdateCacheInfo(file, 'resume');
    }); */
    removeFileList.forEach((file) => {
      this.autoUpdateCacheInfo(file, 'remove');
    });
  };

  /** 自动更新缓存信息, 用于暂停, 恢复等 */
  autoUpdateCacheInfo(file: UploadFile, type: 'remove' | 'resume') {
    /** 不在Upload.onRemove中调用, 场景太多,移除不一定通过onRemove, 也可能是Form表单重置 */
    if ((file?.originFileObj as TypeCacheFile)?.md5 === undefined) {
      /** 没有md5, 说明不是缓存文件, 不需要更新状态 */
      return;
    }
    const cacheInfo = this.getCacheInfo(file.originFileObj as TypeCacheFile);

    if (cacheInfo && cacheInfo.requestOptionMap) {
      const { status, requestOptionMap } = cacheInfo;

      if (status === 'uploading' && type === 'remove') {
        delete requestOptionMap[file.uid];
        if (Object.keys(requestOptionMap).length < 1) {
          /** 没有文件正在上传了, 移除需要终止上传,
           * 可再次添加, 触发断点续传
           */
          cacheInfo.cancel?.();
          cacheInfo.status = 'paused';
          console.info('暂停上传--->', file.name);
          return;
        }
        console.info('存在同源文件继续上传中--->', file.name);
      }
    }
  }
}

export const cacheFileManager = new CacheFileManager();
// console.log('=======> cacheFileManager:', (window.cacheFileManager = cacheFileManager));

/** 创建控制器, 统一管理上传请求的回调, 重复文件统一调度 */
function createControl(cacheInfo: TypeCacheInfo) {
  const { requestOptionMap } = cacheInfo;
  const keyNameArr = ['onProgress', 'onSuccess', 'onError'] as const;

  function bindControl(optionMap: typeof requestOptionMap, keyArr: typeof keyNameArr) {
    const control = {} as Pick<TypeRequestOption, typeof keyArr[number]>;

    for (const key of keyArr) {
      control[key] = function (...args) {
        Object.values(optionMap || {}).forEach((option) => {
          if (key === 'onSuccess' && option?.file) {
            option.file.url = cacheInfo.url!;
            /** 成功后移除事件监听 */
            delete optionMap![option.file.uid];
            option[key]?.(...args);
            return;
          }
          option[key]?.(...args);
        });
      };
    }
    return control;
  }
  const control = bindControl(requestOptionMap, keyNameArr);

  return control;
}

/**
 * 计算文件Md5, 并写入File对象
 * 将文件分片逐步计算最终合并得出整个文件md5, 提升计算速度
 */
export default function asyncComputeFileMD5(file: File) {
  return new Promise<string>((resolve, reject) => {
    const blobSlice = File.prototype.slice;
    const chunkSize = 2097152; // 按照一片 2MB 分片
    const chunks = Math.ceil(file.size / chunkSize); // 片数
    let currentChunk = 0;
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    fileReader.onload = function (e) {
      spark.append(e.target?.result as ArrayBuffer);
      currentChunk++;

      if (currentChunk < chunks) {
        loadNext();
      } else {
        const md5 = spark.end(); // 最终md5值

        spark.destroy(); // 释放缓存
        resolve(md5);
      }
    };

    fileReader.onerror = function (e) {
      console.warn('oops, something went wrong.');
      reject(e);
    };

    function loadNext() {
      const start = currentChunk * chunkSize;
      const end = start + chunkSize >= file.size ? file.size : start + chunkSize;

      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }

    loadNext();
  });
}
