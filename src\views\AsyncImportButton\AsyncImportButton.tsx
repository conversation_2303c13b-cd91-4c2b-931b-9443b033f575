import type { FormInstance, FormProps, ModalProps } from 'antd';
import { notification, Button, message, Form, Modal } from 'antd';
import type { NamePath } from 'antd/lib/form/interface';
import React, { useRef, useState } from 'react';
import classnames from 'classnames';
import { historyGoPush, tipsUtils, useSafeStateUpdate } from '@/utils';
import { goToPath } from './aide-import';
import { textMap } from './text-map';
import type { TypeSimpleUploadRef, TypeLoadButtonProps, TypeSimpleUploadProps } from '@/components';
import { SimpleUpload, LoadButton, SearchSelect } from '@/components';
import { EnumUploadFileType, apiMapDictType, apiUploadFileNew, useDictTypeValueEnum } from '@/api';
import { TextValue } from '../TextValue';
import { apiDownloadTemplate, apiCommonImport } from './importApi';
import './AsyncImportButton.less';
import type { TypeModalChildren } from './business-config';
import { EnumImportType, importTypeParamsDict } from './business-config';

export { EnumImportType };

type TypeParamsOptions = {
  form: FormInstance<{
    templateType?: string;
    fileUrl?: string;
  }>;
  modalController: {
    asyncOpen: () => Promise<void>;
    close: () => void;
  };
};

export type TypeAsyncImportButtonProps = {
  /** 按钮名称 */
  children?: React.ReactNode;
  formProps?: FormProps;
  modalProps?: Omit<ModalProps, 'children'> & {
    /** 自定义弹窗表单内容渲染 */
    children?: TypeModalChildren;
  };
  simpleUploadProps?: TypeSimpleUploadProps;
  buttonProps?: Omit<TypeLoadButtonProps, 'onClick'> & {
    onClick?: (e: React.MouseEvent, params: TypeParamsOptions) => void | Promise<void>;
  };
  // request?: (params: TypeParamsOptions) => Promise<void>;
  /** 模板类型, 也是导入类型 */
  importType: EnumImportType;
  /** 按钮类型, 默认 default */
  btnType?: 'link';
};

/** 异步批量导入按钮 */
export default function AsyncImportButton(inProps: TypeAsyncImportButtonProps) {
  const { modalConfig, modalController, form, uploadRef } = useModalConfig({ inProps });
  const { dictTypeMap } = useDictTypeValueEnum(['importType']);
  const {
    fileSuffix,
    maxFileSizeMb = 10,
    labelColSpan = 4,
    limitDesc = [],
    allowDownloadTemp = true,
    showExtParams,
    showFileLimitTip,
    ...otherProps
  } = importTypeParamsDict[inProps.importType];
  const fileLimitNode = textMap.txtFileLimitTitle(fileSuffix, maxFileSizeMb);
  /** 单据识别对象 */
  const nodeIdentityNoType = (
    <Form.Item
      {...{
        label: textMap.txtIdentityNoType,
        name: npExtParams('identityNoType'),
        rules: [{ required: true }],
        initialValue:
          showExtParams?.identityNoType === true ? undefined : showExtParams?.identityNoType,
      }}
    >
      <SearchSelect
        {...{
          request: apiMapDictType.IdentityNoType,
        }}
      />
    </Form.Item>
  );
  const nodeImportType = (
    <Form.Item
      {...{
        label: textMap.txtImportType,
        name: 'importType',
      }}
    >
      <TextValue>
        {function ({ value }) {
          return dictTypeMap.importType[value] ?? value;
        }}
      </TextValue>
    </Form.Item>
  );
  const nodeSimpleUpload = (
    <Form.Item
      {...{
        label: textMap.txtChooseFile,
        name: 'fileList',
        rules: [{ required: true }],
      }}
    >
      <SimpleUpload
        {...{
          ref: uploadRef,
          accept: fileSuffix ? fileSuffix.map((item) => `.${item}`).join(',') : undefined,
          fileSuffix,
          maxFileSizeMb,
          btnExtra: allowDownloadTemp
            ? () => {
                return (
                  <LoadButton onClick={() => apiDownloadTemplate(inProps.importType)}>
                    {textMap.txtDownloadTemplateBtn}
                  </LoadButton>
                );
              }
            : undefined,
          async uploadRequest(fileList) {
            for (const file of fileList) {
              if (file.status !== 'done' && file.originFileObj) {
                const { data } = await apiUploadFileNew({
                  file: file.originFileObj,
                  pathType: EnumUploadFileType.temp,
                });

                file.fileName = data.originFileName;
                file.filePath = data.filePath;
                file.status = 'done';
                form.setFieldsValue({
                  fileName: data.originFileName,
                  fileUrl: data.filePath,
                });
              }
            }
          },
          onOriginChange(info) {
            /** 原生上传时触发 */
            if (info.file.status === 'done' && info.file.originFileObj) {
              form.setFieldsValue({
                fileName: info.file.name,
                fileUrl: info.file.url,
              });
            }
          },
          ...otherProps.simpleUploadProps,
          ...inProps.simpleUploadProps,
        }}
      >
        <LoadButton {...{ type: 'default' }}>{textMap.txtUploadBtn}</LoadButton>
      </SimpleUpload>
    </Form.Item>
  );
  const nodeMap = {
    nodeIdentityNoType,
    nodeImportType,
    nodeSimpleUpload,
  };
  const innerModalChildren: TypeModalChildren =
    inProps.modalProps?.children ??
    otherProps.modalChildren ??
    ((params) => {
      return (
        <>
          {nodeImportType}
          {showExtParams?.identityNoType && nodeIdentityNoType}
          {nodeSimpleUpload}
        </>
      );
    });

  return (
    <>
      <LoadButton
        {...{
          ...(inProps.btnType === 'link' ? {} : { type: 'default' }),
          ...inProps.buttonProps,
          async onClick(e) {
            await inProps.buttonProps?.onClick?.(e, { form, modalController });
            await modalController.asyncOpen();
          },
        }}
      >
        {inProps.children || textMap.txtBtn}
      </LoadButton>
      <Modal
        {...{
          ...modalConfig,
          className: classnames('async-import-button', inProps.modalProps?.className),
        }}
      >
        <Form
          {...{
            form,
            labelCol: { span: labelColSpan },
            initialValues: {
              importType: inProps.importType,
            },
            ...inProps.formProps,
          }}
        >
          {innerModalChildren({ nodeMap, npExtParams })}
          <Form.Item hidden>
            <TextValue label={textMap.txtFileNameLabel} name="fileName" />
            <TextValue label={textMap.txtFileUrl} name="fileUrl" />
          </Form.Item>
        </Form>
        <div className="async-import-button__file-limit-box">
          {showFileLimitTip && fileLimitNode && <div className="limit-tip">{fileLimitNode}</div>}
          <div>
            {limitDesc.length > 0 ? (
              <div className="limit-desc-box">
                {limitDesc.map((item, i) => (
                  <div className="li" key={i}>
                    <span className="serial-number">{i + 1}.</span>
                    {item}
                  </div>
                ))}
              </div>
            ) : null}
          </div>
        </div>
      </Modal>
    </>
  );
}

function useModalConfig({ inProps }: { inProps: TypeAsyncImportButtonProps }) {
  const { apiCustomizeImport } = importTypeParamsDict[inProps.importType];
  const { safeUpdateFn } = useSafeStateUpdate();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const uploadRef = useRef<TypeSimpleUploadRef>();
  const modalController = {
    async asyncOpen() {
      safeUpdateFn(() => setVisible(true))();
    },
    close() {
      form.resetFields();
      safeUpdateFn(() => setVisible(false))();
    },
  };

  const modalConfig = {
    visible,
    title: textMap.txtModalTitle,
    keyboard: false,
    maskClosable: false,
    ...inProps.modalProps,
    children: undefined,
    /** 不在loading中才允许关闭 */
    closable: loading === false,
    onCancel() {
      modalController.close();
    },
    footer: [
      <Button
        key="cancel"
        {...{
          disabled: loading,
          onClick: modalController.close,
        }}
      >
        {textMap.txtModalCancel}
      </Button>,
      <LoadButton
        key="submit"
        {...{
          type: 'primary',
          async onClick() {
            const handleGoImportList = () => {
              historyGoPush({ newTab: true, pathname: goToPath });
            };

            setLoading(true);

            try {
              await form.validateFields();
            } catch (err) {
              message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
              console.log(err);
              return;
            }
            await uploadRef.current?.fetchUploadRequest();

            const formData = form.getFieldsValue();

            await (apiCustomizeImport || apiCommonImport)(formData);
            // await inProps.request({ form, modalController });
            notification.open({
              duration: 5,
              message: textMap.txtNotifyMessage,
              description: (
                <>
                  {textMap.txtDes1} <a onClick={handleGoImportList}>{textMap.txtDes2}</a>{' '}
                  {textMap.txtDes3}
                </>
              ),
              btn: (
                <Button type="primary" onClick={handleGoImportList}>
                  {textMap.txtGoto}
                </Button>
              ),
            });
            modalController.close();
          },
          onClickFinally() {
            setLoading(false);
          },
        }}
      >
        {textMap.txtModalConfirm}
      </LoadButton>,
    ],
  } as ModalProps;

  return {
    modalConfig,
    modalController,
    form,
    uploadRef,
  };
}

/** 额外扩展参数 */
export function npExtParams(name: string) {
  return ['extParams', name] as NamePath;
}
