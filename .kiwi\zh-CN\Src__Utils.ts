export default {
  Const: {
    issue: '出库',
    warehousing: '入库',
    exitFromWarehouse: '退仓出库',
    standardDelivery: '标准出库',
    enclosure: 'Lbel附件',
    passport: '护照',
    id: '身份证',
    other: '其它',
    otherECommerce: '其它电商平台',
    statement: '结单',
    expenseConfirmation: '费用确认',
    signingIn: '签收中',
    inTransit: '运输中',
    inboundForecast: '入库预报通知',
    supplementTheFirstStep: '补充头程物流信息',
    submitForReview: '提交审核',
    customerCreatesOrder: '客户建单',
    imperial: '英制',
    metric: '公制',
    lithiumMetalElectricity: '锂金属电池',
    lithiumIonElectricity: '锂离子电池',
    batteryInstallation: '电池安装在设备中运输',
    batteryMatching: '电池搭配设备运输（未安装）',
    reviewed: '已复核',
    toBeReviewed: '待复核',
    mergePackaging: '合并打包',
    independentPackaging: '独立打包',
    doNotSet: '不设置',
    specialPackaging: '特殊包装',
    selfPacking: '自带包装',
    unpacked: '无包装',
    outboundOrder: '出库单',
    receiptDoc: '入库单',
    headToBeSupplemented: '待补充头程物流信息',
    express: 'EXPRESS(快递)',
    truck: 'TRUCK(卡车)',
    oceanShipping: 'MWB(海运)',
    abnormal: '异常',
    issued: '已下发',
    toBeIssued: '待下发',
    distributeAll: '全部下发',
    partialDistribution: '部分下发',
    returnReceipt: '退货入库',
    transferWarehousing: 'FBA中转入库',
    issuingTheFirstCourse: '代发头程入库',
    theFirstJourneyOfTheWorld: '环世头程入库',
    issue2: 'TOB出库',
    warehousing1: 'TOB入库',
    explosionChamber: '爆舱',
    sufficientSpace: '舱位充足',
    tightSpace: '舱位紧张',
    paid: '已支付',
    toBeConfirmed: '待确认',
    toBeAdded: '待加入',
    shipped: '已配送',
    customsCleared: '已清关',
  },
  Rules: {
    pleaseEnterAPositive: '请输入正确的URL',
    supportInteger: '支持整数最多10位，小数最多4位的正数',
    supportInteger1: '支持整数最多10位，小数最多3位的正数',
    supportInteger2: '支持整数最多10位，小数最多2位的正数',
    onlyAllowed: '只允许输入数字、英文、空格、字符包含(=+-.:/*&<>)_,',
    specialFeaturesAreNotSupported: '不支持 # & 特殊符号',
    emptyIsNotSupported: '不支持空格',
    notSupported: '不支持中文及中文符号',
    onlyWordsAreSupported: '仅支持字母或数字',
  },
  Util: {
    submit:
      '<form action="{val1}" method="post">\n        <button type="submit" style="display:none;" value="提交"></button>\n        </form>',
  },
  Table_schema_aide: {
    pleaseSelect: '请选择',
  },
} as const;
