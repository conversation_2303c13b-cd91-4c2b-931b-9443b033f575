export default {
  Index: {
    return: '返回',
    fileUpload: '文件上传',
    import: '导入VGM',
    preConfiguredManifest: '预配舱单',
    upload: 'VGM上传',
    orderInformation: '订单信息',
    cargoTracking: '货物跟踪详情',
    uploadFiles: '上传文件成功',
    uploadSucceeded: 'VGM上传成功',
    oceanShippingOrder: '海运订单详情页',
  },
  PrePane: {
    descriptionOfTheGoods: '货物描述',
    marks: '唛头',
    totalVolume: '总体积',
    totalGrossWeight: '总毛重',
    total2: '总件数',
    cargoInformation: '货物信息',
    cargoDetails: '货物明细',
    fax: '传真',
    telephone: '电话',
    enterpriseCode: '企业代码',
    country: '国家',
    notifier: '通知人',
    partners: '合作方',
    bookingInformation: '订舱信息',
    preConfiguredImport: '预配导入',
  },
  Share: {
    splitOrder: '拆单',
    splitOrder1: '{val1} {val2}',
    billOfLading: '提单',
    detailsPage: '详情页',
    consolidation: '并单',
  },
  SiItem: {
    lengthNotExceeding: '长度不超过900个字符',
    remarks: '备注',
    preparerMail: '制单人邮箱（多个邮箱请用英文“,”隔开）',
    preparationTime: '制单时间',
    preparedBy: '制单人电话',
    preparedBy1: '制单人',
    packingMethod: '装箱方式',
    signingMethod: '签单方式',
    transportationAndSupport: '运输及支付信息',
    totalVolume: '总体积',
    totalGrossWeight: '总毛重',
    fixedClause: '固定条款',
    pleaseVerifyThatItIs: '请核实BKG NO是否正确，再进行下一步补料',
    secondNotice: '第二通知人',
    status: '状态：',
    billOfLadingExport: '提单导出',
    pleaseKnow: '，请知晓',
    in: '中',
    theBillOfLadingHas: '该提单已合并至',
    theBillOfLadingHas1: '该提单已合并',
    weight: '重量',
  },
  SiPane: {
    confirm: '确认',
    pleaseEnterTheMail: '请输入邮箱',
    pleaseEnterAPositive: '请输入正确的邮箱',
    recipientEmail: '收件人邮箱',
    platformDefault:
      '平台默认收件人为经分享人授权可以编辑、提交此票提单的相关方，请谨慎分享超链接。',
    shareReplenishment: '分享补料',
    cancelSharing: '取消分享',
    share1: '分享',
    newlyAdded: 'SI新增',
    submit: 'SI递交',
    shareLink: '分享链接已发送',
    shareTaken: '分享已取消',
    cancelSharing1: '取消分享后，原分享用户将失去编辑权限,是否确认',
  },
  SoPane: {
    feeDetails: '费用详情',
    addASingleLetter: '加拼单信息',
    agentContact: '代理联系人',
    customerService: '客服',
    sale: '销售',
    epochConnection: '环世联系人',
    zoomlionContact: '中联联系人',
    multipleMailboxes: '多个邮箱请用英文“,”隔开',
    ccEmail: '抄送邮箱',
    ccEmail1: '{val1} ({val2})',
    mobilePhone: '手机',
    yourContact: '贵司联系人',
    customerContact: '客户联系人信息',
    boxTypeAndQuantity: '箱型箱量信息',
    multipleMust: '多个必须用英文逗号(,)分隔开',
    multipleMust1: 'PO NO({val1})',
    volume: '{val1} (CBM)',
    grossWeight: '{val1} (KGS)',
    actualNumber: '实际件数',
    numberOfEntrustedPieces: '委托件数',
    chineseName: '中文品名',
    englishName: '英文品名',
    transportationTerms: '运输条款',
    paymentMethod: '付款方式',
    signingMethod: '签单方式',
    billOfLadingRequirements: '提单要求',
    cutOffTime: '截VGM时间',
    cutOffTime1: '截单时间',
    entrustedBoxMaking: '委托做箱',
    tradeTerms: '贸易条款',
    entrustedCustomsDeclaration: '委托报关',
    expectedGoodsPreparation: '预计备货日',
    secondWay: '二程ETD',
    twoVoyages: '二程航次',
    secondVoyageShipName: '二程船名',
    future: '前程ATD',
    future1: '前程ETD',
    futureVoyage: '前程航次',
    forwardShipName: '前程船名',
    essentialInformation: '基本信息',
    ourSales: '我司销售',
    ourSales1: '我司销售联系人',
    yourParticipation: '贵公司参考号',
    contractNo: '合约号',
    bookDownload: '托书下载',
    modify: 'SO修改',
    copy: 'SO复制',
    powerOfAttorneyDocument: '托书文件',
    file: '文件',
    analysisOfEntrustmentLetter: '托书解析',
    type: '类型',
    date: '日期',
    productName: '品名',
    addOrderNo: '加拼单号',
  },
};
