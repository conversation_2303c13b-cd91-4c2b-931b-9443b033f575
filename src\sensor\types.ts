/** 独立类型定义, 通过 declare module './types' 扩展类型, 具体参考events定义 */
export declare namespace NsSensor {
  /** 自定义data类型 */
  interface dataCustomizeMap {}

  /** 内置data类型 */
  interface dataInnerMap {}

  type dataMap = dataCustomizeMap & {
    /** 实现dataCustomizeMap能够覆盖 dataInnerMap */
    [P in Exclude<keyof dataInnerMap, keyof dataCustomizeMap>]: dataInnerMap[P];
  } & Record<
      Exclude<import('@/sensor/event').EnumSensor, keyof dataCustomizeMap | keyof dataInnerMap>,
      undefined
    >;
}
