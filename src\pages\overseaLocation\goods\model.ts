import type { Effect } from 'dva';
import {
  queryOverseaLocationGoods,
  queryOverseaLocationGoodsStatData,
  queryOverseaLocationGoodsDetail,
  sendCreateOverseaLocationGoods,
  sendUpdateOverseaLocationGoods,
  sendDeleteOverseaLocationGoods,
  queryOverseaLocationGoodsList,
  queryGoodsRelaInfo,
} from '@/service/overseaLocation';

export interface GoodsModelType {
  namespace: string;
  state: null;
  effects: {
    getOverseaLocationGoodsList: Effect;
    getOverseaLocationGoodsStatData: Effect;
    getOverseaLocationGoodsDetail: Effect;
    createOrEditOverseaLocationGoods: Effect;
    deleteOverseaLocationGoods: Effect;
    searhOverseaLocationGoodsList: Effect;
    getGoodsRelaInfo: Effect;
  };
}

const Model: GoodsModelType = {
  namespace: 'goods',
  state: null,
  effects: {
    /** 已废弃迁移至OMS */
    *getOverseaLocationGoodsList({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoods, payload);

      if (success) {
        return {
          data: result.records,
          total: result.totalSize ? Number(result.totalSize) : 0,
        };
      }
    },
    /** @deprecated 找不到调用 */
    *getOverseaLocationGoodsStatData({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoodsStatData, payload);

      if (success) {
        return result;
      }
      return false;
    },
    /** 商品详情接口废弃
     * @deprecated 已废弃迁移至OMS
     */
    *getOverseaLocationGoodsDetail({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoodsDetail, payload);

      if (success) {
        return result;
      }

      return false;
    },
    /** 商品新增编辑废弃
     * @deprecated 已废弃迁移至OMS
     */
    *createOrEditOverseaLocationGoods({ payload }, { call }) {
      const { success } = yield call(
        payload.id ? sendUpdateOverseaLocationGoods : sendCreateOverseaLocationGoods,
        payload,
      );

      return success;
    },
    /** @deprecated 入口已隐藏 */
    *deleteOverseaLocationGoods({ payload }, { call }) {
      const { success } = yield call(sendDeleteOverseaLocationGoods, payload);

      return success;
    },
    /** @deprecated 已废弃迁移至OMS */
    *searhOverseaLocationGoodsList({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoodsList, payload);

      if (success) {
        return {
          data: result.records,
          total: result.totalSize ? Number(result.totalSize) : 0,
        };
      }
    },
    /** @deprecated 已废弃迁移至OMS */
    *getGoodsRelaInfo({ payload }, { call }) {
      const { success, result } = yield call(queryGoodsRelaInfo, payload);

      if (success) {
        return result;
      }

      return false;
    },
  },
};

export default Model;
