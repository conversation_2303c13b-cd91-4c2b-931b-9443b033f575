import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Modal } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import I18N from '@/utils/I18N';
import { AmountMoney } from '@/views';
import { apiCreditLimitRecords } from '../FundsAccountApi';
import style from './CreditLimitRecords.less';

type TypeProps = {
  /** 公司id  */
  accountId: string;
  /** 金额 */
  amount?: number;
};
function CreditLimitRecords(props: TypeProps) {
  const { config } = useConfig(props);
  const modalConfig = useModal();

  return (
    <>
      <div
        {...{
          className: style['custom-cursor'],
          onClick: () => {
            modalConfig.showModal();
          },
        }}
      >
        <AmountMoney
          {...{
            num: props?.amount,
            positiveClassName: style['normal-money-color'],
            zeroClassName: style['normal-money-color'],
          }}
        />
      </div>

      <Modal {...modalConfig.config}>
        <ProTable {...config} />
      </Modal>
    </>
  );
}

/** 基础配置 */
function useConfig(props: TypeProps) {
  const actionRef = useRef<ActionType>();
  const columns = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    pagination: {},
    search: false,
    options: false,
    cardProps: false,
    tableType: 'simple',
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
          accountId: props.accountId,
        },
      } as Parameters<typeof apiCreditLimitRecords>[0];

      const { data } = await apiCreditLimitRecords(query);

      return {
        data: data?.records,
        success: true,
        total: data?.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
  };
}

function useColumns() {
  const columns = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components.CreditLimitRecords
          .chineseSymbols3,
      dataIndex: 'createDate',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components.CreditLimitRecords
          .chineseSymbols2,
      dataIndex: 'afterUpdateCreditLimit',
      render: (_, record) => {
        return (
          <AmountMoney
            {...{
              num: record?.afterUpdateCreditLimit,
            }}
          />
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components.CreditLimitRecords
          .chineseSymbols1,
      dataIndex: 'creditLimitMaturityDate',
      valueType: 'date',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components.CreditLimitRecords
          .operator,
      dataIndex: 'createByName',
    },
  ] as ProColumns<TypeCreditLimitRecordsList>[];

  return columns;
}

/** Modal配置 */
function useModal() {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const config = {
    title:
      I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount__Components.CreditLimitRecords
        .chineseSymbols,
    visible: isModalVisible,
    onOk: handleOk,
    onCancel: handleCancel,
    destroyOnClose: true,
    footer: null,
  };

  return { config, showModal };
}

export default CreditLimitRecords;

/** 信用额度变更记录 */
export type TypeCreditLimitRecordsList = {
  /** 主键id */
  id?: string;
  /** 账户id */
  accountId?: string;
  /** 公司id */
  companyId?: string;
  /** 公司名 */
  companyName?: string;
  /** 变更前额度 */
  beforeUpdateCreditLimit?: number;
  /** 变更后额度 */
  afterUpdateCreditLimit?: number;
  /** 额度有效期 */
  creditLimitMaturityDate?: string;
  /** 申请理由 */
  reason?: string;
  /** 操作人 */
  createByName?: string;
  /** 变更时间 */
  createDate?: string;
};
