export default {
  EnterWarehouseList: {
    signingTime: '签收时间',
    executed: '已执行:',
    toBeImplemented: '待执行:',
    performanceQuantity: '履约数量',
    total: '总件数:',
    totalNumberOfBoxes: '总箱数:',
    requestedQuantity: '申请数量',
    receivingOrganization: '收货组织',
    logisticsChannel: '物流渠道',
    applicationNo: '申请单号',
    createReceipt: '创建入库申请',
    creationDate: '创建日期',
    targetOrganization: '目标组织',
    pleaseEnterTheForm: '请输入单号',
    oddNumbers: '单号',
    pleaseEnterTheSupplier: '请输入商品sku',
    commodity: '商品sku',
    wholeOrderCancellation: '整单取消',
    list: '列表',
    success: '{val1}成功',
    whetherToConfirm: '是否确认{val1}此订单',
    thisOrder:
      '此笔订单已通知仓库，如果取消订单可能会产生费用，若仓库还未执行拣货则正常取消',
    deleteOrder: '删除订单',
    cancel: '取消',
    delete: '删除',
    chineseSymbols: '批量创建入库单',
    chineseSymbols1: '预约状态',
    chineseSymbols2: '履约标记',
    chineseSymbols3: '预约详情',
    chineseSymbols4:
      '无有效的预约数据，请对应催办（三方、环世）预约送仓，或联系客服更改履约类型。',
    chineseSymbols5: '该单为环世履约送仓，无须发起预约，可直接查看预约详情。',
    chineseSymbols7: '下载箱唛',
  },
} as const;
