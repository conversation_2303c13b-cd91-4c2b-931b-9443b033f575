/* eslint-disable no-spaced-func */
import React, { useContext, useRef } from 'react';
import { useDispatch } from 'umi';
import {
  Form,
  Table,
  Space,
  Select,
  Input,
  Button,
  Popconfirm,
  message,
  Upload,
  Row,
  Col,
  InputNumber,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import type { ValidatorRule } from 'rc-field-form/lib/interface';
import type { FormInstance } from 'antd/lib/form';
import type { RcFile } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload';
import { cloneDeep } from 'lodash';
import type { LabeledValue } from 'antd/lib/select';
import type { ColumnsType } from 'antd/lib/table';
import I18N from '@/utils/I18N';
import { useRefresh } from '@/utils/util';
import Context from './context';
import REGEX from '@/utils/regex';
import {
  includesInArray,
  ossPathCombine,
  regexUtils,
  ruleUtils,
} from '@/pages/overseaLocation/utils';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import type { apiUploadFile } from '@/pages/overseaLocation/api';
import { apiMapDictType, EnumUploadFileType } from '@/pages/overseaLocation/api';
import css from './sendTableForm.less';
import { booleanOptions } from '../../enum';
import { TextValue } from '@/views';

interface IProps {
  form: FormInstance;
  name: string;
  rule?: ValidatorRule[];
}

type TypeLabelFile = {
  id?: string;
  fileType?: number;
  fileName?: string;
  filePath?: string;
  deleted?: boolean;
};

enum UploadFileType {
  'LABEL' = 1,
  'OTHER' = 99,
}

const MAX_FILE_SIZE = 20 * 1024 * 1024;
/**
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 * @deprecated 废弃历史出库编辑src\pages\overseaLocation\outWarehouse\action, 实现太脏了
 */
const SendTableForm: React.FC<IProps> = ({ form, name: prefix, rule = [] }) => {
  const dispatch = useDispatch();
  const isInsure = Form.useWatch(['express', 'insured'], form);
  const goodsList = Form.useWatch(['goodsList'], form);
  const { contextState } = useContext(Context);
  const secondList = (contextState.dispatchServiceList || [])
    .map((item: any) => {
      return item.children;
    })
    .flat(2);
  const tableData = form.getFieldValue(prefix) || [];

  const refresh = useRefresh();

  /** 获取需要处理结果数据 */
  const getTargetDispatchService = (value: any) => {
    const target =
      secondList && secondList.length
        ? secondList.filter((o: any) => `${o.value}` === `${value}`)[0]
        : {};

    return target;
  };

  /** 统一处理赋值操作 */
  const handleDispatchServiceChange = (
    key: number,
    value: LabeledValue,
    type = 'dispatchServiceName',
  ) => {
    const nextData = cloneDeep(tableData) || [];

    if (type === 'dispatchServiceType') {
      nextData[key] = {
        ...nextData[key],
        dispatchServiceType: value,
        dispatchServiceName: null,
        trackingNo: null,
        fileList: [],
      };
    } else {
      nextData[key] = {
        ...nextData[key],
        dispatchServiceName: value,
        trackingNo: null,
        fileList: [],
      };
    }

    form.setFieldsValue({
      [prefix]: nextData,
    });

    refresh.trigger();
  };

  const handleBeforeUpload = (file: RcFile) => {
    if (file.size > MAX_FILE_SIZE) {
      message.info(I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.fileSize);
      return false;
    }

    return true;
  };

  const handleFileUpload = async ({ file }: any, key: number, type: UploadFileType) => {
    const { data } = (await dispatch({
      type: 'global/doGlobalUploadFile',
      payload: {
        file,
        directory: 'overseaLocation',
        param: type,
        pathType: EnumUploadFileType.temp,
      },
    })) as any as Awaited<ReturnType<typeof apiUploadFile>>;

    if (data) {
      const nextData = cloneDeep(tableData) || [];

      nextData[key].fileList = nextData[key].fileList.concat([
        {
          fileType: type,
          fileName: data.originFileName,
          filePath: data.filePath,
        },
      ]);

      form.setFieldsValue({
        [prefix]: nextData,
      });
      refresh.trigger();
    }
  };

  const handleRemoveFile = (key: number, type: UploadFileType, file: TypeLabelFile) => {
    const formData = form.getFieldValue(prefix) || [];

    const newData = formData.map((o: any) => ({
      ...o,
      fileList: (tableData[0]?.fileList || [])?.filter((i: any) => i !== file),
    }));

    form.setFieldsValue({
      [prefix]: newData,
    });

    refresh.trigger();
  };

  /** Form.List 操作 */
  const operateRef = useRef<{ add?: (...args: any[]) => void }>({});
  const operateNode = (
    <Space style={{ marginBottom: 10 }}>
      <Button
        type="primary"
        onClick={() => {
          if (tableData.length > 0) {
            message.info(
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                .onlyAddingIsSupported,
            );
            return;
          }
          if (contextState.currentWarehouseId) {
            operateRef.current?.add?.({
              fileList: [],
            });

            refresh.trigger();
          } else {
            message.info(
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.pleaseAddFirst,
            );
          }
        }}
      >
        {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.addShipping}
      </Button>
      <div className={css.signatureTypeBox}>
        <span>
          {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.signingServices}
        </span>
        <Form.Item
          {...{
            noStyle: true,
            name: ['express', 'signatureType'],
            initialValue: 'NO_SIGNATURE' /** 默认: 无需签署 */,
          }}
        >
          <ZSearchSelect
            {...{
              style: { width: 120 },
              request: apiMapDictType.outboundSignatureTypes,
              allowClear: false,
            }}
          />
        </Form.Item>
      </div>
      <div className={css.signatureTypeBox}>
        <span>
          {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.chineseSymbols2}
        </span>
        <Form.Item
          {...{
            // noStyle: true,
            name: ['express', 'insured'],
            initialValue: false /** 默认: 否 */,
            rules: [
              {
                async validator(r, val) {
                  if (val === true && goodsList?.length > 1) {
                    throw new Error(
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.chineseSymbols1,
                    );
                  }
                },
              },
            ],
          }}
        >
          <ZSearchSelect
            {...{
              style: { width: 120 },
              options: booleanOptions,
              allowClear: false,
            }}
          />
        </Form.Item>
      </div>
      <div className={css.signatureTypeBox}>
        <span>
          {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.chineseSymbols}
        </span>
        <Form.Item
          {...{
            // noStyle: true,
            name: ['express', 'insuredAmount'],
            rules: [
              // { required: isInsure, message: '投保时金额必填' },
              ruleUtils.ruleNumAndFloatTwo,
            ],
          }}
        >
          <InputNumber
            {...{
              addonAfter: (
                <Form.Item {...{ noStyle: true, name: ['express', 'insuredAmountCurrency'] }}>
                  <TextValue />
                </Form.Item>
              ),
            }}
          />
        </Form.Item>
      </div>
    </Space>
  );

  return (
    <div className={css.sendTableForm}>
      {operateNode}
      <Form.List name={prefix} rules={rule}>
        {(fields, { add, remove }, { errors }) => {
          operateRef.current = { add };
          const columns = [
            {
              title:
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                  .distributionType,
              // width: 300,
              render: (_: any, field: any) => {
                return (
                  <Form.Item
                    {...field}
                    name={[field.name, 'dispatchServiceType']}
                    fieldKey={[field.key, 'dispatchServiceType']}
                    rules={[
                      {
                        required: true,
                        message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                      },
                    ]}
                  >
                    <Select
                      labelInValue
                      placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
                      style={{ width: '100%' }}
                      onChange={(value) =>
                        handleDispatchServiceChange(field.name, value, 'dispatchServiceType')
                      }
                      optionLabelProp="label"
                    >
                      {contextState.dispatchServiceList.map((item: any) => (
                        <Select.Option key={item.value} value={item.value} label={item.label}>
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                );
              },
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                  .deliveryService,
              // width: 300,
              render: (_: any, field: any) => {
                const current = tableData[field.name];
                const serviceTypeList =
                  contextState.dispatchServiceNameInfo[current?.dispatchServiceType?.value] || [];

                return (
                  <Form.Item
                    {...field}
                    name={[field.name, 'dispatchServiceName']}
                    fieldKey={[field.key, 'dispatchServiceName']}
                    rules={[
                      {
                        required: true,
                        message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                      },
                    ]}
                  >
                    <Select
                      labelInValue
                      placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
                      style={{ width: '100%' }}
                      onChange={(value) =>
                        handleDispatchServiceChange(field.name, value, 'dispatchServiceName')
                      }
                      optionLabelProp="label"
                    >
                      {serviceTypeList.map((item: any) => (
                        <Select.Option key={item.value} value={item.value} label={item.label}>
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                );
              },
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                  .logisticsTracking,
              // width: 300,
              render: (_: any, field: any) => {
                const current = tableData[field.name];
                const currentDispatch: any =
                  current && current.dispatchServiceName?.value
                    ? getTargetDispatchService(current.dispatchServiceName?.value)
                    : null;
                // const dispatchServiceType = Number(current?.dispatchServiceType?.value ?? NaN);
                // const { specifyExpress, defaultWarehouse, customerAccountCode } = EnumDispatchType;
                // /** 这几个配送类型禁止填写物流追踪单号 */
                // const disabledTrackingNo = includesInArray(
                //   [specifyExpress, defaultWarehouse, customerAccountCode],
                //   dispatchServiceType,
                // );

                // if (disabledTrackingNo) {
                //   /** 禁填追踪单号的时候清空值, 不展示 */
                //   current.trackingNo = null;
                //   return null;
                // }

                return (
                  <Form.Item
                    {...field}
                    name={[field.name, 'trackingNo']}
                    fieldKey={[field.key, 'trackingNo']}
                    rules={[
                      {
                        required: currentDispatch && currentDispatch.requiredRule?.trackingNo,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                            .pleaseEnterSomething1,
                      },
                      {
                        pattern: REGEX.NUMBER_AND_ENGLISH,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                            .LogisticsTableForm.pleaseEnterTheNumber,
                      },
                    ]}
                  >
                    {currentDispatch?.trackingNoShow ? (
                      <Input
                        // disabled={disabledTrackingNo}
                        onChange={() => refresh.trigger()}
                        placeholder={
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                            .pleaseEnterSomething
                        }
                      />
                    ) : (
                      <div />
                    )}
                  </Form.Item>
                );
              },
            },
            {
              title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.enclosure,
              // width: 300,
              render: (_: any, field: any) => {
                const current = tableData[field.name];
                const currentFileList =
                  current && current.fileList
                    ? current.fileList.filter((o: any) => o.fileType === UploadFileType.LABEL)
                    : [];
                const currentDispatch: any =
                  current && current.dispatchServiceName?.value
                    ? getTargetDispatchService(current.dispatchServiceName?.value)
                    : null;

                return (
                  <Form.Item
                    {...field}
                    name={[field.name, 'labelFile']}
                    fieldKey={[field.key, 'labelFile']}
                    rules={[
                      {
                        validator: () => {
                          if (
                            currentFileList.length <= 0 &&
                            currentDispatch &&
                            currentDispatch.requiredRule?.labelFile
                          ) {
                            return Promise.reject(
                              new Error(
                                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.pleaseUploadTheAttachment,
                              ),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    // shouldUpdate
                  >
                    <ul>
                      {currentFileList.map((file: any) => (
                        <li key={file.filePath}>
                          <Space>
                            <a
                              href={ossPathCombine(file.filePath, false)}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {file.fileName}
                            </a>
                            <CloseOutlined
                              onClick={() =>
                                handleRemoveFile(field.name, UploadFileType.LABEL, file)
                              }
                            />
                          </Space>
                        </li>
                      ))}
                    </ul>
                  </Form.Item>
                );
              },
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                  .otherAccessories,
              render: (_: any, field: any) => {
                const current = tableData[field.name];
                const currentFileList =
                  current && current.fileList
                    ? current.fileList.filter((o: any) => o.fileType === UploadFileType.OTHER)
                    : [];
                const currentDispatch: any =
                  current && current.dispatchServiceName?.value
                    ? getTargetDispatchService(current?.dispatchServiceName?.value)
                    : null;

                return (
                  <Form.Item
                    {...field}
                    name={[field.name, 'otherFile']}
                    fieldKey={[field.key, 'otherFile']}
                    rules={[
                      {
                        validator: () => {
                          if (
                            currentFileList.length <= 0 &&
                            currentDispatch &&
                            currentDispatch.requiredRule?.file
                          ) {
                            return Promise.reject(
                              new Error(
                                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.pleaseUploadIt,
                              ),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    // dependencies={[prefix]}
                  >
                    <ul>
                      {currentFileList.map((file: any) => (
                        <li key={file.filePath}>
                          <Space>
                            <a
                              href={ossPathCombine(file.filePath, false)}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {file.fileName}
                            </a>
                            <CloseOutlined
                              onClick={() =>
                                handleRemoveFile(field.name, UploadFileType.OTHER, file)
                              }
                            />
                          </Space>
                        </li>
                      ))}
                    </ul>
                  </Form.Item>
                );
              },
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                  .numberOfPallets1,
              width: 210,
              render: (value, field) => {
                return (
                  <Form.Item
                    {...{
                      name: [field.name, 'palletQuantity'],
                      fieldKey: [field.key, 'palletQuantity'],
                      rules: [
                        {
                          async validator(_rule, val) {
                            if (!val) {
                              return;
                            }
                            if (
                              val > 5000 ||
                              regexUtils.nonZeroPositiveInteger.test(val) === false
                            ) {
                              throw new Error(
                                I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm.numberOfPalletsIs,
                              );
                            }
                          },
                        },
                      ],
                    }}
                  >
                    <Input />
                  </Form.Item>
                );
              },
            },
            {
              title: I18N.Src__Pages__Common__Template.Index.operation,
              width: 200,
              valueType: 'option',
              render: (_: any, field: any) => {
                const current = tableData[field.name];
                const currentDispatch: any =
                  current && current.dispatchServiceName?.value
                    ? getTargetDispatchService(current.dispatchServiceName?.value)
                    : null;
                const currentLabelFileList =
                  current && current.fileList
                    ? current.fileList.filter((o: any) => o.fileType === UploadFileType.LABEL)
                    : [];
                const currentOtherFileList =
                  current && current.fileList
                    ? current.fileList.filter((o: any) => o.fileType === UploadFileType.OTHER)
                    : [];

                return (
                  <Space>
                    {currentDispatch &&
                    currentDispatch.labelFileShow &&
                    currentLabelFileList.length === 0 ? (
                      <Upload
                        fileList={[]}
                        accept=".pdf,.png"
                        beforeUpload={(file) => {
                          const { name } = file;
                          const arr = name.split('.');
                          const suffix = arr[arr.length - 1].toLocaleLowerCase?.();
                          const fileSuffix = ['pdf', 'png'];

                          if (fileSuffix.includes(suffix) === false) {
                            message.error(
                              I18N.template(
                                I18N.Src__Pages__OverseaLocation__Components__SingleUpload
                                  .SingleUpload.onlyLatticesAreSupported,
                                { val1: fileSuffix.join(', ') },
                              ),
                            );
                            return false;
                          }

                          return handleBeforeUpload(file);
                        }}
                        customRequest={(params) =>
                          handleFileUpload(params, field.name, UploadFileType.LABEL)
                        }
                      >
                        <a>
                          {
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                              .enclosure
                          }
                        </a>
                      </Upload>
                    ) : null}
                    {/* 仅支持TOB订单多附件上传 */}
                    {currentDispatch && currentDispatch.fileShow ? (
                      <Upload
                        fileList={[]}
                        beforeUpload={(file) => {
                          const { name } = file;
                          const arr = name.split('.');
                          const suffix = arr[arr.length - 1].toLocaleLowerCase?.();
                          const fileSuffix = [] as string[]; /** 不限制文件类型 */

                          if (fileSuffix.length > 0 && fileSuffix.includes(suffix) === false) {
                            message.error(
                              I18N.template(
                                I18N.Src__Pages__OverseaLocation__Components__SingleUpload
                                  .SingleUpload.onlyLatticesAreSupported,
                                { val1: fileSuffix.join(', ') },
                              ),
                            );
                            return false;
                          }

                          return handleBeforeUpload(file);
                        }}
                        customRequest={(params) =>
                          handleFileUpload(params, field.name, UploadFileType.OTHER)
                        }
                      >
                        <a>
                          {
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.SendTableForm
                              .otherAccessories
                          }
                        </a>
                      </Upload>
                    ) : null}
                    <Popconfirm
                      title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                      onConfirm={() => {
                        remove(field.name);
                        refresh.trigger();
                      }}
                    >
                      <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ] as ColumnsType<any>;

          return (
            <>
              <Table
                columns={columns}
                dataSource={fields}
                pagination={false}
                size="small"
                scroll={{ x: '100%' }}
              />
              <Form.ErrorList errors={errors} />
            </>
          );
        }}
      </Form.List>
    </div>
  );
};

export default SendTableForm;
