import React from 'react';
import { Form, Input, Space, Typography } from 'antd';
import { SearchSelect } from '@/components';
import type { TypeCustomerOmsOption } from '@/pages/pagesApi';
import { apiActualWarehouseOptions, apiCustomerOmsSelect } from '@/pages/pagesApi';
import { RowLayout, TextValue } from '@/views';
import { EnumReturnOrderStatus } from '../ReturnOrderEnum';
import { isPortal } from '@/oms-portal-diff-import';

const { Item } = Form;

/** 基础信息 */
export default function FormBaseInfo() {
  const form = Form.useFormInstance();
  const orderStatus = Form.useWatch('orderStatus', form);
  const isClaim = orderStatus === EnumReturnOrderStatus.CLAIMED;

  return (
    <div>
      <Item noStyle hidden>
        <TextValue {...{ label: '客户公司Id', name: 'companyId' }} />
        <TextValue {...{ label: '客户名称', name: 'companyName' }} />
        <TextValue {...{ label: '退件单状态', name: 'orderStatus' }} />
      </Item>
      <div {...{ style: { marginBottom: 20 } }}>
        <Typography.Title
          {...{
            level: 5,
            children: (
              <Space>
                <span>{'退件单号'}</span>
                <span style={{ fontSize: 14, color: '#333' }}>
                  <Item {...{ name: 'podOrderNo', noStyle: true, children: <TextValue /> }} />
                </span>
              </Space>
            ),
          }}
        />
      </div>
      <RowLayout>
        <Item
          {...{
            label: '跟踪单号',
            name: 'returnTrackingNo',
            rules: [
              {
                required: true,
                type: 'string',
                max: 100,
                transform(value: any) {
                  return value?.trim?.();
                },
              },
            ],
            children: <Input {...{ disabled: isPortal, placeholder: '请输入' }} />,
          }}
        />
        <Item
          {...{
            label: '仓库组织',
            name: ['orderChild', 'targetWarehouseCode'],
            rules: [{ required: true }],
          }}
        >
          <SearchSelect
            {...{
              disabled: isPortal,
              request: (queryParam: string) =>
                apiActualWarehouseOptions({ queryParam }, { valueName: 'code' }),
            }}
          />
        </Item>
        <Item
          {...{
            label: '客户名称',
            name: 'companyInfo',
            children: (
              <SearchSelect
                {...{
                  disabled: isClaim || isPortal,
                  request: (name: string) =>
                    apiCustomerOmsSelect({ name, enabled: true }, 'companyId'),
                  onChange(val, option?: TypeCustomerOmsOption) {
                    form.setFieldsValue({
                      companyId: option?.companyId,
                      companyName: option?.companyName,
                    });
                  },
                }}
              />
            ),
          }}
        />
        <Item
          {...{
            label: '申请人',
            name: 'createByName',
            children: <TextValue />,
          }}
        />
      </RowLayout>
    </div>
  );
}
