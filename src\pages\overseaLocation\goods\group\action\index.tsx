import React, { useEffect, useState } from 'react';
import type { ConnectRC, Dispatch } from '@umijs/max';
import { connect, useIntl, useModel } from '@umijs/max';
import { FooterToolbar } from '@ant-design/pro-layout';
import { Spin, Form, Card, Row, Col, Space, Input, message, Button } from 'antd';
import H from 'history';
import { HiveModule } from '@portal/hive-sdk';
import { PageContainer } from '@/components/PageContainer';

import I18N from '@/utils/I18N';
import { CustomizeDivider } from '@/views';
import GoodsBelongSkuTableForm from '../../components/GoodsBelongSkuTableForm';
import GoodsPlatformSkuTableForm from '../../components/GoodsPlatformSkuTableForm';
import GoodsImageUpload from '../../components/GoodsImageUpload';
import { AddDeletedFlagByList, Exchange, GetPageQuery } from '@/utils/util';
import { CommonRulesMap } from '@/utils/rules';
import { historyGoPrev } from '@/pages/overseaLocation/utils';
import { overseaRulesMap } from '@/utils-business';
import { apiGoodsCombineCreate, apiGoodsCombineDetail, apiGoodsCombineUpdate } from '../groupApi';

const { history } = HiveModule;
const { Item } = Form;

type Iprops = {
  dispatch: Dispatch;
  loading: boolean;
};
const GoodsGroupActionPage: ConnectRC<Iprops> = ({ dispatch, loading }) => {
  const [form] = Form.useForm();
  const [goodsGroup, setGoodsGroup] = useState<any>({});
  const { id = '' } = GetPageQuery();

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    if (id) {
      //   const result = await dispatch({
      //     type: 'goodsGroup/getOverseaLocationGoodsGroupDetail',
      //     payload: { id },
      //   });

      //   if (result) {
      //   result.file = { url: result.pictureUrl, name: result.serverPictureName };
      //   setGoodsGroup(result);
      //   form.setFieldsValue(result);
      //   }

      const { data } = await apiGoodsCombineDetail({ id });

      setGoodsGroup(data);
      form.setFieldsValue(data);
    }
  };

  // 返回
  const handleGoBack = () => {
    historyGoPrev();
  };

  const onFinish = async (values: any) => {
    // const result = await dispatch({
    //   type: 'goodsGroup/createOrEditOverseaLocationGoodsGroup',
    //   payload: getFormData(values),
    // });

    await (id ? apiGoodsCombineUpdate : apiGoodsCombineCreate)(getFormData(values));

    // if (result) {
    message.success(
      id
        ? I18N.Src__Pages__OverseaLocation__Goods__Action.Index.modificationSucceeded
        : I18N.Src__Pages__OverseaLocation__Goods__Action.Index.successfullyAdded,
    );
    historyGoPrev();
    // }
  };

  const getFormData = (values: any) => {
    values.pictureUrl = values.file?.url;
    values.serverPictureName = values.file?.name;
    delete values.file;

    const formData = { ...values };

    if (id) {
      return {
        ...goodsGroup,
        ...formData,
        goodsList: AddDeletedFlagByList(goodsGroup.goodsList || [], formData.goodsList || []),
        platformList: AddDeletedFlagByList(
          goodsGroup.platformList || [],
          formData.platformList || [],
        ),
      };
    }

    return formData;
  };

  const onFinishFailed = (errorInfo: any) => {
    const names: string[] = errorInfo.errorFields[0].name;
    const labelNode = document.querySelector(
      `label[for="${names.length > 1 ? `${names[0]}_${names[1]}` : `${names[0]}`}"]`,
    );

    if (labelNode) {
      labelNode.scrollIntoView({ block: 'center' });
    } else {
      form.scrollToField(errorInfo.errorFields[0].name.toString());
    }
  };

  return (
    <PageContainer title={false} fixedHeader className="container-layout-card">
      <Spin spinning={false}>
        <Card>
          <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed}>
            <CustomizeDivider title={I18N.Src__Pages__Order__Detail.SoPane.essentialInformation} />
            <Row gutter={24}>
              <Col span={6}>
                <Item name="file">
                  <GoodsImageUpload />
                </Item>
              </Col>
              <Col span={18}>
                <Row gutter={24}>
                  <Col span={8}>
                    <Item
                      name="sku"
                      label="SKU"
                      rules={[...overseaRulesMap.overseaSKU]}
                      normalize={(value) => Exchange(value, true)}
                    >
                      <Input
                        placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
                        disabled={goodsGroup?.id}
                      />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      name="name"
                      label={I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName}
                      rules={[CommonRulesMap.commonStrVerify(255, true)]}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      name="enName"
                      label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.commodityEnglish}
                      rules={[
                        CommonRulesMap.commonStrVerify(255, true),
                        CommonRulesMap.commonNoChinese,
                      ]}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={24}>
                    <Item
                      name="comment"
                      label={I18N.Src__Pages__Order__Detail.SiItem.remarks}
                      rules={[CommonRulesMap.commonStrVerify(255)]}
                    >
                      <Input.TextArea
                        rows={3}
                        placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
                      />
                    </Item>
                  </Col>
                </Row>
              </Col>
            </Row>
            <CustomizeDivider
              title={I18N.Src__Pages__OverseaLocation__Goods__Group__Action.Index.belongingTo}
            />
            <GoodsBelongSkuTableForm
              name="goodsList"
              form={form}
              rules={[
                {
                  validator: async (_: any, names: any[]) => {
                    if (!names || names.length < 2 || names.length > 100) {
                      return Promise.reject(
                        new Error(
                          I18N.Src__Pages__OverseaLocation__Goods__Group__Action.Index.atLeast,
                        ),
                      );
                    }
                  },
                },
              ]}
              initGoods={goodsGroup?.goodsList}
            />
            <CustomizeDivider
              title={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.eCommercePlatform}
            />
            <GoodsPlatformSkuTableForm
              name="platformList"
              rules={[
                {
                  validator: async (_: any, names: any[]) => {
                    if (names?.length > 100) {
                      return Promise.reject(
                        new Error(
                          I18N.Src__Pages__OverseaLocation__Goods__Action.Index.platformAtMost,
                        ),
                      );
                    }
                  },
                },
              ]}
            />
          </Form>
        </Card>
        <FooterToolbar
          style={{ textAlign: 'center' }}
          extra={
            <Space>
              <Button onClick={handleGoBack}>{I18N.Src__Pages__Order__Detail.Index.return}</Button>
              <Button type="primary" onClick={() => form.submit()}>
                {I18N.Src__Components__UploadFileModal.Index.submit}
              </Button>
            </Space>
          }
        />
      </Spin>
    </PageContainer>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.models.goodsGroup,
}))(GoodsGroupActionPage);
