import I18N from '@/utils/I18N';

export const textMap = {
  //   txtAddGoods: '添加商品',
  //   txtChooseGoods: '选择商品',
  //   txtConfirmAdd: '确认添加',
  //   txtActualWare: '物理仓',
  //   txtVirtualWare: '虚拟仓',
  //   txtWarehouseOrganization: '仓库组织',
  //   txtGoodsSku: '商品SKU',
  //   txtGoodsPicture: '商品图片',
  //   txtGoodsName: '商品名称',
  //   txtGoodsNetWeight: '商品净重',
  //   txtGoodsVolume: '商品体积',
  //   txtInventoryQuantity: '库存数量',
  //   txtOutboundQuantity: '出库件数',
  //   txtMsgTotalQuantity: '请输入出库件数',
  //   txtInventoryNotEnough: '库存数量不足',
  //   txtOperation: '操作',
  //   txtMsgTotalValid: (obj: any) => {
  //     return `出库件数必须在整数${obj.val1}-${obj.val2}之间`;
  //   },
  //   txtMsgConfirmDelete: '是否要删除此行',
  //   txtDelete: '删除',
  txtPleaseChooseCompany: '请先选择公司名称',
  txtWmsSystemCode: '仓库系统CODE',
  txtOutboundWarehouseId: '建单时出库id',
  txtAddGoods: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.addItem,
  txtChooseGoods:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.selectProduct,
  txtConfirmAdd: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.confirmAdd,
  txtActualWare:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.chineseSymbols,
  txtVirtualWare: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
  txtWarehouseOrganization:
    I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.warehouseOrganization,
  txtGoodsSku: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
  txtGoodsPicture:
    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
  txtGoodsName: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
  txtGoodsNetWeight:
    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.netWeightOfCommodity,
  txtGoodsVolume:
    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
  txtInventoryQuantity:
    I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.inventoryQuantity,
  txtOutboundQuantity:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.numberOfOutboundPieces1,
  txtMsgTotalQuantity:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.pleaseInput,
  txtInventoryNotEnough:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.inventoryQuantity,
  txtMsgTotalValid: (obj: any) => {
    return I18N.template(
      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.numberOfOutboundPieces,
      { val1: obj.val1, val2: obj.val2 },
    );
  },
  txtOperation: I18N.Src__Pages__Common__Template.Index.operation,
  txtMsgConfirmDelete: I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete,
  txtDelete: I18N.Src__Pages__Common__Template.Index.delete,
} as const;
