import type { DatePickerProps, FormItemProps } from 'antd';
import { DatePicker } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import React from 'react';

const formatMap = {
  START: 'YYYY-MM-DD 00:00:00',
  END: 'YYYY-MM-DD 23:59:59',
};

/** 计算表单日期项的属性, 处理moment转换问题 */
export function calcItemDateProps(params?: {
  datePickerProps?: DatePickerProps;
  format?: string | 'START' | 'END';
}) {
  const { datePickerProps } = params || {};
  let { format } = params || {};

  format = formatMap[format as keyof typeof formatMap] || 'YYYY-MM-DD 00:00:00';

  return {
    getValueFromEvent(date?: Moment) {
      return date?.format(format);
    },
    getValueProps(value) {
      return { value: value ? moment(value) : undefined };
    },
    children: <DatePicker {...datePickerProps} />,
  } as FormItemProps;
}
