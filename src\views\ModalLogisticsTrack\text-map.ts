import I18N from '@/utils/I18N';
export const textMap = {
  txtModalTitle: I18N.Src__Views__ModalLogisticsTrack.Text_map.chineseSymbols3,
  txtQueryError: I18N.Src__Views__ModalLogisticsTrack.Text_map.chineseSymbols2,
  txtOk: I18N.Src__Views__ModalLogisticsTrack.Text_map.close,
  txtDesc1: I18N.Src__Views__ModalLogisticsTrack.Text_map.logisticsChannel,
  txtDesc2: I18N.Src__Views__ModalLogisticsTrack.Text_map.chineseSymbols1,
  txtDesc3: I18N.Src__Views__ModalLogisticsTrack.Text_map.trackingSheetNo,
  txtDesc4: I18N.Src__Views__ModalLogisticsTrack.Text_map.chineseSymbols,
} as const;
