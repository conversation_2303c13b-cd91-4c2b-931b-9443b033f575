import { request } from 'umi';

/** 导出列表接口 */
export function apiExportList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 数据源 1-费用清单 */
      businessType?: number;
      /** 申请时间-开始 */
      dateStart?: string;
      /** 申请时间-结束 */
      dateEnd?: string;
      /** 申请人 */
      userName?: string;
      /** 文件名称 */
      fileName?: string;
      /** 状态 1-解析中，2-解析成功，3-解析失败 */
      status?: number;
    };
  }>,
) {
  return request('/zouwu-oms-system/portal/system/export/search', { method: 'POST', data });
}
