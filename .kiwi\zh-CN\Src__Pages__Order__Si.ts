export default {
  Failure: {
    noOperationRight: '无操作权限',
    linkLost: '链接已失效',
    worldWideMailbox: '环世邮箱',
    universalTelephone: '环世电话',
    huanShi: '环世',
    ifInDoubt: '如有疑问请联系环世相关人员',
  },
  Index: {
    billOfLadingPreview: '提单预览',
    splitOrder: '{val1}{val2}',
    viewVoucherPreparation: '查看制单说明',
    sendOut: '发送SI',
    sendingSucceeded: '发送成功',
    stagingSucceeded: '暂存成功',
    sendPage: 'SI发送页',
    submissionPage: '提交页',
    temporaryBillOfLading: '暂存提单数据已发生更新，如有疑问请联系客服',
    orderInformation: '订单信息更新',
    orderPage: 'SI订单页面',
  },
  Record: {
    lengthNotExceeding: '长度不超过{val1}个字符',
    multipleMailboxes: '{val1} ({val2})',
    time: '时间',
    cannotBeEmpty: '不能为空|必须为英文|长度不超过{val1}个字符',
    totalCargoDescription: '总货物描述',
    totalShippingMark: '总唛头',
    downloadTemplate: '下载模版',
    batchImport: '批量导入箱型箱量',
    lengthNotExceeding1: '长度不超过{val1}个字符',
    editPort: '编辑港口显示',
    lengthNotExceeding2: '长度不超过{val1}个字符',
    preview: '预览',
    copyOriginalDocument: '复制原单信息',
    oneClickUpload: '一键上传',
    pleaseStoreTemporarily: '请先暂存内容',
    quickReplenishment: '快捷补料功能已上线，点击“一键上传”按钮前往上传补料文件吧',
    pleaseNote: '，请知悉',
    cannotExceed: '不能超过{val1}行,超出部分请补充至货物描述中',
    eachRowCannot: '每行不能超过{val1}字,超出部分请补充至货物描述中',
    mailbox: '邮箱',
  },
  Share: {
    sharePage: 'SI分享页',
  },
};
