export default {
  GoodsTableForm: {
    deleteBox: '删除箱',
    numberOfCases: '箱数量',
    mixedBox: '混箱',
    totalPieces: '总件数（PCS）',
    singleBoxPackaging: '单箱包装数量',
    productPicture: '商品图片',
    commodityVolume: '商品体积',
    netWeightOfCommodity: '商品净重',
    commodity1: '商品SKU',
    pleaseEnterTheTotal: '请输入总件数',
    totalNumberOfPieces: '总件数必须是正整数,且不超过9位',
    addList: '添加列表',
    confirmAdd: '确认添加',
    oneInABox: '一箱一件',
    oneInABox1: '一箱一件货',
  },
  Index: {
    characterLength: '字符长度不能超过50',
    submitForWarehousing: '提交入库申请',
    productInformation: '商品信息',
    firstJourneyLogistics: '头程物流发出后，需要补全货物及物流相关信息',
    logisticsInformation: '物流信息',
    pleaseEnterStandby: '请输入备注',
    characterLength1: '字符长度不能超过100',
    pleaseSelectPositive: '请选择正确的ETA',
    expectedArrival: '预计到达时间',
    expectedDelivery: '预计发货时间',
    pleaseSelectTheItem: '请选择目标入库仓',
    targetWarehousing: '目标入库仓',
    pleaseSelectSea: '请选择海外仓入库类型',
    overseasWarehousing: '海外仓入库类型',
    applicant: '申请人',
    corporateName: '公司名称',
    addReceipt: '新增入库单成功',
    editReceipt: '编辑入库单成功',
    pleaseFillInTheSupplier: '请填写商品信息，最少需要一条',
    temporaryStockIn: '暂存入库单成功',
    chineseSymbols: '🚀 ~ file: index.tsx ~ line 556 ~ businessType',
  },
  LogisticsTableForm: {
    addLogistics: '添加物流追踪单',
    onlyAddingIsAllowed: '只允许添加一条物流追踪单',
    pleaseInputBox: '请输入箱号',
    pleaseEnterTheNumber: '请输入数字或英文字符',
    pleaseSelectBox: '请选择箱型',
    pleaseEnter1: '请输入提单号',
    thisLogistics: '此物流追踪单号已存在',
    logisticsTracking: '物流跟踪单号',
    pleaseSelectPie: '请选择派送服务商',
    deliveryService: '派送服务商名称',
  },
} as const;
