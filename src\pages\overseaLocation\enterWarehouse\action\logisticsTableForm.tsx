import React from 'react';
import { Form, Table, Space, Select, Input, Button, Popconfirm, message } from 'antd';
import { ValidatorRule } from 'rc-field-form/lib/interface';
import { FormInstance } from 'antd/lib/form';
import { uniqBy } from 'lodash';
import I18N from '@/utils/I18N';
import { LOGISTICS_PROVIDER_TYPE } from '@/utils/const';
import { useRefresh } from '@/utils/util';
import REGEX from '@/utils/regex';

interface IProps {
  form: FormInstance;
  name: string;
  rule?: ValidatorRule[];
  containerNoList: any[];
}

const LogisticsTableForm: React.FC<IProps> = ({
  form,
  name: prefix,
  rule = [],
  containerNoList,
}) => {
  const tableData = form.getFieldValue(prefix) || [];
  const refresh = useRefresh();

  const validator = (_: any, value: any): Promise<any> => {
    const uniqLen = uniqBy(tableData, 'logisticsNo').length;

    if (uniqLen !== tableData.length) {
      return Promise.reject();
    }

    return Promise.resolve();
  };

  return (
    <Form.List name={prefix} rules={rule}>
      {(fields, { add, remove }, { errors }) => {
        const columns = [
          {
            title:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                .deliveryService,
            width: '24%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'logisticsProvider']}
                  fieldKey={[field.key, 'logisticsProvider']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                  ]}
                >
                  <Select
                    placeholder={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                        .pleaseSelectPie
                    }
                    style={{ width: '100%' }}
                    onChange={() => refresh.trigger()}
                  >
                    {LOGISTICS_PROVIDER_TYPE.map((item: any) => (
                      <Select.Option key={item.value} value={Number(item.value)}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              );
            },
          },
          {
            title:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                .logisticsTracking,
            width: '24%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'logisticsNo']}
                  fieldKey={[field.key, 'logisticsNo']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                    {
                      validator,
                      message:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                          .thisLogistics,
                    },
                    {
                      pattern: REGEX.NUMBER_AND_ENGLISH,
                      message:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                          .pleaseEnterTheNumber,
                    },
                    {
                      max: 100,
                      message:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                          .characterLength1,
                    },
                  ]}
                  // dependencies={[prefix]}
                >
                  <Input
                    onChange={() => refresh.trigger()}
                    placeholder={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                        .pleaseEnter1
                    }
                  />
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Order__BookingTracking.Index.boxType,
            width: '22%',
            render: (_: any, field: any) => {
              // 仅针对派送服务商名称为“MWB-海运(1)”时，方有此输入框
              return tableData[field.name] && tableData[field.name].logisticsProvider === 1 ? (
                <Form.Item
                  {...field}
                  name={[field.name, 'containerModel']}
                  fieldKey={[field.key, 'containerModel']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                  ]}
                >
                  <Select
                    placeholder={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                        .pleaseSelectBox
                    }
                  >
                    {containerNoList.map((item) => (
                      <Select.Option key={item.code} value={item.code}>
                        {item.enName}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null;
            },
          },
          {
            title: I18N.Src__Pages__Dynamic__Statistics.Index.caseNo1,
            width: '24%',
            render: (_: any, field: any) => {
              // 仅针对派送服务商名称为“MWB-海运(1)”时，方有此输入框
              return tableData[field.name] && tableData[field.name].logisticsProvider === 1 ? (
                <Form.Item
                  {...field}
                  name={[field.name, 'containerNo']}
                  fieldKey={[field.key, 'containerNo']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                    {
                      pattern: REGEX.NUMBER_AND_ENGLISH,
                      message:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                          .pleaseEnterTheNumber,
                    },
                  ]}
                >
                  <Input
                    placeholder={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                        .pleaseInputBox
                    }
                  />
                </Form.Item>
              ) : null;
            },
          },
          {
            title: I18N.Src__Pages__Common__Template.Index.operation,
            width: '6%',
            // valueType: 'option',
            // width: 200,
            render: (_: any, field: any) => {
              return (
                <Form.Item {...field} fieldKey={[field.key, 'action']}>
                  <span>
                    <Popconfirm
                      title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                      onConfirm={() => {
                        // if (fields.length <= 1) {
                        //   message.error('至少保留一条箱型箱量');
                        //   return;
                        // }

                        remove(field.name);
                        refresh.trigger();
                      }}
                    >
                      <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                    </Popconfirm>
                  </span>
                </Form.Item>
              );
            },
          },
        ];

        return (
          <>
            <Space style={{ marginBottom: 10 }}>
              <Button
                type="primary"
                onClick={() => {
                  if (tableData.length > 0) {
                    message.info(
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                        .onlyAddingIsAllowed,
                    );
                  } else {
                    add();
                    refresh.trigger();
                  }
                }}
              >
                {
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                    .addLogistics
                }
              </Button>
              {/* <Button>批量导入物流信息</Button> <Button type="link">点击下载导入模版</Button> */}
            </Space>
            <Table
              columns={columns}
              dataSource={fields}
              pagination={false}
              size="small"
              // footer={
              //   () => <Form.ErrorList errors={errrors}/>
              // }
            />
            <Form.ErrorList errors={errors} />
          </>
        );
      }}
    </Form.List>
  );
};

export default LogisticsTableForm;
