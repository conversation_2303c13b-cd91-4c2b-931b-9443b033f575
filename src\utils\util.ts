// kiwi-disable
import { useCallback, useMemo, useState } from 'react';
import { parse } from 'qs';
import type { Moment } from 'moment';
import moment from 'moment';
import { DOWNLOAD_FORM_ACTION_URL, PRINT_DOCUMENT_URL } from './const';

export const BlobToBase64 = (blob: Blob) => {
  if (!blob) return;

  // eslint-disable-next-line consistent-return
  return new Promise((resolve) => {
    const reader = new FileReader();

    reader.readAsDataURL(blob);
    reader.onload = () => {
      resolve(reader.result);
    };
  });
};

export const IsUrl = (path: string): boolean => {
  const reg =
    /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

  return reg.test(path);
};

export const GetPageQuery = (): any => {
  return parse(window.location.href.split('?')[1]);
};

export const GetPageQueryStr = (): any => {
  return window.location.href.split('?')[1];
};

// /userinfo/2144/id => ['/userinfo','/useinfo/2144,'/userindo/2144/id']
export const urlToList = (url: string) => {
  const urllist = url.split('/').filter((i) => i);

  return urllist.map((urlItem, index) => `/${urllist.slice(0, index + 1).join('/')}`);
};

const DATE_BASE_NUM = 1514736000;

export const FormatDate = (timestamp: number, type = 'YYYY-MM-DD', unFormat = false) => {
  if (!timestamp) return '';
  const dateStr = (timestamp + DATE_BASE_NUM) * 1000;

  return unFormat ? moment(dateStr) : moment(dateStr).format(type);
};

export const UnFormatDate = (date: string) => {
  return moment(date).startOf('day').valueOf() / 1000 - DATE_BASE_NUM;
};

export const rangeDateFormate = (list: Moment[] | undefined): any[] =>
  list?.map((item: Moment) => moment(item.format('YYYY-MM-DD')).valueOf() / 1000 - DATE_BASE_NUM) ||
  [];

/**
 * 新窗口打开下载附件
 * @param urls
 * @param fileName
 */
export const OpenAttachmentFile = (
  urls: string,
  fileName: string,
  ncHistoryOrder: boolean,
): any => {
  if (!urls) return;
  if (ncHistoryOrder) {
    const link = `${DOWNLOAD_FORM_ACTION_URL}=${urls}&fileName=${fileName}`;

    const newWindow: any = window.open();

    const newForm = `<form action="${link}" method="post">
        <button type="submit" style="display:none;" value="提交"></button>
        </form>`;

    newWindow.document.body.innerHTML = newForm;
    newWindow.document.querySelector('button').click();
  } else {
    window.open(urls);
  }
};

export const OpenWindow = (path: string, id: string) => {
  if (!path) return;
  const a = document.createElement('a');

  const pathArray = path.split('/');
  const fileName = pathArray && pathArray.length ? pathArray[pathArray.length - 1] : '';
  const url = PRINT_DOCUMENT_URL + encodeURIComponent(path);

  fetch(url).then((response) => {
    response.blob().then((myBlob) => {
      const objectURL = URL.createObjectURL(myBlob);

      a.setAttribute('href', objectURL);
      a.setAttribute('download', fileName);
      a.setAttribute('target', '_blank');
      a.setAttribute('id', id);
      if (!document.getElementById(id)) document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
    });
  });
};

export const Exchange = (s: string, toUpperCase?: boolean): string => {
  /* 正则转换中文标点 */
  s = s.replace(/：/g, ':');
  s = s.replace(/。/g, '.');
  s = s.replace(/“/g, '"');
  s = s.replace(/”/g, '"');
  s = s.replace(/【/g, '[');
  s = s.replace(/】/g, ']');
  s = s.replace(/《/g, '<');
  s = s.replace(/》/g, '>');
  s = s.replace(/，/g, ',');
  s = s.replace(/？/g, '?');
  s = s.replace(/、/g, ',');
  s = s.replace(/；/g, ';');
  s = s.replace(/（/g, '(');
  s = s.replace(/）/g, ')');
  s = s.replace(/‘/g, "'");
  s = s.replace(/’/g, "'");
  s = s.replace(/『/g, '[');
  s = s.replace(/』/g, ']');
  s = s.replace(/「/g, '[');
  s = s.replace(/」/g, ']');
  s = s.replace(/﹃/g, '[');
  s = s.replace(/﹄/g, ']');
  s = s.replace(/〔/g, '{');
  s = s.replace(/〕/g, '}');
  s = s.replace(/—/g, '-');
  s = s.replace(/·/g, '.');
  /* 正则转换全角为半角 */

  // 字符串先转化成数组
  const list = s.split('');

  for (let i = 0; i < list.length; i += 1) {
    // 全角空格处理
    if (list[i].charCodeAt(0) === 12288) {
      list[i] = String.fromCharCode(32);
    }
    /* 其他全角 */
    if (list[i].charCodeAt(0) > 0xff00 && list[i].charCodeAt(0) < 0xffef) {
      list[i] = String.fromCharCode(list[i].charCodeAt(0) - 65248);
    }
  }
  // 数组转换成字符串
  s = list.join('');
  return toUpperCase ? s.toUpperCase() : s;
};

// 字典数据
// 1.老数据 2.gm2 3.新老都可以
// ncHistoryOrder : false: 2,3
// ncHistoryOrder : true: 1,3

// 直接查
// ncHistoryOrder
// ncHistoryOrder ：  ture ? 不管 ： 40hc 45hc

type MapType = Record<string, any[]>;
/**
 * 根据ncHistoryOrder 修改字典数据
 * @param dataMap
 * @param ncHistoryOrder
 */
export const changeDictData = (dataMap: MapType, ncHistoryOrder: boolean): MapType => {
  const filterList = ncHistoryOrder ? [1, 3] : [2, 3];
  const filterDataList: [string, any[]][] = Object.entries(dataMap).map((item: [string, any[]]) => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    return [item[0], item[1].filter((item: any) => filterList.includes(item.dictSource))];
  });
  const combineData: MapType = {};

  filterDataList.forEach((item: [string, any[]]) => {
    combineData[item[0]] = item[1];
  });
  return combineData;
};

export function useRefresh() {
  /*
  使用的时候尽量不要解构, 语义更明确
  sign 是为了配合trigger 触发依赖更新
  const refresh = useRefresh()
  refresh 作为依赖
  refresh.trigger() 触发sign更新
   */
  const [sign, setValue] = useState(0);
  const trigger = useCallback(() => setValue((signOld) => signOld + 1), []);
  const refresh = useMemo(() => {
    return { sign, trigger };
  }, [sign, trigger]);

  return refresh;
}

/**
 * 列表加删除行标记
 */
export function AddDeletedFlagByList(oldList: any[] = [], newList: any[] = []) {
  const ids = newList
    .filter((item) => item.id !== null && item.id !== undefined)
    .map((item) => item.id);

  oldList
    .filter((item) => !ids.includes(item.id))
    .forEach((item) => {
      item.deleted = true;
      newList.push(item);
    });

  return newList;
}

// 下划线转驼峰
export const HumpToUnderline = (str: string) => {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
};

type AccessType =
  | 'String'
  | 'Object'
  | 'Number'
  | 'Boolean'
  | 'Symbol'
  | 'Undefined'
  | 'Null'
  | 'AsyncFunction'
  | 'Function'
  | 'Date'
  | 'Array'
  | 'RegExp'
  | 'Error'
  | 'HTMLDocument'
  | 'global';

/**
 * 类型获取
 * @param access 参数
 */
export const getAccessType = (access: any): AccessType => {
  return Object.prototype.toString.call(access).slice(8, -1) as AccessType;
};

/**
 * 对象转FormData对象
 * @param obj
 * @param ignore 过滤的内容，如`null`、`undefined`、`''`
 * @returns
 */
export const objectToFormData = (obj: Record<string, any>, ignore = [null, undefined, '']) => {
  if (!obj) return null;
  const formdata = new FormData();

  // eslint-disable-next-line no-restricted-syntax
  for (const key of Object.keys(obj)) {
    // eslint-disable-next-line no-continue
    if (ignore.includes(obj[key])) continue;

    if (getAccessType(obj[key]) === 'Array') {
      // eslint-disable-next-line no-restricted-syntax
      for (const value of obj[key]) {
        formdata.append(key, value);
      }
    } else {
      formdata.append(key, obj[key]);
    }
  }

  return formdata;
};
