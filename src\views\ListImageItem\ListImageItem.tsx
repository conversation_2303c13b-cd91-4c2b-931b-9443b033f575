import './ListImageItem.less';
import { getImagePath } from '@/utils-business';
import type { ImageProps } from 'antd';
import { Image } from 'antd';
import classNames from 'classnames';
import { EyeOutlined } from '@ant-design/icons';
import React from 'react';

/** 列表图片展示 */
export default function ListImageItem(props: ImageProps) {
  const { src, className, style, ...args } = props;

  return (
    <div className={classNames(['list-image-item', className])} style={style}>
      <Image
        {...{
          ...args,
          src: getImagePath(src),
          preview: {
            ...(args.preview as Exclude<ImageProps['preview'], boolean>),
            mask: (
              <div>
                <EyeOutlined />
              </div>
            ),
          },
        }}
      />
    </div>
  );
}
