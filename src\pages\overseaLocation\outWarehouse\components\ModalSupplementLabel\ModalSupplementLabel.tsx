import type { TypeSimpleUploadProps, TypeZouModalRef } from '@/components';
import { LoadButton, ZouModal, SimpleUpload } from '@/components';

import { TextValue } from '@/views';
import { tipsUtils, useTimeSeries } from '@/utils';
import { Form, Input, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { apiOutboundSupplementLabel } from '../../OutboundApi';
import { textMap } from './text-map';
import { EnumOutboundBusinessType, EnumOutboundFileType } from '../../OutboundEnum';
import { EnumUploadFileType, getUploadPropsConfig } from '@/api';

type TypeProps = {
  /** 组件参数 */
};

type TypeOpenParams = {
  /** 申请单号 */
  podOrderNo?: string;
  /** 订单附件集合 */
  fileList?: NsOutbound.TypeFileItem[];
  /** 追踪单号 */
  trackingNo?: string;
  /** 物流渠道 */
  customerChannelCode?: string;
  /** 订单业务类型 */
  businessType?: EnumOutboundBusinessType;
};

type TypeFormData = TypeOpenParams;

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
  businessType?: EnumOutboundBusinessType;
};

export type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

/** 出库补充面单 */
export default React.forwardRef(function ModalSupplementLabel(
  props: TypeProps,
  ref: React.Ref<TypeModalRef | undefined>,
) {
  const { modalRef, asyncInit, asyncClear, asyncSubmit, form, isTOB } = useConfig({ props });

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams, operate?: TypeOperateMap) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title: textMap.txtSupplementarySheet,
          afterClose() {
            asyncClear();
          },
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      <Form {...{ form, labelCol: { span: 5 } }}>
        <Form.Item
          {...{
            label: textMap.txtPodOrderNo,
            name: 'podOrderNo',
          }}
        >
          <TextValue />
        </Form.Item>
        <Form.Item
          {...{
            label: textMap.txtCustomerChannelCode,
            name: 'customerChannelCode',
          }}
        >
          <TextValue />
        </Form.Item>
        <Form.Item
          {...{
            label: textMap.txtTrackingNo,
            name: 'trackingNo',
            rules: [{ required: true }],
          }}
        >
          <Input />
        </Form.Item>
        <Form.Item
          {...{
            label: textMap.txtLabelFile,
            name: 'fileListLabel',
            rules: [
              {
                required: true,
                // message: textMap.txtMsgChooseFile,
              },
            ],
          }}
        >
          <SimpleUpload
            {...{
              ...calcUploadProps(EnumOutboundFileType.label),
            }}
          >
            <LoadButton>{textMap.txtUploadFile}</LoadButton>
          </SimpleUpload>
        </Form.Item>
        {isTOB && (
          <Form.Item
            {...{
              label: 'BOL',
              name: 'fileListBOL',
            }}
          >
            <SimpleUpload
              {...{
                ...calcUploadProps(EnumOutboundFileType.BOL),
              }}
            >
              <LoadButton>{textMap.txtUploadFile}</LoadButton>
            </SimpleUpload>
          </Form.Item>
        )}
      </Form>
    </ZouModal>
  );
});

function useConfig({ props }: { props: TypeProps }) {
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<any>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const modalRef = useRef<TypeZouModalRef>();
  const isTOB = detailData.businessType === EnumOutboundBusinessType.tob;

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    const initData = transDetail(params);

    setDetailData(initData);
    form.setFieldsValue(initData);
    setOperateMap(operate || {});
  }

  async function asyncClear() {
    setOperateMap({});
    setDetailData({});
    form.resetFields();
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();

    await apiOutboundSupplementLabel({
      ...formData,
      fileListLabel: undefined,
      fileListBOL: undefined,
      fileList: [...(formData?.fileListLabel || []), ...(formData?.fileListBOL || [])],
    });
    message.success(tipsUtils.TIPS_FORM_SUBMIT_SUCCESS);
    operateMap?.submitSuccessCB?.();
  }

  return { detailData, asyncInit, asyncClear, asyncSubmit, form, modalRef, isTOB };
}

/** 转换DetailData */
function transDetail(inData: TypeFormData) {
  return {
    ...inData,
    fileListLabel: (inData?.fileList || [])
      .filter((item) => item.fileType === EnumOutboundFileType.label)
      .map((item) => ({
        ...item,
        name: item.fileName,
        url: item.filePath,
        status: 'done',
      })),
    fileListBOL: (inData?.fileList || [])
      .filter((item) => item.fileType === EnumOutboundFileType.BOL)
      .map((item) => ({
        ...item,
        name: item.fileName,
        url: item.filePath,
        status: 'done',
      })),
  };
}

function calcUploadProps(fileType: EnumOutboundFileType) {
  const uploadProps = {
    ...getUploadPropsConfig(),
    fileSuffix: ['pdf', 'png'],
    multiple: false,
    maxCount: 1,
    data: {
      pathType: EnumUploadFileType.temp,
    },
    allowManual: false,
    allowValidFileStatus: true,
    onOriginChange(info) {
      getUploadPropsConfig.fileDoneProcess(info.file);
      info.file.fileType = fileType;
    },
  } as TypeSimpleUploadProps<{ fileType: EnumOutboundFileType }>;

  return uploadProps;
}
