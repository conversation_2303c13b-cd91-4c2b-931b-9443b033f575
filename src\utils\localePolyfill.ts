import { setLocale } from '@umijs/max';
import Cookies from 'js-cookie';

/**
 * 兼容kiwi的语言标识符
 */
export const localePolyfill = () => {
  const currentLanguage = Cookies.get('language_website');
  const navigatorLanguage = window.navigator.language.toLocaleLowerCase();

  if (currentLanguage) {
    setLocale(currentLanguage === 'en' ? 'en-US' : 'zh-CN');
    Cookies.set('dzg_language', currentLanguage === 'en' ? 'en-US' : 'zh-CN');
  } else {
    Cookies.set(
      'language_website',
      navigatorLanguage === 'zh-cn' || navigatorLanguage === 'zh' ? 'cn' : 'en',
    );
    Cookies.set(
      'dzg_language',
      navigatorLanguage === 'zh-cn' || navigatorLanguage === 'zh' ? 'zh-CN' : 'en-US',
    );
    setLocale(navigatorLanguage === 'zh-cn' || navigatorLanguage === 'zh' ? 'zh-CN' : 'en-US');
  }
};
