import type { Effect, Reducer } from 'umi';
import {
  queryPodNo,
  queryWarehouseList,
  queryDispatchServiceList,
  queryDistributionChannel,
} from '@/service/overseaLocation';

export interface StateType {
  warehouseList: any[];
  entryWarehouseList: any[];
  entryWarehouseTotal: number;
  entryWarehouseStatusCount: any;
}

export interface Modal {
  namespace: string;
  state: StateType;
  effects: {
    getPodNo: Effect;
    getWarehouseList: Effect;
    getDispatchServiceList: Effect;
    getDistributionChannel: Effect;
  };
  reducers: {
    saveWarhouseList: Reducer<any>;
  };
}

const Model: Modal = {
  namespace: 'oversea_location',
  state: {
    warehouseList: [],
    entryWarehouseList: [],
    entryWarehouseTotal: 0,
    entryWarehouseStatusCount: {},
  },
  effects: {
    /** @deprecated 已废弃迁移至OMS */
    *getPodNo({ payload }, { call, put }) {
      const response = yield call(queryPodNo, payload);

      if (response && response.result) {
        return response.result;
      }
    },
    /** @deprecated 已废弃, 请使用apiQueryWarehouseOptions */
    *getWarehouseList({ payload }, { call, put }) {
      const response = yield call(queryWarehouseList, payload);

      if (response && response.result) {
        yield put({
          type: 'saveWarhouseList',
          payload: response.result,
        });

        return response.result;
      }
    },
    /** @deprecated 已废弃 */
    *getDispatchServiceList({ payload }, { call, put }) {
      const response = yield call(queryDispatchServiceList, payload);

      if (response && response.result) {
        // yield put({
        //   type: 'saveWarhouseList',
        //   payload: response.result
        // });

        return response.result;
      }
    },
    /** 配送服务名称
     * @deprecated 已废弃
     */
    *getDistributionChannel({ payload }, { call, put }) {
      const response = yield call(queryDistributionChannel, payload);

      if (response && response.result) {
        const newList = transList(response.result || []);

        return newList;
      }
    },
  },
  reducers: {
    saveWarhouseList(state, { payload }) {
      return {
        ...state,
        warehouseList: payload.records || [],
      };
    },
  },
};

/** 将后端返回数据按照label、value、children形式转换数据 */
function transList(data: any[]) {
  return data?.map((item) => {
    return {
      ...item,
      label: item.dispatchServiceTypeName,
      value: item.dispatchServiceTypeCode,
      children: item.channelList.map((i: any) => {
        return {
          ...i,
          value: i.dispatchServiceName,
          label: i.dispatchServiceNameStr,
        };
      }),
    };
  });
}
export default Model;
