import I18N from '@/utils/I18N';
import type { TypeZouModalRef } from '@/components';
import { ZouModal } from '@/components';
import { TextValue } from '@/views';
import { tipsUtils, useTimeSeries } from '@/utils';
import { Alert, Form, Input, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { apiOutboundLogisticsInterceptApply } from '../../OutboundApi';

type TypeProps = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};

type TypeOpenParams = {
  /** 出库单号 */
  orderId?: string;
  /** 申请单号 */
  podOrderNo?: string;
};

export type TypeModalInterceptApplyRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
  /** 是否一单多件 */
  isMultiPackage?: boolean;
};

/** 弹窗-物流拦截 */
export default React.forwardRef(function ModalInterceptApply(
  props: TypeProps,
  ref: React.Ref<TypeModalInterceptApplyRef | undefined>,
) {
  const { modalRef, asyncInit, asyncClear, asyncSubmit, form, operateMap } = useConfig({ props });

  useImperativeHandle(ref, () => ({
    async open(params, operate) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title:
            I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply
              .ModalInterceptApply.chineseSymbols1,
          afterClose() {
            asyncClear();
          },
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      {operateMap?.isMultiPackage && (
        <Alert
          {...{
            description:
              '当前订单为一单多件，确定发起物流拦截吗？发起拦截申请将拦截当前订单下的所有面单。',
            style: { marginBottom: 6 },
            type: 'warning',
            showIcon: true,
          }}
        />
      )}
      <Form {...{ form, labelCol: { span: 4 } }}>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply
                .ModalInterceptApply.applicationNo,
            name: 'podOrderNo',
          }}
        >
          <TextValue />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply
                .ModalInterceptApply.chineseSymbols,
            name: 'remark',
            rules: [{ max: 255 }],
          }}
        >
          <Input.TextArea />
        </Form.Item>
        <Form.Item hidden>
          <TextValue
            {...{
              label:
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply
                  .ModalInterceptApply.outboundOrder,
              name: 'orderId',
            }}
          />
        </Form.Item>
      </Form>
    </ZouModal>
  );
});

function useConfig({ props }: { props: TypeProps }) {
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<any>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const modalRef = useRef<TypeZouModalRef>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    const initData = transDetail(params);

    setDetailData(initData);
    form.setFieldsValue(initData);
    setOperateMap(operate || {});
  }

  async function asyncClear() {
    setDetailData({});
    form.resetFields();
    setOperateMap({});
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();

    await apiOutboundLogisticsInterceptApply(formData);
    message.success(tipsUtils.TIPS_FORM_SUBMIT_SUCCESS);
    operateMap?.submitSuccessCB?.();
  }

  return { detailData, asyncInit, asyncClear, asyncSubmit, form, modalRef, operateMap };
}

/** 转换DetailData */
function transDetail(inData: TypeOpenParams) {
  return {
    ...inData,
  };
}
