import { promiseCacheApi } from '@/utils';
import { request } from 'umi';
import { apiQueryWarehouseOptions } from '@/pages/overseaLocation/api/warehouseApi';

export type TypeWarehouseOption = Awaited<ReturnType<typeof apiQueryWarehouseOptions>>[0];

/** 仓库类型 */
export enum EnumWarehouseType {
  /** 虚拟仓库 */
  virtual = 9,
}

/** 物理+虚拟仓下拉框, 默认查询全部停启用 */
export const apiMixWarehouseOptions = async function (
  ...[data, options]: Parameters<typeof apiQueryWarehouseOptions>
) {
  return apiQueryWarehouseOptions(
    {
      ...data,
      /** 不传就混合查询 */
      virtual: undefined,
    },
    options,
  );
};

/** 虚拟仓搜索下拉, 默认只返回启用 */
export function apiVirtualWarehouseOptions(
  ...[data, options]: Parameters<typeof apiQueryWarehouseOptions>
) {
  return apiQueryWarehouseOptions(
    {
      warehouseEnable: true,
      ...data,
      virtual: true,
    },
    options,
  );
}

type TypeWarehouseOptionsParams = {
  data: Parameters<typeof apiQueryWarehouseOptions>[0];
  options: Parameters<typeof apiQueryWarehouseOptions>[1];
};

/** 启用的物理仓下拉框 */
export const apiActualWarehouseOptions = promiseCacheApi(
  async function (
    data: TypeWarehouseOptionsParams['data'],
    options: TypeWarehouseOptionsParams['options'],
  ) {
    return apiQueryWarehouseOptions({ warehouseEnable: true, ...data }, options);
  },
  { expired: 10000 },
);
