#前端
stages:
  - deploy

sonarqube_master_job:
  stage: deploy
  only:
    - develop
  image: registry.900jit.com/yw/dzg-mvn-sonar:0.6
  script:
    - mvn sonar:sonar  -Dsonar.sources=src -Dsonar.sourceEncoding=UTF-8 -Dsonar.exclusions=src/service/**

sonarqube_feature_job:
  stage: deploy
  image: registry.900jit.com/yw/dzg-mvn-sonar:0.6
  script:
    - mvn sonar:sonar  -Dsonar.sources=src -Dsonar.sourceEncoding=UTF-8 -Dsonar.exclusions=src/service/** -Dsonar.branch.name=$CI_COMMIT_REF_NAME
  when: manual