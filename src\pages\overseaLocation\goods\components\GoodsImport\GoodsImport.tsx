import { Button, message, Modal } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import ProTable from '@ant-design/pro-table';
import I18N from '@/utils/I18N';
import { LoadButton, ModalUpload } from '@/pages/overseaLocation/components';
import css from './GoodsImport.less';
import { apiGoodsBatchImport, apiGoodsImportTemplateDownload } from '../../goodsApi';
import { apiUploadFileNew, EnumUploadFileType } from '@/pages/overseaLocation/api';

type TypeProps = {
  submitSuccessCB?: () => void;
};

/** 商品批量新增导入 */
export default function GoodsImport(props: TypeProps) {
  const errorModalRef = useRef<any>() as TypeModalErrorProps['modalRef'];

  return (
    <>
      <ModalUpload
        {...{
          className: css.GoodsImport,
          fileSuffix: ['xlsx', 'xls'],
          modalProps: {
            title: '批量上传商品',
          },
          btnExtra: () => (
            <LoadButton
              onClick={async () => {
                await apiGoodsImportTemplateDownload();
              }}
            >
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols14
              }
            </LoadButton>
          ),
          limitDesc: [
            <>
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols13
              }
              <span className={css.tips}>
                {
                  I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                    .chineseSymbols11
                }
              </span>
            </>,
            <>
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols12
              }
              <span className={css.tips}>
                {
                  I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                    .chineseSymbols11
                }
              </span>
            </>,
            I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
              .chineseSymbols10,
            <span className={css.tips} key="a">
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols9
              }
            </span>,
            I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
              .chineseSymbols8,
          ],
          async asyncSubmit({ fileList, modalControl }) {
            const file = fileList[0];
            const originFile = file.originFileObj;
            const { data: uploadRes } = await apiUploadFileNew({
              pathType: EnumUploadFileType.excel,
              file: originFile!,
            });

            const filePath = uploadRes.filePath;
            const { data } = await apiGoodsBatchImport({ filePath });

            if (data?.records?.length !== 0) {
              errorModalRef?.current?.open(data);
            } else {
              message.success(
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols6,
              );
              props.submitSuccessCB?.();
            }
            modalControl.close();
          },
        }}
      >
        <Button>
          {
            I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
              .chineseSymbols5
          }
        </Button>
      </ModalUpload>
      <ModalError modalRef={errorModalRef} />
    </>
  );
}

type TypeErrorData = Awaited<ReturnType<typeof apiGoodsBatchImport>>['data'];
type TypeModalErrorProps = {
  modalRef?: React.MutableRefObject<{
    open: (data: TypeErrorData) => void;
  }>;
};

/** 导入错误信息展示 */
function ModalError(props: TypeModalErrorProps) {
  const [visible, setVisible] = useState(false);
  const dataRef = useRef<TypeErrorData>();
  const { records, totalSize } = dataRef.current || {};

  useImperativeHandle(props.modalRef, () => ({
    open: (data) => {
      dataRef.current = data;
      setVisible(true);
    },
  }));

  return (
    <Modal
      {...{
        title:
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols4,
        width: 680,
        visible,
        maskClosable: false,
        keyboard: false,
        cancelText: I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.close,
        okButtonProps: {
          style: { display: 'none' },
        },
        className: css.GoodsImport,
        onCancel() {
          setVisible(false);
        },
      }}
    >
      <div className={css.errorTips}>
        {I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport.common}
        {totalSize}
        {
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols3
        }
        <span className={css.tips}>{records?.length}</span>
        {
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols2
        }
      </div>
      <ProTable<TypeErrorData['records'][number]>
        {...{
          cardProps: false,
          search: false,
          toolBarRender: false,
          dataSource: records,
          scroll: { x: '100%' },
          pagination: {
            showSizeChanger: false,
            pageSize: 10,
          },
          columns: [
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .serialNo,
              dataIndex: 'index',
            },
            { title: 'SKU', dataIndex: 'sku' },
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols1,
              dataIndex: 'recordLine',
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols,
              dataIndex: 'message',
              render(dom, record) {
                return (
                  <>
                    {record.message
                      .split(';')
                      .filter(Boolean)
                      .map((item) => (
                        <div key={item}>{item};</div>
                      ))}
                  </>
                );
              },
            },
          ],
        }}
      />
    </Modal>
  );
}
