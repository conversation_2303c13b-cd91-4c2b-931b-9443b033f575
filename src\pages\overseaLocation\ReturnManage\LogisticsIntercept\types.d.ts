/** 物流拦截列表数据结构 */
type TypeLogisticsInterceptTB = {
  /** 主键 id */
  id: string;
  /** 出库订单id */
  orderId?: string;
  /** 拦截状态(待拦截PENDING  拦截中INTERCEPTING  拦截成功SUCCEEDED 拦截失败FAILED) */
  interceptStatus?: string;
  /** 拦截状态: 后端翻译 */
  interceptStatusName?: string;
  /** 备注 */
  remark?: string;
  /** 出库单号 */
  podOrderNo?: string;
  /** 客户销售单号 */
  customerSalesNo?: string;
  /** 客户关联单号 */
  customerRelatedNo?: string;
  /** 物流追踪单号 */
  trackingNo?: string;
  /** sku信息 */
  skuList?: string[];
  /** 仓库id */
  warehouseId?: string;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 公司唯一标识 */
  companyId?: string;
  /** 客户名称 | 公司名称 */
  companyName?: string;
  /** 创建人id */
  createById?: number;
  /** 创建人名称 */
  createByName?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新人id */
  updateById?: number;
  /** 更新人名称 */
  updateByName?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 快递信息 */
  orderExpress?: {
    /** 收件人姓名 */
    recipientName?: string;
    /** 收件人国家 */
    recipientCountry?: string;
    /** 收件人省/州 */
    recipientProvince?: string;
    /** 收件人城市 */
    recipientCity?: string;
    /** 收件人邮编 */
    recipientPostcode?: string;
    /** 收件人分邮编 */
    recipientBranchPostcode?: string;
    /** 收件人详细地址1 */
    recipientAddress1?: string;
    /** 收件人详细地址2 */
    recipientAddress2?: string;
  };
  /** 配送信息 */
  orderDelivery?: {
    /** 派送服务类型 | 物流类型 */
    dispatchServiceType?: number;
    /** 派送服务名称 | 物流渠道 */
    dispatchServiceName?: string;
    /** 派送服务名称 | 物流渠道 : 后端翻译*/
    dispatchServiceNameName?: string;
    /** 客户渠道code */
    customerChannelCode?: string;
    /** 客户渠道名称 : 后端翻译 */
    customerChannelName?: string;
    /** 派送服务商 | 渠道商 */
    dispatchServiceProvider?: string;
    /** 派送服务商: 后端翻译 */
    dispatchServiceProviderName?: string;
    /** 销售运单 (追踪单号) */
    trackingNo?: string;
    /** 追踪物流进展 */
    trackingProgress?: string;
    /** 物流轨迹查询状态 */
    trackNode?: string;
  };
};
