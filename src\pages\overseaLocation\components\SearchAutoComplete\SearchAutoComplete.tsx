import type { AutoCompleteProps } from 'antd';
import { AutoComplete } from 'antd';
import type { Ref } from 'react';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { useRequest } from 'umi';
import { textMap } from './text-map';

export type TypeSearchAutoCompleteProps = {
  /** 回填到输入框的字段, 即value, 默认取 label
   * @ 下拉框显示啥回填啥
   */
  optionLabelProp?: string;
  /** 是否自动加载, 默认false */
  autoLoad?: boolean;
  /** focus是否可以加载数据 默认 是 */
  allowFocusLoad?: boolean;
  /** 请求方法 */
  request?: (...args: any[]) => Promise<{ label: React.ReactNode; value: any; [P: string]: any }[]>;
} & AutoCompleteProps;

export type TypeSearchAutoCompleteRef = {
  isLoading: boolean;
  options: any[];
  fetchOptions: (...args: any[]) => Promise<void>;
  /** 用于联动时发起Options请求,会立刻屏蔽旧的Options */
  fetchOptionsForCascade: (...args: any[]) => Promise<void>;
};

const SearchAutoComplete = React.forwardRef(
  (inProps: TypeSearchAutoCompleteProps, ref: Ref<TypeSearchAutoCompleteRef | undefined>) => {
    const {
      request,
      allowFocusLoad = true,
      autoLoad = false,
      optionLabelProp = 'label',
      ...rest
    } = inProps;
    /** 是否因联动发起请求, 会屏蔽options展示,直到新的请求完成 */
    const [isCascadeLoading, setIsCascadeLoading] = useState(false);
    const {
      loading,
      data: dataOptions,
      run,
    } = useRequest(request!, {
      debounceInterval: 300,
      formatResult: (response) => response,
      manual: true,
      onSuccess() {
        setIsCascadeLoading((oldVal) => {
          return oldVal === true ? false : oldVal;
        });
      },
    });
    /** 合并options用于渲染 */
    const newOptions = dataOptions || [];
    const fetchOptions = async (searchValue?: string) => {
      /** run返回是null, 根本不是异步 */
      request && run(searchValue?.trim());
    };
    const fetchOptionsForCascade = async (...args: any[]) => {
      setIsCascadeLoading(true);
      fetchOptions(...args);
    };
    const defaultProps = {
      placeholder: textMap.placeholder,
      allowClear: true,
      filterOption: (inputValue, option) => {
        let val = inputValue;
        let optionValue = (option?.value ? `${option?.value}` : option?.value) as string;

        /** 默认开启trim和大小写模糊 */
        val = inputValue?.trim?.();
        val = val?.toUpperCase?.();
        optionValue = optionValue.toUpperCase?.();

        return optionValue.indexOf(val) !== -1;
      },
      onSearch: (searchValue) => {
        fetchOptions(searchValue);
        inProps.onSearch?.(searchValue);
      },
      onFocus(e) {
        allowFocusLoad && fetchOptions();
        inProps.onFocus?.(e);
      },
    } as AutoCompleteProps;
    const props = {
      ...defaultProps,
      ...rest,
    };

    useEffect(() => {
      /** 初始化自动加载 */
      autoLoad && fetchOptions();
    }, []);
    useImperativeHandle(ref, () => ({
      isLoading: loading,
      options: newOptions,
      fetchOptions,
      fetchOptionsForCascade,
    }));

    return (
      <AutoComplete {...props}>
        {(isCascadeLoading ? [] : newOptions).map((item) => {
          return (
            <AutoComplete.Option key={item.key ?? item.value} value={item[optionLabelProp]}>
              {item.label}
            </AutoComplete.Option>
          );
        })}
      </AutoComplete>
    );
  },
);

export default SearchAutoComplete;
