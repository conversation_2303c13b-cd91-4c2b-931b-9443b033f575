/** 商品组表单数据 */
type TypeGoodsGroupFormData = {
  /** id */
  id?: string;
  /** 商品名称 */
  name?: string;
  /** 商品英文名称 */
  enName?: string;
  /** 商品描述 */
  comment?: string;
  /** 组合商品图片名称 */
  serverPictureName?: string;
  /** 图片链接 */
  pictureUrl?: string;
  /** 组合商品与商品关系列表 */
  goodsList?: {
    /** 商品id */
    goodsId?: number;
    /** 数量 */
    number?: number;
    /** 备注 */
    comment?: string;
    /** id */
    id?: number;
    /** 删除标志 */
    deleted?: boolean;
  }[];
  /** 组合商品与电商平台关系列表 */
  platformList?: {
    /** 商家平台code */
    platformCode?: string;
    /** 商家名称 */
    platformName?: string;
    /** 商家平台图片 */
    platformUrl?: string;
    /** 商家平台销售URL */
    platformSaleUrl?: string;
    /** 备注 */
    platformRemark?: string;
    /** 平台SKU */
    platformSku?: string;
    /** id */
    id?: number;
    /** 是否删除 */
    deleted?: boolean;
  }[];
};

type TypeGoodsGroupRecord = {
  /** 主键 id */
  id?: string;
  /** 公司id */
  companyId?: string;
  /** 公司名称 */
  companyName?: string;
  /** 合作者 code */
  partnerCode?: string;
  /** SKU */
  sku?: string;
  /** 商品名称 */
  name?: string;
  /** 商品英文名称 */
  enName?: string;
  /** 备注 */
  comment?: string;
  /** 组合商品图片名称 */
  serverPictureName?: string;
  /** 图片链接 */
  pictureUrl?: string;
  /** 创建人类型 */
  createByType?: string;
  /** 创建人 ID */
  createById?: number;
  /** 创建人名 */
  createByName?: string;
  /** 创建时间 */
  createDate?: string;
  /** 创建人类型 */
  updateByType?: string;
  /** 更新人 ID */
  updateById?: number;
  /** 更新人 */
  updateByName?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 逻辑删除标志 0 - 未删除 1 - 已删除 */
  deleted?: boolean;
  /** 所属商品数组 */
  goodsList?: {
    /** 商品id */
    goodsId?: number;
    /** 商品sku */
    sku?: string;
    /** 商品名称 */
    name?: string;
    /** 商品英文名 */
    enName?: string;
    /** 商品图片名称 */
    serverPictureName?: string;
    /** 商品图片 */
    pictureUrl?: string;
    /** 数量 */
    number?: number;
    /** 备注 */
    comment?: string;
    /** 主键 id */
    id?: string;
    /** 公司id */
    companyId?: string;
    /** 公司名称 */
    companyName?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志 0 - 未删除 1 - 已删除 */
    deleted?: boolean;
  }[];

  /** 电商平台数组 */
  platformList?: {
    /** 商品ID */
    goodsId?: number;
    /** 商家平台ID */
    platformCode?: string;
    /** 商家名称 */
    platformName?: string;
    /** 商家平台图片 */
    platformUrl?: string;
    /** 商家平台销售URL */
    platformSaleUrl?: string;
    /** 备注 */
    platformRemark?: string;
    /** 平台SKU 及 ASIN */
    platformSku?: string;
    /** 图片URL */
    pictureUrl?: string;
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志 0 - 未删除 1 - 已删除 */
    deleted?: boolean;
  }[];
};
