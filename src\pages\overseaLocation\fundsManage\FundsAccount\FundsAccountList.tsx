import { <PERSON><PERSON>, Divide<PERSON>, <PERSON> } from 'antd';
import React, { useRef } from 'react';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable, PageContainer } from '@/common-import';
import { apiFundsAccountList } from './FundsAccountApi';
import { ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiAccountCurrencyOptions } from '@/pages/overseaLocation/api';
import { AmountMoney } from '@/views';
import { historyGoPush } from '../../utils';

function FundsAccountList() {
  const { config } = useConfig();

  return (
    <PageContainer>
      <ProTable {...config} />
    </PageContainer>
  );
}

export default FundsAccountList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const { columns } = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiFundsAccountList>[0];
      const { data } = await apiFundsAccountList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<TypeFundsAccountTB, any>;

  return {
    config,
  };
}

function useColumns() {
  const columns = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .chineseSymbols5,
      dataIndex: 'companyName',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .settlementCurrency,
      dataIndex: 'customerCurrency',
      search: true,
      fieldProps: {
        request: (currency: string) => apiAccountCurrencyOptions({ currency }),
      },
      renderFormItem() {
        return <ZSearchSelect />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .availableBalance,
      dataIndex: 'availableBalance',
      search: false,
      renderText(text) {
        return <AmountMoney num={text} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .chineseSymbols4,
      dataIndex: 'creditLimit',
      search: false,
      /** 产品要求门户不展示额度变更明细 */
      renderText(text) {
        return <AmountMoney num={text} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .chineseSymbols3,
      dataIndex: 'creditLimitMaturityDate',
      valueType: 'date',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList.frozenAmount,
      dataIndex: 'frozenAmount',
      search: false,
      renderText(text) {
        return <AmountMoney num={text} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .chineseSymbols2,
      dataIndex: 'cumulativeRechargeAmount',
      search: false,
      renderText(text) {
        return <AmountMoney num={text} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
          .chineseSymbols1,
      dataIndex: 'cumulativeConsumptionAmount',
      search: false,
      renderText(text) {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.operation,
      dataIndex: 'action',
      valueType: 'option',
      key: 'action',
      fixed: 'right',
      search: false,
      width: 100,
      render: (dom, record) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <a
              {...{
                onClick() {
                  historyGoPush({
                    pathname: '../../cash-flow',
                    query: { currency: record.customerCurrency as string },
                  });
                },
              }}
            >
              {
                I18N.Src__Pages__OverseaLocation__FundsManage__FundsAccount.FundsAccountList
                  .chineseSymbols
              }
            </a>
          </Space>
        );
      },
    },
  ] as ProColumns<TypeFundsAccountTB>[];

  return { columns };
}
