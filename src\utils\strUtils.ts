/** 分割字符串为数组 */
function strSplitForCommon(str: string | undefined | null, regex: RegExp) {
  return (str || '').replace(regex, '\n').split('\n').filter(Boolean);
}

/** 分割sku字符串为数组 */
function strSplitForSku(sku: string | undefined | null) {
  return strSplitForCommon(sku, /[\s,，\u00A0\n]+/g);
}

/** 分割订单字符串为数组 */
function strSplitForOrderNo(no: string | undefined | null) {
  return strSplitForCommon(no, /[\s,，;；\u00A0\n]+/g);
}

/** 分割邮箱字符串为数组 */
function strSplitForEmail(no: string | undefined | null) {
  return strSplitForCommon(no, /[\s,，;；\u00A0\n]+/g);
}

/** str处理工具 */
export const strUtils = {
  strSplitForCommon,
  strSplitForSku,
  strSplitForOrderNo,
  strSplitForEmail,
};
