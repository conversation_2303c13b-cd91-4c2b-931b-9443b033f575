export default {
  Index: {
    allRead: '全部已读',
    markRead: '标记已读',
    messageSubscription: '消息订阅设置',
    creationTime: '创建时间',
    state: '状态',
    messageType: '消息类型',
    title: '标题',
  },
  Subscribe: {
    messageNotification: '消息通知节点设置（勾选代表同意通知）',
    applyToJoin: '申请加入结果通知',
    employeeApplication: '员工申请加入通知',
    shipDepartureReminder: '开船提醒',
    inboundReminder: '进港提醒',
    departureWarehouse: '离厂/仓库提醒',
    arrivalWarehouse: '到厂/仓库提醒',
    suitcaseReminder: '提箱提醒',
    bookingConfirmation: '订舱确认提醒',
  },
};
