import type { Effect } from 'dva';
import { message } from 'antd';
import {
  queryOverseaLocationGoodsGroup,
  queryOverseaLocationGoodsGroupDetail,
  sendCreateOverseaLocationGoodsGroup,
  sendUpdateOverseaLocationGoodsGroup,
  sendDeleteOverseaLocationGoodsGroup,
} from '@/service/overseaLocation';

export interface GoodsModelType {
  namespace: string;
  state: null;
  effects: {
    getOverseaLocationGoodsGroupList: Effect;
    getOverseaLocationGoodsGroupDetail: Effect;
    createOrEditOverseaLocationGoodsGroup: Effect;
    deleteOverseaLocationGoodsGroup: Effect;
  };
}

const Model: GoodsModelType = {
  namespace: 'goodsGroup',
  state: null,
  effects: {
    /** @deprecated 已废弃迁移至OMS */
    *getOverseaLocationGoodsGroupList({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoodsGroup, payload);

      if (success) {
        return {
          data: result.records,
          total: result.totalSize ? Number(result.totalSize) : 0,
        };
      }

      return {
        data: [],
        total: 0,
      };
    },
    /** @deprecated 已废弃迁移至OMS */
    *getOverseaLocationGoodsGroupDetail({ payload }, { call }) {
      const { success, result } = yield call(queryOverseaLocationGoodsGroupDetail, payload);

      if (success) {
        return result;
      }
      return false;
    },
    /** @deprecated 已废弃迁移至OMS */
    *createOrEditOverseaLocationGoodsGroup({ payload }, { call }) {
      const { success } = yield call(
        payload.id ? sendUpdateOverseaLocationGoodsGroup : sendCreateOverseaLocationGoodsGroup,
        payload,
      );

      return success;
    },
    /** @deprecated 已废弃迁移至OMS */
    *deleteOverseaLocationGoodsGroup({ payload }, { call }) {
      const { success } = yield call(sendDeleteOverseaLocationGoodsGroup, payload);

      if (success) {
        return true;
      }
      return false;
    },
  },
};

export default Model;
