/** 文件相关处理 */
import { emptyRenderArray } from '@/utils';
import { imagePreviewIns } from '@/views';
import React from 'react';

type TypeFileItem = { filePath?: string; fileName: string };

/** 渲染可下载文件dom */
export function renderDownloadableFile(file: TypeFileItem | any) {
  if (!file) {
    return null;
  }
  return (
    <div key={file.filePath}>
      {file.filePath && (
        <a target="_blank" rel="noreferrer" download href={file.filePath}>
          {file.fileName}
        </a>
      )}
    </div>
  );
}

/** 渲染文件数组 */
export function renderDownloadableFileList(fileList: TypeFileItem[] | any) {
  return <div>{emptyRenderArray(fileList, renderDownloadableFile)}</div>;
}

/** 渲染单张可预览图片, 如果不是图片会下载 */
export function renderImageFile(
  file: TypeFileItem | any,
  index?: number,
  /** 仅在数组遍历下可获取, index 也是 */
  arr?: TypeFileItem[],
) {
  if (!file) {
    return null;
  }
  const isImg = imagePreviewIns?.isImgUrl(file.filePath);

  return (
    <div key={file.filePath}>
      {file.filePath && (
        <a
          {...(isImg
            ? {
                onClick() {
                  imagePreviewIns?.open({
                    imgList: (arr || [file]).map((item) => ({ url: item.filePath, ...item })),
                    curUrl: file.filePath,
                  });
                },
              }
            : {
                href: file.filePath,
                target: '_blank',
                rel: 'noreferrer',
                download: true,
              })}
        >
          {file.fileName}
        </a>
      )}
    </div>
  );
}

/** 渲染图片文件数组 */
export function renderImageFileList(fileList: TypeFileItem[] | any) {
  return <div>{emptyRenderArray(fileList, renderImageFile)}</div>;
}
