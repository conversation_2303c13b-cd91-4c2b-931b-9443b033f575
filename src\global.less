@import '~antd/es/style/themes/default.less';

* {
  box-sizing: border-box;
}

html,
body,
#root,
#root-master {
  height: 100%;
  font-family: 'Microsoft YaHei';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-page-container {
  padding-bottom: 20px;
}

.ant-layout-header,
.ant-pro-fixed-header {
  height: 56px !important;
  line-height: 56px !important;
}

.ant-pro-global-header {
  height: 56px !important;
}

.ant-page-header {
  background-color: #fff !important;
}
.ant-pro-basicLayout-content .ant-pro-page-container {
  margin: 0 !important;
}

.ant-pro-global-header-logo {
  display: flex;
  align-items: center;
}

.form-custom-label {
  label {
    width: 100%;
  }
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}
