import React from 'react';
import { PageContainer as AntProPageContainer, PageContainerProps } from '@ant-design/pro-layout';
import { HiveModule, Link } from '@portal/hive-sdk';

export const PageContainer: React.FC<PageContainerProps> = (props) => {
  const customProps = React.useContext(HiveModule.customPropsContext) || {};
  const { pageInfo } = customProps;

  return (
    <AntProPageContainer
      {...pageInfo}
      {...props}
      breadcrumb={{
        itemRender: (route) => {
          return <Link to={route.path}>{route.breadcrumbName}</Link>;
        },
        ...(pageInfo?.breadcrumb || {}),
      }}
    />
  );
};
