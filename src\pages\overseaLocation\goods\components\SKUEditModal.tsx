import I18N from '@/utils/I18N';
import React from 'react';
import { useIntl, Dispatch } from 'umi';
import { Modal, Input, Alert, Form } from 'antd';
import { CommonRulesMap } from '@/utils/rules';

interface IProps {
  visible: boolean;
  onCancel: () => void;
  dispatch: Dispatch;
  id: string;
  sku: string;
}

const { Item } = Form;
const SKUEditModal: React.FC<IProps> = ({ dispatch, visible, onCancel, id, sku }) => {
  const [form] = Form.useForm();

  return (
    <Modal
      width={500}
      title={I18N.Src__Pages__OverseaLocation__Goods__Components.SKUEditModal.applyForModification}
      maskClosable={false}
      visible={visible}
      onCancel={onCancel}
      onOk={() => {}}
    >
      <Alert
        message={I18N.Src__Pages__OverseaLocation__Goods__Components.SKUEditModal.canOnlyApply}
        type="warning"
        showIcon
        style={{ marginBottom: 20 }}
      />
      <Form form={form} layout="vertical">
        <Item
          label={I18N.Src__Pages__OverseaLocation__Goods__Components.SKUEditModal.primary}
          name="originSku"
        >
          <span>{sku}</span>
        </Item>
        <Item
          label={I18N.Src__Pages__OverseaLocation__Goods__Components.SKUEditModal.new}
          name="newSku"
          rules={[CommonRulesMap.commonStrVerify(50, true)]}
        >
          <Input />
        </Item>
        <Item
          label={
            I18N.Src__Pages__OverseaLocation__Goods__Components.SKUEditModal.reasonForModification
          }
          name="remark"
          rules={[CommonRulesMap.commonStrVerify(255)]}
        >
          <Input.TextArea rows={3} />
        </Item>
      </Form>
    </Modal>
  );
};

export default SKUEditModal;
