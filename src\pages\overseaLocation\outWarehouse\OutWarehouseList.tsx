import React, { useState, useEffect, useRef } from 'react';
import type { Dispatch } from 'umi';
import { connect } from 'umi';
import ProForm, {
  QueryFilter,
  ProFormText,
  ProFormSelect,
  ProFormDateRangePicker,
  ProFormTextArea,
} from '@ant-design/pro-form';
import moment from 'moment';
import { Spin, Card, Button, Tabs, Space, Modal, message, Tag } from 'antd';
import { forIn } from 'lodash';
import classnames from 'classnames';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { HumpToUnderline } from '@/utils/util';
import type { StateType } from './model';
import styles from './OutWarehouseList.less';
import type { TypeModalCompensateRef, TypeModalInterceptApplyRef } from './components';
import {
  ModalSupplementLabel,
  ModalInterceptApply,
  ModalCompensate,
  ModalEditAddress,
  OrderCancelStatus,
} from './components';
import type { TypeModalLogisticsTrackRef } from '@/views';
import {
  renderLogisticsTrackNode,
  ModalLogisticsTrack,
  AsyncExportButton,
  AmountMoney,
} from '@/views';
import { LoadButton, SearchSelect, ZSearchSelect } from '@/pages/overseaLocation/components';
import { renderOutboundCancelBtn, useOutOrderCancel } from './detail';
import { apiMapDictType, useDictTypeValueEnum } from '@/pages/overseaLocation/api';
import type { ActionType, ProColumns } from '@/common-import';
import { ProTable } from '@/common-import';
import {
  apiOutWarehouseFundsRetry,
  apiOutWarehouseList,
  apiOutboundDraftDelete,
  apiOutboundExport,
} from './OutboundApi';
import {
  arrAllowEditAddress,
  OUT_WAREHOUSE_OPTIONS,
  OUT_WAREHOUSE_OPTIONS_MAP,
} from '@/pages/overseaLocation/enum';
import { historyGoChild, includesInArray, useModalSet } from '@/pages/overseaLocation/utils';
import { useSensors } from '@/hooks/useSensors';
import { dateUtils, strUtils, tableUtils } from '@/utils';
import { renderReturnFaceSheetApply } from '@/pages/overseaLocation/ReturnManage/LogisticsIntercept/LogisticsInterceptList';
import { apiMixWarehouseOptions } from '@/pages/pagesApi';
import type { FormInstance } from 'antd/es/form';
import { exportUtils } from '@/utils-business';
import { EnumOutOrderSource } from './OutboundEnum';

const { transformDate } = dateUtils;
const formatMoney = AmountMoney.formatMoney;

interface IProps extends StateType {
  dispatch: Dispatch;
  pageLoading: boolean;
}

const TO_BE_SUBMIT_STATUS = 200;

const OutWarehouseListPage: React.FC<IProps> = ({ dispatch }) => {
  const [{ outWarehouseList, outWarehouseTotal, outWarehouseStatusCount }, setOutWarehouseData] =
    useState<{
      outWarehouseList: TypezOutWarehouseTable[];
      outWarehouseTotal: number;
      outWarehouseStatusCount: Record<string, number>;
    }>({
      outWarehouseList: [],
      outWarehouseTotal: 0,
      outWarehouseStatusCount: {},
    });
  const [pageLoading, setPageLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [searchValues, setSearchValues] = useState({});
  const [activeKey, setActiveKey] = useState('total');
  const [searchTrigger, setSearchTrigger] = useState(1);
  const { asyncOutOrderCancel } = useOutOrderCancel();
  const formRef = useRef<FormInstance>();
  const { columns, modalLogisticsTrackRef, modalNodeList, modalActions } = useColumns({
    renderRecordActionButtons,
  });
  const modalInterceptApplyRef = useRef<TypeModalInterceptApplyRef>();
  /** 资金不足的keys */
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const { trackerSend } = useSensors({
    midfix: 'OL_OUT',
  });
  const { cssTableStickyHasTab } = tableUtils.useTableSticky();
  const modalCompensateRef = useRef<TypeModalCompensateRef>();

  useEffect(() => {
    asyncSearchList();
  }, [searchTrigger, activeKey]);

  const asyncSearchList = async () => {
    setPageLoading(true);
    try {
      /** total 就是查询全部, 不设置 params.orderStatus */
      // params.orderStatus = activeKey === 'total' ? undefined : activeKey;

      const { data } = await apiOutWarehouseList({
        currentPage,
        pageSize,
        condition: {
          ...searchValues,
          /** total 就是查询全部, 不要传 */
          orderStatus: activeKey === 'total' ? undefined : activeKey,
        },
      });
      const { records, tabNum } = data || {};

      setOutWarehouseData({
        outWarehouseList: records,
        outWarehouseTotal: tabNum?.[activeKey],
        outWarehouseStatusCount: tabNum,
      });
    } finally {
      setPageLoading(false);
    }
  };
  /** 重新请求数据 */
  const reload = () => {
    asyncSearchList();
  };

  const handleTableSearch = async (values: any) => {
    const nextValues = transFormData(values);

    setSearchValues(nextValues);
    setCurrentPage(1);
    setSearchTrigger(searchTrigger + 1);

    const sensorsData: any = {};

    forIn(nextValues, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    trackerSend({
      name: 'LIST_SEARCH',
      data: sensorsData,
    });
  };

  const handleCreate = () => {
    // 出库新增按钮点击埋点
    trackerSend({
      name: 'CREATE_BUTTON_CLICK',
      data: {},
    });

    historyGoChild({
      newTab: true,
      pathname: './outbound-create',
    });
  };

  /** 删除事件
   * @ 以前取消和删除都混合在这个里面了, 现在取消不用这个, 代码没动
   */
  const handleDeleteRecord = async (record: any) => {
    const title =
      record.orderStatus === TO_BE_SUBMIT_STATUS
        ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.deleteOrder
        : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
            .wholeOrderCancellation;
    // 待发货的状态提醒会产生费用
    const content =
      record.orderStatus === 203
        ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.thisOrder
        : I18N.template(
            I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.whetherToConfirm,
            {
              val1:
                record.orderStatus === TO_BE_SUBMIT_STATUS
                  ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.delete
                  : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.cancel,
            },
          );

    Modal.confirm({
      title,
      content,
      onOk: async () => {
        // const result: any = await dispatch({
        //   type:
        //     record.orderStatus === TO_BE_SUBMIT_STATUS
        //       ? 'out_warehouse/deleteOutBoundOrder'
        //       : 'out_warehouse/cancelOutBoundOrder',
        //   payload: { id: record.id, orderSource: record.orderSource },
        // });
        await apiOutboundDraftDelete({ id: record.id, orderSource: record.orderSource });

        // if (result?.success) {
        trackerSend({
          name: 'OPTION_BUTTON_CLICK',
          data: {
            option_type: 'delete',
            order_status: record.orderStatus,
            oversea_pod_no: record.podOrderNo,
          },
        });

        message.success(
          I18N.template(
            I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.success,
            { val1: title },
          ),
        );
        reload();
        // }
      },
    });
  };

  /** 资金批量重试 */
  const asyncFundsRetry = async (podOrderNos: string[], { isBatch }: { isBatch: boolean }) => {
    const res = await apiOutWarehouseFundsRetry({ podOrderNos });

    if (res?.success) {
      message.success(
        isBatch
          ? I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.batchRetry1
          : I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.retrySucceeded,
      );
      setSelectedRowKeys((prevKeys) =>
        prevKeys.filter((key) => podOrderNos.includes(key) === false),
      );
      reload();
    }
  };

  /** 获取查询条件, 不同于searchValues, 这个是时时结果,用于导出条件 */
  function getSearchData() {
    const searchData = formRef.current?.getFieldsValue();

    return {
      ...transFormData(searchData),
      orderStatus: activeKey === 'total' ? undefined : activeKey,
    };
  }

  /** 操作列 */
  function renderRecordActionButtons(item: TypezOutWarehouseTable, action?: ActionType) {
    const { orderStatusMapping, errorType } = item;
    const btnCancelVNode = renderOutboundCancelBtn(
      {
        orderStatus: item.orderStatus,
        businessType: item.businessType,
        orderStatusMapping,
      },
      <LoadButton
        onClick={() => {
          return asyncOutOrderCancel(item).then(() => reload());
        }}
      >
        {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.applyForWholeDocument}
      </LoadButton>,
    );
    /** 仅200草稿状态允许编辑和删除
     * @ erp 草稿单除外
     */
    const hasBtnEdit =
      orderStatusMapping === 'DRAFT' && item.orderSource !== EnumOutOrderSource.erp;
    const hasBtnDelete =
      orderStatusMapping === 'DRAFT' && item.orderSource !== EnumOutOrderSource.erp;
    /** 仅非草稿单允许复制 */
    const hasBtnCopy = orderStatusMapping !== 'DRAFT'; /** 非草稿单 */
    /** 是否有修改地址按钮 */
    const hasBtnModifyAddress =
      orderStatusMapping === 'ORDER_ERROR' &&
      includesInArray(arrAllowEditAddress, errorType); /** 异常单 */
    const hasBtnFundsRetry = isFundsNotEnough(item); /** 资金不足 */
    /** 履约完结且不超过60天的订单 */
    let hasBtnCompensate =
      includesInArray(['null', '1001' /** 待索赔 */], `${item.compensateStatus}`) &&
      orderStatusMapping === 'FULFILLMENT_OVER' &&
      item.fulfillmentDays! <= 60;
    /** 存在索赔状态且不为待索赔, 显示详情按钮 */
    let hasBtnCompensateDetail =
      item.compensateStatus && `${item.compensateStatus}` !== '1001'; /** 待索赔 */
    /** 履约完结且首次申请 */
    const hasBtnLogisticsIntercept =
      orderStatusMapping === 'FULFILLMENT_OVER' &&
      item.logisticsIntercepted !== true; /** 履约完结 */
    /** 待补充面单状态 */
    const hasBtnSupplementLabel = orderStatusMapping === 'WAIT_UPLOAD_LABEL';
    const deliveryDTO = item.deliveryDTOList?.[0];

    /* TODO 产品要求暂时隐藏索赔功能, 业务侧未规范使用方式 */
    hasBtnCompensate = false;
    hasBtnCompensateDetail = false;

    return (
      <Space wrap>
        {hasBtnSupplementLabel && (
          <a
            onClick={() => {
              modalActions.supplementLabel?.open(
                {
                  podOrderNo: item?.podOrderNo,
                  customerChannelCode: deliveryDTO?.customerChannelCode,
                  trackingNo: deliveryDTO?.trackingNo,
                  fileList: deliveryDTO?.fileList,
                  businessType: item.businessType,
                },
                {
                  submitSuccessCB() {
                    reload();
                  },
                },
              );
            }}
          >
            {
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
                .ModalSupplementLabel.supplementarySheet
            }
          </a>
        )}
        {hasBtnEdit && (
          <a onClick={() => handleEditOrCopy(item, { optionType: 'edit' })}>
            {I18N.Src__Pages__Common__Template.Index.edit}
          </a>
        )}
        {hasBtnDelete && (
          <a onClick={() => handleDeleteRecord(item)}>
            {I18N.Src__Pages__Common__Template.Index.delete}
          </a>
        )}
        {/** 申请整单取消 */}
        {btnCancelVNode}
        {hasBtnCopy && (
          <a onClick={() => handleEditOrCopy(item, { optionType: 'copy' })}>
            {I18N.Src__Pages__Order__Si__Component__ShareModals.Alert.copy}
          </a>
        )}
        {hasBtnCompensate && (
          <a
            onClick={() => {
              modalCompensateRef.current?.open({
                complianceNo: item.complianceNo!,
                podOrderNo: item.podOrderNo!,
                compensateCompanyId: item.companyId,
                compensateCompanyName: item.companyName,
                compensateStatus: item.compensateStatus,
                currency: item.currency,
              });
            }}
          >
            {
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols15
            }
          </a>
        )}
        {hasBtnCompensateDetail && (
          <a
            onClick={() => {
              historyGoChild({
                newTab: true,
                pathname: './outbound-compensate-detail',
                query: { complianceNo: item.complianceNo! },
              });
            }}
          >
            {
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols16
            }
          </a>
        )}
        {hasBtnFundsRetry && (
          <LoadButton
            onClick={async () => {
              return asyncFundsRetry([item.podOrderNo!], { isBatch: false });
            }}
          >
            {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.retry}
          </LoadButton>
        )}
        {hasBtnLogisticsIntercept && (
          <a
            onClick={() => {
              modalInterceptApplyRef.current?.open(
                {
                  podOrderNo: item.podOrderNo!,
                  orderId: item.id!,
                },
                {
                  isMultiPackage: item.packageList?.length > 1,
                  submitSuccessCB() {
                    reload();
                    ssTrack(EnumSensor.RETURN_INTERCEPT_PROCESS);
                  },
                },
              );
            }}
          >
            {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols1}
          </a>
        )}
        {/** 申请退货面单 */}
        {renderReturnFaceSheetApply({
          action,
          reload,
          record: {
            orderId: item.id!,
            orderStatusMapping,
            orderPackageSize: item.packageList?.length,
          },
        })}
        {hasBtnModifyAddress && (
          <ModalEditAddress
            {...{
              rowData: item,
              reload: () => {
                reload();
                ssTrack(EnumSensor.OUTBOUND_ADDRESS_MODIFY);
              },
            }}
          />
        )}
      </Space>
    );
  }

  return (
    <PageContainer
      title={false}
      content={
        <Card bordered={false} bodyStyle={{ padding: '20px 0 0 0' }} className={styles.header}>
          <QueryFilter
            // span={6}
            formRef={formRef}
            labelWidth={100}
            defaultCollapsed={false}
            onFinish={handleTableSearch}
            onReset={() => {
              setSearchValues({});
              setActiveKey('total');
              setCurrentPage(1);
              setSearchTrigger(searchTrigger + 1);
            }}
          >
            <ProFormText name="sku" label="SKU" />
            {/* <ProFormText name="variousOrders" label="各类单号" /> */}
            <ProFormTextArea
              name="podOrderNo"
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9
              }
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.applicationNo
              }
            />
            <ProFormTextArea
              name="trackingNo"
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9
              }
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
                  .logisticsTracking
              }
            />
            <ProForm.Item
              {...{
                name: 'createDeliveryWarehouseIdList',
                label:
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .shipmentOrganization,
              }}
            >
              <ZSearchSelect
                {...{
                  style: { width: '100%' },
                  request: async (queryParam: string) =>
                    apiMixWarehouseOptions({ queryParam }, { valueName: 'id' }),
                  mode: 'multiple',
                }}
              />
            </ProForm.Item>
            <ProFormDateRangePicker
              name="createDate"
              label={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.creationDate
              }
            />
            <ProFormTextArea
              name="customerRelatedNo"
              label={
                I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.customerAssociation1
              }
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9
              }
            />
            <ProFormTextArea
              name="customerSalesNo"
              label={I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.customerSales1}
              placeholder={
                I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9
              }
            />
            <ProFormTextArea
              {...{
                name: 'platformSellerIdListStr',
                label: '平台卖家ID',
                placeholder:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9,
              }}
            />
            <ProFormTextArea
              {...{
                name: 'platformStoreIdListStr',
                label: '平台店铺ID',
                placeholder:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols9,
              }}
            />
            <ProFormSelect
              {...{
                label: I18N.Src__Pages__Home.Index.businessType,
                name: 'businessTypeList',
                request: apiMapDictType.outboundBusinessType,
                fieldProps: {
                  showSearch: false,
                  mode: 'multiple',
                },
              }}
            />
            <ProForm.Item
              {...{
                label:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols8,
                name: 'orderSourcePlatformCodeList',
              }}
            >
              <SearchSelect
                {...{
                  mode: 'multiple',
                  request: apiMapDictType.orderSourcePlatform,
                }}
              />
            </ProForm.Item>
            <ProFormText
              name="recipientName"
              label={I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.addressee1}
            />
            <ProForm.Item
              {...{
                label:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols7,
                name: 'dispatchServiceTypeList',
              }}
            >
              <SearchSelect
                {...{
                  mode: 'multiple',
                  request: apiMapDictType.DcDispatchServiceType,
                }}
              />
            </ProForm.Item>
            <ProForm.Item
              {...{
                name: 'trackNodes',
                label:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols,
              }}
            >
              <SearchSelect
                {...{
                  mode: 'multiple',
                  request: apiMapDictType.logisticsTrackType,
                }}
              />
            </ProForm.Item>
            {/* TODO 产品要求暂时隐藏索赔功能, 业务侧未规范使用方式 */}
            {/* <ProForm.Item
              {...{
                name: 'compensateStatusList',
                label:
                  I18N
                    .Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
                    .CompensateOperateRecord.chineseSymbols4,
              }}
            >
              <SearchSelect
                {...{
                  mode: 'multiple',
                  request: apiMapDictType.compensationStatusType,
                }}
              />
            </ProForm.Item> */}
            <ProFormText {...{ label: 'ZONE', name: 'zone' }} />
          </QueryFilter>
        </Card>
      }
    >
      {modalNodeList}
      <ModalLogisticsTrack {...{ ref: modalLogisticsTrackRef }} />
      <ModalInterceptApply
        {...{
          ref: modalInterceptApplyRef,
        }}
      />
      <ModalCompensate
        {...{
          ref: modalCompensateRef,
          submitSuccessCB() {
            reload();
            ssTrack(EnumSensor.OUTBOUND_COMPENSATE_APPLY);
          },
        }}
      />
      <Spin spinning={pageLoading !== undefined ? pageLoading : false}>
        <div className={classnames(styles.container, cssTableStickyHasTab)}>
          <Space>
            <Button type="primary" onClick={handleCreate}>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.createIssue}
            </Button>
            <AsyncExportButton
              {...{
                buttonProps: {
                  type: 'default',
                  async onClick(e, params) {
                    const searchData = getSearchData();
                    const { createDateBegin, createDateEnd } = searchData;

                    if (
                      exportUtils.isAllowDaysLimit(createDateBegin, createDateEnd, 31) === false
                    ) {
                      return Promise.reject();
                    }
                    params.form.setFieldsValue({
                      // kiwi-disable
                      fileName: `出库单${moment(createDateBegin).format('YYYYMMDD')}_${moment(
                        createDateEnd,
                      ).format('YYYYMMDD')}`,
                      // kiwi-enable
                    });
                  },
                },
                async request({ form }) {
                  const { fileName } = form.getFieldsValue();
                  const searchData = getSearchData();

                  await apiOutboundExport({ ...searchData, fileName });
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.export}
            </AsyncExportButton>
            <Button
              {...{
                onClick() {
                  historyGoChild({ pathname: 'outbound-cost-calc', newTab: true });
                },
              }}
            >
              尾程费用试算
            </Button>
            {activeKey === 'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR' && (
              <LoadButton
                type="default"
                disabled={selectedRowKeys.length === 0}
                onClick={async () => {
                  return asyncFundsRetry(selectedRowKeys, { isBatch: true });
                }}
              >
                {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.batchRetry}
              </LoadButton>
            )}
          </Space>
          {outWarehouseStatusCount ? (
            <Tabs
              size="small"
              tabBarStyle={{
                padding: '0 20px',
                margin: 0,
              }}
              activeKey={activeKey}
              onChange={(key) => {
                setCurrentPage(1);
                setActiveKey(key);
                trackerSend({
                  name: 'TAB_CHANGE',
                  data: {
                    toggle_tab: key,
                  },
                });
              }}
            >
              {OUT_WAREHOUSE_OPTIONS.filter((item: any) => item.hidden !== true).map((item) => (
                <Tabs.TabPane
                  tab={
                    <div className={styles.tab}>
                      <span>
                        {item.label}
                        {outWarehouseStatusCount[item.tabKey!] > 0
                          ? `(${outWarehouseStatusCount[item.tabKey!]})`
                          : null}
                      </span>
                    </div>
                  }
                  key={item.tabKey}
                />
              ))}
            </Tabs>
          ) : null}

          <ProTable<TypezOutWarehouseTable>
            {...{
              tableLayout: 'auto',
              rowKey: 'podOrderNo',
              search: false,
              options: false,
              cardProps: false,
              columnEmptyText: false,
              sticky: true,
              /** 资金异常才有批量选择框 */
              rowSelection:
                activeKey !== 'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR'
                  ? undefined
                  : {
                      // hideSelectAll: activeKey !== 'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR',
                      selectedRowKeys,
                      renderCell(checked, record, index, originNode) {
                        return isFundsNotEnough(record) ? originNode : undefined;
                      },
                      onChange(selectedKeys, selectedRows) {
                        setSelectedRowKeys(
                          selectedRows.filter(isFundsNotEnough).map((record) => record.podOrderNo!),
                        );
                      },
                    },
              pagination: {
                showTotal: undefined,
                position: ['bottomCenter'],
                current: currentPage,
                pageSize,
                total: outWarehouseTotal,
                showSizeChanger: true,
                onChange: (page: number, size: number) => {
                  setCurrentPage((prevPage) => {
                    if (prevPage !== page) {
                      /** ProTable 有bug, 分页onChange会触发2次,改造为仅数字有变化在触发刷新 */
                      setSearchTrigger((prevSearchTrigger) => prevSearchTrigger + 1);
                    }
                    return page;
                  });
                  setPageSize((prevSize) => {
                    if (prevSize !== size) {
                      setSearchTrigger((prevSearchTrigger) => prevSearchTrigger + 1);
                    }
                    return size;
                  });
                  // setSearchTrigger(searchTrigger + 1);
                },
              },
              scroll: { x: 'max-content' },
              dataSource: outWarehouseList,
              columns,
            }}
          />
        </div>
      </Spin>
    </PageContainer>
  );
};

export default connect(
  ({ out_warehouse, loading }: { out_warehouse: StateType; loading: any }) => ({
    outWarehouseList: out_warehouse.outWarehouseList,
    outWarehouseTotal: out_warehouse.outWarehouseTotal,
    outWarehouseStatusCount: out_warehouse.outWarehouseStatusCount,
    pageLoading: loading.models.out_warehouse,
  }),
)(OutWarehouseListPage);

/** 判断资金不足 */
function isFundsNotEnough(record: TypezOutWarehouseTable) {
  /** 资金不足 211 */
  // return Number(record.orderStatus) === 211;
  return record.orderStatusMapping === 'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR';
}

function useColumns({
  renderRecordActionButtons,
}: {
  renderRecordActionButtons: (record: any, action?: ActionType) => any;
}) {
  const { modalActions, modalNodeList } = useModalSet({ supplementLabel: ModalSupplementLabel });
  const modalLogisticsTrackRef = useRef<TypeModalLogisticsTrackRef>();
  const { dictTypeMap } = useDictTypeValueEnum([
    'outboundBusinessType',
    'DcDispatchServiceType',
    'DcDispatchServiceName',
    'addressRemoteAttribute',
    'orderSource',
  ]);
  const handleToDetailPage = (item: any) => {
    if (![200].includes(item.orderStatus)) {
      historyGoChild({ newTab: true, pathname: './detail', query: { id: item.id } });
      // HiveModule.history.push(`/oversea-location/out-warehouse/detail/${item.id}`);

      sensorsTrack('WWL_PORTAL_OUT_WAREHOUSE_OPTION_BUTTON_CLICK', {
        option_type: 'detail',
        order_status: item.orderStatus,
        oversea_pod_no: item.podOrderNo,
      });
    }
  };
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.applicationNo,
      dataIndex: 'podOrderNo',
      render(dom, item) {
        return (
          <>
            <a
              className={classnames(styles.podNo, item.orderStatus !== 200 && styles.podActive)}
              // style={{ color: item.orderStatus === 200 ? '#999' : '#1890ff' }}
              onClick={() => handleToDetailPage(item)}
            >
              {item.podOrderNo}
            </a>
            <div className={styles.createDate}>
              {moment(item.createDate).format('YYYY-MM-DD HH:mm')}
            </div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.state,
      dataIndex: 'status',
      align: 'center',
      render(dom, item) {
        // const orderStatusName =
        //   (OUT_WAREHOUSE_OPTIONS_MAP[item.orderStatus!]?.label || item.orderStatusValue) ??
        //   item.orderStatus;
        const { orderStatusName, compensateStatusName } = item;

        return (
          <Space direction="vertical" align="center">
            <div
              className={styles.status}
              style={{
                backgroundColor: OUT_WAREHOUSE_OPTIONS_MAP[item.orderStatusMapping!]?.color,
              }}
            >
              <span>{orderStatusName}</span>
            </div>
            <OrderCancelStatus cancelOrderStatus={item.cancelOrderStatus} />
            {/* TODO 产品要求暂时隐藏索赔功能, 业务侧未规范使用方式 */}
            {/* {compensateStatusName && <Tag color="gold">{compensateStatusName}</Tag>} */}
          </Space>
        );
      },
    },
    {
      title: I18N.Src__Pages__Home.Index.businessType,
      dataIndex: 'businessType',
      align: 'center',
      valueType: 'select',
      request: apiMapDictType.outboundBusinessType,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.logisticsInformation,
      dataIndex: 'deliveryDTOList',
      render(dom, item) {
        const deliveryDTO = item.deliveryDTOList?.[0];

        return item.deliveryDTOList?.length ? (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.logisticsType}
              {dictTypeMap.DcDispatchServiceType[deliveryDTO?.dispatchServiceType!]}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.logisticsChannel}
              {deliveryDTO?.customerChannelCode ||
                dictTypeMap.DcDispatchServiceName[deliveryDTO?.dispatchServiceName!]}
            </div>
            <div className={styles.multiplePackagesBox}>
              <div>
                {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols6}
                {item.deliveryDTOList?.[0]?.trackingNo}
              </div>
              {item.packageList?.length > 1 && <span className={styles.multiplePackages} />}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.logisticsProgress}
              {renderLogisticsTrackNode({
                ...item,
                onClick() {
                  modalLogisticsTrackRef.current?.open({ orderId: item.id });
                  ssTrack(EnumSensor.OUTBOUND_LOGISTICS_TRACK);
                },
              })}
              {/* item?.latestNode ? (
                <a
                  onClick={() => {
                    modalLogisticsTrackRef.current?.open({ orderId: item.id });
                    ssTrack(EnumSensor.OUTBOUND_LOGISTICS_TRACK);
                  }}
                >
                  {item?.latestNode}
                </a>
              ) : (
                '-'
              ) */}
            </div>
            {deliveryDTO?.returnShippingNum && (
              <div>回邮单号: {deliveryDTO?.returnShippingNum}</div>
            )}
          </>
        ) : null;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .shipmentOrganization,
      dataIndex: 'deliveryWarehouseCode',
      render(dom, item) {
        /** 如果下单仓库和发货仓库相同, 则仅展示下单仓库 */
        const isEqual = item?.deliveryWarehouseCode === item?.createDeliveryWarehouseCode;

        return (
          <>
            <div>{item.createDeliveryWarehouseCode}</div>
            <div>{isEqual ? null : item.deliveryWarehouseCode}</div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__Order__Detail.Index.orderInformation,
      dataIndex: 'source',
      width: 200,
      render(dom, item) {
        return (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.orderSource}
              {dictTypeMap.orderSource[item.orderSource!]}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.orderPlatform}
              {item.orderSourcePlatformName}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.customerSales}
              {item.customerSalesNo}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.customerAssociation}
              {item.customerRelatedNo}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.platformSeller}
              {item.platformSellerId}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.platformShop}
              {item.platformStoreId}
            </div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.commodityDetails,
      dataIndex: 'detail',
      render(dom, item) {
        return (
          <div className={styles.leftLine}>
            {(item.goodsList || []).map((o: any) => (
              <div key={o.sku}>
                {o.goodsName} x{o.totalQuantity} SKU:{o.sku}
              </div>
            ))}
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.consigneesLetter,
      dataIndex: 'sAddress',
      render(dom, item) {
        const residentialMap = {
          false: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.nonResidential,
          true: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.residence,
        };

        return (
          <>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.addressee}
              {item.orderExpressDTO ? item.orderExpressDTO.recipientName : ''}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.recipientCountry}
              {item.orderExpressDTO ? item.orderExpressDTO.recipientCountry : ''}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.recipientProvince}
              {item.orderExpressDTO ? item.orderExpressDTO.recipientProvince : ''}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.recipientEmail}
              {item.orderExpressDTO?.recipientPostcode}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.addressType}
              {
                residentialMap[
                  `${item.orderExpressDTO?.residential}` as keyof typeof residentialMap
                ]
              }
            </div>
            <div>
              ODA：
              {dictTypeMap.addressRemoteAttribute[item.orderExpressDTO?.remote!]}
            </div>
            <div>ZONE: {item.orderExpressDTO?.zone}</div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols5,
      dataIndex: 'chargeInfo',
      render(dom, record) {
        const { chargeAmount } = record;
        const {
          warehouseAmount,
          warehouseCurrency,
          expressAmount,
          expressCurrency,
          totalAmount,
          totalCurrency,
        } = chargeAmount || {};

        return (
          <div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols4}
              {formatMoney(warehouseAmount) ?? '-'} {warehouseCurrency}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols3}
              {formatMoney(expressAmount) ?? '-'} {expressCurrency}
            </div>
            <div>
              {I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.chineseSymbols2}
              {formatMoney(totalAmount) ?? '-'} {totalCurrency}
            </div>
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.errorMessage,
      dataIndex: 'errorMsgText',
      render(text, record) {
        return includesInArray(
          ['ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR' /** 资金不足 */, 'ORDER_ERROR' /** 异常单 */],
          record.orderStatusMapping,
        )
          ? text
          : '';
      },
    },
    {
      title: I18N.Src__Pages__Order__Components__CoustomTable.Index.orderOperation,
      dataIndex: 'action',
      align: 'center',
      fixed: 'right',
      width: 100,
      className: 'action',
      render(dom, item, i, action) {
        return renderRecordActionButtons(item, action);
      },
    },
  ] as ProColumns<TypezOutWarehouseTable>[];

  return {
    columns,
    modalLogisticsTrackRef,
    modalNodeList,
    modalActions,
    dictTypeMap,
  };
}

/** 转换formData条件参数 */
function transFormData(values: any) {
  const nextValues = {
    ...values,
    createDateBegin: transformDate(values?.createDate?.[0], 'START'),
    createDateEnd: transformDate(values?.createDate?.[1], 'END'),
    createDate: undefined,
    podOrderNoList: strUtils.strSplitForOrderNo(values?.podOrderNo),
    trackingNoList: strUtils.strSplitForOrderNo(values?.trackingNo),
    customerSalesNoList: strUtils.strSplitForOrderNo(values?.customerSalesNo),
    customerRelatedNoList: strUtils.strSplitForOrderNo(values?.customerRelatedNo),
    platformSellerIdList: strUtils.strSplitForOrderNo(values?.platformSellerIdListStr),
    platformStoreIdList: strUtils.strSplitForOrderNo(values?.platformStoreIdListStr),
    podOrderNo: undefined,
    trackingNo: undefined,
    customerSalesNo: undefined,
    customerRelatedNo: undefined,
    platformSellerIdListStr: undefined,
    platformStoreIdListStr: undefined,
  };

  return nextValues;
}

/** 编辑或复制出库单 */
export function handleEditOrCopy(record: any, { optionType }: { optionType: 'edit' | 'copy' }) {
  sensorsTrack('WWL_PORTAL_OUT_WAREHOUSE_OPTION_BUTTON_CLICK', {
    option_type: optionType,
    current_click_page: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.list,
    order_status: record.orderStatus,
    oversea_pod_no: record.podOrderNo,
  });

  historyGoChild({
    newTab: true,
    /** 在出库详情也有使用, 所以使用绝对路径 */
    pathname:
      optionType === 'edit'
        ? '/oversea-location/out-warehouse/outbound-edit'
        : '/oversea-location/out-warehouse/outbound-create',
    query: { id: record.id, orderStatus: record.orderStatus },
  });
}
