import React, { useEffect, useRef, useState } from 'react';
import type { ConnectRC } from 'umi';
import { connect } from 'umi';
import {
  Button,
  Divider,
  Space,
  Select,
  InputNumber,
  Tooltip,
  Card,
  Tabs,
  Input,
  Spin,
  Form,
  DatePicker,
} from 'antd';
import ProForm, { ProFormDatePicker, QueryFilter } from '@ant-design/pro-form';
import { forIn } from 'lodash';
import { HiveModule } from '@portal/hive-sdk';
import classnames from 'classnames';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { HumpToUnderline } from '@/utils/util';
import styles from './index.less';
import UnreceiveDetailModal from './components/UnreceivedDetailModal';
import GoodsImage from '@/components/GoodsImage';
import type { ProColumns } from '@/common-import';
import { ProTable } from '@/common-import';
import { SearchSelect, ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiQueryWarehouseOptions } from '@/pages/overseaLocation/api';
import { dateUtils, tableUtils } from '@/utils';
import { historyGoChild } from '@/pages/overseaLocation/utils';
import { apiInventoryList, apiInventorySnapshotExport } from '../inventoryApi';
import { apiMixWarehouseOptions, apiVirtualWarehouseOptions } from '@/pages/pagesApi';
import { AsyncExportButton } from '@/views';
import moment from 'moment';

const { history } = HiveModule;

const { Item } = ProForm;
const OverseaInventoryListPage: ConnectRC<{ loading: boolean }> = ({ dispatch }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [tabCode, setTabCode] = useState<string>('ALL');
  const [statData, setStatData] = useState<any>({});
  const [pageIndex, setPageIndex] = useState<any>(1);
  const [showUnreceiveDetail, setShowUnreceiveDetail] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>({});
  const [searchValues, setSearchValues] = useState<any>({});
  const tableRef = useRef<any>();
  const formRef = useRef<any>();
  const { cssTableStickyHasTab } = tableUtils.useTableSticky();

  const tabList = [
    {
      label: I18N.template(I18N.Src__Pages__OverseaLocation__Inventory__List.Index.whole, {
        val1: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.whole,
        val2: statData?.ALL || 0,
      }),
      key: 'ALL',
    },
    {
      label: I18N.template(
        I18N.Src__Pages__OverseaLocation__Inventory__List.Index.underInventory1,
        {
          val1: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.underInventory,
          val2: statData?.UNSAFE_NUM || 0,
        },
      ),
      key: 'UNSAFE_NUM',
    },
    {
      label: I18N.template(I18N.Src__Pages__OverseaLocation__Inventory__List.Index.abnormalParts1, {
        val1: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.abnormalParts,
        val2: statData?.ERROR || 0,
      }),
      key: 'ERROR',
    },
    {
      label: I18N.template(
        I18N.Src__Pages__OverseaLocation__Inventory__List.Index.badPartsSupplier1,
        {
          val1: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.badPartsSupplier,
          val2: statData?.BAD || 0,
        },
      ),
      key: 'BAD',
    },
  ];
  const columns: ProColumns[] = [
    {
      title: '',
      key: 'pictureUrl',
      dataIndex: 'pictureUrl',
      render: (_: any, record: any) => {
        return <GoodsImage src={record.goods?.pictureUrl} />;
      },
      width: 100,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.productInformation,
      key: 'goodsName',
      dataIndex: 'goodsName',
      // ellipsis: true,
      width: 200,
      render: (_: any, record: any) => {
        return (
          <div>
            <div>{record.goodsName}</div>
            <div>{record.goodsEnName}</div>
            {/* <Tooltip title={record.goodsName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsName}</span>
            </Tooltip>
            <Tooltip title={record.goodsEnName} placement="top">
              <span className={styles['goods-name-text']}>{record.goodsEnName}</span>
            </Tooltip> */}
          </div>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      key: 'sku',
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.warehouseOrganization,
      key: 'warehouseCode',
      dataIndex: 'warehouseCode',
      search: false,
      render: (text: any, record: any) => {
        return (
          <>
            <div>{record.warehouseCode}</div>
            <div>{record.warehouseAreaName}</div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece1,
      key: 'goodQuantity',
      dataIndex: 'goodQuantity',
      width: 120,
      search: false,
      render: (_: any) => {
        return <span>{_ > 0 ? _ : '-'}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts1,
      key: 'badQuantity',
      dataIndex: 'badQuantity',
      width: 120,
      search: false,
      render: (_: any) => {
        return <span>{_ > 0 ? _ : '-'}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.abnormalParts1,
      key: 'errorQuantity',
      dataIndex: 'errorQuantity',
      width: 120,
      search: false,
      render: (_: any) => {
        return <span>{_ > 0 ? _ : '-'}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.orderFreezing,
      key: 'frozenQuantity',
      dataIndex: 'frozenQuantity',
      width: 120,
      search: false,
      render: (_: any) => {
        return <span>{_ > 0 ? _ : '-'}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.cumulativeIssue,
      key: 'outboundQuantity',
      dataIndex: 'outboundQuantity',
      width: 120,
      search: false,
      render: (_: any) => {
        return <span>{_ > 0 ? _ : '-'}</span>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.theFirstJourneyIsOnTheWay,
      key: 'unreceivedTotalQuantity',
      dataIndex: 'unreceivedTotalQuantity',
      width: 120,
      search: false,
      render: (_: any, record: any) => {
        return (
          <span>{_ > 0 ? <a onClick={() => showUnreceiveDetailModal(record)}>{_}</a> : '-'}</span>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.inventoryWaterLevel,
      key: 'safeNums',
      dataIndex: 'safeNums',
      width: 120,
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
      dataIndex: 'virtualWarehouseCode',
      search: false,
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      key: 'option',
      width: 180,
      fixed: 'right',
      search: false,
      render: (_: any, record: any) => {
        const { goodQuantity, badQuantity, errorQuantity, frozenQuantity } = record;

        return (
          <>
            {(goodQuantity > 0 || badQuantity > 0 || errorQuantity > 0 || frozenQuantity > 0) && (
              <>
                <a
                  onClick={() => {
                    historyGoChild({
                      newTab: true,
                      pathname: '../detail',
                      query: { sku: record.sku, warehouseId: record.warehouseId },
                    });
                    // HiveModule.history.push({
                    //   path: '/oversea-location/inventory/detail',
                    //   search: { sku: record.sku, warehouseId: record.warehouseId },
                    // })
                  }}
                >
                  {I18N.Src__Pages__OverseaLocation__Inventory__List.Index.viewStockAge}
                </a>
                <Divider type="vertical" />
              </>
            )}
            <a
              onClick={() => {
                historyGoChild({
                  newTab: true,
                  pathname: '../trade/flow',
                  query: { sku: record.sku, warehouseId: record.warehouseId },
                });
                // HiveModule.history.push({
                //   path: '/oversea-location/inventory/trade/flow',
                //   search: { sku: record.sku, warehouseId: record.warehouseId },
                // });
              }}
            >
              {I18N.Src__Pages__OverseaLocation__Inventory__List.Index.viewTransaction}
            </a>
          </>
        );
      },
    },
  ];

  const handleTabChange = (key: string) => {
    setTabCode(key);
    setPageIndex(1);
    tableRef.current.reload();
  };

  const handleSearchList = async (params: any) => {
    const { pageSize, current: currentPage, ...args } = params;
    const { data } = await apiInventoryList({
      currentPage,
      pageSize,
      condition: {
        ...args,
        tabCode,
        ...searchValues,
      },
    });
    const result = data || {};

    setStatData(result?.tabNum || {});

    const sensorsData: any = {};

    forIn(params, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    sensorsTrack('WWL_PORTAL_OVERSEA_INVENTORY_LIST_SEARCH', sensorsData);

    return {
      data: formatRecords(result?.inventory?.records || []),
      total:
        result.inventory && result.inventory.totalSize ? Number(result.inventory.totalSize) : 0,
    };
  };

  const formatRecords = (records: TypeInventoryTB[]) => {
    records.forEach((item, index, arr) => {
      arr[index].unreceivedTotalQuantity = item.unsignedOrders?.reduce((acc, cur) => {
        return acc + (cur?.unreceivedQuantity ?? 0);
      }, 0);
    });

    return records;
  };

  const showUnreceiveDetailModal = (record: any) => {
    sensorsTrack('WWL_PORTAL_OVERSEA_INVENTORY_UNRECEIVE_CLICK', {
      inventory_sku: record.sku,
    });

    setCurrentRecord(record);
    setShowUnreceiveDetail(true);
  };

  const closeUnreceiveDetailModal = () => {
    setCurrentRecord({});
    setShowUnreceiveDetail(false);
  };

  const handleTableSearch = async (values: any) => {
    setSearchValues(values);
    setPageIndex(1);
    tableRef.current.reload();
  };

  return (
    <PageContainer className={classnames(cssTableStickyHasTab)}>
      <Card className={styles['inventory-search-form']}>
        <QueryFilter
          defaultCollapsed={false}
          onFinish={handleTableSearch}
          onReset={() => handleTableSearch({})}
          submitter={{
            submitButtonProps: {
              loading,
            },
          }}
        >
          <Item
            {...{
              name: 'warehouseIds',
              label:
                I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index
                  .warehouseOrganization,
            }}
          >
            <ZSearchSelect
              {...{
                request: async (queryParam: string) =>
                  apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
                mode: 'multiple',
              }}
            />
          </Item>
          <Item
            name="goodsName"
            label={I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName}
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            name="sku"
            label={
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1
            }
          >
            <Input
              placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
              allowClear
            />
          </Item>
          <Item
            label={
              I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.inventoryQuantity
            }
          >
            <div className={styles['display-inline']}>
              <Item name="qualityType" noStyle>
                <Select
                  allowClear
                  options={[
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece1,
                      value: 'GOOD',
                    },
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts1,
                      value: 'BAD',
                    },
                    {
                      label:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index
                          .abnormalParts1,
                      value: 'ERROR',
                    },
                  ]}
                />
              </Item>
              <Item name="minQuantity" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
              <div className={styles.split}>~</div>
              <Item name="maxQuantity" noStyle>
                <InputNumber min={0} precision={0} />
              </Item>
            </div>
          </Item>
          <Item
            {...{
              name: 'virtualWarehouseList',
              label: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse.chineseSymbols2,
            }}
          >
            <ZSearchSelect
              {...{
                mode: 'multiple',
                request: async (queryParam: string) =>
                  apiVirtualWarehouseOptions({ queryParam }, { valueName: 'id' }),
              }}
            />
          </Item>
        </QueryFilter>
      </Card>

      <Spin spinning={loading}>
        <Tabs
          size="small"
          tabBarStyle={{
            padding: '0 20px',
            margin: 0,
          }}
          activeKey={tabCode}
          onChange={(key) => handleTabChange(key as string)}
          tabBarExtraContent={
            <AsyncExportButton
              {...{
                buttonProps: {
                  type: 'primary',
                },
                formProps: {
                  labelCol: { span: 4 },
                },
                modalProps: {
                  title: I18N.Src__Pages__OverseaLocation__Inventory__List.Index.chineseSymbols,
                },
                formChildren: ({ nodeFileName }) => {
                  return (
                    <>
                      <Form.Item
                        {...{
                          label:
                            I18N.Src__Pages__OverseaLocation__Inventory__List.Index.chineseSymbols1,
                          name: 'snapshotDate',
                          rules: [{ required: true }],
                        }}
                      >
                        {/* @ts-ignore */}
                        <DatePicker
                          {...{
                            style: {
                              width: '100%',
                            },
                            disabledDate: (current) => {
                              return current && current > moment().endOf('day');
                            },
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        {...{
                          label:
                            I18N.Src__Pages__OverseaLocation__Inventory__List.Index.warehouseCode,
                          name: 'warehouseCodeList',
                        }}
                      >
                        <SearchSelect
                          {...{
                            mode: 'multiple',
                            request: async (queryParam: string) =>
                              apiMixWarehouseOptions({ queryParam }, { valueName: 'code' }),
                          }}
                        />
                      </Form.Item>
                      {nodeFileName}
                    </>
                  );
                },
                async request({ form }) {
                  const formData = form.getFieldsValue() as Parameters<
                    typeof apiInventorySnapshotExport
                  >[0];

                  await apiInventorySnapshotExport({
                    ...formData,
                    snapshotDate: dateUtils.dateFormatter(formData?.snapshotDate),
                  });
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__Inventory__List.Index.chineseSymbols}
            </AsyncExportButton>
          }
        >
          {tabList.map((item: any) => (
            <Tabs.TabPane tab={<div className={styles.tab}>{item.label}</div>} key={item.key} />
          ))}
        </Tabs>
        <ProTable
          className={classnames(styles['inventory-list'])}
          request={(params) => {
            setLoading(true);
            const res = handleSearchList(params);

            res?.finally(() => {
              setLoading(false);
            });
            return res;
          }}
          /** 不使用ProTable的Loading效果, 使用外部Spin.spinning */
          loading={false}
          columns={columns}
          rowKey="id"
          actionRef={tableRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          options={false}
          sticky
          pagination={{
            showSizeChanger: true,
            current: pageIndex,
            defaultPageSize: 50,
            position: ['bottomLeft'],
          }}
          search={false}
          onChange={(pagination) => {
            setPageIndex(pagination.current);
          }}
        />
      </Spin>
      {showUnreceiveDetail && (
        <UnreceiveDetailModal
          visible={showUnreceiveDetail}
          warehouseCode={currentRecord.warehouseCode}
          warehouseAreaName={currentRecord.warehouseAreaName}
          unsignedOrders={currentRecord.unsignedOrders}
          onCancel={closeUnreceiveDetailModal}
        />
      )}
    </PageContainer>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.models.inventory,
}))(OverseaInventoryListPage);
