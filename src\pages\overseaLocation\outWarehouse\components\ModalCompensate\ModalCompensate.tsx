import I18N from '@/utils/I18N';
import type { TypeZouModalRef, TypeSimpleUploadRef } from '@/components';
import { SearchSelect, ZouModal, SimpleUpload, LoadButton } from '@/components';
import {
  apiOutboundCompensateDetail,
  apiOutboundCompensateApply,
  apiUploadFileNew,
} from './ModalCompensateImport';
import { Form, Input, InputNumber, message } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { TextValue } from '@/views';
import { apiMapDictType, EnumUploadFileType } from '@/api';
import { tipsUtils, ruleUtils, transString, historyGoChild } from '@/utils';

type TypeProps = {
  /** 是否为OMS侧, 默认false, 表示门户侧 */
  isOms?: boolean;
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};
type TypeOpenParams = {
  /** 履约单号 */
  complianceNo: string;
  /** 申请单号 */
  podOrderNo: string;
  /** 客户公司ID */
  compensateCompanyId?: string;
  /** 客户公司名称 */
  compensateCompanyName?: string;
  /** 索赔状态 */
  compensateStatus?: string;
  /** 仓库币种 */
  currency?: string;
};

/** 索赔弹窗Ref */
export type TypeModalCompensateRef = {
  open: (params: TypeOpenParams) => Promise<any>;
};

/** 索赔弹窗组件
 * 同时兼容门户侧和OMS, 可以一键复制
 */
export default React.forwardRef(function ModalCompensate(
  props: TypeProps,
  ref: React.Ref<TypeModalCompensateRef | undefined>,
) {
  const { isOms = false } = props;
  const { modalRef, uploadRef, asyncInit, form, asyncClear, asyncSubmit, detailData } = useConfig({
    props,
  });
  const title = isOms
    ? I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate.ModalCompensate
        .chineseSymbols16
    : I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate.ModalCompensate
        .chineseSymbols15;
  const required = true;

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams) {
      modalRef.current?.open();
      await asyncInit(params.complianceNo, params);
    },
  }));

  /** 详情币种 */
  const currencyFormItem = (
    <Form.Item name="currency" noStyle>
      <TextValue />
    </Form.Item>
  );

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title,
          // visible: true,
        },
        async onOk() {
          await asyncSubmit();
        },
        async onCancel() {
          asyncClear();
        },
      }}
    >
      <Form {...{ form, labelCol: { span: 6 } }}>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.applicationNo,
            name: 'podOrderNo',
          }}
        >
          <TextValue />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
                .CompensateOperateRecord.chineseSymbols4,
            name: 'compensateStatus',
            /** 仅oms侧必填 */
            rules: [
              { required: isOms },
              {
                async validator(rule, val) {
                  if (detailData.compensateStatus !== '1001' /** 待索赔 */ && val === '1001') {
                    throw new Error(
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate.ModalCompensate.chineseSymbols14,
                    );
                  }
                },
              },
            ],
          }}
        >
          <SearchSelect
            {...{
              disabled: isOms === false,
              request: apiMapDictType.compensationStatusType,
            }}
          />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
                .CompensateOperateRecord.chineseSymbols3,
            name: 'applyCompensateAmount',
            rules: [{ required }, ruleUtils.ruleNumAndFloatTwo],
          }}
        >
          <InputNumber
            {...{
              stringMode: true,
              addonAfter: currencyFormItem,
            }}
          />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols13,
            name: 'actualCompensateAmount',
            rules: [ruleUtils.ruleNumAndFloatTwo],
          }}
        >
          <InputNumber
            {...{
              disabled: isOms === false,
              stringMode: true,
              addonAfter: currencyFormItem,
            }}
          />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
                .CompensateOperateRecord.chineseSymbols1,
            name: 'remark',
            rules: [{ max: 255 }],
          }}
        >
          <Input.TextArea />
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols12,
            name: 'fileList',
            rules: [
              {
                required,
                message:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                    .ModalCompensate.chineseSymbols11,
              },
              {
                type: 'array',
                max: 10,
                message:
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                    .ModalCompensate.chineseSymbols10,
              },
            ],
          }}
        >
          <SimpleUpload
            {...{
              ref: uploadRef,
              multiple: true,
              maxCount: 10,
              btnExtra: () => {
                return (
                  <span style={{ color: '#7F7F7F', fontSize: 12 }}>
                    {
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                        .ModalCompensate.chineseSymbols9
                    }
                    <span style={{ color: 'red' }}>
                      {
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                          .ModalCompensate.chineseSymbols8
                      }
                    </span>
                    {
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                        .ModalCompensate.chineseSymbols7
                    }
                  </span>
                );
              },
              btnExtraAlign: 'top',
              async uploadRequest(fileList) {
                for (const file of fileList) {
                  if (file.status !== 'done' && file.originFileObj) {
                    const { data } = await apiUploadFileNew({
                      file: file.originFileObj,
                      pathType: EnumUploadFileType.temp,
                    });

                    file.fileName = data.originFileName;
                    file.filePath = data.filePath;
                    file.status = 'done';
                  }
                }
              },
            }}
          >
            <LoadButton {...{ type: 'default' }}>
              {
                I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                  .ModalCompensate.chineseSymbols6
              }
            </LoadButton>
          </SimpleUpload>
        </Form.Item>
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols5,
            hidden: isOms === false,
          }}
        >
          <a
            onClick={() =>
              historyGoChild({
                newTab: true,
                pathname: './outbound-compensate-detail',
                query: {
                  complianceNo: detailData?.complianceNo || '',
                },
              })
            }
          >
            {detailData.compensateId
              ? I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                  .ModalCompensate.chineseSymbols4
              : null}
          </a>
        </Form.Item>
        <Form.Item hidden>
          <TextValue
            name="compensateId"
            label={
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols3
            }
          />
          <TextValue
            name="compensateCompanyId"
            label={
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols2
            }
          />
          <TextValue
            name="compensateCompanyName"
            label={
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols1
            }
          />
          <TextValue
            name="complianceNo"
            label={
              I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
                .ModalCompensate.chineseSymbols
            }
          />
        </Form.Item>
      </Form>
    </ZouModal>
  );
});

function useConfig({ props }: { props: TypeProps }) {
  const modalRef = useRef<TypeZouModalRef>();
  const uploadRef = useRef<TypeSimpleUploadRef>();
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<TypeOutboundCompensateApplyFormData>({});

  async function asyncInit(complianceNo: string, params: TypeOpenParams) {
    const initData = transDetail({
      ...params,
      compensateStatus: props.isOms
        ? params.compensateStatus
        : params.compensateStatus ?? '1001' /** 门户侧值为null时赋值一个待索赔 */,
    });

    setDetailData(initData);
    form.setFieldsValue(initData);

    const { data } = await apiOutboundCompensateDetail({ complianceNo });
    const newData = transDetail(data);

    /** 存在索赔记录时才赋值 */
    if (data?.compensateId) {
      setDetailData(newData);
      form.setFieldsValue(newData);
    }
  }

  async function asyncClear() {
    form.resetFields();
    setDetailData({});
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      // message.error(err.errorFields[0].errors);
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    await uploadRef.current?.fetchUploadRequest();
    const formData = form.getFieldsValue();

    await apiOutboundCompensateApply(formData);
    message.success(
      I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate.ModalCompensate
        .submittedSuccessfully,
    );
    props.submitSuccessCB?.();
    asyncClear();
  }

  return {
    asyncInit,
    modalRef,
    uploadRef,
    form,
    detailData,
    asyncClear,
    setDetailData,
    asyncSubmit,
  };
}

/** 转换DetailData */
function transDetail(inData: TypeOutboundCompensateApplyFormData) {
  return {
    ...inData,
    compensateStatus: transString(inData.compensateStatus),
    fileList: (inData.fileList || []).map((item) => ({
      ...item,
      status: 'done',
      name: item.fileName,
    })),
  };
}
