import { useCallback, useEffect, useMemo, useState } from 'react';
import { request } from 'umi';

/** 账户币种信息 */
export type TypeAccountCurrencyOption = {
  /** 主键id */
  id?: number;
  /** 公司id */
  companyId?: string;
  /** 公司名 */
  companyName?: string;
  /** 客户结算币种 */
  customerCurrency?: string;
  /** 账户余额 */
  accountBalance?: number;
  /** 可用余额 */
  availableBalance?: number;
  /** 信用额度 */
  creditLimit?: number;
  /** 额度到期日（定时任务过期额度没有到期时间） */
  creditLimitMaturityDate?: string;
  /** 冻结金额 */
  frozenAmount?: number;
  /** 累计充值金额 */
  cumulativeRechargeAmount?: number;
  /** 累计消费金额 */
  cumulativeConsumptionAmount?: number;
};

/** 账户多币种信息获取 */
export function apiAccountCurrencyOptions(data: {
  /** 客户币种（模糊查询） */
  currency?: string;
}) {
  return request<NsApi.TypeResponseData<TypeAccountCurrencyOption[]>>(
    '/zouwu-oms-charge/portal/charge/account/list/company/currency',
    { data, method: 'POST' },
  ).then((res) => {
    return (res.data || []).map((item) => {
      return {
        ...item,
        label: item.customerCurrency,
        value: item.customerCurrency,
      };
    });
  });
}

type TypeHookAccountCurrencyResult = {
  currencyOptions: { label?: string; value?: string }[];
  defaultCurrency?: string;
  firstCurrency?: string;
};

/** 获取账户币种hook */
export function useAccountCurrencyOptions({
  defaultCurrency,
  callback,
}: {
  /** 默认币种,有设置默认读设置,没有就选第一个 */
  defaultCurrency?: string;
  callback?: (params: TypeHookAccountCurrencyResult) => void;
} = {}) {
  const [currencyOptions, setCurrencyOptions] = useState<{ label?: string; value?: string }[]>([]);
  /** 第一个币种 */
  const firstCurrency = getFirstCurrency(currencyOptions);
  /** 异步获取账户币种信息, 直接返回获取结果 */
  const asyncAccountCurrencyOptions = useMemo(async function () {
    const options = await apiAccountCurrencyOptions({});

    setCurrencyOptions(options);
    if (options.length === 0) {
      console.error('The number of currencies is less than 1');
    }

    /** 第一个币种 */
    const innerFirstCurrency = getFirstCurrency(options);

    const result = {
      currencyOptions,
      defaultCurrency: defaultCurrency || innerFirstCurrency,
      firstCurrency: innerFirstCurrency,
    };

    callback?.(result);
    return result;
  }, []);

  function getFirstCurrency(options: { label?: string; value?: string }[]) {
    return options.length > 0 ? options[0].value : undefined;
  }

  return {
    currencyOptions,
    defaultCurrency: defaultCurrency || firstCurrency,
    firstCurrency,
    asyncAccountCurrencyOptions,
  };
}
