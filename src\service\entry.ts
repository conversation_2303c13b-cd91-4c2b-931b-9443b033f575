import { request } from 'umi';

/**
 * 登录
 * @param data
 */

export const userLogin = async (data: any) => {
  return request('/api/website/web/webLogin/login.do', {
    method: 'POST',
    data,
  });
};

// 手机号码登录
export const loginByPhone = async (data: any) => {
  return request('/api/website/web/webLogin/loginByPhone.do', {
    method: 'POST',
    data,
  });
};

// 注册、登录、忘记密码 发送手机验证码(公共接口) 通过手机号发送验证码
export const sendPhoneMessage = async (data: any) => {
  return request('/api/website/web/verCode/sendPhoneMessage', {
    method: 'POST',
    data,
  });
};

// 注册时给邮件发送验证码
export const sendRegistEmailCode = async (data: any) => {
  return request('/api/website/web/verCode/sendRegistEmailCode', {
    method: 'POST',
    data,
  });
};

// 验证用户名是否存在 不存在添加公司字段
export const checkUserExists = async (data: any) => {
  return request('/api/website/web/webLogin/checkUserExists.do', {
    method: 'POST',
    data,
  });
};

// 根据用户名注册(网站)
export const userRegisterWeb = async (data: any) => {
  return request('/api/website/web/webRegister/userRegisterWeb', {
    method: 'POST',
    data,
  });
};

// 根据邮箱注册(网站)
export const userRegisterFromEmail = async (data: any) => {
  return request('/api/website/web/webRegister/userRegisterFromEmail', {
    method: 'POST',
    data,
  });
};

// 忘记密码（手机找回）
export const forgetPwdByPhone = async (data: any) => {
  return request('/api/website/web/webRegister/forgetPwd', {
    method: 'POST',
    data,
  });
};

// 忘记密码（邮箱找回）
export const forgetPwdByEmail = async (data: any) => {
  return request('/api/website/web/webRegister/forgetPwdByEmail', {
    method: 'POST',
    data,
  });
};

/** 获取用户信息 */
export const getUserInfo = async () => {
  return request('/api/website/webUserManager/getUserInfo');
};

/** 获取用户消息 */
export const getUserMsgInfo = async (data?: any) => {
  return request('/api/website/webUserManager/getUserMsgInfo', {
    method: 'POST',
    data,
  });
};

// 退出
export const doLogOut = async (data = {}) => {
  return request('/api/website/web/webLogin/loginOut.do', {
    method: 'POST',
    data,
  });
};

// 获取微信账号是否绑定账户
export const sendCheckWechatCodeIsBind = async (data: any) => {
  return request('/api/website/wechat/login/bindAccount.do', {
    method: 'POST',
    data,
  });
};

// 账号存在绑定微信
export const sendHaveAccountBindWechat = async (data: any) => {
  return request('/api/website/wechat/login/wechatLogin.do', {
    method: 'POST',
    data,
  });
};

// 账号不存在注册并绑定微信
export const sendLackAccountBindWechat = async (data: any) => {
  return request('/api/website/wechat/login/wechatRegisteredlogin.do', {
    method: 'POST',
    data,
  });
};

// 校验注册步骤
export const checkRegisterStep = async (data: any) => {
  return request('/api/website/web/webRegister/userRegisterPreCheck', {
    method: 'POST',
    data,
  });
};

// 校验用户是否为待激活状态
export const checkUserNameWaitActivate = async (data: any) => {
  return request('/api/website/member/invite/checkUserNameWaitActivate', {
    method: 'POST',
    data,
  });
};
