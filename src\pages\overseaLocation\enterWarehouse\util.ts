import { uniq } from 'lodash';

// 转换为相似箱唛头合并的数据
export const convertSkuData = (list: any[]) => {
  if (!list || !list.length) return [];

  const result: any[] = [];

  const uniqueIds = uniq(list.map(o => o.boxUniqueId));


  const sortList = uniqueIds.map((id: any) => {
    const obj: any = {
      key: id,
      children: []
    };

    obj.children = list.filter((o: any) => id === o.boxUniqueId);

    return obj;
  });

  sortList.forEach((item: any) => {
    item.children.forEach((child: any, index: number) => {
      result.push({
        ...child,
        index,
        rowSpan: index === 0 ? item.children.length : 0
      });
    });
  });

  return result;
};