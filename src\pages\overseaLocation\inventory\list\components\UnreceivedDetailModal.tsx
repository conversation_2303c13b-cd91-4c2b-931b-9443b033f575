import I18N from '@/utils/I18N';
import React, { useRef } from 'react';
import { Modal, Tooltip } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import moment from 'moment';
import { ENTER_WAREHOUSE_TYPE } from '@/utils/const';
import './index.less';
import { apiMapDictType } from '@/api';

interface IProps {
  visible: boolean;
  unsignedOrders: any[] | [];
  warehouseCode: string;
  warehouseAreaName: string;
  onCancel: () => void;
}

const UnreceiveDetailModal: React.FC<IProps> = (props) => {
  const { visible, warehouseCode, warehouseAreaName, unsignedOrders, onCancel } = props;
  const tableRef = useRef<any>();

  const columns: ProColumns[] = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__List__Components.UnreceivedDetailModal
          .warehouseCode,
      key: 'warehouseCode',
      dataIndex: 'warehouseCode',
      align: 'center',
      // width: 150,
      render: () => {
        return (
          <div>{warehouseCode}</div>
          // <Tooltip title={warehouseCode} placement="leftTop">
          //   <span className="table-normal-text">{warehouseCode}</span>
          // </Tooltip>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.reservoirArea,
      key: 'warehouseAreaName',
      dataIndex: 'warehouseAreaName',
      align: 'center',
      // width: 150,
      render: () => {
        return (
          <div>{warehouseAreaName}</div>
          // <Tooltip title={warehouseAreaName} placement="leftTop">
          //   <span className="table-normal-text">{warehouseAreaName}</span>
          // </Tooltip>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.transactionNote,
      key: 'complianceNo',
      dataIndex: 'complianceNo',
      // width: 200,
      align: 'center',
      // render: (_: any) => {
      //   return (
      //     <Tooltip title={_} placement="leftTop">
      //       <span className="compliance-no-text">{_}</span>
      //     </Tooltip>
      //   );
      // },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__List__Components.UnreceivedDetailModal
          .documentStatus,
      key: 'orderStatus',
      dataIndex: 'orderStatus',
      width: 140,
      align: 'center',
      valueType: 'select',
      request: apiMapDictType.portalInboundOrderStatus,
      // render: (_: any, record: any) => {
      //   return <span>{ENTER_WAREHOUSE_TYPE[record.orderStatus]}</span>;
      // },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__List__Components.UnreceivedDetailModal
          .expectedArrival,
      key: 'expectTime',
      dataIndex: 'expectTime',
      // width: 150,
      align: 'center',
      render: (_: any, record: any) => moment(record.expectTime).format('YYYY-MM-DD'),
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__Inventory__List__Components.UnreceivedDetailModal
          .unsignedQuantity,
      key: 'unreceivedQuantity',
      dataIndex: 'unreceivedQuantity',
      align: 'center',
      // width: 100,
    },
  ];

  return (
    <Modal
      title={
        I18N.Src__Pages__OverseaLocation__Inventory__List__Components.UnreceivedDetailModal
          .goodsInTransit
      }
      width={1040}
      visible={visible}
      maskClosable={false}
      onCancel={onCancel}
      footer={false}
    >
      <ProTable
        className="unreceived-detail-list"
        cardProps={false}
        search={false}
        dataSource={unsignedOrders}
        columns={columns}
        rowKey="key"
        actionRef={tableRef}
        scroll={{ x: 'max-content' }}
        pagination={false}
        options={false}
      />
    </Modal>
  );
};

export default UnreceiveDetailModal;
