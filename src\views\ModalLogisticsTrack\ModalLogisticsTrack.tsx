import type { TypeZouModalRef } from '@/components';
import { ZouModal } from '@/components';
import { eR, useTimeSeries } from '@/utils';
import { FormProDescriptions } from '@/views';
import { Steps } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import type { TypeLogisticsTrackDetail } from './ModalLogisticsImport';
import { isWWL, apiLogisticsTrackDetail } from './ModalLogisticsImport';
import css from './ModalLogisticsTrack.less';
import { textMap } from './text-map';

type TypeProps = {};
type TypeOpenParams = {
  /** 订单id | 出库单id, 物流拦截也会使用 */
  orderId?: string;
  /** 物流可视化id: 退货面单使用 */
  orderLogisticsId?: string;
};

export type TypeModalLogisticsTrackRef = { open: (params: TypeOpenParams) => Promise<void> };

/** 物流逻辑查看 */
export default React.forwardRef(function ModalLogisticsTrack(
  props: TypeProps,
  ref: React.Ref<TypeModalLogisticsTrackRef | undefined>,
) {
  const modalRef = useRef<TypeZouModalRef>();
  const { asyncInit, asyncClear, detailData } = useConfig();

  useImperativeHandle(ref, () => ({
    async open(params) {
      modalRef.current?.open();
      await asyncInit(params);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        hideCancelBtn: true,
        fixedHeight: true,
        modalProps: {
          title: textMap.txtModalTitle,
          okText: textMap.txtOk,
          width: 600,
          afterClose() {
            asyncClear();
          },
        },
      }}
    >
      <div className={css.logisticsDetail}>
        <div className={css.leftBox}>
          <FormProDescriptions
            {...{
              dataSource: detailData,
              column: 1,
              columns: [
                { title: textMap.txtDesc1, dataIndex: 'logisticsChannel' },
                {
                  title: textMap.txtDesc2,
                  dataIndex: 'registerCarrier',
                  /** 门户不展示 查询渠道 */
                  hideInDescriptions: isWWL,
                  render(dom, record: TypeLogisticsTrackDetail) {
                    return (
                      [record.registerCarrierName, record.registerCarrier]
                        .filter(Boolean)
                        .join('-') || '-'
                    );
                  },
                },
                { title: textMap.txtDesc3, dataIndex: 'trackingNo' },
                { title: textMap.txtDesc4, dataIndex: 'latestNode' },
              ],
            }}
          />
        </div>
        <div className={css.rightBox}>
          <Steps
            progressDot
            current={detailData.logisticsTrackDetails?.length || 3}
            direction="vertical"
          >
            {(detailData.logisticsTrackDetails || []).map((item) => {
              return (
                <Steps.Step
                  key={item.actionCodeDesc}
                  {...{
                    title: item.actionCodeDesc,
                    description: (
                      <>
                        <div> {item.actionAddress}</div>
                        <div> {item.timeZone}</div>
                        <div> {item.time}</div>
                      </>
                    ),
                  }}
                />
              );
            })}
          </Steps>
        </div>
      </div>
    </ZouModal>
  );
});

function useConfig() {
  const [detailData, setDetailData] = useState<TypeLogisticsTrackDetail>({});
  const { timeSeries } = useTimeSeries();

  async function asyncInit(params: TypeOpenParams) {
    const timingCur = timeSeries.init();
    const { data } = await apiLogisticsTrackDetail(params);

    if (timeSeries.notEquals(timingCur)) {
      return;
    }

    setDetailData(data);
  }

  async function asyncClear() {
    timeSeries.clear();
    setDetailData({});
  }

  return { asyncInit, asyncClear, detailData };
}

/** 判断物流信息查询异常 */
export function isLogisticsQueryError(record: { /** 物流查询状态 */ trackNode?: string }) {
  return record.trackNode === 'QUERY_ERROR';
}

/** 渲染物流进展节点 */
export function renderLogisticsTrackNode(params: {
  /** 有异常时是否展示异常, 默认true
   * @门户不展示查询异常
   */
  showError?: boolean;
  /** 最后一个节点名称 */
  latestNode?: string;
  /** 物流轨迹查询状态 */
  trackNode?: string;
  onClick?: () => void;
}): React.ReactNode {
  const {
    /** 门户默认值false, oms默认值true */
    showError = isWWL ? false : true,
    latestNode,
    trackNode,
    onClick,
  } = params;

  const isTrackNodeError = isLogisticsQueryError({ trackNode });

  if (showError === false && isTrackNodeError) {
    return '-';
  }

  /** 物流进展 */
  const logisticsProgressNode = {
    true: <span style={{ color: 'red' }}>{textMap.txtQueryError}</span>,
    false: eR(latestNode, () => <a onClick={onClick}>{latestNode}</a>),
  }[`${isTrackNodeError}`];

  return logisticsProgressNode;
}
