/* stylelint-disable selector-pseudo-class-no-unknown */
.header {
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label > label {
      display: block;
      width: 100%;
      overflow: hidden;
      font-size: 12px;
      line-height: 32px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.container {
  .multiplePackagesBox {
    display: flex;
    justify-content: space-between;
  }

  /* 多包裹标记 */
  .multiplePackages {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-left: 8px;
    color: red;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    border: 1px solid red;
    border-radius: 50%;

    &::after {
      /* 设计是一个图标, 防止被国际化 */
      display: inline-block;

      /* 中文: 多 */
      content: '\591a';
    }
  }

  :global(.ant-tabs) {
    margin-top: 13px;
    margin-bottom: 8px;
    background-color: #fff;
  }
  .tab {
    font-weight: bold;
  }

  /* 重构样式 */
  :global {
    .ant-table-thead > tr > th {
      background-color: #fff;
      border-bottom: 10px solid #f0f2f5;
    }

    /* 表头分割线 */
    .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      width: 0;
    }

    /* 表单样式 */
    .ant-table {
      font-size: 12px;

      thead {
        font-size: 14px;
      }
      .ant-table-tbody > tr > td {
        max-width: 300px;
        vertical-align: top;

        /* 操作列 */
        &.action {
          white-space: nowrap;
          // vertical-align: middle;
          > .ant-space {
            justify-content: center;
          }
        }
      }

      td.ant-table-cell > a {
        font-size: inherit;
      }
    }

    .ant-pagination {
      padding: 12px 0;
      background-color: #fff;
    }
  }

  /* 局部module样式 */
  :local {
    .status {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1px 14px;
      color: #fff;
      font-weight: bold;
      white-space: nowrap;
      background-color: #ffa940;
    }

    .podNo {
      font-weight: bold;
      word-break: break-word;

      &:not(.podActive) {
        color: #999;
        cursor: initial;
      }

      // &.podActive {
      //   cursor: pointer;
      // }
    }
    .createDate {
      color: #999;
    }
    .leftLine {
      padding-left: 4px;
      border-left: 2px solid #91d5ff;
    }
    .noWrap {
      white-space: nowrap;
    }
  }
}
