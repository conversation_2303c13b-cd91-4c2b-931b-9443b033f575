import I18N from '@/utils/I18N';
export const textMap = {
  /** 按钮文字 */
  txtBtn: I18N.Src__Views__AsyncImportButton.Text_map.batchImport,
  txtModalTitle: I18N.Src__Views__AsyncImportButton.Text_map.batchImport,
  txtModalCancel: I18N.Src__Views__AsyncImportButton.Text_map.cancel,
  txtModalConfirm: I18N.Src__Views__AsyncImportButton.Text_map.determine,
  txtImportType: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.businessType,
  txtChooseFile: I18N.Src__Views__AsyncImportButton.Text_map.selectFile,
  txtUploadBtn: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols5,
  txtDownloadTemplateBtn: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols4,
  txtNotifyMessage: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols3,
  txtGoto: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols2,
  txtDes1: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols1,
  txtDes2: I18N.Src__Routes.Batch_manage.chineseSymbols,
  txtDes3: I18N.Src__Views__AsyncImportButton.Text_map.chineseSymbols,
  txtFileNameLabel:
    I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols6,
  txtFileUrl: I18N.Src__Views__AsyncImportButton.AsyncImportButton.file,
  txtIdentityNoType: '单据识别对象',
  txtFileLimitTitle(fileSuffix: string[] | undefined, maxFileSizeMb: number) {
    if ((fileSuffix || [])?.length < 1) {
      return null;
    }
    return I18N.template(
      I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.chineseSymbols1,
      { val1: (fileSuffix || []).join(', '), val2: maxFileSizeMb },
    );
  },
} as const;
