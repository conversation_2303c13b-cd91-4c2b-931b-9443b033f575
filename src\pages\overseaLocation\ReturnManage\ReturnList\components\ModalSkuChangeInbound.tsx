import type { TypeZouModalRef } from '@/components';
import { SearchSelect, ZouModal } from '@/components';
import { TextValue } from '@/views';
import { tipsUtils } from '@/utils';
import { Form, Input, message, Table } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import classnames from 'classnames';
import { cssFormListTable } from '@/styles';
import { apiReturnOrderSkuChangeInbound } from '../ReturnApi';
import { apiGoodsSkuOptions } from '@/pages/pagesApi';

type TypeProps = {
  /** 组件Props定义 */
};

type TypeOpenParams = {
  /** 组件打开参数定义 */
  /** 申请单号 */
  podOrderNo?: string;
  /** 换标sku */
  skuChangeList?: {
    /** 原始sku  */
    sku?: string;
    /** 变更sku */
    skuChange?: string;
  }[];
};

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};

export type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

const listName = 'skuChangeList';

/** 换标入库 */
export default React.forwardRef(function ModalSkuChangeInbound(
  props: TypeProps,
  ref: React.Ref<TypeModalRef | undefined>,
) {
  const { modalRef, asyncInit, asyncClear, asyncSubmit, form } = useConfig({ props });

  useImperativeHandle(ref, () => ({
    async open(params, operate) {
      modalRef.current?.open();
      asyncInit(params, operate);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        modalProps: {
          title: '换标入库',
          width: 680,
          afterClose() {
            asyncClear();
          },
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      <Form {...{ form, labelCol: { span: 4 } }}>
        <div className={classnames(cssFormListTable.FormListTable)}>
          <Form.List {...{ name: listName, initialValue: [] }}>
            {function (fieldList, action, { errors }) {
              return (
                <>
                  <Table
                    {...{
                      dataSource: fieldList.map((item) => ({ key: item.key })),
                      pagination: false,
                      columns: [
                        {
                          title: '商品SKU',
                          dataIndex: 'sku',
                          width: 260,
                          render(val, record, index) {
                            return (
                              <Form.Item
                                {...{
                                  label: '商品SKU',
                                  name: [index, 'sku'],
                                  rules: [
                                    {
                                      required: true,
                                    },
                                  ],
                                  children: <Input disabled />,
                                }}
                              />
                            );
                          },
                        },
                        {
                          title: '换标SKU',
                          dataIndex: 'skuChange',
                          width: 260,
                          render(val, record, index) {
                            return (
                              <Form.Item
                                {...{
                                  label: '换标SKU',
                                  name: [index, 'skuChange'],
                                  rules: [{ required: true, message: 'SKU未维护，无法换标入库' }],
                                  children: (
                                    <SearchSelect
                                      {...{ request: (sku) => apiGoodsSkuOptions({ sku }) }}
                                    />
                                  ),
                                }}
                              />
                            );
                          },
                        },
                      ],
                    }}
                  />
                  <Form.ErrorList errors={errors} />
                </>
              );
            }}
          </Form.List>
        </div>
        <Form.Item hidden>
          <TextValue {...{ label: '申请单号	', name: 'podOrderNo' }} />
        </Form.Item>
      </Form>
    </ZouModal>
  );
});

function useConfig({ props }: { props: TypeProps }) {
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<any>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const modalRef = useRef<TypeZouModalRef>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    const initData = transDetail(params);

    setDetailData(initData);
    form.setFieldsValue(initData);
    setOperateMap(operate || {});
  }

  async function asyncClear() {
    setOperateMap({});
    setDetailData({});
    form.resetFields();
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();
    const skuChangeMap = (formData[listName] || []).reduce((pre: any, cur: any) => {
      pre[cur.sku] = cur.skuChange;
      return pre;
    }, {});

    await apiReturnOrderSkuChangeInbound({
      ...formData,
      skuChangeMap,
      [listName]: undefined,
    });
    message.success(tipsUtils.TIPS_FORM_SUBMIT_SUCCESS);
    operateMap?.submitSuccessCB?.();
  }

  return { detailData, operateMap, asyncInit, asyncClear, asyncSubmit, form, modalRef };
}

/** 转换DetailData */
function transDetail(inData: TypeOpenParams) {
  return {
    ...inData,
  };
}
