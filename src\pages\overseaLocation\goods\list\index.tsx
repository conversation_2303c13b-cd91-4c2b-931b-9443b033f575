import React, { useRef, useState } from 'react';
import { useDispatch } from 'umi';
import { Button, Divider, message, Space, Input, Popconfirm } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { forIn } from 'lodash';
import I18N from '@/utils/I18N';
import css from './index.less';
import { PageContainer } from '@/components/PageContainer';
import { EnumActionType } from '@/utils/const';
import { HumpToUnderline } from '@/utils/util';
import GoodsImage from '@/components/GoodsImage';
import DeleteGoodsModal from '../components/DeleteGoodsModal';
import type { TypeGoodsModalPrintLabelRef } from '../components';
import { GoodsEditImport, GoodsImport, GoodsModalPrintLabel } from '../components';
import { useSensors } from '@/hooks/useSensors';
import { historyGoChild } from '@/pages/overseaLocation/utils';
import { eR, strUtils } from '@/utils';
import type { ProFormInstance } from '@/common-import';
import { ProFormSelect } from '@/common-import';
import { apiGetCombinedGoodsByGoodsId, apiGoodsBatchExport, apiGoodsList } from '../goodsApi';
import { AsyncExportButton } from '@/views';
import { EnumExportDataSource } from '@/constants/enum';

/** 商品列表 */
export default function OverseaGoodsListPage() {
  const dispatch = useDispatch();
  const [pageIndex, setPageIndex] = useState<any>(1);
  const [showDeleteGoodsModel, setShowDeleteGoodsModel] = useState<boolean>(false);
  const [existedOrder, setExistedOrder] = useState<boolean>(false);
  const [relatedGroupGoodsList, setRelatedGroupGoodsList] = useState<any[]>([]);
  const [currentId, setCurrentId] = useState<string>('');
  const [currentName, setCurrentName] = useState<string>('');
  const tableRef = useRef<any>();
  const formRef = useRef<ProFormInstance>();
  const goodsModalPrintLabelRef = useRef<TypeGoodsModalPrintLabelRef>();
  /** 批量打印sku */
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<TypeGoodsTB[]>([]);
  const { trackerSend } = useSensors({
    midfix: 'OL_GOODS_LIST',
  });
  const searchOptions = [
    { label: '客户SKU', value: 'skuRemark' },
    { label: '其它编码', value: 'otherCode' },
    { label: 'UPC', value: 'upc' },
  ] as const;
  const columns: ProColumns[] = [
    {
      title: '',
      key: 'pictureUrl',
      dataIndex: 'pictureUrl',
      render: (_: any, record: any) => {
        return <GoodsImage src={record.pictureUrl} />;
      },
      width: 100,
      search: false,
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      renderFormItem() {
        return <Input.TextArea />;
      },
      formItemProps: {
        rules: [
          {
            async validator(rule, value) {
              if (value) {
                const skuList = strUtils.strSplitForSku(value);

                if (skuList.length > 100) {
                  throw new Error(
                    I18N.Src__Pages__OverseaLocation__Goods__List.Index.chineseSymbols4,
                  );
                }
              }
            },
          },
        ],
      },
      search: {
        transform: (val: string) => {
          const skuList = strUtils.strSplitForSku(val);

          return {
            skuList: skuList.length > 1 ? skuList : undefined,
            sku: skuList.length === 1 ? skuList[0] : undefined,
          };
        },
      },
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      key: 'name',
      dataIndex: 'name',
      render: (_: any, record: any) => {
        return (
          <>
            <div>{record.name}</div>
            <div>{record.enName}</div>
          </>
        );
      },
    },
    {
      title: '多选搜索',
      dataIndex: 'searchInfo',
      hideInTable: true,
      search: {
        transform: (val, field, formData) => {
          const { searchInfo, searchType } = formData;

          return {
            [searchType]: searchInfo,
            searchType: undefined,
          };
        },
      },
      fieldProps: {
        addonBefore: (
          <ProFormSelect
            {...{
              width: 100,
              name: 'searchType',
              initialValue: searchOptions[0].value,
              noStyle: true,
              allowClear: false,
              options: searchOptions as any,
            }}
          />
        ),
      },
      formItemProps: {
        noStyle: true,
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__List.Index.commodityAssociation,
      key: 'wwlSku',
      dataIndex: 'wwlSku',
      search: false,
      render: (dom: any, record: any) => {
        return (
          <>
            <div>客户SKU：{eR(record.skuRemark)}</div>
            <div>其他编码：{eR(record.otherCode)}</div>
            <div>UPC：{eR(record.upc)}</div>
          </>
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__List.Index.long,
      key: 'length',
      dataIndex: 'length',
      search: false,
      width: 80,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__List.Index.wide,
      key: 'width',
      dataIndex: 'width',
      search: false,
      width: 80,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__List.Index.high,
      key: 'height',
      dataIndex: 'height',
      search: false,
      width: 80,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Goods__List.Index.weight,
      key: 'weight',
      dataIndex: 'weight',
      search: false,
      width: 80,
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      key: 'option',
      fixed: 'right',
      width: 'auto',
      className: 'table-cell-option',
      render: (_: any, record: any) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <a
              onClick={() => {
                goodsModalPrintLabelRef.current?.open({ goodsList: [record] });
              }}
            >
              {I18N.Src__Pages__OverseaLocation__Goods__List.Index.print}
            </a>
            <a
              onClick={() => {
                historyGoChild({
                  newTab: true,
                  pathname: './action',
                  query: { id: record.id, type: EnumActionType.EDIT },
                });
              }}
            >
              {I18N.Src__Pages__Common__Template.Index.edit}
            </a>
            <a
              onClick={() => {
                historyGoChild({
                  newTab: true,
                  pathname: './action',
                  query: { id: record.id, type: EnumActionType.COPY },
                });
              }}
            >
              {I18N.Src__Pages__Order__Si__Component__ShareModals.Alert.copy}
            </a>
            {/* 因下游无法删除商品, 隐藏该功能 */}
            {/* <Popconfirm
              {...{
                title: '确定删除？',
                onConfirm: () => asyncDelete(record.id, record.name),
              }}
            >
              <a>删除</a>
            </Popconfirm> */}
          </Space>
        );
      },
      search: false,
    },
  ];

  async function asyncGoodsSearchList(params: any) {
    const { pageSize, current: currentPage, ...args } = params;
    const { data } = await apiGoodsList({
      currentPage,
      pageSize,
      condition: {
        ...args,
        searchType: undefined,
      },
    });

    if ((args.skuList && args.skuList.length > 0) || args.sku) {
      message.info(
        I18N.template(I18N.Src__Pages__OverseaLocation__Goods__List.Index.chineseSymbols3, {
          val1: data.totalSize || 0,
        }),
      );
    }

    const sensorsData: any = {};

    forIn(params, (value, key) => {
      sensorsData[HumpToUnderline(key)] = value;
    });

    trackerSend({
      name: 'OVERSEA_GOODS_LIST_SEARCH',
      data: sensorsData,
    });

    return {
      data: data.records,
      success: true,
      total: data.totalSize,
    };
  }

  async function asyncDelete(id: string, name: string) {
    const { data: result } = await apiGetCombinedGoodsByGoodsId({ id });

    if (result?.exist || result?.combinedGoodsList?.length! > 0) {
      setCurrentId(id);
      setCurrentName(name);
      setExistedOrder(result?.exist!);
      setShowDeleteGoodsModel(true);
      setRelatedGroupGoodsList(result?.combinedGoodsList || []);
    } else {
      doDelete(id);
    }
  }

  const doDelete = async (id: string) => {
    const result = await dispatch({
      type: 'goods/deleteOverseaLocationGoods',
      payload: { id },
    });

    if (result) {
      hiddenGoodsDeleteModal();
      tableRef.current.reload();
      message.success(I18N.Src__Pages__Common__Template.Index.deletionSucceeded);
    }
  };

  /** 获取查询条件, 这个是时时结果, 用于导出条件 */
  function getSearchData() {
    const searchData = formRef.current?.getFieldsFormatValue?.();

    return {
      ...searchData,
      searchType: undefined,
    };
  }

  /** 操作按钮 */
  const actionButtons = (
    <Space>
      <Button
        type="primary"
        onClick={() => {
          trackerSend({
            name: 'WWL_PORTAL_OVERSEA_GOODS_CREATE_BUTTON_CLICK',
          });

          historyGoChild({
            newTab: true,
            pathname: './action',
          });
        }}
        key="add"
      >
        {I18N.Src__Pages__OverseaLocation__Goods__Group__List.Index.newlyAdded}
      </Button>
      <div key="import">
        <GoodsImport key="GoodsImport" submitSuccessCB={() => tableRef?.current?.reload()} />
      </div>
      <GoodsEditImport key="GoodsEditImport" submitSuccessCB={() => tableRef?.current?.reload()} />
      <Button
        key="batchPrint"
        {...{
          onClick() {
            if (selectedRowKeys.length === 0) {
              message.error(I18N.Src__Pages__OverseaLocation__Goods__List.Index.chineseSymbols2);
              return;
            }
            if (selectedRowKeys.length > 50) {
              message.error(I18N.Src__Pages__OverseaLocation__Goods__List.Index.chineseSymbols1);
              return;
            }
            goodsModalPrintLabelRef.current?.open({ goodsList: selectedRows });
          },
        }}
      >
        {I18N.Src__Pages__OverseaLocation__Goods__List.Index.chineseSymbols}
      </Button>
      <AsyncExportButton
        key={'batchExport'}
        {...{
          exportDataSource: EnumExportDataSource.GOODS_EXPORT,
          type: 'default',
          children: '批量导出',
          async request({ form }) {
            const { fileName } = form.getFieldsValue();
            const skuList = selectedRows.map((item) => item.sku);

            await apiGoodsBatchExport({
              ...(skuList.length > 0 ? { skuList } : getSearchData()),
              fileName,
            });
            setSelectedRows([]);
            setSelectedRowKeys([]);
          },
        }}
      />
    </Space>
  );

  const hiddenGoodsDeleteModal = () => {
    setCurrentId('');
    setShowDeleteGoodsModel(false);
    setRelatedGroupGoodsList([]);
    setExistedOrder(false);
  };

  return (
    <PageContainer>
      <GoodsModalPrintLabel
        {...{
          ref: goodsModalPrintLabelRef,
          submitSuccessCB: () => {
            setSelectedRows([]);
            setSelectedRowKeys([]);
          },
        }}
      />
      <ProTable
        className={css['goods-list']}
        tableLayout="auto"
        request={(params) => asyncGoodsSearchList(params)}
        columns={columns}
        rowKey="id"
        actionRef={tableRef}
        formRef={formRef}
        form={{
          isKeyPressSubmit: false,
          ignoreRules: false,
        }}
        scroll={{ x: '100%' }}
        headerTitle={actionButtons}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys as string[]);
            setSelectedRows(rows);
          },
        }}
        search={{ defaultCollapsed: false }}
        pagination={{
          showSizeChanger: true,
          current: pageIndex,
          defaultPageSize: 10,
          position: ['bottomLeft'],
        }}
        onChange={(pagination) => {
          setPageIndex(pagination.current);
        }}
      />
      {showDeleteGoodsModel && (
        <DeleteGoodsModal
          visible={showDeleteGoodsModel}
          id={currentId}
          goodsName={currentName}
          combinedGoodsList={relatedGroupGoodsList}
          exist={existedOrder}
          onCancel={hiddenGoodsDeleteModal}
          onOk={() => {
            doDelete(currentId);
          }}
        />
      )}
    </PageContainer>
  );
}
