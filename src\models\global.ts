import type { Reducer, Effect } from 'umi';
import { request, getLocale } from 'umi';
import {
  queryGlobalCountryList,
  queryGlobalCountryCityList,
  queryGlobalPortList,
  queryGlobalShippingList,
  sendGlobalUploadFile,
  sendRegisterAntAccount,
  queryCommercePaltformRichSelectData,
  queryOverseaContainerBox,
} from '@/service/global';
import { apiUploadFile } from '@/pages/overseaLocation/api';

export interface StateType {
  showIm: boolean;
  ncHistoryOrder: boolean;
  overseaContainerBox: any[];
}

export interface GlobalModelType {
  namespace: string;
  state: StateType;
  effects: {
    getGlobalCountryList: Effect;
    getGlobalCountryCityList: Effect;
    getGlobalPortList: Effect;
    getGlobalShippingList: Effect;
    registerAntAccount: Effect;
    doGlobalUploadFile: Effect;
    getCommercePaltformRichSelectData: Effect;
    getOverseaContainerBox: Effect;
  };
  reducers: {
    setImShowStatus: Reducer<any>;
    setNcHistoryOrder: Reducer<any>;
    saveOverseaContainerBoxType: Reducer<any>;
  };
}

const Model: GlobalModelType = {
  namespace: 'global',

  state: {
    showIm: false,
    ncHistoryOrder: false,
    overseaContainerBox: [],
  },

  effects: {
    *getGlobalCountryList({ payload }, { call }) {
      const response = yield call(queryGlobalCountryList, payload);

      if (response && response.result) {
        return response.result.records;
      }

      return [];
    },
    /** @deprecated 出库单创建时使用, 已废弃 */
    *getGlobalCountryCityList({ payload }, { call }) {
      const response = yield call(queryGlobalCountryCityList, payload);

      if (response && response.result && response.result.length) {
        return response.result;
      }

      return [];
    },

    *getGlobalPortList({ payload }, { call }) {
      const response = yield call(queryGlobalPortList, payload);

      if (response && response.result) {
        return response.result;
      }

      return [];
    },
    *getGlobalShippingList({ payload }, { call }) {
      const response = yield call(queryGlobalShippingList, payload);

      if (response && response.result) {
        return response.result;
      }

      return [];
    },
    *registerAntAccount({ payload }, { call }) {
      const response = yield call(sendRegisterAntAccount);

      if (response && response.result) {
        return response.result;
      }

      return [];
    },
    *doGlobalUploadFile({ payload }, { call }) {
      // const response = yield call(sendGlobalUploadFile, payload);
      const response = yield call(apiUploadFile, payload);

      // if (response && response.result) {
      if (response && response.data) {
        return response;
      }
      return false;
    },
    /** @deprecated 已废弃, 改用字典orderSourcePlatform */
    *getCommercePaltformRichSelectData({ payload }, { call }) {
      const { result, success } = yield call(queryCommercePaltformRichSelectData, payload);

      if (success) {
        return result;
      }

      return [];
    },

    *getOverseaContainerBox({ payload }, { call, put }) {
      const response = yield call(queryOverseaContainerBox, payload);

      if (response && response.success) {
        yield put({
          type: 'saveOverseaContainerBoxType',
          payload: response.result,
        });
      }
    },
  },

  reducers: {
    setImShowStatus(state, { payload }) {
      return {
        ...state,
        showIm: payload,
      };
    },
    setNcHistoryOrder(state, { payload }) {
      return {
        ...state,
        ncHistoryOrder: payload.ncHistoryOrder,
      };
    },
    saveOverseaContainerBoxType(state, { payload }) {
      return {
        ...state,
        overseaContainerBox: payload,
      };
    },
  },
};

export default Model;
