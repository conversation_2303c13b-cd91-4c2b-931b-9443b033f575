import type { FC } from 'react';
import React, { useState, useEffect, useRef, useReducer } from 'react';
import type { Dispatch } from '@umijs/max';
import { connect, history, useModel } from '@umijs/max';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import {
  Card,
  Spin,
  Form,
  Row,
  Col,
  Input,
  Radio,
  Select,
  DatePicker,
  Space,
  Button,
  message,
  Descriptions,
} from 'antd';
import type { FormInstance } from 'antd';
import moment from 'moment';
import { differenceBy } from 'lodash';
import { ProFormDependency } from '@ant-design/pro-form';
import I18N from '@/utils/I18N';

import { FormGoodsBoxInfo, LogisticsInfo } from './components';
import GoodsTableForm from './goodsTableForm';
import type { StateType } from '../model';
import styles from './index.less';
import { dateUtils } from '@/utils';
import { LoadButton, ZSearchSelect } from '@/components';
import { CustomizeDivider, TextValue } from '@/views';
import {
  apiInboundDraftCreate,
  apiInboundDraftDetail,
  apiInboundDraftEdit,
  apiInboundOrderSubmit,
  apiInboundPodNoCreate,
  apiListShippingCompany,
} from '../enterWarehouseApi';
import { omitUndefined } from '@/utils/utils-three';
import { historyGetQuery, historyGoPrev } from '@/pages/overseaLocation/utils';
import { asyncGoodsInfoRefresh } from '@/pages/overseaLocation/goods/utils';
import Context, { initState, contextReducer, ContextType } from './context';
import { GetPageQuery } from '@/utils/util';
import { WAREHOUSE_BUSINESS_TYPE } from '@/utils/const';
import { apiQueryWarehouseOptions } from '@/api';
import { apiReturnOrderDetail } from '../../ReturnManage/ReturnList/ReturnApi';
import { apiGoodsListForGoodsChoose } from '../../goods/goodsApi';
import { calcItemDateProps } from '@/utils-business';

const { Item } = Form;

/** 数据处理 手动处理ETD、ETA、接收时间、船公司信息 */
type TypeOutput = 'ALL' | 'TIMEDATA' | 'LOGISTICS';

interface IProps extends StateType {
  dispatch: Dispatch;
  pageLoading: boolean;
}

/** 入库单编辑页面 */
const InboundEdit: FC<IProps> = ({ dispatch, pageLoading }) => {
  const { warehouseList, asyncGetWarehouseList } = useEnterWarehouseOptions();
  const { id } = GetPageQuery();

  /** 不包含 /action-create 表示编辑模式 */
  const isUpdate = history.location.pathname.indexOf('/action-create') === -1;
  /** 是否为草稿单 */
  const isDraft = (data: any) => Number(data?.orderStatus) === 100;
  /** 是否为复制模式, id存在且不是编辑模式 */
  const isCopy = (/* data: any */) => !!id && isUpdate === false;
  const { initialState, setInitialState } = useModel('@@initialState');
  const { webUser = {} } = initialState as any;
  const [contextState, contextDispatch] = useReducer<any>(contextReducer, initState) as any;

  const [form] = Form.useForm();
  //   const [podNo, setPodNo] = useState('');
  const goodsTableRef = useRef<any>();
  const orderStatus = Form.useWatch('orderStatus', form);
  // const [orderStatus, setOrderStatus] = useState(0);
  const [orderChildId, setOrderChildId] = useState('');

  useReturnInbound({ form, warehouseList });

  useEffect(() => {
    asyncInit();
    // convertData(goodData);
    // setInitialState({
    //   ...initialState,
    //     collapsed: true,
    // });
  }, []);
  async function asyncInit() {
    if (id) {
      //   const detail = await dispatch({
      //     type: 'entry_warehouse/getEnterBoundOrderDetail',
      //     payload: { id },
      //   });

      //   if (detail) {
      //     await fullDetailInfo(detail);
      //   }
      const { data } = await apiInboundDraftDetail({ id });

      if (data) {
        await fullDetailInfo(data);
      }
    }
    /** 只要不是编辑模式都需要请求新的podNo */
    if (!isUpdate) {
      const { data } = await apiInboundPodNoCreate();

      form.setFieldsValue({ podOrderNo: data });
    }
    asyncGetWarehouseList();
  }

  async function fullDetailInfo(data: TypeInboundDraftDetail) {
    const { orderLogistics } = data;
    const order = data.order || {};
    const orderGoods = data.orderGoods || [];
    let newOrderGoods = isUpdate
      ? orderGoods
      : orderGoods.map((item) =>
          omitUndefined({ ...item, id: undefined, receivedQuantity: undefined }),
        );

    /** 复制单 或者 草稿单需要更新商品信息 */
    if (isCopy() || isDraft(order)) {
      newOrderGoods = await asyncGoodsInfoRefresh(newOrderGoods);
    }

    const params = {
      orderId: isUpdate ? order.id : undefined,
      orderChildId: isUpdate ? order.orderChildId : undefined,
      podOrderNo: isUpdate ? order.podOrderNo : undefined,
      orderStatus: order.orderStatus,
      remark: order.remark,
      businessType: order.businessType,
      warehouseId: order.targetWarehouseId,
      expectTime: order.expectTime,
      expectSendTime: order.expectSendTime,
      //   expectTime: order.expectTime ? moment(order.expectTime) : undefined,
      //   expectSendTime: order.expectSendTime ? moment(order.expectSendTime) : undefined,
      shippingCompany: order.shippingCompany,
      shippingCompanyCode: order.shippingCompanyCode,
      /** 船公司数据处理 */
      company:
        order.shippingCompanyCode && order.shippingCompany
          ? { value: order.shippingCompanyCode, label: order.shippingCompany }
          : undefined,
      /** 物流信息 tempTotalNum作为总箱数，需要在详情初始化时手动获取数据，留着后续使用,总托盘 */
      logisticsData: {
        ...(orderLogistics?.[0] || {}),
        tempTotalNum: totalBoxs(newOrderGoods),
        // 后端返回string，前端使用的是number
        totalTrayNum: Number(orderLogistics?.[0]?.totalTrayNum || 0),
      },
      goodsList: newOrderGoods,
      boxList: data.boxList || [],
    };

    form.setFieldsValue(params);

    if (goodsTableRef.current) {
      goodsTableRef.current.init(newOrderGoods);
    }

    if (isUpdate) {
      //   setPodNo(order.podOrderNo);
      //   setOrderStatus(order.orderStatus);
      //   setOrderChildId(order.orderChildId);
    }

    // 存储初始化存在的商品和物流信息
    contextDispatch({
      type: ContextType.SET_EDIT_INIT_DATA,
      payload: {
        goodsList: newOrderGoods || [],
      },
    });
    // 存储初始化存在的物流信息
  }

  const getWarehouseRecord = (warehouseId: string) => {
    if (!warehouseId) return {};
    const current = warehouseList.find((o) => o.id === warehouseId);

    return {
      warehouseId: current.id,
      warehouseCode: current.code,
      warehouseName: current.name,
      warehouseType: current.warehouseType,
    };
  };

  const removeRecordId = (records: any[], newKey: string) => {
    return records.map((o) => ({
      ...o,
      id: undefined,
      [newKey]: o.id ? o.id : undefined,
    }));
  };

  /** 创建草稿和提交入库都用 */
  const rewriteData = (data: TypeInboundFormData) => {
    // console.log('orderChildId-id', data, orderChildId, id);

    let result: any = {
      ...data,
      //   podOrderNo: data.podOrderNo,
      //   businessType: data.businessType,
      //   warehouseId: data.warehouseId,
      ...outputData('ALL', data, isCopy()),
    };

    // if (orderStatus === 100 && isUpdate) {
    //   result.orderId = id;
    // }

    // if (orderChildId) {
    //   result.orderChildId = orderChildId;
    // }

    // if (data.remark) {
    //   result.remark = data.remark;
    // }

    if (data.warehouseId) {
      const currentWarehouse = getWarehouseRecord(data.warehouseId);

      result = Object.assign(result, currentWarehouse);
    }

    if (data.goodsList && data.goodsList.length) {
      result.goodsDTOS = removeRecordId(data.goodsList, 'orderGoodsId');
    }

    return result;
  };

  const rewriteEditData = (data: TypeInboundFormData) => {
    const cacheGoods = contextState.cacheGoodsList;
    const goodsList = data?.goodsList || [];

    const hasDeleteGoods = differenceBy(cacheGoods, goodsList, 'id').map((o: any) => ({
      ...o,
      deleted: true,
    }));

    const result = {
      orderDTO: {
        orderId: id,
        podOrderNo: data.podOrderNo,
      },
      orderChildDTO: {
        orderChildId: data.orderChildId,
        businessType: data.businessType,
        remark: data.remark,
        ...outputData('TIMEDATA', data, isCopy()),
        ...getWarehouseRecord(data.warehouseId!),
      },
      ...outputData('LOGISTICS', data, isCopy()),
      goodsDTOS: removeRecordId(goodsList.concat(hasDeleteGoods), 'orderGoodsId'),
      boxList: data.boxList,
    };

    return result;
  };

  async function asyncHandleSave() {
    const values = form.getFieldsValue();

    if (orderStatus === 100) {
      await apiInboundDraftEdit(rewriteEditData(values));

      message.success(I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.editReceipt);
      handleGoBack();
    } else {
      await apiInboundDraftCreate(rewriteData(values));

      message.success(
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.temporaryStockIn,
      );
      handleGoBack();
    }
  }

  // 提交入库申请
  const onFinish = async (values: any) => {
    const { goodsList } = values;

    if (!goodsList || !goodsList.length) {
      message.info(
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.pleaseFillInTheSupplier,
      );
      return;
    }

    const params = rewriteData(values);

    await apiInboundOrderSubmit(params);

    message.success(I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.addReceipt);
    handleGoBack();
  };

  // 返回
  const handleGoBack = () => {
    historyGoPrev();
  };

  /** 总箱数量 */

  function totalBoxs(arr: any[]) {
    let num = 0;

    arr.forEach((item) => {
      num += item.boxQuantity || 0;
    });
    return num;
  }

  return (
    <PageContainer title={false}>
      <Spin spinning={pageLoading === true}>
        <Context.Provider value={{ contextState, contextDispatch }}>
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              initialValues={{
                list: [],
                logisticsData: { logisticsProvider: '1' },
              }}
              onValuesChange={(changedValues: any, values: any) => {
                if (changedValues.hasOwnProperty('goodsList')) {
                  // 设置总箱数
                  const totalNum = Array.isArray(changedValues.goodsList)
                    ? totalBoxs(changedValues.goodsList)
                    : undefined;

                  form.setFieldsValue({
                    logisticsData: {
                      tempTotalNum: totalNum,
                    },
                  });
                }
                if (changedValues.hasOwnProperty('expectSendTime')) {
                  // 手动情况ETA
                  form.setFieldsValue({
                    expectTime: undefined,
                  });
                }
                if (changedValues.hasOwnProperty('businessType')) {
                  /** 手动清空选择的仓库信息 */
                  form.setFieldsValue({ warehouseId: undefined });
                  // 监听订单类型变化,重新更新仓库列表信息，达到依据不同入库类型筛选不同的仓库
                  asyncGetWarehouseList();
                }
              }}
            >
              <Descriptions
                title={
                  <Space>
                    <span>
                      {
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                          .applicationNo
                      }
                    </span>
                    <span style={{ fontSize: 14, color: '#333' }}>
                      <TextValue {...{ name: 'podOrderNo', label: '申请单号', noStyle: true }} />
                    </span>
                    <Form.Item hidden>
                      <TextValue {...{ name: 'orderId', label: '主单id' }} />
                      <TextValue {...{ name: 'orderChildId', label: '履约单id' }} />
                      <TextValue {...{ name: 'orderStatus', label: '履约单状态' }} />
                    </Form.Item>
                  </Space>
                }
              >
                <Descriptions.Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.corporateName
                  }
                >
                  {webUser.companyName}
                </Descriptions.Item>
                <Descriptions.Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.applicant}
                >
                  {webUser.userName}
                </Descriptions.Item>
              </Descriptions>
              <CustomizeDivider
                title={I18N.Src__Pages__Order__Detail.SoPane.essentialInformation}
              />
              <Row gutter={24}>
                <Col span={24}>
                  <Item
                    label={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                        .overseasWarehousing
                    }
                    name="businessType"
                    rules={[
                      {
                        required: true,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                            .pleaseSelectSea,
                      },
                    ]}
                  >
                    <Radio.Group disabled={orderStatus !== 100 && isUpdate}>
                      {WAREHOUSE_BUSINESS_TYPE.map((item) => (
                        <Radio value={item.value} key={item.value}>
                          {item.label}
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Item>
                </Col>
                <Col span={8}>
                  <Item
                    label={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                        .targetWarehousing
                    }
                    name="warehouseId"
                    rules={[
                      {
                        required: true,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                            .pleaseSelectTheItem,
                      },
                    ]}
                  >
                    <ZSearchSelect
                      {...{
                        disabled: orderStatus !== 100 && isUpdate,
                        options: warehouseList.map((item) => ({
                          ...item,
                          value: item.id,
                          label: item.code,
                        })),
                      }}
                    />
                    {/* <Select
                      placeholder="请选择"
                      showSearch
                      disabled={orderStatus !== 100 && isUpdate}
                      // optionFilterProp="label"
                      // optionLabelProp="label"
                      filterOption={(input, option: any) =>
                        option.children.toLowerCase().indexOf(input?.trim().toLowerCase()) >= 0
                      }
                    >
                      {warehouseList.map((item: any) => (
                        <Option key={item.id} value={item.id}>
                          {item.code}
                        </Option>
                      ))}
                    </Select> */}
                  </Item>
                </Col>
                {/* 预计发货时间 */}
                <Col span={8}>
                  <Item
                    label="ETD"
                    name="expectSendTime"
                    rules={[{ required: true }]}
                    tooltip={
                      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                        .expectedDelivery
                    }
                    {...{
                      ...calcItemDateProps({
                        datePickerProps: {
                          placeholder: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                          style: { width: '100%' },
                        },
                        format: 'END',
                      }),
                    }}
                  />
                </Col>
                {/* 预计到达时间  */}
                <Col span={8}>
                  <ProFormDependency name={['expectSendTime']}>
                    {() => {
                      return (
                        <Item
                          label="ETA"
                          name="expectTime"
                          tooltip={
                            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                              .expectedArrival
                          }
                          rules={[
                            { required: true },
                            ({ getFieldValue }) => ({
                              validator(rule, value) {
                                /** 开始时间结束时间存在一个没有数据，校验通过 */
                                if (!value || !getFieldValue('expectSendTime')) {
                                  return Promise.resolve();
                                }
                                /** 两者都输入需要校验，并且开始时间小于结束时间,比较日期 */
                                if (
                                  value &&
                                  getFieldValue('expectSendTime') &&
                                  dateUtils.dateFormatter(getFieldValue('expectSendTime')) <
                                    dateUtils.dateFormatter(value)
                                ) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(
                                  new Error(
                                    // I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.pleaseSelectPositive,
                                    'ETA时间必须大于ETD时间',
                                  ),
                                );
                              },
                            }),
                          ]}
                          {...{
                            ...calcItemDateProps({
                              datePickerProps: {
                                placeholder: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                                style: { width: '100%' },
                              },
                              format: 'END',
                            }),
                          }}
                        />
                      );
                    }}
                  </ProFormDependency>
                </Col>

                <Col span={8}>
                  <ProFormDependency name={['businessType']}>
                    {(depValues) => {
                      const { businessType } = depValues;

                      return renderCompany(
                        Number(businessType),
                        !!(orderStatus !== 100 && isUpdate),
                      );
                    }}
                  </ProFormDependency>
                </Col>
                <Col span={8}>
                  <Item
                    label={I18N.Src__Pages__Order__Detail.SiItem.remarks}
                    name="remark"
                    rules={[
                      {
                        max: 100,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                            .characterLength1,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                          .pleaseEnterStandby
                      }
                    />
                  </Item>
                </Col>
              </Row>
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                    .logisticsInformation
                }
                /** 没查明白这个属性有啥用, 看着也没生效 */
                // extra={
                //   <span className={styles.tips}>
                //     {
                //       I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                //         .firstJourneyLogistics
                //     }
                //   </span>
                // }
              />
              <LogisticsInfo />
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.productInformation
                }
              />
              <FormGoodsBoxInfo />
              {/* <Item name="goodsList">
                <GoodsTableForm
                  ref={goodsTableRef}
                  form={form}
                  dispatch={dispatch}
                  formItemName="goodsList"
                />
              </Item> */}
            </Form>
          </Card>

          <FooterToolbar
            style={{ textAlign: 'center' }}
            extra={
              <Space>
                <Button onClick={handleGoBack}>
                  {I18N.Src__Pages__Order__Detail.Index.return}
                </Button>
                {
                  // 新增和待提交的订单可以进行暂存（新增的暂存走草稿接口、暂存订单走编辑接口）
                  !isUpdate || orderStatus === 100 ? (
                    <LoadButton type="default" onClick={asyncHandleSave}>
                      {I18N.Src__Pages__Order__LclBooking.Index.staging}
                    </LoadButton>
                  ) : null
                }
                <LoadButton type="primary" onClick={async () => form.submit()}>
                  {
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index
                      .submitForWarehousing
                  }
                </LoadButton>
              </Space>
            }
          />
        </Context.Provider>
      </Spin>
    </PageContainer>
  );
};

/**
 * @description 不同类型展示不同的内容，能否操作;环世头程入库
 * @param businessType 入库业务类型  101: '环世头程入库',  102: '代发头程入库',  103: '退货入库',
 * @param disabled 是否操作
 * @returns
 */
function renderCompany(inBusinessType: number | string, disabled: boolean) {
  const businessType = Number(inBusinessType);
  const maxSizeInfo = {
    max: 50,
    message: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.characterLength,
  };

  switch (businessType) {
    case 101:
      return (
        <Item
          label={I18N.Src__Pages__Common__Template.Index.shippingCompany}
          name="company"
          rules={[{ required: !disabled }]}
        >
          <ZSearchSelect
            {...{
              labelInValue: true,
              disabled,
              request: (searchValue) => apiListShippingCompany({ searchValue }),
            }}
          />
        </Item>
      );
    case 102:
    case 105:
      return (
        <Item
          label={I18N.Src__Pages__Common__Template.Index.shippingCompany}
          name="shippingCompany"
          rules={[{ required: true }, maxSizeInfo]}
        >
          <Input disabled={disabled} />
        </Item>
      );

    default:
      return (
        <Item
          label={I18N.Src__Pages__Common__Template.Index.shippingCompany}
          name="shippingCompany"
          rules={[maxSizeInfo]}
        >
          <Input disabled={disabled} />
        </Item>
      );
  }
}

/**
 * @description 手动处理ETD、ETA、接收时间、物流信息、 船公司信息
 * @param data
 */
function outputData(dataType: TypeOutput, data: any, isCopy: boolean) {
  /** 时间相关 */
  const timeData = {
    // expectTime: data.expectTime ? dateUtils.transformDate(data.expectTime, 'END') : undefined,
    // expectSendTime: data.expectSendTime
    //   ? dateUtils.transformDate(data.expectSendTime, 'END')
    //   : undefined,
    expectTime: data.expectTime,
    expectSendTime: data.expectSendTime,
    shippingCompany: data.company?.label ? data.company?.label : data.shippingCompany,
    shippingCompanyCode: data.company?.value,
  };
  /** 物流相关信息 */
  const logisticsData = {
    logisticsDTOS: [
      {
        ...data.logisticsData,
        id: isCopy ? undefined : data.logisticsData?.id,
        logisticsId: isCopy ? undefined : data.logisticsData?.id,
      },
    ],
  };

  /** 时间和物流信息 */
  const timeAndLogistics = {
    TIMEDATA: timeData,
    LOGISTICS: logisticsData,
    ALL: {
      ...timeData,
      ...logisticsData,
    },
  };

  return timeAndLogistics[dataType];
}

export default connect(({ global, oversea_location, loading }: any) => ({
  pageLoading: loading.models.entry_warehouse || loading.models.oversea_location,
  //   warehouseList: oversea_location.warehouseList,
  //   overseaContainerBox: global.overseaContainerBox,
}))(InboundEdit);

/** 获取仓库Options */
function useEnterWarehouseOptions() {
  const [warehouseList, setWarehouseList] = useState<any[]>([]);

  function asyncGetWarehouseList() {
    return apiQueryWarehouseOptions({ warehouseEnable: true }, { valueName: 'id' }).then(
      (records) => {
        setWarehouseList(records);
        return records;
      },
    );
  }
  return { warehouseList, asyncGetWarehouseList };
}

/** 退件入库跳转到入库单创建界面 */
function useReturnInbound({ form, warehouseList }: { form: FormInstance; warehouseList: any[] }) {
  const { podOrderNoInReturnInfo } = historyGetQuery();

  useEffect(() => {
    if (podOrderNoInReturnInfo && warehouseList.length) {
      initReturnData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [warehouseList]);

  const initReturnData = async () => {
    const { data } = await apiReturnOrderDetail({ podOrderNo: podOrderNoInReturnInfo });
    let goodsList: TypeGoodsTableForm[] = [];

    if (data?.orderGoodsList?.length) {
      const skuList = data.orderGoodsList.map((item) => item.sku!) || [];
      const len = skuList.length;
      const { data: info } = await apiGoodsListForGoodsChoose({
        currentPage: 1,
        pageSize: len,
        condition: {
          skuList: len > 1 ? skuList : undefined,
          sku: len === 1 ? skuList[0] : undefined,
        },
      });

      const quantityPerBox = 1;
      const generateNextSkuList = (list: TypeGoodsTableForm[]) => {
        return list.map((o, i) => {
          const totalQuantity = data.orderGoodsList!.find(
            (good) => good.sku === o.sku,
          )?.totalQuantity;

          return {
            ...o,
            totalQuantity,
            boxQuantity: totalQuantity! / quantityPerBox,
            /** 混箱id, id一致表示在同箱里, 会生成一致的箱唛号, 由前端生成, 后端会做校验必填,
             * 很重要, 有历史包袱 */
            boxUniqueId: `${o.sku}-${new Date().getTime()}`,
            quantityPerBox,
            boxType: 1 /** 默认都是整箱 */,
          };
        });
      };

      goodsList = generateNextSkuList(info.records);
    }

    const currentReturnWarehouse = warehouseList.find(
      (o) => o.code === data?.orderChild?.targetWarehouseCode,
    );

    form.setFieldsValue({
      businessType: 104,
      warehouseId: currentReturnWarehouse?.id,
      logisticsData: { logisticsProvider: '5', logisticsNo: data?.returnTrackingNo },
      goodsList: goodsList,
    });
  };
}
