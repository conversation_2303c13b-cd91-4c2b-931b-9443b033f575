import I18N from '@/utils/I18N';
import path from 'path';
import React, { useRef, useState } from 'react';

import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, message, Upload } from 'antd';
import classNames from 'classnames';
import type { UploadFile } from 'antd';
import style from './GoodsImageUpload.less';
import { useAliyunPath } from '@/hooks/useAliyunPath';
import { SPLIC_URL } from '@/request.config';
import { getImagePath } from '@/utils-business';

export enum EnumUploadType {
  goodsPicture = 0,
  goodsBoxPicture = 1,
}

function getUploadConfig({
  uploadType,
  fileListSize,
}: {
  uploadType: EnumUploadType;
  fileListSize: number;
}) {
  let config = null;

  switch (uploadType) {
    case EnumUploadType.goodsPicture:
      config = {
        listType: 'picture-card' as const,
        className: 'avatar-uploader',
        data: { directory: 'goods' },
        showUploadList: {
          showPreviewIcon: false,
        },
        UploadButton: (
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>
              {I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsImageUpload.uploadPhotos}
            </div>
          </div>
        ),
      };
      break;
    case EnumUploadType.goodsBoxPicture:
      config = {
        data: { directory: 'goods' },
        UploadButton: (
          <Button icon={<UploadOutlined />}>
            {fileListSize > 0
              ? I18N.Src__Pages__Order__Import.Index.reUpload
              : I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBoxTableForm
                  .pictureOfOuterBox}
          </Button>
        ),
      };
      break;
    default:
      throw Error(I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsImageUpload.unknownOn);
  }
  return config;
}

/** 截取名称, 服务端存值会带hash字符串 */
function generateFileList(file: any, aliyunPath: string) {
  // 从第一个#号开始计算真实名称
  // const arr = fileUrl?.split('/') || [];
  // // const realName = fileUrl?.substring(fileUrl?.indexOf('/') + 1);
  // const realName = arr[arr?.length - 1];

  return [
    {
      name: file.name,
      status: 'done',
      url: getImagePath(file.url),
    },
  ];
}

/** 通过file的response来判断上传是否成功 */
function processErrorFile(file: UploadFile) {
  const f = file;

  if (typeof file.response?.code === 'string' && file.response.code !== '000000') {
    f.status = 'error';
  }
  return file;
}

function GoodsImageUpload({
  value,
  onChange,
  uploadType = EnumUploadType.goodsPicture,
}: {
  value?: any;
  onChange?: (file: any) => void;
  uploadType?: EnumUploadType;
}) {
  const once = useRef(true);
  const [fileList, setFileList] = useState<any[]>([]);
  const currentFilename = useRef<any>();
  const config = getUploadConfig({ uploadType, fileListSize: fileList.length });
  const aliyunPath = useAliyunPath();

  if (once.current && value?.url) {
    setFileList(generateFileList(value, aliyunPath));
    once.current = false;
  }
  const accept = ['.jpg', '.jpeg', '.png'];
  const errMsg = I18N.template(
    I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsImageUpload.onlyLatticesAreSupported,
    { val1: accept.join(', ') },
  );

  return (
    <div
      className={classNames([
        uploadType === EnumUploadType.goodsPicture && style['goods-image-upload'],
      ])}
    >
      <Upload
        name="file"
        action={SPLIC_URL('/api/website/upload/uploadOne')}
        {...config}
        maxCount={1}
        accept={accept.join(',')}
        fileList={fileList}
        beforeUpload={(file) => {
          const filename = file.name;
          const fileType = filename.substring(filename.lastIndexOf('.'));

          if (accept.includes(fileType) === false) {
            message.error(errMsg);
            return Upload.LIST_IGNORE;
          }

          const overFileSizeLimit = file.size >= 1024 * 1024 * 20;

          if (overFileSizeLimit) {
            message.error(
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsImageUpload.uploadFiles,
            );
            return Upload.LIST_IGNORE;
          }

          return true;
        }}
        onChange={({ file, fileList: newFileList }: any) => {
          // eslint-disable-next-line no-param-reassign
          processErrorFile(file);
          setFileList(newFileList);
          if (file.status === 'removed') {
            onChange?.(null);
          }
          if (file.status === 'uploading') {
            return;
          }
          if (file.status === 'done') {
            const { result } = file?.response;
            const saveFile = { name: result?.nameUrl, url: result?.fileName };

            console.log(1001, saveFile);

            onChange?.(saveFile);
            setFileList(generateFileList(saveFile, aliyunPath));
            currentFilename.current = saveFile;
          }
        }}
      >
        {(fileList.length === 0 || uploadType === EnumUploadType.goodsBoxPicture) &&
          config.UploadButton}
      </Upload>
      {uploadType === EnumUploadType.goodsPicture && <span>{errMsg}</span>}
    </div>
  );
}

export default GoodsImageUpload;
