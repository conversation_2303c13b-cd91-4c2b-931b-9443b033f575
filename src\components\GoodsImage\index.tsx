import './index.less';
import type { ImageProps } from 'antd';
import { Image } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import React from 'react';
import { useAliyunPath } from '@/hooks/useAliyunPath';
import { getImagePath } from '@/utils-business';

const defaultSkuImg = require('@/images/default-goods.png');

/** 门户的历史图片展示组件, 新版统一使用@/views/ListImageItem */
function GoodsImage(props: ImageProps) {
  const { src, className, style, ...args } = props;

  const preview = usePreView();
  //   const aliyunPath = useAliyunPath();

  return (
    <Image
      {...args}
      //   src={src ? aliyunPath + src : defaultSkuImg}
      src={getImagePath(src)}
      className={`list-image-item-img ${className}`}
      preview={src ? preview : false}
    />
  );
}

export function usePreView() {
  return {
    mask: (
      <div>
        <EyeOutlined />
      </div>
    ),
  };
}

export default GoodsImage;
