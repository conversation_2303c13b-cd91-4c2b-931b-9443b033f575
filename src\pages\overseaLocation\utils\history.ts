import type { History } from 'umi';
import { history } from 'umi';
import path from 'path';
import type { LocationDescriptorObject } from 'history-with-query';
import { createLocation } from 'history-with-query';
import type { ModuleTo } from '@/common-import';
import { HiveModule } from '@/common-import';

/** 处理成新的Location
 * @ path.resolve 如果参数是相对路径, 则拼接, 绝对路径则保留
 * @ 可用于Link组件to属性
 */
export function historyCreateLocation(...args: [string | LocationDescriptorObject, unknown?]) {
  let pathname: string;
  let state;
  let location;
  const curPathname = history.location.pathname;

  if (typeof args[0] === 'string') {
    [pathname, state] = args;
  } else {
    location = args[0];
    pathname = location.pathname!;
    state = location.state;
  }
  const newLocation = createLocation({
    ...location,
    state,
    pathname: path.resolve(curPathname, pathname),
  }) as LocationDescriptorObject;

  return newLocation;
}

/** 蜂巢的location创建 */
export function historyCreateLocationForHive(...args: Parameters<typeof historyCreateLocation>) {
  return locationToHiveLocation(historyCreateLocation(...args));
}

/** 跳转当前路径的子路径 */
type TypeLocation = {
  /* 在新的窗口打开 */ newTab?: boolean;
} & LocationDescriptorObject;
export const historyGoChild: History['push'] & ((location: TypeLocation) => void) = (...args) => {
  const newOpen = new URLSearchParams(window.location.search).get('newOpen');
  const fnName = (args[0] as TypeLocation)?.newTab && newOpen !== '0' ? 'open' : 'push';

  HiveModule.history[fnName](historyCreateLocationForHive(...args));
};

/** 支持绝对路径相对路径页面跳转 */
export function historyGoPush(...args: Parameters<History['push']>): void;
export function historyGoPush(...args: [TypeLocation]): void;
export function historyGoPush(...args: Parameters<typeof historyCreateLocation>) {
  const newOpen = new URLSearchParams(window.location.search).get('newOpen');
  const fnName = (args[0] as TypeLocation)?.newTab && newOpen !== '0' ? 'open' : 'push';

  HiveModule.history[fnName](historyCreateLocationForHive(...args));
}

/** 返回上级路径, 最好不要用history.goBack(), 1. 浏览器数据清空后会不跳转 2. 新开窗口因为没有历史记录 history.goBack 无效 */
export function historyGoPrev(location?: LocationDescriptorObject) {
  const { pathname } = history.location;
  const newPath = path.join(pathname, '../');

  HiveModule.history.push(
    locationToHiveLocation({
      ...location,
      /** substring是为了去除结尾的斜杠 */
      pathname: newPath.substring(0, newPath.length - 1),
    }),
  );
}

/** 获取完整href路径
 * @ 会自动拼接basepath前缀
 * @ umi Link组件不要用
 */
export function historyGetHref(...args: Parameters<typeof historyCreateLocation>) {
  return history.createHref(historyCreateLocation(...args));
}

/* 转换适配层, history.location 和 HiveModule.history 适配  */
function locationToHiveLocation(location: LocationDescriptorObject) {
  return {
    ...location,
    search: location?.query,
    path: location.pathname,
  } as ModuleTo;
}

/** 从umi history.location获取Query参数 */
export function historyGetQuery() {
  type TypeQuery = Record<string, string | undefined>;
  if ((history.location as any).query) {
    /** umi3 history 有query, umi4没有, 做一层兼容处理 */
    return (history.location as any).query as TypeQuery;
  }
  const query = {} as TypeQuery;
  const search = new URLSearchParams(history.location.search);

  for (const [key, value] of search) {
    query[key] = value;
  }
  return query;
}
