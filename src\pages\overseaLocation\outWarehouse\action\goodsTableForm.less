.goods-table-form {
  :global {
    .ant-table-cell {
      .ant-form-item {
        margin: 0;
        .ant-input-number {
          width: 100%;
        }
      }
    }

    .ant-pagination {
      /* 分页器下拉框边框颜色, 防止变红 */
      .ant-select-status-error.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {
        border-color: #d9d9d9 !important;
      }
    }

    .ant-pro-table-list-toolbar-container {
      padding: 0;
      padding-bottom: 5px;

      .ant-tabs-tab {
        padding-bottom: 3px;
      }
    }
  }
}
