import { history, useRouteData } from '@umijs/max';
import React, { useEffect } from 'react';
import Cookies from 'js-cookie';
import { ReportFrame } from './components';

/**
 * 数据报表内嵌页面，通过数据中心提供数据链接
 */
function DtcReportBasic() {
  const { newSrc } = useFrameSrc();

  return (
    <ReportFrame
      {...{
        iframeSrc: newSrc,
      }}
    />
  );
}

function useFrameSrc() {
  const { route }: any = useRouteData();

  // 获取路径绑定的自定义参数，该参数是访问数据中心的链接
  const dtcReportPath = route.dtcReportPath;
  // 获取当前系统的token
  const token = Cookies.get('auth_website');
  // 按照访问资源路径、auth_website、dzgAppCode 进行数据拼接,门户的dzgAppCode是portal
  const newSrc = `${dtcReportPath}?auth_website=${token}&dzgAppCode=portal`;

  return {
    newSrc,
  };
}

export default DtcReportBasic;
