import I18N from '@/utils/I18N';

export const textMap = {
  txtCalculateCosts: '计算费用',
  txtOrderId: '订单id',
  txtCompanyId: '公司id',
  txtOrderSource: '订单来源',
  /* 新旧文本分割线 */
  //   txtApplicationNo: '申请单号',
  //   txtOrderType: '订单类型',
  //   txtCompanyName: '公司名称',
  //   txtApplicant: '申请人',
  txtApplicationNo:
    I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.applicationNo,
  txtOrderType: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.orderType,
  txtCompanyName: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.corporateName,
  txtApplicant: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.applicant,
} as const;
