import I18N from '@/utils/I18N';

/** 上传组件的的文本提示 */
export const textMap = {
  limitCount: (maxCount: number) =>
    I18N.template(
      I18N.Src__Pages__OverseaLocation__Components__SimpleUpload.SimpleUpload.chineseSymbols2,
      { val1: maxCount },
    ),
  limitFileType: (fileSuffix: string[]) =>
    I18N.template(
      I18N.Src__Pages__OverseaLocation__Components__SimpleUpload.SimpleUpload.chineseSymbols1,
      { val1: fileSuffix.join(', ') },
    ),
  limitFileSize: (maxFileSizeMb: number) =>
    I18N.template(
      I18N.Src__Pages__OverseaLocation__Components__SimpleUpload.SimpleUpload.chineseSymbols,
      { val1: maxFileSizeMb },
    ),
  errorFileUploading:
    I18N.Src__Pages__OverseaLocation__Components__SimpleUpload.Text_map.chineseSymbols1,
  errorFileError:
    I18N.Src__Pages__OverseaLocation__Components__SimpleUpload.Text_map.chineseSymbols,
};
