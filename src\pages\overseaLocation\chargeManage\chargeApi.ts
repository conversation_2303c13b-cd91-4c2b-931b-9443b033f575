import { request } from '@umijs/max';
import { promisePolling } from '@/pages/overseaLocation/utils';
import { exportUtils } from '@/utils-business';

/** 费用账单列表 */
export function apiChargeBillRecordsList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 计费类型1-应收，2-应付(门户查询应填写固定值 1-应收) */
      costType?: number;
      /** 费用单号 */
      chargeRecordBillNo?: string;
      /** 业务场景：1-入库、2-出库、3-仓租 */
      businessTypeList?: number[];
      /** 仓库code */
      warehouseCode?: string;
      /** 仓库名称 */
      warehouseName?: string;
      /** 业务关联单号 */
      podOrderNo?: string;
      /** 中台费用code */
      gmChargeCode?: string;
      /** 中台费用名称 */
      gmChargeName?: string;
      /** 中台费用英文名称 */
      gmChargeEnName?: string;
      /** 状态：1-待结算 2-已结算 3-已失效 */
      status?: number;
      /** 查询开始时间 */
      startTime?: string;
      /** 查询结束时间 */
      endTime?: string;
      /** 费用结算时间 */
      settlementDateStart?: string;
      /** 费用结算时间 */
      settlementDateEnd?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypezChargeBillRecordsTB[]>>(
    '/zouwu-oms-charge/portal/charge/record/bill/page',
    {
      data,
      method: 'POST',
    },
  );
}

/** 费用账单导出 */
export function apiChargeBillRecordsExport(
  data: NsApi.TypeRequestListQuery<{
    /** 计费类型1-应收，2-应付(门户查询应填写固定值 1-应收) */
    costType?: number;
    /** 费用单号 */
    chargeRecordBillNo?: string;
    /** 业务场景：1-入库、2-出库、3-仓租 */
    businessTypeList?: number[];
    /** 业务关联单号 */
    podOrderNo?: string;
    /** 中台费用code */
    gmChargeCodeList?: string[];
    /** 中台费用名称 */
    gmChargeNameList?: string[];
    /** 中台费用英文名称 */
    gmChargeEnNameList?: string[];
    /** 状态：1-待结算 2-已结算 3-已失效  */
    statusList?: number[];
    /** 查询费用创建时间 */
    startTime?: string;
    /** 查询费用创建时间 */
    endTime?: string;
    /** 费用结算时间 */
    settlementDateStart?: string;
    /** 费用结算时间 */
    settlementDateEnd?: string;
    /** 自定义文件名 */
    fileName?: string;
  }>,
) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-system/portal/charge/record/bill/export',
    {
      data: exportUtils.transExportPageQuery(data),
      method: 'POST',
    },
  );
}

/** 轮询导出
 * @deprecated 弃用, 统一改为异步列表导出
 */
export const apiChargeBillPollingExport = promisePolling(
  async function (data: { id: string }) {
    return request<
      NsApi.TypeResponseData<{
        /** 下载地址 */
        url?: string;
        /** status 状态 1-解析中，2-解析完成，3-解析失败 */
        status: 1 | 2 | 3;
        /** 文件名 */
        fileName?: string;
        /** 来源类型 1-oms 2-门户 */
        dataSourceType?: number;
        /** 导出数据总量 */
        recordTotalNum: number;
        /** 当前执行数据数量 */
        executeNum: number;
      }>
    >('/zouwu-oms-system/portal/system/export/detail', { data, method: 'POST' });
  },
  function ({ result, next }) {
    const {
      data: { status },
    } = result;

    if (status === 2) {
      return next.resolve(result);
    }
    if (status === 3) {
      return next.reject(new Error('export fail'));
    }
    return next();
  },
  { retry: 3 },
);

/** 仓租列表 */
export function apiChargeWarehouseList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 计费类型1-应收，2-应付(门户查询应填写固定值 1-应收) */
      costType?: number;
      /** 费用单号 */
      chargeRecordBillNo?: string;
      /** 仓库Code */
      warehouseCode?: string;
      /** 仓库名称 */
      warehouseName?: string;
      /** 查询开始时间 */
      startTime?: string;
      /** 查询结束时间 */
      endTime?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypezChargeWarehouseTB[]>>(
    '/zouwu-oms-charge/portal/charge/record/bill/storage/list',
    {
      data,
      method: 'POST',
    },
  );
}

/** 导出仓租明细 */
export function apiChargeWarehouseDetailExport(data: {
  /** 计费类型1-应收，2-应付(门户查询应填写固定值 1-应收) */
  costType: number;
  /** 仓租计费开始时间(日结日期)（格式：2022-12-31） */
  startChargeDate?: string;
  /** 仓租计费结束时间(日结日期)（格式：2022-12-31） */
  endChargeDate?: string;
  /** 资金扣减时间开始 */
  startTime?: string;
  /** 资金扣减时间结束 */
  endTime?: string;
  fileName?: string;
}) {
  return request<NsApi.TypeResponseData>(
    '/zouwu-oms-system/portal/charge/record/bill/export/storage',
    {
      data,
      method: 'POST',
    },
  );
}
