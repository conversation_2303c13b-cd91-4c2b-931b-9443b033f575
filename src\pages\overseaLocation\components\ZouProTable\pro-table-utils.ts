import type { SpanConfig } from '@ant-design/pro-form/lib/layouts/QueryFilter';
import type { FormProps } from 'antd';

/**
 * protable组件源码位置 packages\form\src\layouts\QueryFilter\index.tsx
 */
const CONFIG_SPAN_BREAKPOINTS = {
  xs: 513,
  sm: 513,
  md: 785,
  lg: 992,
  xl: 1057,
  xxl: Infinity,
};
/** 配置表单列变化的容器宽度断点 */
const BREAKPOINTS = {
  vertical: [
    // [breakpoint, cols, layout]
    [513, 1, 'vertical'],
    [785, 2, 'vertical'],
    [1057, 3, 'vertical'],
    [Infinity, 4, 'vertical'],
  ],
  default: [
    [513, 1, 'vertical'],
    [701, 2, 'vertical'],
    [1062, 3, 'horizontal'],
    [1352, 3, 'horizontal'],
    [Infinity, 4, 'horizontal'],
  ],
  horizontal: undefined,
  inline: undefined,
};

/**
 * 计算QueryFilter Span大小
 *
 * @param layout
 * @param width
 */
export const getSpanConfig = (
  layout: FormProps['layout'],
  width: number,
  span?: SpanConfig,
): { span: number; layout: FormProps['layout'] } => {
  if (span && typeof span === 'number') {
    return {
      span,
      layout,
    };
  }

  const spanConfig = span
    ? (['xs', 'sm', 'md', 'lg', 'xl', 'xxl'] as const).map((key) => [
        CONFIG_SPAN_BREAKPOINTS[key],
        24 / (span as Exclude<SpanConfig, number>)[key],
        'horizontal',
      ])
    : BREAKPOINTS[layout || 'default'];

  const breakPoint = ((spanConfig || BREAKPOINTS.default) as any[]).find(
    (item: [number, number, FormProps['layout']]) => width < item[0] + 16, // 16 = 2 * (ant-row -8px margin)
  );

  return {
    span: 24 / breakPoint[1],
    layout: breakPoint[2],
  };
};
