import React, { useState, useEffect } from 'react';
import { connect, useModel, useDispatch, useParams, history } from 'umi';
import { FooterToolbar, PageLoading } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import {
  Card,
  Spin,
  Descriptions,
  Steps,
  Typography,
  Space,
  Button,
  Tabs,
  Modal,
  Row,
  Col,
  Divider,
  message,
  Table,
} from 'antd';
import { isEmpty } from 'lodash';
import type { ColumnType } from 'antd/lib/table';
import { historyGetQuery, historyGoPrev, includesInArray } from '@/pages/overseaLocation/utils';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import PortSteps from '@/components/PortSteps';
import { TextTooltip } from '@/components';
import { GetPageQuery, OpenWindow } from '@/utils/util';
import { convertSkuData } from '../util';
import { WAREHOUSE_DOCUMENT_STATUS } from '@/utils/const';
import type { ProDescriptionsItemProps, ProColumns } from '@/common-import';
import { ProDescriptions } from '@/common-import';
import { apiQueryOverseaContainerBox } from '@/pages/overseaLocation/api';
import { apiInboundDoorExecuteInfoByGoods, apiReachedRecords } from '../enterWarehouseApi';
import { dateUtils, eR } from '@/utils';
import moment from 'moment';
import { EnumInboundTabKey, EnumIssuanceType, isEqualInboundOrderStatus } from '../inboundEnum';

const { Item } = Descriptions;
const { Step } = Steps;
const { Title } = Typography;
const { dateTimeFormatter } = dateUtils;

const EntryWarehouseDetailPage = ({ dispatch, match, pageLoading, overseaContainerBox }: any) => {
  // console.log(
  //   '🚀 ~ file: index.tsx ~ line 21 ~ EntryWarehouseDetailPage ~ overseaContainerBox',
  //   overseaContainerBox,
  // );
  const { id } = historyGetQuery();
  const [data, setData] = useState<any>({});
  const {
    modalConfig,
    executionData,
    executionColumns,
    handleShowQuantityModal,
    handleQuantityModalCancel,
  } = useModalExec();
  /** 是否为 TOB入库 */
  const isTOB = data?.order?.businessType === 105;
  /** 非人工单且处于签收中的订单 才有签收按钮 */
  const hasBtnSign =
    EnumIssuanceType.manual !== data?.order?.issuanceType &&
    isEqualInboundOrderStatus([EnumInboundTabKey.SIGN_FOR_ING], data?.order?.orderStatus);

  const columns = [
    // {
    //   title: '箱唛头',
    //   dataIndex: 'shippingMarkNo',
    //   onCell: (row) => ({ rowSpan: row.rowSpan }),
    // },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
      render: (_: any, record: any) => record.sku,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      search: false,
      render: (_, record: any) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      dataIndex: 'goodsName',
      search: false,
      render: (_: any, record: any) => <TextTooltip title={record.goodsName} />,
    },
    // {
    //   title: '商品价值',
    //   dataIndex: 'goodsValue',
    //   search: false,
    // },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'goodsWeight',
      search: false,
      render: (_: string, record: any) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'goodsVolume',
      search: false,
      render: (_: any, record: any) => (
        <span>
          {record.volumeUnit}: {`${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`}
        </span>
      ),
    },
    // {
    //   title: '商品保质期',
    //   dataIndex: 'shelfLife',
    //   valueType: 'dateTime',
    //   search: false,
    // },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.singleBoxPackaging,
      dataIndex: 'quantityPerBox',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.totalPieces,
      dataIndex: 'totalQuantity',
      search: false,
    },
    {
      title: I18N.Src__Pages__Order__Detail.SiItem.packingMethod,
      dataIndex: 'boxTypeValue',
      search: false,
      onCell: (row) => ({ rowSpan: row.rowSpan }),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.numberOfCases,
      dataIndex: 'boxQuantity',
      onCell: (row) => ({ rowSpan: row.rowSpan }),
    },
    // {
    //   title: '操作',
    //   key: 'option',
    //   width: 150,
    //   fixed: 'right',
    //   onCell: (row) => ({ rowSpan: row.rowSpan }),
    //   render: (dom, record) => {
    //     return (
    //       <a key="copy" onClick={() => handlePrintRecord(record)}>
    //         打印箱唛
    //       </a>
    //     );
    //   },
    // },
  ] as ProColumns<any, any>[];

  const column2 = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.serialNo,
      dataIndex: 'index',
      valueType: 'indexBorder',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_: any, record: any) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      dataIndex: 'goodsName',
      render: (_: any, record: any) => <TextTooltip title={record.goodsName} />,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.commodityCode,
      dataIndex: 'sku',
      render: (_: any, record: any) => record.sku,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.requestedQuantity,
      dataIndex: 'totalQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.numberToBeExecuted,
      dataIndex: 'notReceivedQuantity',
    },
    // { title: '执行类型', dataIndex: 'executeTypeValue' },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.quantityInWarehouse1,
      dataIndex: 'reachedQuantity',
      renderText: (text, record) => {
        return <a onClick={() => handleShowQuantityModal(record, 'reached')}>{text}</a>;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.executed1,
      dataIndex: 'nums',
      render: (_: any, record: any) => {
        /** 签收类型 1-未签收，2-部分签收，3-已全部签收，4-异常签收 */
        if (includesInArray([2, 3], record.receivedType)) {
          return (
            <Space onClick={() => handleShowQuantityModal(record, 'execution')}>
              {record.goodQuantity ? (
                <a>
                  {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece1}
                  {record.goodQuantity}
                </a>
              ) : null}
              {record.badQuantity ? (
                <a>
                  {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts1}
                  {record.badQuantity}
                </a>
              ) : null}
              {record.errorQuantity ? (
                <a>
                  {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.abnormalParts1}
                  {record.errorQuantity}
                </a>
              ) : null}
            </Space>
          );
        }
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.completionOfPerformance,
      dataIndex: 'fulfillmentTypeValue',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.warehousePerformance1,
      dataIndex: 'complianceNo',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodsReceipt,
      dataIndex: 'receivedTypeValue',
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      key: 'option',
      width: 110,
      fixed: 'right',
      render: (value: string, record: any) => {
        // if (isTOB) {
        //   /** TOB入库没有签收 */
        //   return null;
        // }
        return (
          //  1-未签收，2-部分签收，3-已签收，4-异常签收
          hasBtnSign && (record.receivedType === 1 || record.receivedType === 2) ? (
            <a key="download" onClick={() => handleConfirmSingleOrderReceived(record)}>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.confirmSigning}
            </a>
          ) : null
        );
      },
    },
  ] as ProColumns<any, any>[];

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    const { podOrderNo } = GetPageQuery();

    await dispatch({
      type: 'global/getOverseaContainerBox',
      payload: {
        limit: 99,
      },
    });

    const result = await dispatch({
      type: 'entry_warehouse/getEnterBoundOrderDetail',
      payload: {
        id,
        podOrderNo,
      },
    });

    if (result) {
      setData(result);
    }
  };

  const handlePrintRecord = async (record: any) => {
    const result = await dispatch({
      type: 'entry_warehouse/getPrintDocumentList',
      payload: {
        podOrderNo: data.order.podOrderNo,
        orderChildList: [
          {
            complianceNo: record.complianceNo,
            shippingMarkNo: record.shippingMarkNo,
          },
        ],
      },
    });

    sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_OPTION_BUTTON_CLICK', {
      option_type: 'print',
      current_click_page: I18N.Src__Pages__Order__Components__CoustomTable.Index.details,
      order_status: data.order.orderStatus,
      oversea_pod_no: data.order.podOrderNo,
    });

    if (result && result.documentUrlList && result.documentUrlList.length) {
      result.documentUrlList.forEach((o: string, i: number) => {
        OpenWindow(o, o + i);
      });
    }
  };

  const getStatusDate = (record: any) => {
    const { orderStatusRecord } = data;
    const arr = Array.isArray(record.value) ? record.value : [record.value];
    const itemArr = orderStatusRecord.filter((o: any) => includesInArray(arr, o.orderStatus));
    const createDateArr = itemArr.map((item: any) => moment(item.createDate));
    const createDate =
      createDateArr.length > 0
        ? moment.max(createDateArr).format('YYYY-MM-DD HH:mm:ss')
        : undefined;

    return {
      date: dateTimeFormatter(createDate, 'YYYY-MM-DD HH:mm'),
    };
  };

  const handleCancelRecord = async () => {
    const title =
      data.order.orderStatus === 100
        ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.deleteOrder
        : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
            .wholeOrderCancellation;
    const content = I18N.template(
      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.whetherToConfirm2,
      {
        val1:
          data.order.orderStatus === 100
            ? I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.delete
            : I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.cancel,
      },
    );

    Modal.confirm({
      title,
      content,
      onOk: async () => {
        sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_OPTION_BUTTON_CLICK', {
          option_type: 'delete',
          current_click_page: I18N.Src__Pages__Order__Components__CoustomTable.Index.details,
          order_status: data.order.orderStatus,
          oversea_pod_no: data.order.podOrderNo,
        });

        const result = await dispatch({
          type: 'entry_warehouse/cancelEnterBoundOrder',
          payload: { orderId: data.order.id },
        });

        if (result) {
          message.success(
            I18N.template(
              I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.success,
              { val1: title },
            ),
          );
          init();
        }
      },
    });
  };

  const handleConfirmOrderReceived = async () => {
    Modal.confirm({
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.informationConfirmation,
      content: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.whetherToConfirm1,
      onOk: async () => {
        sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_ALL_GOODS_CONFIRM', {
          order_status: data.order.orderStatus,
          oversea_pod_no: data.order.podOrderNo,
        });

        const result = await dispatch({
          type: 'entry_warehouse/confirmOrderReceived',
          payload: {
            podOrderNo: data.order.podOrderNo,
          },
        });

        if (result) {
          message.success(
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.wholeOrderConfirmation,
          );

          init();
        }
      },
    });
  };

  const handleConfirmSingleOrderReceived = async (record: any) => {
    Modal.confirm({
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.informationConfirmation,
      content: I18N.template(
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.whetherToConfirm,
        { val1: record.sku },
      ),
      // okText: '确定完全到货',
      onOk: async () => {
        sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_SINGLE_GOODS_CONFIRM', {
          order_status: data.order.orderStatus,
          oversea_pod_no: data.order.podOrderNo,
          received_type: record.receivedType,
          goods_sku: record.sku,
          compliance_no: record.complianceNo,
        });

        const result = await dispatch({
          type: 'entry_warehouse/confirmSingleOrderReceived',
          payload: {
            goodsId: record.id,
          },
        });

        if (result) {
          message.success(
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.confirmationSucceeded,
          );

          init();
        }
      },
    });
  };

  const getStepStatus = (step: any, orderStatusArr: any[]) => {
    if (!step || !orderStatusArr) return 'wait';
    const valueArr = Array.isArray(step.value) ? step.value : [step.value];
    const statusList = orderStatusArr.map((o) => o.orderStatus);

    for (let i = 0; i < valueArr.length; i++) {
      if (includesInArray(statusList, valueArr[i])) {
        if (includesInArray(valueArr, 110)) {
          return 'error';
        }
        return 'process';
      }
    }
    return 'wait';
  };

  const handleStepChange = (type: string) => {
    sensorsTrack('WWL_PORTAL_ENTER_WAREHOUSE_LOGISTIC_CLICK', {
      current_click_page: I18N.Src__Pages__Order__Components__CoustomTable.Index.details,
      order_status: data.order.orderStatus,
      oversea_pod_no: data.order.podOrderNo,
      logistic_type: type,
    });
  };

  const renderActionButton = () => {
    // const stautus = data.order.orderStatus;

    return (
      hasBtnCancel(data.order) && (
        <Button onClick={handleCancelRecord}>
          {
            I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
              .wholeOrderCancellation
          }
        </Button>
      )
    );

    // switch (stautus) {
    //   case 102:
    //   case 103:
    //   case 105:
    //     return (
    //       <Space>
    //         {renderInboundCancelBtn(
    //           {
    //             orderStatus: data.order.orderStatus,
    //             businessType: data.order.businessType,
    //           },
    //           <Button onClick={handleCancelRecord}>
    //             {
    //               I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
    //                 .wholeOrderCancellation
    //             }
    //           </Button>,
    //         )}
    //       </Space>
    //     );
    //   default:
    //     break;
    // }
  };

  if (!data || isEmpty(data)) return <PageLoading />;

  const {
    order = {},
    orderStatusRecord = [],
    orderLogistics = [],
    orderGoods = [],
    orderFulfillment = [],
  } = data;

  return (
    <PageContainer title={false}>
      <Spin spinning={pageLoading !== undefined ? pageLoading : false}>
        <Card>
          <Row>
            <Col span={4}>
              <Title level={5}>
                {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.documentProcess}
              </Title>
              <Steps progressDot size="small" direction="vertical">
                {WAREHOUSE_DOCUMENT_STATUS.filter((item) => item.hide !== true).map((o) => (
                  <Step
                    title={o.label}
                    key={o.label}
                    description={getStatusDate(o).date}
                    status={getStepStatus(o, orderStatusRecord)}
                  />
                ))}
              </Steps>
            </Col>
            <Col span={20}>
              <Descriptions
                title={I18N.Src__Pages__Order__Detail.Index.orderInformation}
                extra={renderActionButton()}
              >
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                      .applicationNo
                  }
                >
                  {order.podOrderNo}
                </Item>
                <Item label={I18N.Src__Pages__Order__TrailerOrder.Index.orderStatus} span={2}>
                  {order.orderStatusValue}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.orderType}
                >
                  {order.orderTypeValue}
                </Item>
                <Item label={I18N.Src__Pages__Home.Index.businessType}>
                  {order.businessTypeValue}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.orderSource}
                >
                  {order.orderSourceValue}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.applicant}
                >
                  {order.companyName}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.applicant}
                >
                  {order.createByName}
                </Item>
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index
                      .entrustmentRemarks
                  }
                >
                  {order.remark}
                </Item>
                <Item label="ETD">{dateUtils.dateFormatter(order.expectSendTime)}</Item>
                <Item label="ETA">{dateUtils.dateFormatter(order.expectTime)}</Item>
                <Item label={I18N.Src__Pages__Common__Template.Index.shippingCompany}>
                  {order.shippingCompany}
                </Item>
              </Descriptions>
              <Descriptions title="From/To">
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.targetInventory
                  }
                >
                  {order.targetWarehouseCode}
                </Item>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.targetType}
                  span={2}
                >
                  {order.targetWarehouseTypeValue}
                </Item>
              </Descriptions>
              <Descriptions
                title={
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.systemInformation
                }
              >
                <Item label={I18N.Src__Pages__Message__Notice.Index.creationTime}>
                  {dateTimeFormatter(order.createDate, 'YYYY-MM-DD HH:mm')}
                </Item>
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.lastModification1
                  }
                >
                  {dateTimeFormatter(order.updateDate, 'YYYY-MM-DD HH:mm')}
                </Item>
                <Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.lastModification
                  }
                >
                  {order.updateByName}
                </Item>
              </Descriptions>
              {renderLogisticsInfo({
                logisticsInfo: orderLogistics?.[0],
                handleStepChange,
                // overseaContainerBox,
              })}
            </Col>
          </Row>
          <Title level={5}>
            {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.documentDetails}
          </Title>
          <Tabs defaultActiveKey="1" type="card">
            <Tabs.TabPane
              tab={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.commodityList}
              key="1"
            >
              <ProTable
                rowKey={(record: any) => `${record.sku}-${record.shippingMarkNo}`}
                dataSource={convertSkuData(orderGoods)}
                columns={columns}
                pagination={false}
                search={false}
                bordered
                toolBarRender={false}
                scroll={{ x: 'max-content' }}
                size="small"
              />
            </Tabs.TabPane>
            <Tabs.TabPane
              tab={
                I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.warehousePerformance
              }
              key="2"
            >
              <ProTable
                rowKey={(record: any) => `${record.sku}-${record.shippingMarkNo}`}
                dataSource={orderFulfillment}
                columns={column2}
                pagination={false}
                search={false}
                bordered
                options={false}
                toolBarRender={() => {
                  // if (isTOB) {
                  //   return [];
                  // }
                  /** 签收中 且 非人工单才允许操作签收 */
                  return hasBtnSign
                    ? [
                        <Button type="primary" key="confirm" onClick={handleConfirmOrderReceived}>
                          {
                            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index
                              .confirmTheWholeDocument
                          }
                        </Button>,
                      ]
                    : [];
                }}
                scroll={{ x: 'max-content' }}
                size="small"
              />
            </Tabs.TabPane>
          </Tabs>
        </Card>

        <Modal
          {...modalConfig}
          width="70%"
          onCancel={handleQuantityModalCancel}
          maskClosable={false}
          destroyOnClose
          footer={
            <Button type="primary" onClick={handleQuantityModalCancel}>
              {I18N.Src__Pages__Enterprise__Account.Index.determine}
            </Button>
          }
        >
          <Table
            rowKey="id"
            size="small"
            pagination={false}
            dataSource={executionData}
            columns={executionColumns}
            bordered
          />
        </Modal>
      </Spin>

      <FooterToolbar
        style={{ width: '100%', textAlign: 'center' }}
        extra={
          <Button type="primary" onClick={() => historyGoPrev()}>
            {I18N.Src__Pages__Order__Detail.Index.return}
          </Button>
        }
      />
    </PageContainer>
  );
};

export default connect(({ global, loading }: any) => ({
  pageLoading: loading.models.entry_warehouse,
  overseaContainerBox: global.overseaContainerBox,
}))(EntryWarehouseDetailPage);

/** 渲染物流信息 */
function renderLogisticsInfo({
  logisticsInfo,
  handleStepChange,
  overseaContainerBox,
}: {
  logisticsInfo: any;
  handleStepChange: (...args: any[]) => void;
  overseaContainerBox?: any[];
}) {
  // console.log('overseaContainerBox-->', overseaContainerBox);
  const o = logisticsInfo || {};
  const logisticsProvider = Number(o.logisticsProvider);
  const columnsMap = {
    /** MWB-海运 */
    1: [
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .containerType,
        dataIndex: 'containerModel',
        valueType: 'select',
        /** 后端说,门户自己匹配, OMS后端匹配,  因为OMS侧没法获取字典列表, 门户自己调的中台 */
        request: () => apiQueryOverseaContainerBox(),
      },
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .containerNo,
        dataIndex: 'containerNo',
      },
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .billOfLadingNo,
        dataIndex: 'logisticsNo',
      },
    ],
    /** TRUCK-卡车 */
    3: [
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .totalNumberOfPallets,
        dataIndex: 'totalTrayNum',
      },
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .trackingSheetNo,
        dataIndex: 'logisticsNo',
      },
    ],
    /** EXPRESS-快递 */
    5: [
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .logisticsChannel,
        dataIndex: 'logisticsChannelProviderValue',
      },
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .trackingSheetNo,
        dataIndex: 'logisticsNo',
      },
      {
        title:
          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
            .totalNumberOfBoxes,
        dataIndex: 'boxTotalQuantity',
      },
    ],
  };
  const columns = (
    [
      {
        title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.logisticsChannel,
        dataIndex: 'logisticsProviderValue',
      },
      ...(columnsMap[logisticsProvider as any as 1] || columnsMap[1]),
      {
        title: I18N.Src__Pages__Message__Notice.Index.state,
        dataIndex: 'currentStatus',
      },
      o.nodeList &&
        o.nodeList.length && {
          title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.process,
          dataIndex: 'nodeList',
          span: 3,
          render(dom, data) {
            return <PortSteps data={data.nodeList} onModeToggleChange={handleStepChange} />;
          },
        },
    ] as ProDescriptionsItemProps[]
  ).filter(Boolean);

  return (
    <ProDescriptions
      {...{
        title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.logisticsInformation,
        columns,
        style: { marginBottom: 10 },
        dataSource: logisticsInfo,
      }}
    />
    // <>
    //   <Title level={5}>物流信息</Title>
    //   <Descriptions key={o.id} style={{ marginBottom: 10 }}>
    //     <Item label="物流渠道">{o.logisticsProviderValue}</Item>
    //     <Item label="追踪单号">{o.logisticsNo}</Item>
    //     <Item label="集装箱型号">
    //       {o.containerModel
    //         ? overseaContainerBox.find((b: any) => b.code && b.code === o.containerModel)?.enName
    //         : ''}
    //     </Item>
    //     <Item label="集装箱号">{o.containerNo}</Item>
    //     <Item label="状态">{o.currentStatus}</Item>
    //     {o.nodeList && o.nodeList.length ? (
    //       <Item label="进程" span={3}>
    //         <PortSteps data={o.nodeList} onModeToggleChange={handleStepChange} />
    //       </Item>
    //     ) : null}
    //   </Descriptions>
    // </>
  );
}

type TypeModalConfig = {
  visible?: boolean;
  /** reached: 到仓数量;
   * execution: 已执行数量; */
  modalType?: 'reached' | 'execution';
  title?: string;
};
/** 到仓数量, 已执行数量 modal框
 * @ oms侧暂无到仓数量,因为仅门户需要签收
 */
function useModalExec() {
  const [modalConfig, setModalConfigInline] = useState<TypeModalConfig>({
    title: '',
    visible: false,
  });
  const setModalConfig = (newCfg: TypeModalConfig) => {
    setModalConfigInline((oldCfg) => ({
      ...oldCfg,
      ...newCfg,
      title:
        newCfg.modalType === 'reached'
          ? I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.quantityInWarehouse
          : I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.executed,
    }));
  };
  const [executionData, setExecutionData] = useState([]);
  const dispatch = useDispatch();
  const handleShowQuantityModal = async (record: any, modalType: TypeModalConfig['modalType']) => {
    let result;
    const params = { id: record.id };

    if (modalType === 'reached') {
      const data = await apiReachedRecords(params);

      result = data?.result;
    } else {
      result = (await apiInboundDoorExecuteInfoByGoods(params)).data;
      // result = (await dispatch({
      //   type: 'entry_warehouse/getWarehousePerformanceExecutionDetail',
      //   payload: params,
      // })) as any;
    }

    if (result) {
      setExecutionData(result);
      setModalConfig({ modalType, visible: true });
    }
  };
  const handleQuantityModalCancel = () => {
    setExecutionData([]);
    setModalConfig({ modalType: undefined, visible: false });
  };
  const executionColumns = (
    [
      {
        title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.productQuality,
        dataIndex: 'executeTypeValue',
        render: (_: any, record: any) => (
          <Space>
            <span>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.goodPiece}
              {record.goodQuantity}
            </span>
            <span>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.badParts}
              {record.badQuantity}
            </span>
            <span>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.abnormalParts}
              {record.errorQuantity}
            </span>
          </Space>
        ),
      },
      {
        title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.triggerTime,
        dataIndex: modalConfig.modalType === 'reached' ? 'remoteReceivedTime' : 'signTime',
        render: (value, record) => dateTimeFormatter(value, 'YYYY-MM-DD HH:mm') || '-',
      },
      modalConfig.modalType === 'reached'
        ? undefined
        : {
            // executeTypeValue
            // 1，签收入库 2，执行出库
            title:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.inventoryTransactions,
            dataIndex: 'reserveNo',
          },
      {
        title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.transactionNote,
        dataIndex: 'wmsReceivedNo',
      },
    ] as ColumnType<any>[]
  ).filter(Boolean);

  return {
    modalConfig,
    // visible,
    executionData,
    executionColumns,
    handleShowQuantityModal,
    handleQuantityModalCancel,
  };
}

/** 是否渲染入库整单取消
 * @deprecated 历史逻辑已废弃
 */
function renderInboundCancelBtn(
  params: {
    orderStatus: string | number | undefined;
    businessType: string | number | undefined;
  },
  vnode: React.ReactNode,
) {
  let { orderStatus, businessType } = params;

  businessType += '';
  orderStatus += '';

  if (businessType === '105' /** TOB入库 */) {
    /* TOB 入库单仅 草稿状态允许取消 */
    return includesInArray(['100' /** 草稿（待提交） */, '101' /** 客户建单 */], orderStatus)
      ? vnode
      : null;
  }
  return includesInArray(['100', '101', '102', '103', '105'], orderStatus) ? vnode : null;
}

/** 是否有 整单取消 按钮 */
export function hasBtnCancel(item: TypeEnterWarehouseTB) {
  const orderStatus = Number(item.orderStatus);
  const { orderStatusTabKey } = item;
  const range = [EnumInboundTabKey.SHIPPING];

  // if ([101].includes(orderStatus)) {
  //   return true;
  // }
  if (
    // [102, 103, 105].includes(orderStatus) &&
    //  && item.businessType?.toString?.() !== '105' /** TOB入库 */
    includesInArray(range, orderStatusTabKey) ||
    isEqualInboundOrderStatus(range, orderStatus)
  ) {
    return true;
  }

  return false;
}
