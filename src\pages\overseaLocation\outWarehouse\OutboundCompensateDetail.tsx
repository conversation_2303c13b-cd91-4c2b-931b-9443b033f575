import I18N from '@/utils/I18N';
import { FormProCard, FormProDescriptions } from '@/views';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import React, { useEffect, useState } from 'react';
import { CompensateOperateRecord } from './components';
import { apiOutboundCompensateDetail } from './OutboundApi';
import { cssContainer } from '@/styles';
import { PageContainer } from '@/common-import';
import { historyGetQuery } from '@/utils';
const moneyFormatter = CompensateOperateRecord.moneyFormatter;

/** 出库单索赔详情 */
export default function OutboundCompensateDetail() {
  const { columnsInfo, detailData } = useConfig();

  return (
    <PageContainer className={cssContainer['container-layout-card']}>
      <FormProCard
        title={
          I18N.Src__Pages__OverseaLocation__OutWarehouse.OutboundCompensateDetail.chineseSymbols2
        }
      >
        <FormProDescriptions
          {...{
            dataSource: detailData,
            columns: columnsInfo,
          }}
        />
      </FormProCard>
      <CompensateOperateRecord detailData={detailData} />
    </PageContainer>
  );
}

function useConfig() {
  /** 索赔信息 */
  const columnsInfo = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate.ModalCompensate
          .applicationNo,
      dataIndex: 'podOrderNo',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse.OutboundCompensateDetail.chineseSymbols1,
      dataIndex: 'compensateCompanyName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutboundCompensateDetail.warehouse,
      dataIndex: 'warehouseCode',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics.ModalLogistics
          .logisticsChannel,
      dataIndex: 'customerChannelCode',
      renderText(text, record) {
        return text || record?.deliveryChannelName;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalLogistics.ModalLogistics
          .trackingSheetNo,
      dataIndex: 'trackingNo',
      span: 2,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols3,
      dataIndex: 'applyCompensateAmount',
      renderText(text, record) {
        return moneyFormatter(text, record.currency);
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols2,
      dataIndex: 'actualCompensateAmount',
      span: 2,
      renderText(text, record) {
        return moneyFormatter(text, record.currency);
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols4,
      dataIndex: 'compensateStatusName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse.OutboundCompensateDetail.chineseSymbols,
      dataIndex: 'fileList',
      span: 2,
      render(dom, record) {
        const fileList = record.fileList || [];
        const node = (fileList.length > 0 ? fileList : [undefined]).map((file) => {
          if (file === undefined) {
            return '-';
          }

          return (
            <div key={file.filePath}>
              {file.filePath && (
                <a target="_blank" download href={file.filePath}>
                  {file.fileName}
                </a>
              )}
            </div>
          );
        });

        return <div>{node}</div>;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__CompensateOperateRecord
          .CompensateOperateRecord.chineseSymbols1,
      dataIndex: 'remark',
    },
  ] as ProDescriptionsItemProps<TypeOutboundCompensateApplyDetail>[];
  const { complianceNo } = historyGetQuery() as { complianceNo: string };
  const [detailData, setDetailData] = useState<TypeOutboundCompensateApplyDetail>();

  async function asyncGetDetail() {
    const { data } = await apiOutboundCompensateDetail({ complianceNo });

    setDetailData(data);
  }

  useEffect(() => {
    if (complianceNo) {
      asyncGetDetail();
    }
  }, [complianceNo]);

  return { columnsInfo, detailData };
}
