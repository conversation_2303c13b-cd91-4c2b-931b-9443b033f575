import I18N from '@/utils/I18N';
import { <PERSON><PERSON>, Divider, Space } from 'antd';
import React, { useRef } from 'react';
import type { ActionType, ProColumns } from '@/common-import';
import { PageContainer } from '@/common-import';
import { ZouProTable } from '@/components';
import type { TypeZouProTableProps } from '@/components';
import { apiImportList } from './ImportApi';
import { dateUtils } from '@/utils';
import { apiMapDictType } from '@/api';

function ImportList() {
  const { config } = useConfig();

  return (
    <PageContainer>
      <ZouProTable {...config} />
    </PageContainer>
  );
}

export default ImportList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const { columns } = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      // defaultCollapsed: false,
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiImportList>[0];
      const { data } = await apiImportList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as TypeZouProTableProps<any, any>;

  return {
    config,
  };
}

function useColumns() {
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.creationTime,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform(value) {
          const { transformDate } = dateUtils;

          return {
            createDateStart: transformDate(value[0], 'START'),
            createDateEnd: transformDate(value[1], 'END'),
          };
        },
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.businessType,
      dataIndex: 'importType',
      valueType: 'select',
      request: apiMapDictType.importType,
      search: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols6,
      dataIndex: 'fileName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols5,
      dataIndex: 'totalNum',
      renderText: (text) => (text > 0 ? text : '-'),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols4,
      dataIndex: 'successNum',
      renderText: (text) => (text > 0 ? text : '-'),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols3,
      dataIndex: 'failNum',
      renderText: (text) => {
        return text > 0 ? <span style={{ color: 'red' }}>{text}</span> : '-';
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.state,
      dataIndex: 'importStatus',
      valueType: 'select',
      search: true,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'importStatusList',
      },
      request: apiMapDictType.importStatus,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.operator,
      dataIndex: 'createByName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.chineseSymbols2,
      dataIndex: 'errorMessage',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList.operation,
      dataIndex: 'action',
      valueType: 'option',
      width: 'auto',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <a target="_blank" download href={record.fileUrl}>
              下载源文件
            </a>
            {record?.errorFileUrl && (
              <a target="_blank" download href={record.errorFileUrl}>
                {
                  I18N.Src__Pages__OverseaLocation__ImportManage__ImportList.ImportList
                    .chineseSymbols
                }
              </a>
            )}
          </Space>
        );
      },
    },
  ] as ProColumns<TypeImportTB>[];

  return { columns };
}
