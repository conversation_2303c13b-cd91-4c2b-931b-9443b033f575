/* stylelint-disable selector-pseudo-class-no-unknown */
.text-overflow(@width) {
  width: @width;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.header {
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }

    .ant-form-item-label > label {
      display: block;
      width: 100%;
      overflow: hidden;
      font-size: 12px;
      line-height: 32px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.container {
  :global(.ant-tabs) {
    margin-top: 13px;
    margin-bottom: 8px;
    background-color: #fff;
  }
  .tab {
    font-weight: bold;
  }

  /* 重构样式 */
  :global {
    .ant-table-thead > tr > th {
      background-color: #fff;
      border-bottom: 10px solid #f0f2f5;
    }

    /* 表头分割线 */
    .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      width: 0;
    }

    /* 表单样式 */
    .ant-table {
      font-size: 12px;

      thead {
        font-size: 14px;
      }

      .ant-table-tbody > tr > td {
        max-width: 300px;
        vertical-align: top;
        border-bottom: 0;

        /* 操作列 */
        &.action {
          white-space: nowrap;

          // vertical-align: middle;
          >.ant-space {
            justify-content: center;
          }
        }
      }

      td.ant-table-cell > a {
        font-size: inherit;
      }

      /** 折叠 */
      .ant-table-expanded-row .ant-table-cell {
        padding-top: 0;
        background: #fff;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .ant-pagination {
      padding: 12px 0;
      background-color: #fff;
    }
  }

  /* 局部module样式 */
  :local {
    .status {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1px 14px;
      color: #fff;
      font-weight: bold;
      white-space: nowrap;
      background-color: #ffa940;
    }

    .leftLine {
      padding-left: 4px;
      border-left: 2px solid #91d5ff;
    }

    // a {
    //   font-size: 12px;
    // }

    // .text {
    //   color: #333;
    //   font-size: 12px;
    //   .text-overflow('100%');
    // }

    .podNo {
      color: #1890ff;
      font-weight: 600;
    }

    .createDate {
      color: #999;
      font-size: 12px;
      .text-overflow('100%');
    }

    .price {
      color: #ff6d00;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .alignColumn {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .middleColumn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2180ff;
    font-size: 12px;
    cursor: pointer;
  }

  .actionWrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    .actionItem {
      margin-right: 8px;
      color: #333;
      font-size: 12px;

      &:nth-last-of-type(1) {
        margin-right: 0;
      }
    }

    // a {
    //   // min-width: 54px;
    // }
  }

  .expand {
    width: 100%;
    padding: 12px;
    background: #fafcfe;
    border: 1px solid #ebebeb;
    border-radius: 6px;

    .expandItem {
      display: flex;
      align-items: center;
      justify-content: start;
      margin-bottom: 6px;

      &:nth-last-of-type(1) {
        margin-bottom: 0;
      }

      .row1 {
        display: flex;
        flex: 0 0 15%;
        flex-direction: column;

        .type {
          align-self: baseline;
          padding: 0 16px;
          color: #9046eb;
          font-size: 12px;
          text-align: center;
          background-color: #eee5fb;
          border-radius: 2px;
        }
      }

      .row2 {
        flex: 0 0 8%;
        text-align: center;

        .logisticsStatus {
          // display: inline-block;
          height: 16px;
          padding: 0 4px;
          color: #52c31a;
          font-size: 12px;
          line-height: 16px;
          background-color: #d9f7bec2;
          border: 1px solid rgba(82, 195, 26, 0.8);
          border-radius: 2px;
        }
      }

      .stepWrap {
        width: 90%;
        transform: translateX(-20px);
      }
    }
  }
}
