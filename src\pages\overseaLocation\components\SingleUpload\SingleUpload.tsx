import I18N from '@/utils/I18N';
import { ButtonProps, message, Upload, UploadProps } from 'antd';
import React, { useState } from 'react';
import { useSafeStateUpdate } from '../../utils';
import { LoadButton } from '../LoadButton';

export type TypezSingleUploadProps = {
  /** 触发按钮 */
  children?: React.ReactNode;
  /** 文件后缀 */
  fileSuffix: string[];
  /** 文件大小限制, 默认100mb */
  maxFileSizeMb?: number;
  /** 上传请求 */
  uploadRequest: (...args: Parameters<NonNullable<UploadProps['customRequest']>>) => Promise<void>;
  /** 优先级高于外部零散属性 */
  uploadProps?: UploadProps;
  buttonProps?: ButtonProps;
};

function SingleUpload(props: TypezSingleUploadProps) {
  const [loading, setLoading] = useState(false);
  const { safeUpdateFn } = useSafeStateUpdate();
  const { maxFileSizeMb = 10, fileSuffix, uploadRequest, buttonProps } = props;

  return (
    <Upload
      {...{
        disabled: loading,
        beforeUpload(file) {
          const { name } = file;
          const arr = name.split('.');
          const suffix = arr[arr.length - 1];
          const sizeMb = file.size / 1024 / 1024;

          if (fileSuffix.includes(suffix) === false) {
            message.error(
              I18N.template(
                I18N.Src__Pages__OverseaLocation__Components__SingleUpload.SingleUpload
                  .onlyLatticesAreSupported,
                { val1: fileSuffix.join(', ') },
              ),
            );
            return false;
          }
          if (sizeMb > maxFileSizeMb) {
            message.error(
              I18N.template(
                I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.fileSize,
                { val1: maxFileSizeMb },
              ),
            );
            return false;
          }
        },
        showUploadList: false,
        customRequest: async (param) => {
          if (!uploadRequest) return;
          setLoading(true);
          const res = uploadRequest(param);

          res.finally(safeUpdateFn(() => setLoading(false)));
        },
        ...props.uploadProps,
      }}
    >
      <LoadButton
        {...{
          loading,
          ...buttonProps,
        }}
      >
        {props.children}
      </LoadButton>
    </Upload>
  );
}

export default SingleUpload;
