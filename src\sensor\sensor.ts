/** 神策包 */
import type { NsSensor } from './types';
import { EnumSensor } from './event';
import { eventPrefix, attrPrefix, setSensorEventPrefix } from './sensorConfig';

/** 初始化神策模块 */
export function sensorInit(params?: {
  /** 用户信息 */
  userInfo?: any;
  /** 环境变量 */
  env?: 'DEV' | 'TEST' | 'GRAY' | 'PRO';
  /** 事件公共前缀 */
  eventPrefix?: Uppercase<string>;
  /** 所属产品线 */
  product?: string;
  /** 工程名称 */
  module?: string;
}) {
  window.ssTrack = ssTrack;
  window.EnumSensor = EnumSensor;
  params?.eventPrefix && setSensorEventPrefix(params?.eventPrefix);
  /** 门户神策初始化由sdk完成 */
}

/** 添加神策埋点, 自动添加前缀 */
export function ssTrack<K extends EnumSensor>(key: Uppercase<K>, data?: NsSensor.dataMap[K]) {
  /** 不要用 window.sensorsTrack 判断, 为前端通过iframe创建注入了一个window,所以会不存在, 它只注入了Sensors */
  window.Sensors && window.Sensors.track?.(`${eventPrefix}${key}`, transData(data));
}

/** 转换事件属性数据, 自动处理添加前缀和大小写 */
function transData<T extends Record<string, any>>(data?: T) {
  if (!data) {
    return data;
  }
  const newData = {} as Record<string, any>;

  for (const key in data) {
    newData[`${attrPrefix}${key}`] = data[key];
  }
  return newData;
}
