import { Button, Form, Modal, message } from 'antd';
import type { FormProps } from 'antd/es/form/Form';
import React, { useRef, useState, useEffect } from 'react';
import { FormRecipientAddress } from '../FormRecipientAddress';
import {
  apiQueryOutBoundOrderForm,
  apiOutWarehouseEditErrorOrder,
  LoadButton,
} from './EditAddressImport';
import { textMap } from './text-map';

type TypeProps = {
  rowData: any;
  reload: () => void;
};

export default function ModalEditAddress(props: TypeProps) {
  // eslint-disable-next-line no-spaced-func
  const addressRef = useRef<{ addressInit?: () => void }>();
  const { formConfig, asyncSubmit, form, getFormData, formInfo } = useModalForm(props, addressRef);
  const modalInfo = useModal(asyncSubmit, getFormData);

  return (
    <>
      <a onClick={modalInfo.showModal}>{textMap.txtModifyAddress}</a>
      <Modal {...modalInfo.config}>
        <Form {...formConfig}>
          <FormRecipientAddress
            {...{
              ref: addressRef,
              form,
              colSpan: 2,
              detailData: formInfo,
            }}
          />
        </Form>
      </Modal>
    </>
  );
}

function useModal(asyncSubmit: () => Promise<any>, getFormData: () => Promise<any>) {
  const [isModalVisible, setIsModalVisible] = useState(false);

  useEffect(() => {
    if (isModalVisible) {
      getFormData();
    }
  }, [isModalVisible]);
  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const config = {
    title: textMap.txtEditAddress,
    width: '800px',
    visible: isModalVisible,
    destroyOnClose: true,
    // 不允许点击蒙层关闭弹窗
    maskClosable: false,
    onCancel: handleCancel,
    footer: [
      <Button key="back" onClick={handleCancel}>
        {textMap.txtCannel}
      </Button>,
      <LoadButton
        key="sub"
        {...{
          type: 'primary',
          onClick: async () => {
            await asyncSubmit().then((result) => {
              if (result) {
                setIsModalVisible(false);
              }
            });
          },
        }}
      >
        {textMap.txtConfirm}
      </LoadButton>,
    ],
  };

  return {
    config,
    showModal,
  };
}
function useModalForm(
  props: TypeProps,
  addressRef: React.MutableRefObject<
    | {
        addressInit?: (() => void) | undefined;
      }
    | undefined
  >,
) {
  const [form] = Form.useForm();
  const [formInfo, setFormInfo] = useState<any>();
  const formConfig = {
    layout: 'vertical',
    form,
    preserve: false,
    initialValues: {
      express: {
        recipientPhoneNumberList: [''],
        recipientAddressList: [''],
      },
    },
  } as FormProps;
  const getFormData = async () => {
    const res = await apiQueryOutBoundOrderForm({ id: props.rowData?.id });
    const result = res.data;

    setFormInfo(result);
    /** 处理地址和联系电话，在没有电话和地址信息的条件下，默认生成一条 */
    const newExpress = {
      ...result?.express,
      recipientAddressList: result
        ? result?.express?.recipientAddressList?.length === 0
          ? ['']
          : result?.express?.recipientAddressList
        : [''],
      recipientPhoneNumberList: result
        ? result?.express?.recipientPhoneNumberList?.length === 0
          ? ['']
          : result?.express?.recipientPhoneNumberList
        : [''],
    };

    form?.setFieldsValue({
      ...result,
      express: {
        ...newExpress,
      },
    });
    /** 获取对应的省州、城市信息 */
    addressRef.current?.addressInit?.();
  };
  const asyncSubmit = async () => {
    try {
      await form?.validateFields();
    } catch (error) {
      return false;
    }
    const formData = form?.getFieldsValue();

    return apiOutWarehouseEditErrorOrder({
      podOrderNo: formInfo?.podOrderNo,
      /** 获取表单的接口没有找到这个字段，需要找后端确认 */
      complianceNo: formInfo?.complianceNo,
      ...formData.express,
    }).then((result: any) => {
      /** 后端说从data取值,门户侧包装成了result, 兼容OMS侧取值 */
      if (result?.success || result?.code === '000000') {
        const data = result?.result || result?.data;

        if (data?.showWindow) {
          Modal.success({
            content: data?.message,
            okText: textMap.txtConfirm,
            keyboard: false,
            onOk: () => {
              props?.reload();
            },
          });
        } else {
          message.success(data?.message || textMap.txtEditSuccess);
          props?.reload();
        }
        return result;
      }
      return false;
    });
  };

  return { formConfig, asyncSubmit, form, getFormData, formInfo };
}
