import { Form } from 'antd';

/** 序号渲染 */
export function SerialNumber(props: { rowIndex: number }) {
  const { rowIndex } = props;
  const form = Form.useFormInstance();
  const boxList = (Form.useWatch('boxList', form) as NsInbound.TypeBoxGoodsInfo[]) || [];

  if (!form) {
    return;
  }
  const row = boxList[rowIndex] || {};
  const boxQuantity = row.boxQuantity ?? 0;
  const prevNum = (rowIndex > 0 ? boxList.slice(0, rowIndex) : []).reduce((prev, cur) => {
    return prev + Number(cur.boxQuantity ?? 0);
  }, 0);

  return `${prevNum + 1}-${prevNum + boxQuantity}`;
}
