import { Alert, Form, Image, InputNumber, message, Spin, Table } from 'antd';
import type { FormProps } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { ProFormRadio } from '@ant-design/pro-form';
import { useRequest } from '@umijs/max';
import I18N from '@/utils/I18N';
import type { TypeZouModalRef } from '@/pages/overseaLocation/components';
import { ZouModal, ZSearchSelect } from '@/pages/overseaLocation/components';
import type { TypeSkuLabelDownload } from '../../goodsApi';
import {
  apiDestinationWarehouseSelect,
  apiGoodsSkuLabelDownload,
  apiGoodsSkuLabelPreview,
} from '../../goodsApi';
import { ruleUtils, tipsUtils } from '@/pages/overseaLocation/utils';
import { useDictTypeValueEnum } from '@/api';
import { TextValue } from '@/views';
import { cssFormListTable } from '@/styles';

export type TypezGoodsModalPrintLabelProps = {
  /** 提交成功回调 */
  submitSuccessCB?: () => void;
};

type TypeOpenParams = {
  /** 商品 list */
  goodsList: TypeGoodsTB[];
};

export type TypeGoodsModalPrintLabelRef = {
  open: (params: TypeOpenParams) => void;
};

/** 弹窗打印商品标签 */
function GoodsModalPrintLabel(
  props: TypezGoodsModalPrintLabelProps,
  ref: React.Ref<TypeGoodsModalPrintLabelRef | undefined>,
) {
  const { modalRef, asyncSubmit, form, config, imgLoading, imgBase64, init, clear } =
    useConfig(props);
  const { dictTypeMap } = useDictTypeValueEnum(['SkuLabelTemplate']);

  useImperativeHandle(ref, () => ({
    open(params: TypeOpenParams) {
      modalRef.current?.open();
      init(params);
    },
  }));

  return (
    <ZouModal
      {...{
        ref: modalRef,
        fixedHeight: true,
        modalProps: {
          maskClosable: false,
          afterClose: clear,
          title:
            I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel
              .GoodsModalPrintLabel.productPrinting,
          okText: I18N.Src__Pages__Dynamic__Statistics.FileModal.download,
          width: 600,
        },
        async onOk() {
          await asyncSubmit();
        },
      }}
    >
      <Form {...{ form, ...config }}>
        <Alert
          {...{
            description: (
              <div>
                <div>温馨提示：首次打印时，建议打印后用扫描枪测试扫描是否成功。</div>
                <div>
                  {
                    I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel
                      .GoodsModalPrintLabel.tooLongPlaying
                  }
                </div>
              </div>
            ),
            style: { marginBottom: 6 },
            type: 'warning',
            showIcon: true,
          }}
        />
        <Form.List {...{ name: 'printSkuList' }}>
          {function () {
            const tableData =
              (form.getFieldValue('printSkuList') as TypeSkuLabelDownload['printSkuList']) || [];

            return (
              <Table
                {...{
                  columns: [
                    { title: 'SKU', dataIndex: 'sku' },
                    { title: '种类分拣码', dataIndex: 'categoryCode' },
                    {
                      title: '打印个数',
                      dataIndex: 'printNum',
                      render(val, record, index) {
                        return (
                          <Form.Item
                            {...{
                              label: '打印个数',
                              name: [index, 'printNum'],
                              rules: [
                                { required: true },
                                ruleUtils.ruleNoZeroPositiveInteger,
                                {
                                  async validator(rule, value) {
                                    if (value > 2000) {
                                      throw new Error('单个打印个数不允许超过2000');
                                    }
                                    const printSkuList =
                                      (form.getFieldValue(
                                        'printSkuList',
                                      ) as TypeSkuLabelDownload['printSkuList']) || [];
                                    const total = printSkuList.reduce((prev, cur) => {
                                      return prev + Number(cur.printNum ?? 0);
                                    }, 0);

                                    if (total > 2000) {
                                      throw new Error('打印总个数不允许超过2000');
                                    }
                                  },
                                },
                              ],
                            }}
                          >
                            <InputNumber {...{ stringMode: true }} />
                          </Form.Item>
                        );
                      },
                    },
                  ],
                  pagination: false,
                  dataSource: tableData,
                }}
              />
            );
          }}
        </Form.List>
        {}
        <Form.Item
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel
                .GoodsModalPrintLabel.destinationWarehouse,
            name: 'warehouseCode',
            rules: [{ required: true }],
            formItemLayout: 'vertical',
          }}
        >
          <ZSearchSelect
            style={{ width: '100%' }}
            request={(query: string) => apiDestinationWarehouseSelect({ query })}
          />
        </Form.Item>
        <ProFormRadio.Group
          {...{
            label:
              I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsModalPrintLabel
                .GoodsModalPrintLabel.paperSize,
            name: 'templateType',
            valueEnum: dictTypeMap.SkuLabelTemplate,
            formItemProps: {
              rules: [{ required: true }],
            },
          }}
        />
        <Spin spinning={imgLoading}>
          <div style={{ textAlign: 'center' }}>
            {imgBase64 && <Image src={imgBase64} preview={false} />}
          </div>
        </Spin>
      </Form>
    </ZouModal>
  );
}
export default React.forwardRef(GoodsModalPrintLabel);

function useConfig(props: TypezGoodsModalPrintLabelProps) {
  const [form] = Form.useForm();
  /** 判断图片是否有效,formData信息不完整,则无效 */
  const [isValidImg, setIsValidImg] = useState(false);
  const modalRef = useRef<TypeZouModalRef>();
  const {
    loading: imgLoading,
    data: { labelPictureBase64 } = {},
    run,
  } = useRequest<{ data: { labelPictureBase64?: string } }>(apiGoodsSkuLabelPreview, {
    manual: true,
  });
  const config = {
    className: cssFormListTable.FormListTable,
    async onValuesChange(changedValues, formData) {
      const { warehouseCode, templateType } = formData;

      if (warehouseCode && templateType) {
        await run(getParams(formData));
        setIsValidImg(true);
      } else {
        setIsValidImg(false);
      }
    },
  } as FormProps;

  function getParams(formData: any) {
    return {
      ...formData,
      goodsId: formData.printSkuList?.[0]?.goodsId,
    } as Parameters<typeof apiGoodsSkuLabelPreview>[0];
  }

  function init(params: TypeOpenParams) {
    setIsValidImg(false);
    setTimeout(() => {
      form.setFieldsValue({
        ...params,
        printSkuList: (params?.goodsList || []).map((item, i) => {
          const code = i + 1;

          return { sku: item.sku, goodsId: item.id, categoryCode: code >= 10 ? code : `0${code}` };
        }),
      });
    });
  }

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();

    await apiGoodsSkuLabelDownload({
      ...formData,
    });
    props.submitSuccessCB?.();
    return true;
  }

  function clear() {
    form.resetFields();
  }

  return {
    form,
    modalRef,
    asyncSubmit,
    config,
    imgLoading,
    imgBase64: isValidImg ? labelPictureBase64 : undefined,
    init,
    clear,
  };
}
