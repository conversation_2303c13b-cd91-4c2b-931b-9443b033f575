export default {
  BatchOrderDetail: {
    confirmDeletion: '确认删除?',
    supplementarySheet: '补充面单',
    updateFaceSheet: '更新面单',
    error: '错误',
    error1: '错误: {val1}',
    result: '结果',
    postalCode: '邮编',
    platformSeller: '平台卖家ID',
    orderSource: '订单来源平台',
    shipmentOrganization: '发货组织',
    totalNumberOfGoods: '商品总数量',
    receivingContact: '收件联系人电话',
    toAddress: '收件地址明细',
    receivingContact1: '收件联系人',
    deliveryService: '配送服务名称',
    deliveryService1: '配送服务类型',
    issuedSuccessfully: '下发成功',
    batchSupplement: '批量补充面单',
    article: '条。',
    entriesFailed: '条， 失败',
    entriesImporting: '条，导入成功',
    common: '共',
    foldersAnd: '文件夹和文件名都只能使用英文和数字，不支持汉字。',
    onlyForNon: '仅可对未下发的订单上传附件；',
    ifTheAttachment: '若附件对应的单号上已上传过附件，则导入时更新原来附件；',
    accordingToUpload:
      '根据上传附件的物流追踪单号匹配导入订单的物流单号；若未匹配上或者匹配多条订单，则该条附件无法上传；',
    attachments: '附件文件名规则：物流追踪单号.pdf ；附件支持pdf和png格式，且不超过 20 M；',
    pleaseAttachTheAttachment:
      '请将附件全部放在一个文件夹中，并将文件压缩成zip，进行上传；不支持多层文件夹解析；',
    selectToDistribute: '选择下发',
    importOrder: '导入订单数：{val1} 下发数：{val2}  待下发数：{val3} 异常数：{val4}',
  },
  BatchOutList: {
    deleted: '删除未下发订单',
    deleted1: '删除未下发订单成功',
    okToDelete: '确定删除?',
    downloadOriginal: '下载原文件',
    heteroconstant: '异常数',
    numberToBeIssued: '待下发数',
    numberOfIssues: '下发数',
    numberOfImports: '导入数',
    batchNo: '批号',
    batchImport: '批量导入',
    batchImport1: '批量导入成功',
    onlyLatticesAreSupported: '仅支持{val1}格式文件',
    getTemplate: '获取模板',
    getTheLatest: '获取最新模板,避免数据异常',
  },
} as const;
