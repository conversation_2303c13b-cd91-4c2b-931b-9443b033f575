/** 库存管理列表数据 */
type TypeInventoryTB = {
  /** id */
  id?: string;
  /** 仓库id */
  warehouseId?: number;
  /** 仓库名称 */
  warehouseName?: string;
  /** 仓库代码 */
  warehouseCode?: string;
  /** 库区id */
  warehouseAreaId?: number;
  /** 库区代码 */
  warehouseAreaCode?: string;
  /** 库区类型 */
  warehouseAreaType?: number;
  /** 库区名称 */
  warehouseAreaName?: string;
  /** 商品id */
  goodsId?: number;
  /** 商品名称 */
  goodsName?: string;
  /** 商品英文名称 */
  goodsEnName?: string;
  /** 商品sku */
  sku?: string;
  /** 商品安全库存(水位值) */
  safeNums?: number;
  /** 好件库存量 */
  goodQuantity?: number;
  /** 坏件库存量 */
  badQuantity?: number;
  /** 异常件库存量 */
  errorQuantity?: number;
  /** 冻结库存量 */
  frozenQuantity?: number;
  /** 累计出库量 */
  outboundQuantity?: number;
  /** 客户公司id */
  companyId?: string;
  /** 客户公司名称 */
  companyName?: string;
  /** 租户code */
  partnerCode?: string;
  /** 供应商code */
  wmsSystemCode?: string;
  /** 虚拟仓 */
  virtualWarehouseCode?: string;
  /** 虚拟仓名称 */
  virtualWarehouseName?: string;
  /** 商品信息 */
  goods?: {};
  /** 头程在途, 计算reduce(unsignedOrders.unreceivedQuantity) */
  unreceivedTotalQuantity?: number;
  /** 未签收头程入库(头程在途) */
  unsignedOrders?: {
    /** 交易单据 */
    complianceNo?: string;
    /** 单据状态 1-客户建单，2-提交审核，3-补充头程物流信息，4-入库预报通知，5-运输中，6-签收中，7-履约完结，8-费用确认，9-结单 */
    orderStatus?: number;
    /** 预期到货时间 */
    expectTime?: string;
    /** 未签收量 */
    unreceivedQuantity?: number;
  }[];
};
