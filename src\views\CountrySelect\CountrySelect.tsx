import { apiCountryCityOptions } from '@/api';
import type { TypeSearchSelectProps, TypeSearchSelectRef } from '@/components';
import { SearchSelect } from '@/components';
import css from './CountrySelect.less';
import React from 'react';

/** 国家下拉选择 */
export default React.forwardRef(function CountrySelect(
  props: TypeSearchSelectProps<any>,
  ref: React.Ref<TypeSearchSelectRef>,
) {
  function labelTrans(item: Awaited<ReturnType<typeof apiCountryCityOptions>>[number]) {
    return item.standardCode;
  }
  return (
    <SearchSelect
      {...{
        ref,
        filterOption: false,
        optionLabelProp: 'label',
        request: async (searchValue: string) => {
          return apiCountryCityOptions({ searchValue }).then((options) => {
            return (options || []).map((item) => {
              return {
                ...item,
                label: labelTrans(item),
                optionNode: (
                  <div className={css.selectLabel}>
                    <div>{item.standardCode}</div>
                    <div className="label-right">{item.label}</div>
                  </div>
                ),
              };
            });
          });
        },
        ...props,
      }}
    />
  );
});
