export default {
  Index: {
    saveTemplate: '保存模板',
    modifyTemplate: '修改模板',
    cannotBeEmpty: '不能为空|',
    cannotBeEmpty1: '\n                    {val1}必须为英文|长度不超过{val2}个字符',
    pleaseEnterLarge: '请输入大于0的整数',
    onlyUsedTo: '仅用于向船公司申请，不作为最终确认的免用箱天数',
    applicationPurpose: '申请目的港免用箱',
    addOthers: '新增其他箱型',
    onlyInteger: '只能为整数,长度不能超过2个字符',
    chineseSymbols: '、',
    currentApproximateNumber: '当前约号可用箱型箱量剩余：',
    routeCode: '航线代码',
    lengthNotExceeding: '长度不超过{val1}个字符',
    lengthNotExceeding1: '长度不超过{val1}个字符',
    useTemplates: '使用模板',
    replaceTheTemplate: '更换模板',
    exportBySea: '海运出口：整箱',
    oceanShippingOrder: '海运订单',
    orderCenter: '订单中心',
    success: '{val1} {val2}',
    notEarlierThan: 'ETD不可早于预计备货日',
    pleaseEnterTheModule: '请输入模板名',
    pleaseSelectAMold: '请选择模板',
    stagingOrder: '暂存订单数据已发生更新，如有疑问请联系环世客服',
    newBooking: '新增订舱',
    freightOrder: '运价SO订单',
    templateAddition: '模板SO新增',
    page: 'SO页面',
    descriptionOfTheGoods: '货物描述{val1}',
  },
  Modals: {
    pleaseSelect: '请选择已有模板',
  },
  Tuoshu: {
    letterOfEntrustment: '托书{val1}',
    parsing: '解析中',
    letterOfEntrustment1: '托书',
  },
  TuoshuItem: {
    cannotBeEmpty: '{val1}必须为英文|长度不超过{val2}个字符',
    reUpload: '重新上传托书文件',
    pleaseSelectBox: '请选择箱型类型',
    pleaseInputBox: '请输入箱信息',
    pleaseFillIn: '请填写符合标准数据',
    reparse: '重新解析上传文件成功',
  },
};
