import React from 'react';

export const initState = {
  currentWarehouseId: '',
  dispatchServiceList: [],
  /** 服务名称 */
  dispatchServiceNameInfo: {} as Record<string, any>,
  cacheGoodsList: [],
  cacheDeliveryList: [],
  /** 仓库系统标识 */
  wmsSystemCode: '',
};

export type StateType = typeof initState;

export enum ContextType {
  SET_WAREHOUSE_ID = 'SET_WAREHOUSE_ID',
  SET_DISPATCH_SERVICES_LIST = 'SET_DISPATCH_SERVICES_LIST',
  SET_DISPATCH_SERVICES_NAME_LIST = 'SET_DISPATCH_SERVICES_NAME_LIST',
  SET_EDIT_INIT_DATA = 'SET_EDIT_INIT_DATA',
  /** 设置仓库系统标识 */
  SET_WAREHOUSE_SYS_MARK = 'SET_WAREHOUSE_SYS_MARK',
}

export const contextReducer = (state: StateType, action: any) => {
  switch (action.type) {
    case ContextType.SET_WAREHOUSE_ID:
      return {
        ...state,
        currentWarehouseId: action.payload,
      };
    case ContextType.SET_DISPATCH_SERVICES_LIST:
      return {
        ...state,
        dispatchServiceList: action.payload,
      };
    case ContextType.SET_EDIT_INIT_DATA:
      return {
        ...state,
        cacheGoodsList: action.payload.goodsList,
        cacheDeliveryList: action.payload.express.deliveryList,
      };
    case ContextType.SET_DISPATCH_SERVICES_NAME_LIST:
      /** 配送服务名称 */
      return {
        ...state,
        dispatchServiceNameInfo: action.payload,
      };
    case ContextType.SET_WAREHOUSE_SYS_MARK:
      return {
        ...state,
        wmsSystemCode: action.payload,
      };
    default:
      break;
  }
};

const OutWarehouseContext = React.createContext<{
  contextState: StateType;
  contextDispatch: any;
}>(null as any);

export default OutWarehouseContext;
export const OutboundEditContext = OutWarehouseContext;
