/** 仓租列表 */
type TypezChargeWarehouseTB = {
  /** 费用单ID */
  id?: string;
  /** 费用单号 */
  chargeRecordBillNo?: string;
  /** 结算周期: 1-日结 */
  settlementInterval?: number;
  /** 仓库Code */
  warehouseCode?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 计算日期 */
  chargeDate?: string;
  /** 发生时间 */
  createDate?: string;
  /** 实际体积 */
  goodsActualTotalVolume?: number;
  /** 数量 */
  goodsTotalQuantity?: number;
  /** 计费用体积 */
  goodsTotalVolume?: number;
  /** 计费总价 */
  chargeTotal?: number;
  /** 计算币种 */
  chargeCurrency?: string;
  /** 结算总价 */
  settlementTotal?: number;
  /** 结算币种 */
  settlementCurrency?: string;
  /** 创建人id */
  createById?: number;
  /** 创建人名称 */
  createByName?: string;
  /** 费用类别 */
  chargeType?: number;
  /** 开票状态 0-待开票，1-已开票 */
  invoiceStatus?: number;
  /** 发票号 */
  invoiceNo?: string;
  /** 发票链接 */
  invoiceUrl?: string;
};

/** 费用账单列表 */
type TypezChargeBillRecordsTB = {
  /** 费用单ID */
  Id?: string;
  /** 费用单号 */
  chargeRecordBillNo?: string;
  /** 收付款确认单编号 */
  chargeBillNo?: string;
  /** 关联单号 */
  podOrderNo?: string;
  /** WMS操作单号 */
  wmsOrderNo?: string;
  /** 业务场景 */
  businessType?: number;
  /** 状态 */
  status?: number;
  /** 中台费用项Code */
  gmChargeCode?: string;
  /** 中台费用项名称 */
  gmChargeName?: string;
  /** 中台费用项英文名称 */
  gmChargeEnName?: string;
  /** 计费单位 */
  chargeUnit?: string;
  /** 计费单价 */
  chargeUnitPrice?: number;
  /** 计费数量 */
  chargeQuantity?: number;
  /** 计费总价 */
  chargeTotal?: number;
  /** 计算币种 */
  chargeCurrency?: string;
  /** 结算总价 */
  settlementTotal?: number;
  /** 结算币种 */
  settlementCurrency?: string;
  /** 仓库Code */
  warehouseCode?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 客户公司ID */
  companyId?: string;
  /** 客户公司 */
  companyName?: string;
  /** 结算对象ID */
  settlementTargeId?: string;
  /** 结算对象 */
  settlementTargetName?: string;
  /** 结算周期: 1-日结 */
  settlementInterval?: number;
  /** 计算时间 */
  calculateDate?: string;
  /** 创建人id */
  createById?: number;
  /** 创建人名称 */
  createByName?: string;
  /** 创建日期 */
  createDate?: string;
  /** 开票状态（0-待开票，1-已开票） */
  invoiceStatus?: number;
  /** 费用类别 */
  chargeType?: number;
  /** 订单创建时间 */
  orderCreateDate?: string;
  /** 订单完结时间 */
  complianceOverDate?: string;
  /** 费用结算时间 */
  settlementDate?: string;
  /** 发票号 */
  invoiceNo?: string;
  /** 发票链接 */
  invoiceUrl?: string;
  /** 下单仓库Code */
  createDeliveryWarehouseCode?: string;
  /** 下单仓库名称 */
  createDeliveryWarehouseName?: string;
};
