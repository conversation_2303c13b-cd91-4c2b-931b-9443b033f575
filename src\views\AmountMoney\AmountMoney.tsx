import React from 'react';
import { mathUtils, typeUtils } from '@/utils';
import classNames from 'classnames';
import './AmountMoney.less';

export type TypeAmountMoneyProps = {
  /** 正数样式 */
  positiveClassName?: string;
  /** 负数样式 */
  negativeClassName?: string;
  /** 零的样式 */
  zeroClassName?: string;
  /** 结果值 默认值 0.0000*/
  num?: number | string;
  /** 是否需要左对齐，默认左对齐 */
  isAlignLeft?: boolean;
  /** 小数位,默认4位 */
  decimals?: number;
  /** 千分位格式化,默认false 例如 8,999,999.9999 */
  isThousands?: boolean;
  /** * 使用外部继承样式，自定义样式不生效,默认使用自定义样式,此属性与自定义类名互斥 */
  noStyle?: boolean;
};
/** 处理正负零数展示，负数默认展示红色，正数、零展示默认颜色，数据默认居左对齐，不做四舍五入操作 */
export default function AmountMoney(props: TypeAmountMoneyProps) {
  const { isAlignLeft = true } = props;
  const result = backShowContent(props);

  return (
    <div
      className={
        !props.noStyle
          ? classNames('amount-money-item', result.className, isAlignLeft ? 'text-left' : undefined)
          : ''
      }
    >
      {result.num}
    </div>
  );
}

AmountMoney.defaultProps = {
  isThousands: false,
  noStyle: false,
};

AmountMoney.formatMoney = formatMoney;
AmountMoney.dealThousands = dealThousands;

/** 千分位处理 ,字符串、数字类型 */
function dealThousands(money: number | string) {
  if (typeUtils.isNumOrNumString(money) === false) return money;

  /** 分割的数组 */
  const splitList = String(money).split('.');

  /** 小数位不进行处理 */
  const afterPoint = splitList[1];
  /** 个位及以上进行千分位处理 */
  const beforePoint = toThousands(splitList[0]);

  return splitList.length > 1 ? [beforePoint, afterPoint].join('.') : money;
}

/** 处理千分位的方法 */
function toThousands(num?: number | string) {
  return (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
}
function formatMoney(money?: any) {
  if (typeUtils.isNumOrNumString(money) === false) {
    /** 非有效数字原样返回 */
    return money;
  }
  // return mathUtils.toFixed(money || 0, 4);
  return money;
}
/**
 * @description 区分正数、负数、零
 * @param num
 * @returns 1：大于0，0：等于0，-1：小于0，99：其它类型
 */
function handleNegative(num: number | string) {
  if (typeUtils.isNumOrNumString(num) === false) return 99;
  const newNum = Number(num);

  if (newNum > 0) {
    return 1;
  } else if (newNum === 0) {
    return 0;
  } else {
    return -1;
  }
}

/**
 * @description 返回处理结果
 * @param props num
 * @returns
 */
function backShowContent(props: TypeAmountMoneyProps) {
  const {
    positiveClassName,
    negativeClassName = 'normal-negative',
    zeroClassName,
    decimals = 4,
    num = '0.0000',
  } = props;
  const result = handleNegative(num);
  const newResult: { num?: string | number; className?: string } = {
    num: props?.isThousands ? dealThousands(formatMoney(num)) : formatMoney(num),
  };

  switch (result) {
    case 1:
      newResult.className = positiveClassName;
      break;
    case 0:
      newResult.className = zeroClassName;
      break;
    case -1:
      newResult.className = negativeClassName;
      break;
    default:
      break;
  }

  return newResult;
}
