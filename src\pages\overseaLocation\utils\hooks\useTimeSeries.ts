import { useState } from 'react';

/** 时间序列, 通常用于异步请求作废 */
export function useTimeSeries() {
  const [timeSeries] = useState(function () {
    /** 时间戳 */
    let timestamp: string | undefined;

    return {
      init() {
        timestamp = new Date().getTime().toString();
        return timestamp;
      },
      getTiming() {
        return timestamp;
      },
      /** 清理时间戳 */
      clear() {
        timestamp = undefined;
      },
      /** 判断不相等, 如果为undefined, 则始终不相等 */
      notEquals(val: any) {
        if (timestamp === undefined) {
          return true;
        }
        return timestamp !== val;
      },
    };
  });

  return { timeSeries };
}
