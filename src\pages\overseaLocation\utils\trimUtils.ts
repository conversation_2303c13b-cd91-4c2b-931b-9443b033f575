export const trimUtils = {
  /** trim 方法 */
  trim(inputValue?: any) {
    return typeof inputValue?.trim === 'function' ? inputValue.trim() : inputValue;
  },
  /**
   * 通用trim; 适配antd filterOption;
   * @ 支持label标签的搜索;
   * @ 目前支持组件 Transfer, select;
   */
  generalTrim: (inputValue: string, option: Record<string, string>, searchName = 'label') => {
    return option?.[searchName]?.indexOf(trimUtils.trim(inputValue)) > -1;
  },
};
