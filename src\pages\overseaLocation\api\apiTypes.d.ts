/** 中台费用项映射OMS系统费用项 */
type TypezChargeItem = {
  /** 费用项ID */
  id?: string;
  /** 中台费用项code */
  gmChargeCode?: string;
  /** 中台费用项名称 */
  gmChargeName?: string;
  /** 中台费用项英文名称 */
  gmChargeEnName?: string;
  /** OMS费用code */
  chargeCode?: string;
  /** OMS费用名称 */
  chargeName?: string;
  /** 费用类别： */
  chargeType?: number;
  /** 1-入库卸货费 */
  /** 2-订单出库 */
  /** 3-仓租 */
  /** 4-尾程快递 */
  /** 5-库内增值服务 */
  /** 备注 */
  remark?: string;
  /** 创建人id */
  createById?: string;
  /** 创建日期 */
  createDate?: string;
};

/** 账户详情 */
type TypeOverseaAccountDetail = {
  /** 主键id */
  id: string;
  /** 公司id */
  companyId?: string;
  /** 公司名 */
  companyName?: string;
  /** 客户币种 */
  customerCurrency?: string;
  /** 可用余额 */
  availableBalance?: number;
  /** 信用额度 */
  creditLimit?: number;
  /** 额度到期日 */
  creditLimitMaturityDate?: string;
  /** 冻结金额 */
  frozenAmount?: number;
  /** 累计充值金额 */
  cumulativeRechargeAmount?: number;
  /** 累计消费金额 */
  cumulativeConsumptionAmount?: number;
};
