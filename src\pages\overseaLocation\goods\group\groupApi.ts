import { request } from 'umi';

/** 组合商品列表 */
export function apiGoodsCombineList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** SKU */
      sku?: string;
      /** 商品名称 */
      name?: string;
      /** 公司id */
      companyId?: string;
      /** 公司名称 */
      companyName?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypeGoodsGroupRecord[]>>(
    '/zouwu-oms-goods/portal/goods/combined/list',
    {
      data,
      method: 'POST',
    },
  );
}

/** 组合商品新增 */
export function apiGoodsCombineCreate(data: TypeGoodsGroupFormData) {
  return request('/zouwu-oms-goods/portal/goods/combined/create', { data, method: 'POST' });
}

/** 组合商品编辑 */
export function apiGoodsCombineUpdate(data: TypeGoodsGroupFormData) {
  return request('/zouwu-oms-goods/portal/goods/combined/update', { data, method: 'POST' });
}

/** 组合商品详情 */
export function apiGoodsCombineDetail(data: { id?: string }) {
  return request<NsApi.TypeResponseData<TypeGoodsGroupFormData>>(
    '/zouwu-oms-goods/portal/goods/combined/get',
    { data, method: 'POST' },
  ).then((res) => {
    const detail = res.data;

    return {
      ...res,
      data: {
        ...detail,
        file: { url: detail.pictureUrl, name: detail.serverPictureName },
      },
    };
  });
}

/** 组合商品删除 */
export function apiGoodsCombineDelete(data: { id?: string }) {
  return request('/zouwu-oms-goods/portal/goods/combined/delete', { data, method: 'POST' });
}
