import I18N from '@/utils/I18N';
import { returnManage } from './return-manage';
import { batchManage } from './batch-manage';

const routes = [
  {
    path: '/oversea-location',
    title: I18N.Config.Router.chineseSymbols39,
    icon: 'BankOutlined',
    access: 'OVERSEA_MODULE',
    routes: [
      {
        path: '',
        redirect: './goods/single',
      },
      {
        path: 'goods',
        title: I18N.Config.Router.chineseSymbols6,
        access: 'OVERSEA_GOODS_MANAGER',
        routes: [
          {
            path: '',
            redirect: './single',
          },
          {
            path: 'single',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols26,
            component: '@/pages/overseaLocation/goods/list',
            access: 'OVERSEA_GOODS_LIST',
          },
          {
            path: 'single/action',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols25,
            hideInMenu: true,
            component: '@/pages/overseaLocation/goods/action',
          },
          {
            path: 'group',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols24,
            component: '@/pages/overseaLocation/goods/group/list',
            access: 'OVERSEA_COMPGOODS_LIST',
          },
          {
            path: 'group/action',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols23,
            hideInMenu: true,
            component: '@/pages/overseaLocation/goods/group/action',
          },
        ],
      },
      // {
      //   path: 'enter-warehouse',
      //   title:  '入库管理',
      //   component: '@/pages/overseaLocation/enterWarehouse/list',
      //   access: 'OVERSEA_IN_STOCK_MANAGER',

      //   // routes: [
      //   //   {
      //   //     path: 'action/:id?',
      //   //     title:  '入库',
      //   //     hideInMenu: true,
      //   //     component: '@/pages/overseaLocation/enterWarehouse/action',
      //   //   },
      //   //   {
      //   //     path: 'detail/:id',
      //   //     title:  '入库订单详情',
      //   //     hideInMenu: true,
      //   //     component: '@/pages/overseaLocation/enterWarehouse/detail',
      //   //   },
      //   // ],
      // },
      {
        path: 'enter-warehouse',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols22,
        component: '@/pages/overseaLocation/enterWarehouse/EnterWarehouseList',
        locale: false,
        access: 'OVERSEA_IN_STOCK_MANAGER',
      },
      {
        /** 编辑入库页面 */
        path: 'enter-warehouse/action',
        title: I18N.Config__Routes.OverseaLocation.warehousing,
        hideInMenu: true,
        component: '@/pages/overseaLocation/enterWarehouse/action',
      },
      {
        /** 新增入库页面 */
        path: 'enter-warehouse/action-create',
        title: I18N.Config__Routes.OverseaLocation.warehousing,
        hideInMenu: true,
        component: '@/pages/overseaLocation/enterWarehouse/action',
      },
      {
        path: 'enter-warehouse/detail',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols21,
        hideInMenu: true,
        component: '@/pages/overseaLocation/enterWarehouse/detail',
      },
      {
        path: 'out-warehouse',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols20,
        access: 'OVERSEA_OUT_STOCK_MANAGER',
        routes: [
          {
            path: '',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols20,
            component: '@/pages/overseaLocation/outWarehouse/OutWarehouseList',
          },
          {
            path: 'outbound-cost-calc',
            title: I18N.Src__Routes.Index.chineseSymbols4,
            hideInMenu: true,
            component: '@/pages/overseaLocation/outWarehouse/OutboundCostCalc/OutboundCostCalc',
          },
        ],
      },
      {
        path: 'out-warehouse/outbound-create',
        title: I18N.Config__Routes.OverseaLocation.issue,
        hideInMenu: true,
        component: '@/pages/overseaLocation/outWarehouse/OutboundEdit',
      },
      {
        path: 'out-warehouse/outbound-edit',
        title: I18N.Config__Routes.OverseaLocation.issue,
        hideInMenu: true,
        component: '@/pages/overseaLocation/outWarehouse/OutboundEdit',
      },
      {
        path: 'out-warehouse/detail',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols19,
        hideInMenu: true,
        component: '@/pages/overseaLocation/outWarehouse/detail',
      },
      {
        path: 'out-warehouse/outbound-compensate-detail',
        title:
          I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalCompensate
            .ModalCompensate.chineseSymbols16,
        hideInMenu: true,
        component: '@/pages/overseaLocation/outWarehouse/OutboundCompensateDetail',
      },
      // {
      //   path: 'performance',
      //   title:  '仓库履约',
      //   component: '@/pages/overseaLocation/performance',
      // },
      /** 退货管理 */
      ...returnManage,
      {
        path: 'inventory',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols18,
        access: 'OVERSEA_STOCK_MANAGER',
        routes: [
          {
            path: '',
            redirect: './list',
          },
          {
            path: 'list',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols18,
            component: '@/pages/overseaLocation/inventory/list',
            access: 'OVERSEA_STOCK_MANAGER_LIST',
          },
          {
            path: 'trade/flow',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols17,
            component: '@/pages/overseaLocation/inventory/flow/list',
            access: 'OVERSEA_STOCK_TRADE_FLOW',
          },
          {
            path: 'detail',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols16,
            component: '@/pages/overseaLocation/inventory/detail/list',
            access: 'OVERSEA_STOCK_AGE_MANAGER',
          },
        ],
      },
      /** 批量管理 */
      ...batchManage,
      {
        path: 'funds-manage/funds-list',
        title: I18N.Src__Routes.Index.chineseSymbols2,
        locale: false,
        component: '@/pages/overseaLocation/fundsManage/FundsAccount/FundsAccountList',
        access: 'OVERSEA_FUNDS_ACCOUNT_LIST',
      },
      {
        path: 'cash-flow',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols12,
        locale: false,
        component: '@/pages/overseaLocation/cashFlow/cashFlowList',
        access: 'OVERSEA_CASH_CASHFLOWLIST',
      },
      {
        path: 'charge-manage',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols11,
        access: 'OVERSEA_CHARGE_MANAGE',
        locale: false,
        routes: [
          {
            path: '',
            redirect: './charge-bill',
          },
          {
            path: 'charge-bill',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols10,
            locale: false,
            component: '@/pages/overseaLocation/chargeManage/chargeBillList',
            access: 'OVERSEA_CHARGE_MANAGE_CHARGEBILLLIST',
          },
          {
            path: 'charge-warehouse',
            title: I18N.Config__Routes.OverseaLocation.warehouseRent,
            locale: false,
            component: '@/pages/overseaLocation/chargeManage/chargeWarehouse',
            access: 'OVERSEA_CHARGE_MANAGE_CHARGEWAREHOUSE',
          },
        ],
      },
      {
        path: 'posting-key',
        title: I18N.Config__Routes.OverseaLocation.customerBookkeeping,
        access: 'OVERSEA_CUSTOMER_ACCOUNT_CODE',
        component: '@/pages/overseaLocation/postingKey/PostingKeyList',
        locale: false,
      },
      // 数据报表
      {
        path: 'dtc-report',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols9,
        access: 'OVERSEA_DTC_REPORT',
        locale: false,
        routes: [
          {
            path: '',
            redirect: './dtc-basic',
          },
          {
            path: 'dtc-basic',
            title: I18N.Config__Routes.OverseaLocation.productInformation,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_BASIC_GOODS',
            access: 'OVERSEA_DTC_REPORT_BASIC',
          },
          {
            path: 'dtc-in-order',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols8,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_INBOUND_ORDER',
            access: 'OVERSEA_DTC_REPORT_IN_ORDER',
          },
          {
            path: 'dtc-out-order',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols7,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_OUTBOUND_ORDER',
            access: 'OVERSEA_DTC_REPORT_OUT_ORDER',
          },
          {
            path: 'dtc-outbound-tob',
            title: I18N.Src__Routes.Index.chineseSymbols3,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_TOB_OUTBOUND',
            access: 'OVERSEA_DTC_REPORT_TOB_OUTBOUND',
          },
          {
            path: 'dtc-in-stock',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols6,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_INVENTORY',
            access: 'OVERSEA_DTC_REPORT_IN_STOCK',
          },
          {
            path: 'dtc-library-age',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols5,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_PORTAL_INVENTORY_DETAIL',
            access: 'OVERSEA_DTC_REPORT_LIBRARY_AGE',
          },
          {
            path: 'dtc-warehouse-stockpiling',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols4,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            // dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_PORTAL_REPORT_2',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_18',
            access: 'OVERSEA_DTC_REPORT_WAREHOUSE_STOCKPILING',
          },
          {
            path: 'dtc-final-delivery-fee',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols3,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            // dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_PORTAL_REPORT_3',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_22',
            access: 'OVERSEA_DTC_REPORT_FINAL_DELIVERY_FEE',
          },
          {
            path: 'outbound-operation-fee',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols2,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            // dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_PORTAL_REPORT_4',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_20',
            access: 'OVERSEA_DTC_REPORT_OUTBOUND_OPERATION_FEE',
          },
          {
            path: 'warehouse-loading-fee',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols1,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            // dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_PORTAL_REPORT_5',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_21',
            access: 'OVERSEA_DTC_REPORT_WAREHOUSE_LOADING_FEE',
          },
          {
            path: 'tally-listing-fee',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            // dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_PORTAL_REPORT_6',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_19',
            access: 'OVERSEA_DTC_REPORT_TALLY_LISTING_FEE',
          },
          {
            path: 'independent-settlement',
            title: I18N.Src__Routes.Index.chineseSymbols,
            locale: false,
            component: '@/pages/overseaLocation/dtcReport/DtcReportBasic',
            dtcReportPath: '/gm2-datacenter-front/#/external/report/DTC_BASIC_REPORT_12',
            access: 'OVERSEA_DTC_REPORT_INDEPENDENT_SETTLEMENT',
          },
        ],
      },
    ],
  },
];

export default routes;
