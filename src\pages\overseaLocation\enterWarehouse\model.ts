import type { Effect, Reducer } from '@umijs/max';
import {
  queryOverseaLocationGoods,
  queryEnterBoundOrderList,
  queryEnterBoundOrderDetail,
  sendCreateEnterBoundOrder,
  sendSaveEnterBoundOrder,
  sendEditEnterBoundOrder,
  sendCancelEnterBoundOrder,
  sendDeleteEnterBoundOrder,
  sendConfirmOrderReceived,
  sendConfirmSingleOrderReceived,
  queryWarehousePerformanceExecutionDetail,
  queryPrintDocumentList,
} from '@/service/overseaLocation';

export interface StateType {
  entryWarehouseList: any[];
  entryWarehouseTotal: number;
  entryWarehouseStatusCount: any;
}

export interface Modal {
  namespace: string;
  state: StateType;
  effects: {
    getGoodsList: Effect;
    getEnterBoundOrderList: Effect;
    getEnterBoundOrderDetail: Effect;
    createEnterBoundOrder: Effect;
    saveEnterBoundOrder: Effect;
    editEnterBoundOrder: Effect;
    cancelEnterBoundOrder: Effect;
    deleteEnterBoundOrder: Effect;
    confirmOrderReceived: Effect;
    confirmSingleOrderReceived: Effect;
    getWarehousePerformanceExecutionDetail: Effect;
    getPrintDocumentList: Effect;
  };
  reducers: {
    saveGoodsList: Reducer<any>;
    saveEnterWarehouseList: Reducer<any>;
  };
}

const Model: Modal = {
  namespace: 'entry_warehouse',
  state: {
    entryWarehouseList: [],
    entryWarehouseTotal: 0,
    entryWarehouseStatusCount: {},
  },
  effects: {
    *getGoodsList({ payload }, { call, put }) {
      const response = yield call(queryOverseaLocationGoods, payload);

      if (response && response.result) {
        if (response.success) {
          return {
            data:
              response.result.records && response.result.records.length
                ? convertGoodsRecord(response.result.records)
                : [],
            total:
              response.result && response.result.totalSize ? Number(response.result.totalSize) : 0,
          };
        }
      }
    },
    *getEnterBoundOrderList({ payload }, { call, put }) {
      const response = yield call(queryEnterBoundOrderList, payload);

      if (response && response.result) {
        yield put({
          type: 'saveEnterWarehouseList',
          payload: response.result,
        });
      }
    },
    /** 入库详情, 草稿单不用, 从apiInboundDraftDetail 获取 */
    *getEnterBoundOrderDetail({ payload }, { call }) {
      const response = yield call(queryEnterBoundOrderDetail, payload);

      if (response && response.result) {
        return response.result;
      }
    },
    /** @deprecated 已废弃迁移至OMS, apiInboundOrderSubmit */
    *createEnterBoundOrder({ payload }, { call }) {
      const response = yield call(sendCreateEnterBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    /** @deprecated 已废弃迁移至OMS, apiInboundDraftCreate */
    *saveEnterBoundOrder({ payload }, { call }) {
      const response = yield call(sendSaveEnterBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    /** @deprecated 已废弃迁移至OMS, apiInboundDraftEdit */
    *editEnterBoundOrder({ payload }, { call }) {
      const response = yield call(sendEditEnterBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    /** @deprecated 已废弃迁移至OMS */
    *cancelEnterBoundOrder({ payload }, { call }) {
      const response = yield call(sendCancelEnterBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    /** @deprecated 已废弃迁移至OMS */
    *deleteEnterBoundOrder({ payload }, { call }) {
      const response = yield call(sendDeleteEnterBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    *confirmOrderReceived({ payload }, { call }) {
      const response = yield call(sendConfirmOrderReceived, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    *confirmSingleOrderReceived({ payload }, { call }) {
      const response = yield call(sendConfirmSingleOrderReceived, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    *getWarehousePerformanceExecutionDetail({ payload }, { call, put }) {
      const response = yield call(queryWarehousePerformanceExecutionDetail, payload);

      if (response && response.result) {
        return response.result;
      }
    },
    *getPrintDocumentList({ payload }, { call, put }) {
      const response = yield call(queryPrintDocumentList, payload);

      if (response && response.result) {
        return response.result;
      }
    },
  },
  reducers: {
    saveGoodsList(state, { payload }) {
      return {
        ...state,
        warehouseList: payload.records || [],
      };
    },
    saveEnterWarehouseList(state, { payload }) {
      return {
        ...state,
        entryWarehouseList: payload.page.records || [],
        entryWarehouseTotal:
          payload.page && payload.page.totalSize ? Number(payload.page.totalSize) : 0,
        entryWarehouseStatusCount: payload.countStatus,
      };
    },
  },
};

export default Model;

const convertGoodsRecord = (records: any[]) => {
  const getPriceValue = (data: any[]) => {
    if (data && data.length) {
      const target = data[0];

      return {
        goodsValue: target.goodsDeclaredValue,
        valueUnit: target.currency,
      };
    }

    return {};
  };

  return records.map((o) => ({
    sku: o.sku,
    // id: o.id,
    goodsPicture: o.pictureUrl,
    goodsName: o.name,
    goodsWeight: o.weight,
    weightUnit: o.weightUnit,
    goodsLength: o.length,
    goodsWidth: o.width,
    goodsHeight: o.height,
    volumeUnit: o.measureUnit,
    ...getPriceValue(o.goodsCustomsInformationList),
  }));
};
