import I18N from '@/utils/I18N';

/** 批量管理 */
export const batchManage = [
  {
    path: 'import-manage',
    /* 产品要改名字, 导入管理变更批量管理 */
    title: I18N.Src__Routes.Batch_manage.chineseSymbols1,
    locale: false,
    access: 'OVERSEA_IMPORT_MANAGER',
    routes: [
      {
        path: '',
        redirect: './batch-out-warehouse',
      },
      {
        path: 'batch-out-warehouse',
        title: I18N.Config__Routes.OverseaLocation.chineseSymbols14,
        locale: false,
        hideChildrenInMenu: true,
        access: 'OVERSEA_IMPORT_MANAGER_BATCH_OUT_WAREHOUSE',
        routes: [
          {
            path: '',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols14,
            locale: false,
            component: '@/pages/overseaLocation/importManage/batchOutWarehouse/batchOutList',
          },
          {
            path: 'batch-order-detail',
            title: I18N.Config__Routes.OverseaLocation.chineseSymbols13,
            locale: false,
            component: '@/pages/overseaLocation/importManage/batchOutWarehouse/batchOrderDetail',
          },
        ],
      },
      {
        path: 'import-list',
        title: I18N.Src__Routes.Batch_manage.chineseSymbols,
        access: 'OVERSEA_BATCH_IMPORT_LIST',
        component: '@/pages/overseaLocation/importManage/ImportList/ImportList',
      },
      {
        path: 'export-manage',
        title: I18N.Src__Routes.Index.chineseSymbols1,
        access: 'OVERSEA_EXPORT_MANAGE_LIST',
        component: '@/pages/overseaLocation/exportManage/ExportList/ExportList',
        locale: false,
      },
    ],
  },
];
