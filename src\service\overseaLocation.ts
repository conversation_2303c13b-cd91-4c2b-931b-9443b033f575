import { request } from 'umi';

/**
 * 获取仓库列表
 * @param data
 * @returns
 * @deprecated 请使用 apiQueryWarehouseOptions
 */
export const queryWarehouseList = async (data: any) => {
  return request('/api/website/web/instorehouse/warehouseGetList.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取海外仓配送渠道
 * @param data
 * @returns
 */
export const queryDispatchServiceList = async (data: any) => {
  return request('/api/website/web/zouwu/getDropDownList.do', {
    method: 'POST',
    data,
  });
};

/**
 * 快递对接，获取配送类型、配送名称，树形结构
 * @param data
 * @returns
 * @deprecated 废弃,  改用 apiCustomerChannelTree
 */
export const queryDistributionChannel = async (data: any) => {
  return request('/api/website/web/zouwu/getDcChannelTree.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取申请单号
 * @param data
 * @returns
 * @deprecated 已废弃迁移至OMS
 */
export const queryPodNo = async (data: any) => {
  return request('/api/website/web/instorehouse/getPodNo.do', {
    method: 'POST',
    data,
  });
};

/**
|--------------------------------------------------
|  海外仓中心-入库订单
|--------------------------------------------------
*/

/**
 * 获取入库单申请列表
 * @param data
 * @returns
 */
export const queryEnterBoundOrderList = async (data: any) => {
  return request('/api/website/web/instorehouse/queryDoorOrder.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取入库单申请详情
 * @param data
 * @returns
 */
export const queryEnterBoundOrderDetail = async (data: any) => {
  const { podOrderNo } = data;

  // const url =
  //   podOrderNo !== undefined
  //     ? '/api/website/web/instorehouse/orderDoorInfoByPodOrderNo.do'
  //     : '/zouwu-oms-order/portal/order/inbound/orderDoorInfo';

  return request('/zouwu-oms-order/portal/order/inbound/orderDoorInfo', {
    method: 'POST',
    data: {
      id: podOrderNo !== undefined ? undefined : data.id,
      podOrderNo,
    },
  }).then((res) => {
    return {
      ...res,
      result: res.result || res.data,
    };
  });
};

/**
 * 新增入库订单
 * @param data
 * @returns
 * @deprecated 废弃
 */
export const sendCreateEnterBoundOrder = async (data: any) => {
  return request('/api/website/web/instorehouse/createOrder.do', {
    method: 'POST',
    data,
  });
};

/**
 * 暂存入库订单
 * @param data
 * @returns
 * @deprecated 废弃
 */
export const sendSaveEnterBoundOrder = async (data: any) => {
  return request('/api/website/web/instorehouse/createOrderDraft.do', {
    method: 'POST',
    data,
  });
};

/**
 * 编辑入库订单
 * @param data
 * @returns
 * @deprecated 废弃
 */
export const sendEditEnterBoundOrder = async (data: any) => {
  return request('/api/website/web/instorehouse/edit.do', {
    method: 'POST',
    data,
  });
};

/**
 * 整单取消入库订单
 * @param data
 * @returns
 * @deprecated 已废弃迁移至OMS
 */
export const sendCancelEnterBoundOrder = async (data: any) => {
  return request('/api/website/web/instorehouse/cancelOrder.do', {
    method: 'POST',
    data,
  });
};

/**
 * 删除入库草稿订单
 * @param data
 * @returns
 * @deprecated 已废弃迁移至OMS
 */
export const sendDeleteEnterBoundOrder = async (data: any) => {
  return request('/api/website/web/instorehouse/delete.do', {
    method: 'POST',
    data,
  });
};

/**
 * 整单确认到货
 * @param data
 * @returns
 */
export const sendConfirmOrderReceived = async (data: any) => {
  return request('/api/website/web/instorehouse/confirmOrderReceived.do', {
    method: 'POST',
    data,
  });
};

/**
 * 单条确认到货
 * @param data
 * @returns
 */
export const sendConfirmSingleOrderReceived = async (data: any) => {
  return request('/api/website/web/instorehouse/confirmReceivedRecord.do', {
    method: 'POST',
    data,
  });
};

/**
 * 打印箱唛
 * @param data
 * @returns
 */
export const queryPrintDocumentList = async (data: {
  podOrderNo: string;
  complianceNos: string[];
}) => {
  return request('/api/website/web/outstorehouse/getDocumentUrl.do', {
    method: 'POST',
    data,
  });
};
/**
|--------------------------------------------------
|  海外仓中心-出库订单
|--------------------------------------------------
*/
/**
 * 获取出库单列表
 * @param data
 * @returns
 */
export const queryOutBoundOrderList = async (data: any) => {
  return request('/api/website/web/outstorehouse/list.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单详情
 * @param data
 * @returns
 * @deprecated 已废弃, 迁移至apiOutWarehouseGetFormData
 */
export const queryOutBoundOrderForm = async (data: any) => {
  return request('/api/website/web/outstorehouse/getForm.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单提交表单信息
 * @param data
 * @returns
 * @deprecated 已废弃, 迁移至apiOutWarehouseDetail
 */
export const queryOutBoundOrderDetail = async (data: any) => {
  const { podOrderNo } = data;

  console.log('data-->', data);
  const url =
    podOrderNo !== undefined
      ? '/api/website/web/outstorehouse/getDetailsByPodOrderNo.do'
      : '/api/website/web/outstorehouse/getDetails.do';

  return request(url, {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 提交订单
 * @param data
 * @returns
 * @deprecated 已废弃, 出库提交统一接口apiOutWarehouseSubmit
 */
export const sendCreateOutBoundOrder = async (data: any) => {
  return request('/api/website/web/outstorehouse/submit.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 编辑订单
 * @param data
 * @returns
 */
export const sendEditOutBoundOrder = async (data: any) => {
  return request('/api/website/web/outstorehouse/edit.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 保存草稿
 * @param data
 * @returns
 * @deprecated 已废弃, 统一为apiOutWarehouseSaveDraft
 */
export const sendSaveOutBoundOrder = async (data: any) => {
  return request('/api/website/web/outstorehouse/draft.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 取消订单
 * @param data
 * @returns
 * @deprecated 已废弃
 */
export const sendCancelOutBoundOrder = async (data: any) => {
  return request('/api/website/web/outstorehouse/cancel.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 删除订单
 * @param data
 * @returns
 * @deprecated 已废弃迁移至OMS
 */
export const sendDeleteOutBoundOrder = async (data: any) => {
  return request('/api/website/web/outstorehouse/delete.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库侧出库单 - 查询表单信息
 * @param data
 * @returns
 * @deprecated 已废弃
 */
export const queryOutBoundOrderFormData = async (data: any) => {
  return request('/api/website/web/outstorehouse/getForm.do', {
    method: 'POST',
    data,
  });
};

/**
|--------------------------------------------------
|  海外仓中心-仓库履约
|--------------------------------------------------
*/

/**
 * 获取仓库履约订单
 * @param data
 * @returns
 */
export const queryWarehousePerformanceList = async (data: any) => {
  return request('/api/website/web/instorehouse/queryWarehouseOrder.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取仓库履约详情
 * @param data
 * @returns
 */
export const queryWarehousePerformanceDetail = async (data: { podOrderNo: string }) => {
  return request('/api/website/web/instorehouse/getByGoodsDoorList.do', {
    method: 'POST',
    data,
  });
};

export const queryWarehousePerformanceSignOrderGoods = async (data: {
  findSignOrderGoods: string;
}) => {
  return request('/api/website/web/instorehouse/findSignOrderGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取执行明细详情
 * @param data
 * @returns
 * @deprecated 接口废弃, 改用 apiInboundDoorExecuteInfoByGoods
 */
export const queryWarehousePerformanceExecutionDetail = async (data: { id: string }) => {
  return request('/api/website/web/instorehouse/doorExecuteInfoByGoods.do', {
    method: 'POST',
    data,
  });
};

// 商品
/**
 * 获取商品列表
 * @param data
 * @deprecated 废弃, 改用apiGoodsList
 */
export const queryOverseaLocationGoods = async (data: any) => {
  return request('/api/website/web/goodsFacade/listByUser.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取商品下拉列表
 * @param data
 * @deprecated 已废弃迁移至OMS
 */
export const queryOverseaLocationGoodsList = async (data: any) => {
  return request('/api/website/web/goodsFacade/list.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取商品列表审核统计数据
 * @param data
 */
export const queryOverseaLocationGoodsStatData = async (data: any = {}) => {
  return request('/api/website/web/goodsFacade/getRedNums.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取商品详情
 * @param data
 * @deprecated  商品详情接口废弃, 改用 apiGoodsDetails
 */
export const queryOverseaLocationGoodsDetail = async (data: any) => {
  return request('/api/website/web/goodsFacade/getDetailsById.do', {
    method: 'POST',
    data,
  });
};

/**
 * 创建商品
 * @param data
 * @deprecated 商品新增编辑废弃
 */
export const sendCreateOverseaLocationGoods = async (data: any) => {
  return request('/api/website/web/goodsFacade/createGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 更新商品
 * @param data
 * @deprecated 商品新增编辑废弃
 */
export const sendUpdateOverseaLocationGoods = async (data: any) => {
  return request('/api/website/web/goodsFacade/updateGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 删除商品
 * @param data
 * @deprecated 入口已隐藏
 */
export const sendDeleteOverseaLocationGoods = async (data: any) => {
  return request('/api/website/web/goodsFacade/deleteById.do', {
    method: 'POST',
    data,
  });
};

/**
 * 查询商品是否已下单，是否绑定到组合商品
 * @param data
 */
export const queryGoodsRelaInfo = async (data: any) => {
  return request('/api/website/web/goodsFacade/getByGoodsId.do', {
    method: 'POST',
    data,
  });
};

// 组合商品
/**
 * 获取组合商品列表
 * @param data
 * @deprecated 已废弃迁移至OMS
 */
export const queryOverseaLocationGoodsGroup = async (data: any) => {
  return request('/api/website/web/goodsFacade/listCombinedGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 获取组合商品详情
 * @param data
 * @deprecated 已废弃迁移至OMS
 */
export const queryOverseaLocationGoodsGroupDetail = async (data: any) => {
  return request('/api/website/web/goodsFacade/getCombinedGoodsById.do', {
    method: 'POST',
    data,
  });
};

/**
 * 创建组合商品
 * @param data
 * @deprecated 迁移废弃
 */
export const sendCreateOverseaLocationGoodsGroup = async (data: any) => {
  return request('/api/website/web/goodsFacade/createCombinedGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 更新组合商品
 * @param data
 * @deprecated 迁移废弃
 */
export const sendUpdateOverseaLocationGoodsGroup = async (data: any) => {
  return request('/api/website/web/goodsFacade/updateCombinedGoods.do', {
    method: 'POST',
    data,
  });
};

/**
 * 删除组合商品
 * @param data
 * @deprecated 已废弃迁移至OMS
 */
export const sendDeleteOverseaLocationGoodsGroup = async (data: any) => {
  return request('/api/website/web/goodsFacade/deleteCombinedGoodsById.do', {
    method: 'POST',
    data,
  });
};

// 库存管理

/**
 * 获取库存管理列表
 * @param data
 * @returns
 */
export const queryOverseaLocationInventoryList = async (data: any) => {
  return request('/api/website/web/inventoryFacade/getPageList.do', {
    method: 'post',
    data,
  });
};

/**
 * 获取组合商品库存管理列表
 * @param data
 * @returns
 */
export const queryOverseaLocationCombinedGoodsInventoryList = async (data: any) => {
  return request('/api/website/web/inventoryFacade/getCombinedGoodsPageList.do', {
    method: 'post',
    data,
  });
};

/**
 * 获取库龄管理列表
 * @param data
 * @returns
 */
export const queryOverseaLocationInventoryDetailList = async (data: any) => {
  return request('/api/website/web/inventoryFacade/getDetailPageList.do', {
    method: 'post',
    data,
  });
};

/**
 * 获取库存交易流水列表
 * @param data
 * @returns
 */
export const queryOverseaLocationInventoryFlowList = async (data: any) => {
  return request('/api/website/web/inventoryFacade/getRecordPageList.do', {
    method: 'post',
    data,
  });
};
