export default {
  ContainerModal: {
    containerTrain: '集装箱列表',
    volume: '体积',
    volume1: '{val1}',
    grossWeight: '毛重',
    grossWeight1: '{val1}',
    number: '件数',
    number1: '{val1}',
    packagingType: '包装类型',
    packagingType1: '{val1}',
    title: '封号',
    title1: '{val1}',
    caseNo: '{val1}',
    boxType: '箱型',
  },
  MergeSiModal: {
    joinLeft: '加入左侧',
    joinRight: '加入右侧',
    consolidatedWithdrawal: '已合并提单',
    consolidatedWithdrawal1: '可合并提单',
    selectAll: '全选',
    matchingConditions: '匹配条件：',
    submitAndConsolidate: '提交并单',
    consolidatedSingleOption: '并单选项框',
    timeFiled: '签发时间',
    sailingDay: '开船日',
    payer: '付款方',
    operationAgent: '操作代理',
    solicitingAgent: '揽货代理',
    placeOfIssue: '签发地',
    issueABillOfLading: '签发提单数量',
    issueABillOfLading1: '签发提单类型',
    forwardTransportation: '前程运输方式',
    currency: '币别',
    placeOfPayment: '付款地点',
    paymentMethod: '付款方式',
    transportationTerms: '运输条款',
    typeOfGoods: '货物类型',
    shipSecretaryNo: '船司约号',
    bookingAgent: '订舱代理',
  },
  Modals: {
    clickOrDrag: '单击或拖动文件到此上传单证类型',
    pleaseSelectAList: '请选择单证类型后上传',
    documentType: '单证类型',
    pleaseSendAMessage: '请上传文件',
    uploadSucceeded: '上传成功',
    uploadSucceeded1: '{val1}{val2}',
    chargebackNo: '已退单不能上传ISF/AMS附件',
    noAfterTheShipLeaves: '船开后不能上传船证明',
    clickOrDrag1: '单击或拖动文件到此导入模版',
    templateDownload: 'VGM模版下载',
  },
};
