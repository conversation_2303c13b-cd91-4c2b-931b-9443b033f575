/* eslint-disable guard-for-in */
import React from 'react';
import { message, notification, Typography } from 'antd';
import type { RequestOptions, AxiosResponse, RequestError } from '@@/plugin-request/request';
import { HiveModule } from '@portal/hive-sdk';
import type { AxiosError } from 'umi';
import I18N from '@/utils/I18N';
import { RemoveAllStorage } from './utils/cache';
import { BASE_API_PREFIX } from '@/constants';
import { cssMessage } from '@/styles';

const WEB_USER_NOT_LOGIN = 101001;
const WEB_USER_EXPIRED = 102107;

const codeMessage = {
  200: I18N.Src.Request.server1,
  201: I18N.Src.Request.newOrRepair,
  202: I18N.Src.Request.oneRequest,
  204: I18N.Src.Request.deleteData,
  400: I18N.Src.Request.requestSent1,
  401: I18N.Src.Request.noUser,
  403: I18N.Src.Request.usersGet,
  404: I18N.Src.Request.requestSent,
  405: I18N.Src.Request.requestMethod,
  406: I18N.Src.Request.requestedCase,
  410: I18N.Src.Request.requestedCapital,
  413: I18N.Src.Request.fileSize,
  422: I18N.Src.Request.whenCreatingA,
  500: I18N.Src.Request.serverSends,
  502: I18N.Src.Request.badGateway,
  503: I18N.Src.Request.serviceUnavailable,
  504: I18N.Src.Request.gatewayTimeout,
};

/** OMS 链路字段名 和门户不一样;
 * OMS: traceId
 * 门户: trackId
 */
function renderErrorMessage(error: string | undefined, traceId: string | undefined) {
  return (
    <Typography.Text
      copyable={{
        text: traceId,
        tooltips: [I18N.Src.Request.chineseSymbols1, I18N.Src.Request.chineseSymbols],
      }}
    >
      <div className={cssMessage.responseError}>{error}</div>
    </Typography.Text>
  );
}

/**
 * 异常处理程序
 */
export const errorHandler = (error: RequestError) => {
  const { response } = error as AxiosError;

  /** 如果异常是标准格式, 跳提示 */
  if ((error as any as AxiosResponse)?.data?.code?.length >= 6) {
    const { data } = error as any as AxiosResponse;

    message.error(renderErrorMessage(data?.message, data?.traceId));
    return;
  }

  if (response && response.status) {
    const errorText =
      codeMessage[response.status as keyof typeof codeMessage] || response.statusText;
    const { status, config } = response;

    switch (Number(status)) {
      case 401:
        message.info(I18N.Src.Request.pleaseLogInAgain);
        break;
      case 403:
        RemoveAllStorage();

        window.location.reload();
        break;
      default:
        notification.error({
          message: I18N.template(I18N.Src.Request.requestError1, {
            val1: status,
            val2: config?.url,
          }),
          description: errorText,
        });
        break;
    }
  }
};

export const responseInterceptors = (response: AxiosResponse) => {
  const contentType = response.headers['content-type'] || response.headers['Content-Type'];

  if (contentType && contentType.match(/application\/json/i)) {
    const { data: content } = response as any as NsApi.TypeResponseData;
    const { success, errorCode = '', errorMsg } = content || {};
    const isZouWuOmsApi = response.config?.url?.startsWith('/zouwu-oms');

    /** 如果返回结果code 长度>=6, 认为是OMS侧结果 */
    if (isZouWuOmsApi && content.code?.length >= 6) {
      /** OMS 侧返回结果 */
      if (content.code === '000000') {
        return response;
      }
      return Promise.reject(response) as any;
    }

    try {
      if (success !== undefined && !success) {
        if (errorCode === WEB_USER_NOT_LOGIN) {
          // return;
          HiveModule.history.replace({
            path: '/user/login',
            search: {
              redirect: window.location.hash.split('#')[1],
            },
          });
        }
        if (errorCode === WEB_USER_EXPIRED) {
          window.location.reload();
        }
        throw new Error(
          errorMsg || I18N.template(I18N.Src.Request.requestError, { val1: errorCode }),
        );
      } else if (content?.code?.length === 6) {
        // 如果code 长度是6, 尝试打印message
        content.message && message.error(content.message);
      }
    } catch (errMsg: any) {
      message.error(renderErrorMessage(errMsg?.message, content?.trackId));
    }
  }

  return response;
};

export const SPLIC_URL = (url: string) => {
  // 非本地开发环境 根据BASE_API配置增加url前缀
  if (BASE_API_PREFIX && url) {
    return `/${BASE_API_PREFIX}${url}`;
  }

  return url;
};

export const requestInterceptors = (config: RequestOptions) => {
  const { url, method, data } = config;
  let uploadConfig: RequestOptions = {};

  const isZouWuOmsApi = url?.startsWith('/zouwu-oms');

  if (method?.toUpperCase() === 'UPLOAD') {
    const formData = new FormData();

    Object.keys(data).forEach((key) => {
      formData.append(key, data[key]);
    });

    uploadConfig = {
      data: formData,
      method: 'POST',
      headers: {
        'Content-type': 'multipart/form-data',
      },
    };
    /* return {
      ...config,
      url: SPLIC_URL(url!),
      method: 'POST',
      body: formData,
      headers: {
        'Content-type': 'multipart/form-data',
      },
      skipErrorHandler: true,
    }; */
  }

  return {
    ...config,
    url: isZouWuOmsApi ? url : SPLIC_URL(url!),
    skipErrorHandler: true,
    ...uploadConfig,
    headers: {
      ...config.headers,
      ...uploadConfig.headers,
      /** 标记门户发起的接口请求 */
      portal: 'wwl-zouwu-front',
    },
  };
};
