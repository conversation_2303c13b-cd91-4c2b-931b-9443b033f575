/* eslint-disable max-params */
import React from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import { SearchSelect } from '@/components';
import { AmountMoney } from '@/views';
import { merge } from '../utils-three';
import { transRangeDate } from '../dateFormat';
import { textMap } from './text-map';

/**
 * 文件和OMS侧保持同步
 *
 */

const valueTypeList = [
  'searchSelect',
  'select',
  'serialNumber',
  'option',
  'dateRange',
  'dateRange2',
  'dateTimeRange',
  'amountMoney',
] as const;
const valueTypeMap: Record<typeof valueTypeList[number], ProColumns<any, any>> = {
  searchSelect: {
    valueType: 'searchSelect',
    fieldProps: {
      placeholder: textMap.txtPleaseSelect,
    },
    renderFormItem: (schema) => {
      return <SearchSelect {...(schema.fieldProps as any)} />;
    },
  },
  select: {
    fieldProps: {
      /** 默认不开启搜索功能,如有需要使用 searchSelect */
      showSearch: false,
    },
  },
  serialNumber: {
    /**  仅用于request 获取数据场景
     * @ 在dataSource 直传数据下, 不准, 请使用 renderTableSerialNumber */
    // 从1跟随翻页递增序号,
    renderText: (text, record, index, action) => {
      const { pageInfo: { current = 1, pageSize = 0 } = {} } = action;

      return pageSize * (current - 1) + index + 1;
    },
  },
  option: {
    valueType: 'option',
    /** 操作列默认样式,永不换行 */
    className: 'table-cell-option',
  },
  dateRange: {
    width: 160,
    render: (_dom, record, index, action, schema) => {
      return record[schema.dataIndex as string] || '-';
    },
  },
  /** 新版日期选择器, 采用新的日期结构, {min: Date, max: Date}, 无需搜索时不使用 */
  dateRange2: {
    width: 160,
    valueType: 'dateRange',
    render: (_dom, record, index, action, schema) => {
      return record[schema.dataIndex as string] || '-';
    },
    search: {
      transform(val, namePath) {
        return {
          [namePath]: transRangeDate(val),
        };
      },
    },
  },
  dateTimeRange: {
    width: 160,
    render: (_dom, record, index, action, schema) => {
      return record[schema.dataIndex as string] || '-';
    },
  },
  amountMoney: {
    renderText: (text) => <AmountMoney num={text} />,
  },
};

export class TableSchemaAide<T = any> {
  get columnDefault(): ProColumns {
    /* 后期会开放允许自定义class实例, 创建实例传入一些自定义默认值 */
    return {
      shouldCellUpdate: (record, prev) => record !== prev,
    };
  }

  tableColumnsIntercept = (columns: TypeProColumns<T>[], dynamicColumn?: ProColumns) => {
    const newColumns = (columns.filter(Boolean) as ProColumns[]).map((item) => {
      const newItem = this.valueTypeMap(item) || item;

      /** lodash.merge 和 Object.assign有区别,  assign会合并显式的undefined (merge不会),  三点解构属于assign */
      return {
        ...merge({}, this.columnDefault, dynamicColumn, newItem),
      };
    });

    return newColumns;
  };

  valueTypeMap = (item: ProColumns /** 外部传入 */): ProColumns | undefined => {
    const valueType = item.valueType as typeof valueTypeList[number];
    /** 内部扩展定义 */
    const newItem = valueTypeMap[valueType];

    if (newItem) {
      return merge(
        {},
        newItem,
        item,
        /** valueType 存在时, 以valueTypeMap内置值生效 */
        newItem.valueType
          ? {
              valueType: newItem.valueType,
              /** 如果外部传入true, 则优先使用内部定义,  否则使用外部定义, 外部无值取false, 这样内外保持一直 */
              search:
                (item as any)?.search === true ? newItem.search ?? true : item?.search ?? false,
            }
          : undefined,
      );
    }
    return undefined;
  };
}

export const tableSchemaAide = new TableSchemaAide();
