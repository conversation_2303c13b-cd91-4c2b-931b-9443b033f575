{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "importHelpers": true,
    "jsx": "react",
    "esModuleInterop": true,
    "sourceMap": true,
    "baseUrl": "./",
    "strict": true,
    "resolveJsonModule": true,
    "paths": {
      "@/*": ["./src/*"],
      "@@/*": ["./src/.umi/*"]
    },
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
    // "noImplicitAny": false
  },
  "include": [
    "mock/**/*",
    "src/**/*",
    "src/components/**/*",
    "config/**/*",
    ".umirc.ts",
    "typings.d.ts",
    "public/**/*"
  ]
}
