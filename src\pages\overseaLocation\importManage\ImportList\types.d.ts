type TypeImportTB = {
  /** id */
  id?: string;
  /** 导入类型 */
  importType?: string;
  /** 导入状态 */
  importStatus?: string;
  /** 导入文件名称 */
  fileName?: string;
  /** 导入文件地址 */
  fileUrl?: string;
  /** 原始导入数 */
  totalNum?: number;
  /** 成功导入数 */
  successNum?: number;
  /** 失败导入数 | 异常条数 */
  failNum?: number;
  /** 异常信息 */
  errorMessage?: string;
  /** 异常文件名称 */
  errorFileName?: string;
  /** 异常文件地址 */
  errorFileUrl?: string;
  /** 公司唯一标识 */
  companyId?: string;
  /** 公司名称 */
  companyName?: string;
  /** 创建人id */
  createById?: number;
  /** 创建人名称 | 操作人 */
  createByName?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新人id */
  updateById?: number;
  /** 更新人名称 */
  updateByName?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 删除标志 */
  isDeleted?: boolean;
};
