import './LoadButton.less';
import { compUtils, useSafeStateUpdate } from '@/utils';
import type { ButtonProps, PopconfirmProps } from 'antd';
import { Popconfirm, Button } from 'antd';
import type { ReactNode } from 'react';
import React, { useRef, useState } from 'react';
import classNames from 'classnames';
import { QuestionCircleFilled } from '@ant-design/icons';

export type TypeLoadButtonProps = ButtonProps & {
  popconfirmProps?: (PopconfirmProps & { content?: string | ReactNode }) | false;
  /** 异步click结束时的调用操作 */
  onClickFinally?: (params: { isCatch: boolean; error?: any }) => any;
  /** loading状态下展示内容 */
  loadingChildren?: React.ReactNode;
  /** click 事件之前
   * @ 如果return false, 则不执行后续事件
   * @ 可用于实现 popconfirm 前置条件判断 */
  beforeClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void | false;
};

function hasPopconfirmProps(
  confirmProps: any,
): confirmProps is Exclude<TypeLoadButtonProps['popconfirmProps'], false> {
  return confirmProps !== false;
}

export default function LoadButton(props: TypeLoadButtonProps) {
  /* 集成popconfirm */
  const { safeUpdateFn } = useSafeStateUpdate();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  /** 判断beforeClick是否存在异常 */
  const isBeforeErrorRef = useRef(false);
  const {
    beforeClick,
    onClick,
    onClickFinally,
    popconfirmProps = false,
    loadingChildren,
    ...args
  } = props;
  const defaultBtnProps = {
    type: 'link',
    className: classNames({
      'zw-load-btn': true,
      'zw-load-btn__link-text': props.type === undefined,
    }),
  } as ButtonProps;
  const newProps = compUtils.propsMerge(defaultBtnProps, args);
  const onClick2 = (e?: React.MouseEvent<HTMLElement, MouseEvent>) => {
    const res: any = onClick?.(e!);

    if (res?.finally) {
      setLoading(true);
      /** 如果 res 是reject状态, 直接 res.finally 调用会触发控制台展示异常
       * @ jest 用例也会捕获异常
       */
      res
        .then(
          () => onClickFinally?.({ isCatch: false }),
          (error: any) => {
            console.error(error);
            onClickFinally?.({ isCatch: true, error });
          },
        )
        .finally(
          safeUpdateFn(() => {
            setLoading(false);
          }),
        );
    }
    // return res;
  };
  const innerBeforeClick = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (beforeClick?.(e!) === false) {
      isBeforeErrorRef.current = true;
      return false;
    }
    isBeforeErrorRef.current = false;
  };

  const btn = (
    <Button
      {...{
        ...newProps,
        /** 如果外部传true, 以外部为准, 否则内部控制 */
        loading: args.loading === true ? true : loading,
        onClick(e) {
          if (innerBeforeClick(e) !== false) {
            hasPopconfirmProps(popconfirmProps) ? undefined : onClick2(e);
          }
        },
      }}
    >
      {/* loading状态优先展示loadingChildren */}
      {loading ? loadingChildren || props.children : props.children}
    </Button>
  );

  let popconfirm: JSX.Element | null = null;

  if (hasPopconfirmProps(popconfirmProps)) {
    /* 自定义title */
    const renderTitle = (confirmProps: typeof popconfirmProps) => {
      return (
        <div className="zw-load-btn zw-load-btn__pop-title">
          <div className="title-label">{confirmProps?.title}</div>
          <div className="content-label">{confirmProps?.content}</div>
        </div>
      );
    };

    popconfirm = (
      <Popconfirm
        {...{
          icon: <QuestionCircleFilled />,
          disabled: args.disabled,
          placement: 'topRight',
          visible: open,
          ...popconfirmProps,
          onVisibleChange(visible, e) {
            /** 仅beforeClick事件不存在异常时, 允许改变显隐状态 */
            isBeforeErrorRef.current !== true && setOpen(visible);
            popconfirmProps?.onVisibleChange?.(visible, e);
          },
          onConfirm: onClick2,
          title: renderTitle(popconfirmProps),
        }}
      >
        {btn}
      </Popconfirm>
    );
  }

  return hasPopconfirmProps(popconfirmProps) ? (popconfirm as JSX.Element) : btn;
}
