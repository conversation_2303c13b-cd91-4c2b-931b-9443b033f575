import { request } from 'umi';

/**
 * 分页查询 常用信息模块 收货人 发货人 通知人
 * @param data
 */

export const queryAddressList = async (data: any) => {
  return request('/api/website/web/sftInformation/selectSftPage.do', {
    method: 'POST',
    data
  });
};


/**
 * 新增、修改 常用信息模块 收货人 发货人 通知人
 * @param data
 */

export const sendCreateOrEditAddress = async (data: any) => {
  return request('/api/website/web/sftInformation/sftSaveOrUpdate.do', {
    method: 'POST',
    data
  });
};

/**
 * 删除 常用信息模块 收货人 发货人 通知人
 * @param data
 */
export const sendeDeleteAddress = async (data: any) => {
  return request('/api/website/web/sftInformation/delteByBath.do', {
    method: 'POST',
    data
  });
};

/**
 * 设置默认 常用信息模块 收货人 发货人 通知人
 * @param data
 */
export const sendSetDefaultAddress = async (data: any) => {
  return request('/api/website/web/sftInformation/setSftDefault.do', {
    method: 'POST',
    data
  });
};

/**
 * 取消默认 常用信息模块 收货人 发货人 通知人
 * @param data
 */
export const sendCancelDefaultAddress = async (data: any) => {
  return request('/api/website/web/sftInformation/cancelSftDefault.do', {
    method: 'POST',
    data
  });
};

/**
 * 查询模板
 * @param data
 */
export const queryTemplateList = async (data: any) => {
  return request('/api/website/web/template/selectTemplatePage.do', {
    method: 'POST',
    data
  });
};

/**
 * 删除模板
 * @param data
 */
export const sendDeleteTemplate = async (data: any) => {
  return request('/api/website/web/template/deleteTemplate.do', {
    method: 'POST',
    data
  });
};