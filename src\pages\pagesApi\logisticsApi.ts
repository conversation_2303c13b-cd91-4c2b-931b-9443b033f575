/** 物流相关api */
import { request } from 'umi';

/** 出库订单/物流拦截/退货面单等 物流轨迹详情 */
export type TypeLogisticsTrackDetail = {
  /** 物流商 */
  logisticsChannel?: string;
  /** 查询渠道名称 */
  registerCarrierName?: string;
  /** 查询渠道code */
  registerCarrier?: string;
  /** 追踪单号 */
  trackingNo?: string;
  /** 最新节点 */
  latestNode?: string;
  /** 物流轨迹节点 */
  logisticsTrackDetails?: {
    /** 轨迹节点描述 */
    actionCodeDesc?: string;
    /** 轨迹节点地址 */
    actionAddress?: string;
    /** 时区 */
    timeZone?: string;
    /** 轨迹节点时间 */
    time?: string;
  }[];
};

/** (出库订单|物流拦截|退货面单)  物流轨迹详情查询 */
export function apiLogisticsTrackDetail(data: {
  /** 订单id */
  orderId?: string;
  /** 物流可视化id */
  orderLogisticsId?: string;
}) {
  return request<NsApi.TypeResponseData<TypeLogisticsTrackDetail>>(
    '/zouwu-oms-order/portal/outbound/logistics/queryLogisticsTrack',
    {
      method: 'POST',
      data: {
        orderId: data.orderId,
        id: data.orderLogisticsId,
      },
    },
  );
}
