import { useEnv } from './useEnv';

// TODO 这个方法从设计上讲应该由session来提供, 暂时先不动
export function getAliyunPath(appEnv: string) {
  return appEnv === 'pro'
    ? 'https://eshipping.oss-cn-shanghai.aliyuncs.com/eshipping/'
    : 'https://eshipping.oss-cn-shanghai.aliyuncs.com/eshippingtest/';
}

/** @deprecated 废弃,使用session.getAliyunPath() 替代 */
export function useAliyunPath() {
  const appEnv = useEnv();

  return getAliyunPath(appEnv);
}
