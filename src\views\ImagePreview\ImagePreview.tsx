import type { ImageProps } from 'antd';
import { Image } from 'antd';
import React, { useImperativeHandle } from 'react';
import './ImagePreview.less';

type TypeImagePreviewOpenProps = {
  /** 图片列表 */
  imgList: (ImageProps & {
    /** 和src相同,使用时更方便,减少数据转换  */
    url?: string;
  })[];
  /** 当前显示的图片 */
  curUrl?: string;
};

/** 图片预览实例类型 */
type TypeImagePreviewIns = {
  open: (params: TypeImagePreviewOpenProps) => void;
  isImgUrl: typeof isImgUrl;
};

/** 全局 ImagePreview 实例 */
let imagePreviewIns: TypeImagePreviewIns | undefined;

/** 全局注册 ImagePreview */
export { imagePreviewIns };

/** 全局图片预览组件 */
export default function ImagePreview() {
  const ref = React.useRef<TypeImagePreviewIns>();
  const groupDivRef = React.useRef<HTMLDivElement>(null);
  const [current, setCurrent] = React.useState<number>(0);
  const [imgList, setImgList] = React.useState<TypeImagePreviewOpenProps['imgList']>([]);

  useImperativeHandle(
    ref,
    () => {
      imagePreviewIns = {
        open(params) {
          const validImgList = validImgFilter(params.imgList || []);
          const currentIndex = params.curUrl
            ? Math.max(
                validImgList.findIndex((item) => item.src === params.curUrl),
                0,
              )
            : 0;

          setCurrent(currentIndex);
          setImgList(validImgList);
          setTimeout(() => {
            groupDivRef.current?.querySelectorAll('img')[currentIndex]?.click();
          });
        },
        isImgUrl,
      };
      //   console.log('=======> imagePreviewIns:', (window.imagePreviewIns = imagePreviewIns));
      return imagePreviewIns;
    },
    [],
  );

  return (
    <div style={{ display: 'none' }}>
      <div ref={groupDivRef}>
        <Image.PreviewGroup
          {...{
            preview: {
              current,
              rootClassName: 'image-preview__root',
            },
          }}
        >
          {imgList.map((item, index) => {
            return <Image key={`${item.src}_${index}`} {...{ src: item.src }} />;
          })}
        </Image.PreviewGroup>
      </div>
    </div>
  );
}

/** 获取图片地址 */
function getImgUrl(item: TypeImagePreviewOpenProps['imgList'][number]) {
  return item.src || item.url;
}

const validSuffix = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];

/** 是否为图片链接 */
function isImgUrl(url?: string) {
  if (!url) {
    return false;
  }
  const urlSuffix = url.toLowerCase().split('.').pop() || '';

  return validSuffix.includes(urlSuffix);
}

/** 过滤合法图片 */
function validImgFilter(arr: TypeImagePreviewOpenProps['imgList']) {
  return arr.filter((item) => {
    if (isImgUrl(getImgUrl(item))) {
      item.src = getImgUrl(item);
      return true;
    }
  });
}

function demoTest() {
  imagePreviewIns?.open({
    imgList: [
      {
        src: 'https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp',
      },
      {
        src: 'https://gw.alipayobjects.com/zos/antfincdn/cV16ZqzMjW/photo-1473091540282-9b846e7965e3.webp',
      },
      {
        src: 'https://gw.alipayobjects.com/zos/antfincdn/x43I27A55%26/photo-1438109491414-7198515b166b.webp',
      },
    ],
    curUrl:
      'https://gw.alipayobjects.com/zos/antfincdn/x43I27A55%26/photo-1438109491414-7198515b166b.webp',
  });
}
