import { Tabs } from 'antd';
import type { ActionType } from '@/common-import';
import React, { useEffect, useRef, useState } from 'react';
import { arrayFilterBoolean } from '@/utils';
import { FormProCard } from '@/views';
import type { TypeProColumns, TypeZouProTableProps } from '@/components';
import { ZouProTable } from '@/components';
import { apiOutboundChargeList } from '../../OutboundApi';
import { apiMapDictType } from '@/api';

/** 计费类型 */
enum EnumCostType {
  /** 应收 */
  receivable = 1,
  /** 应付 */
  payable = 2,
  /** 二级应付, 口岸, 同业 */
  secondaryPayable = 12,
}

type TypeProps = {
  detailData?: TypeOutboundDetail;
};

/** 费用清单
 * @ 和OMS 侧同名 不同 码, 不可公用
 */
export default function DetailChargeChecklist(props: TypeProps) {
  const { config, costType, setCostType, tabItems } = useConfig({ props });

  if (tabItems.length < 1) {
    return null;
  }

  return (
    <FormProCard title={'费用清单'}>
      <Tabs
        {...{
          activeKey: costType,
          onChange: setCostType,
        }}
      >
        {tabItems.map((item) => {
          return (
            <Tabs.TabPane {...{ ...item, children: <ZouProTable {...config} /> }} key={item.key} />
          );
        })}
      </Tabs>
    </FormProCard>
  );
}

function useConfig({ props }: { props: TypeProps }) {
  const { podOrderNo } = props.detailData || {};
  const tabItems = arrayFilterBoolean([
    {
      tab: '应收',
      key: EnumCostType.receivable.toString(),
    },
  ]);
  const [costType, setCostType] = useState(tabItems[0]?.key);
  const actionRef = useRef<ActionType>();
  const columns = useColumns({ costType, props });
  const config = {
    actionRef,
    rowKey: (record) => {
      return record.id || record.key;
    },
    scroll: { x: 'max-content' },
    tableType: 'simple',
    async request(params) {
      if (!podOrderNo) {
        return { data: [], success: true };
      }
      const { data } = await apiOutboundChargeList({
        costType,
        podOrderNo,
      });

      return {
        data,
        success: true,
      };
    },
    columns,
  } as TypeZouProTableProps<any, any>;

  useEffect(() => {
    actionRef.current?.reload();
  }, [costType, podOrderNo]);

  return {
    config,
    costType,
    setCostType,
    tabItems,
    actionRef,
  };
}

function useColumns({ costType, props }: { costType: string; props: TypeProps }) {
  const columns = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      valueType: 'serialNumber',
    },
    { title: '费用单号', dataIndex: 'chargeRecordBillNo' },
    {
      title: '费用CODE',
      dataIndex: 'gmChargeCode',
    },
    {
      title: '费用名称',
      dataIndex: 'gmChargeName',
    },
    {
      title: '费用金额',
      dataIndex: 'chargeTotal',
    },
    {
      title: '结算币种',
      dataIndex: 'chargeCurrency',
    },
    {
      title: '结算状态',
      dataIndex: 'status',
      valueType: 'select',
      request: apiMapDictType.chargeRecordBillStatus,
    },
  ] as TypeProColumns<any>[];

  return columns;
}
