import { exportUtils } from '@/utils-business';
import { request } from 'umi';

/** 库存管理列表 */
export function apiInventoryList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 仓库id */
      warehouseIds?: number[];
      /** 库区id */
      warehouseAreaIds?: number[];
      /** 商品名称 */
      goodsName?: string;
      /** 商品sku */
      sku?: string;
      /** tab编码 */
      tabCode?: string;
      /** 数量类型 */
      qualityType?: string;
      /** 最小数量 */
      minQuantity?: number;
      /** 最大数量 */
      maxQuantity?: number;
      /** 订单类型 */
      businessType?: number;
      /** 虚拟仓 */
      virtualWarehouseList?: string[];
    };
  }>,
) {
  return request<
    NsApi.TypeResponseData<{
      /** 库存数据 */
      inventory?: {
        /** 总数 */
        totalSize?: number;
        /** 总页数 */
        totalPage?: number;
        /** 分页大小 */
        pageSize?: number;
        /** 当前页 */
        currentPage?: number;
        /** 当前大小 */
        currentSize?: number;
        records?: TypeInventoryTB[];
      };
      tabNum?: {
        ALL?: number;
        UNSAFE_NUM?: number;
        ERROR?: number;
        BAD?: number;
      };
    }>
  >('/zouwu-oms-stock/portal/inventory/list', {
    data,
    method: 'POST',
  });
}

/** 库存快照导出 */
export function apiInventorySnapshotExport(data: {
  /** 仓库代码 */
  warehouseCodeList?: string[];
  /** 快照日期 */
  snapshotDate?: string;
  /** 文件名称 */
  fileName?: string;
}) {
  return request('/zouwu-oms-system/portal/stock/export/inventory', {
    data,
    method: 'POST',
  });
}

/** 库龄管理列表 */
export function apiInventoryAgeList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 仓库id */
      warehouseIds?: string[];
      /** 库区id */
      warehouseAreaIds?: string[];
      /** 商品名称 */
      goodsName?: string;
      /** 商品sku */
      sku?: string;
      /** 数量类型 */
      qualityType?: string;
      /** 最小数量 */
      minQuantity?: number;
      /** 最大数量 */
      maxQuantity?: number;
      /** 关联上游单据单号 */
      associatedDocNo?: string;
      /** 批次号 */
      batchNo?: string;
      /** 库存最小数量 */
      minStorageAge?: number;
      /** 库存最大数量 */
      maxStorageAge?: number;
    };
  }>,
) {
  return request<NsApi.TypeResponseList>('/zouwu-oms-stock/portal/inventory/detail/list', {
    data,
    method: 'POST',
  });
}

/** 库存流水查询条件 */
export type TypeInventoryFlowQuery = {
  /** 仓库id */
  warehouseIds?: string[];
  /** 库区id */
  warehouseAreaIds?: string[];
  /** 商品名称 */
  goodsName?: string;
  /** 商品sku */
  sku?: string;
  /** 关联单号 */
  docNo?: string;
  /** 关联上游单据单号 */
  associatedDocNo?: string;
  /** 交易场景(交易类型) */
  businessTypes?: number[];
};

/** 库存流水列表 */
export function apiInventoryFlowList(
  data: NsApi.TypeRequestListQuery<{
    condition: TypeInventoryFlowQuery;
  }>,
) {
  return request<NsApi.TypeResponseList>('/zouwu-oms-stock/portal/inventory/record/list', {
    data,
    method: 'POST',
  });
}

/** 库存流水导出 */
export function apiInventoryFlowExport(data: {
  fileName?: string;
  condition?: TypeInventoryFlowQuery;
}) {
  return request<NsApi.TypeResponseList>('/zouwu-oms-system/portal/stock/inventory/record/export', {
    data: exportUtils.transExportPageQuery(data),
    method: 'POST',
  });
}
