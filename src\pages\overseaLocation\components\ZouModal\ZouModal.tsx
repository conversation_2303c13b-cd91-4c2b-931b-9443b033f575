import './ZouModal.less';
import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import classnames from 'classnames';

export type TypeZouModalProps = {
  modalProps?: ModalProps;
  /** 关闭前异步回调 */
  onCancel?: () => void | Promise<void>;
  /** 确认按钮事件 */
  onOk?: () => void | Promise<void>;
  children?: React.ReactNode;
  /** 隐藏取消按钮 */
  hideCancelBtn?: boolean;
  className?: string;
  /** 固定最大高度 */
  fixedHeight?: boolean;
};

export type TypeZouModalRef = {
  open: () => void;
  close: () => void;
};

/** 通用modal设计 */
export default React.forwardRef(function ZouModal(
  props: TypeZouModalProps,
  ref: React.Ref<TypeZouModalRef | undefined>,
) {
  const { modalConfig, modalController } = useModalConfig({ props });

  useImperativeHandle(ref, () => modalController);

  return <Modal {...modalConfig}>{props.children}</Modal>;
});

function useModalConfig({ props }: { props: TypeZouModalProps }) {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const modalController = {
    open() {
      setVisible(true);
    },
    close() {
      setVisible(false);
    },
  };

  const modalConfig = {
    visible,
    keyboard: false,
    maskClosable: false,
    async onOk() {
      const res = props.onOk?.();

      if (res?.finally) {
        setLoading(true);
        res.finally(() => setLoading(false));
      }

      await res;
      modalController.close();
    },
    async onCancel(e) {
      await props.onCancel?.();
      modalController.close();
    },
    ...props.modalProps,
    className: classnames(
      'zw-modal',
      props.fixedHeight && 'zw-modal__fixed-height',
      props.className,
    ),
    cancelButtonProps: {
      disabled: loading,
      style: props.hideCancelBtn === true ? { display: 'none' } : undefined,
      ...props.modalProps?.cancelButtonProps,
    },
    okButtonProps: {
      loading,
      ...props.modalProps?.okButtonProps,
    },
  } as ModalProps;

  return { modalConfig, modalController };
}
