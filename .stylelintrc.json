{"extends": ["stylelint-config-standard", "stylelint-config-rational-order"], "rules": {"order/properties-order": [], "at-rule-no-unknown": null, "number-leading-zero": null, "no-descending-specificity": null, "declaration-colon-newline-after": null, "font-family-no-missing-generic-family-keyword": null, "no-empty-source": null, "rule-empty-line-before": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "local"]}], "selector-type-no-unknown": [true, {"ignore": ["custom-elements"], "ignoreTypes": ["page", "text", "view", "swiper", "swiper-item"]}]}, "ignoreFiles": ["src/**/*.tsx"]}