.goods-delete-modal {
  .exist-order-desc {
    color: #333;
    font-weight: 600;
    font-size: 20px;
    .exist-order-icon {
      margin-right: 10px;
      color: #faad14;
      font-size: 24px;
    }
  }
  .rela-goods-group-desc {
    margin-bottom: 10px;
    color: #333;
    font-size: 20px;
  }
  .rela-goods-group-list {
    height: 350px;
    overflow-y: auto;
    .rela-item {
      display: flex;
      align-items: center;
      justify-content: start;
      margin-bottom: 10px;
      padding: 8px;
      background-color: #f6f6f6;
      border-radius: 4px;
      .goods-img {
        width: 50px !important;
        height: 50px !important;
      }
      .goods-desc {
        margin-left: 10px;
        .title {
          color: #333;
          font-weight: 500;
          font-size: 16px;
        }
        .desc {
          display: flex;
          align-items: center;
          justify-content: start;
          color: #b2b2b2;
        }
      }
    }
  }
  .goods-text-show {
    display: block;
    max-width: 400px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-wrap: break-word;
    cursor: pointer;
  }
}
