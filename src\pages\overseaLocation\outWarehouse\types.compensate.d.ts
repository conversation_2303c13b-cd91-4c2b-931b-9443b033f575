/** 出库申请赔付表单 */
type TypeOutboundCompensateApplyFormData = {
  /** 索赔ID */
  compensateId?: number;
  /** 申请单号 */
  podOrderNo?: string;
  /** 履约单ID */
  orderChildId?: number;
  /** 履约单号 */
  complianceNo?: string;
  /** 索赔状态 */
  compensateStatus?: string;
  /** 索赔状态后端翻译 */
  compensateStatusName?: string;
  /** 索赔金额 */
  applyCompensateAmount?: number;
  /** 实际赔付金额 */
  actualCompensateAmount?: number;
  /** 物流渠道名称, 后端翻译 */
  deliveryChannelName?: string;
  /** 客户渠道Code */
  customerChannelCode?: string;
  /** 备注 */
  remark?: string;
  /** 币种 */
  currency?: string;
  /** 客户公司ID */
  compensateCompanyId?: string;
  /** 客户公司名称 */
  compensateCompanyName?: string;
  /** 文件集合 */
  fileList?: {
    /** 附件ID */
    fileId?: number;
    /** 文件名称 */
    fileName?: string;
    /** 文件链接 */
    filePath?: string;
    /** 是否删除 */
    isDeleted?: boolean;
  }[];
};

/** 索赔详情接口 */
type TypeOutboundCompensateApplyDetail = TypeOutboundCompensateApplyFormData & {
  /** 索赔操作记录 */
  compensateRecord?: {
    /** 日志id */
    logId?: string;
    /** 索赔状态 */
    compensateStatus?: number;
    /** 索赔状态后端翻译 */
    compensateStatusName?: string;
    /** 申请索赔金额 */
    applyCompensateAmount?: number;
    /** 实际赔付金额 */
    actualCompensateAmount?: number;
    /** 备注 */
    remark?: string;
    /** 币种 */
    currency?: string;
    /** 文件集合 */
    fileList?: {
      /** 附件ID */ fileId?: number;
      /** 文件名称 */
      fileName?: string;
      /** 文件链接 */
      filePath?: string;
    }[];
    /** 操作时间 */
    operateTime?: string;
    /** 操作人 */
    operatorName?: string;
  }[];
};
