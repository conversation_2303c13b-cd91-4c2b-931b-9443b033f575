import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Divider, message, Space } from 'antd';
import React, { useRef } from 'react';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { DISPATCH_SERVICE_PROVIDER_OPTIONS, ORDER_SOURCE_PLATFORM } from '@/utils/const';
import { LoadButton, StatusShow, ZSearchSelect } from '../components';
import { ableOptions } from '../enum';
import { apiGetListPostingKey, apiPostingKeyEnable } from './postingKeyApi';
import PostingKeyEdit from './PostingKeyEdit';
import { apiMapDictType, apiQueryWarehouseOptions } from '@/pages/overseaLocation/api';

function PostingKeyList() {
  const { config, reload } = useConfig();

  return (
    <PageContainer>
      <ProTable
        {...{
          ...config,
          headerTitle: (
            <PostingKeyEdit
              btnText={I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyEdit.newBookkeeping}
              reload={reload}
            />
          ),
        }}
      />
    </PageContainer>
  );
}

export default PostingKeyList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const queryParams = useRef<any>({});

  /** 重置 */
  const reload = () => {
    actionRef?.current?.reload();
  };

  const columns = useColumns(reload);
  const config = {
    tableLayout: 'auto',
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      // defaultCollapsed: false,
    },
    pagination: { position: ['bottomLeft'] },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
          ...queryParams.current,
        },
      } as Parameters<typeof apiGetListPostingKey>[0];
      const { data } = await apiGetListPostingKey(query);

      return {
        data: data?.records,
        success: true,
        total: data?.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    reload,
  };
}

function useColumns(reload: () => void) {
  const columns: ProColumns<TypePostingKeyRecord>[] = [
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.warehouse,
      dataIndex: 'warehouseCode',
      valueType: 'select',
      formItemProps: {
        name: 'warehouseIds',
      },
      renderFormItem() {
        return (
          <ZSearchSelect
            {...{
              mode: 'multiple',
              showSearch: true,
              request: async (queryParam: string) =>
                apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.eCommercePlatform,
      dataIndex: 'orderSourcePlatformName',
      valueType: 'select',
      formItemProps: {
        name: 'orderSourcePlatformCodes',
      },
      request: apiMapDictType.orderSourcePlatform,
      fieldProps: {
        // options: ORDER_SOURCE_PLATFORM,
        showSearch: false,
        mode: 'multiple',
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.shop,
      dataIndex: 'platformStoreId',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.expressService,
      dataIndex: 'dispatchServiceProvider',
      valueType: 'select',
      formItemProps: {
        name: 'dispatchServiceProviders',
      },
      fieldProps: {
        options: DISPATCH_SERVICE_PROVIDER_OPTIONS,
        mode: 'multiple',
      },
    },

    {
      title: I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.postingCode,
      dataIndex: 'postingKey',
      search: false,
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.state,
      dataIndex: 'enable',
      valueType: 'select',
      fieldProps: {
        options: ableOptions,
      },
      renderText: (_) => {
        return (
          <StatusShow
            value={_}
            valueEnum={{
              1: {
                text: I18N.Src__Pages__OverseaLocation.Enum.enable,
                status: 'success',
              },
              0: {
                text: I18N.Src__Pages__OverseaLocation.Enum.deactivate,
                status: 'error',
              },
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__Company__DepManager.Index.operator,
      dataIndex: 'updateByName',
      search: false,
    },
    {
      title: I18N.Src__Pages__Company__DepManager.Index.operationTime,
      dataIndex: 'updateDate',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      dataIndex: 'action',
      valueType: 'option',
      key: 'action',
      search: false,
      width: 130,
      render: (text, record) => {
        const otherInfo =
          record?.enable === 0
            ? {
                confirmTitle:
                  I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.confirmEnable,
                btnContent: I18N.Src__Pages__OverseaLocation.Enum.enable,
                successTips:
                  I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.enabledSuccessfully,
              }
            : {
                confirmTitle:
                  I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.confirmDeactivation,
                btnContent: I18N.Src__Pages__OverseaLocation.Enum.deactivate,
                successTips:
                  I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.deactivationSucceeded,
              };

        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <PostingKeyEdit
              {...{
                btnText: I18N.Src__Pages__Common__Template.Index.edit,
                reload,
                editData: {
                  ...record,
                  orderSourcePlatformCode: record?.orderSourcePlatformCode
                    ? {
                        label: record?.orderSourcePlatformName,
                        value: record?.orderSourcePlatformCode,
                      }
                    : undefined,
                  warehouseIds: record?.warehouseId
                    ? [
                        {
                          label: record?.warehouseCode,
                          value: record?.warehouseId,
                        },
                      ]
                    : undefined,
                },
                isUpdate: true,
                disabled: !record?.hasEdit,
              }}
            />
            <LoadButton
              popconfirmProps={{ title: otherInfo.confirmTitle }}
              // type="link"
              onClick={async () => {
                let formData = {};

                if (record?.enable === 0) {
                  formData = {
                    id: record?.id,
                    warehouseIds: record?.warehouseId ? [record.warehouseId] : undefined,
                    orderSourcePlatformCode: record?.orderSourcePlatformCode,
                    dispatchServiceProvider: record?.dispatchServiceProvider,
                    enable: 1,
                    postingKey: record?.postingKey,
                    platformStoreId: record?.platformStoreId,
                  };
                } else {
                  formData = {
                    enable: 0,
                    id: record?.id,
                  };
                }

                const res = await apiPostingKeyEnable(formData);

                // if (res?.success) {
                message.success(otherInfo.successTips);
                reload();
                // }
              }}
            >
              {otherInfo.btnContent}
            </LoadButton>
          </Space>
        );
      },
    },
  ];

  return columns;
}
