import React, { useEffect, useState, useRef } from 'react';
import type { FormInstance } from 'antd';
import { Button, Space, Modal, Popconfirm, Form, InputNumber, message } from 'antd';
import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { includesInArray, ruleUtils } from '@/utils';
import { EnumWarehouseType } from '@/pages/pagesApi';
import type { TypeOutboundGoodsTB } from './FormGoodsChooseApi';
import { apiOutboundHasVirtualWarehouse, apiOutboundGoodsList } from './FormGoodsChooseApi';
import css from './FormGoodsChoose.less';
import { SearchSelect } from '@/components';
import { TextValue } from '@/views';
import { GoodsImage } from '../../OutboundDiffImport';
import { textMap } from './text-map';

const { Item } = Form;

export interface GoodsTableFormProps {
  value?: any[];
  onGoodsSelect: (goods: any) => void;
  onGoodsRemove: (record: any) => void;
  warehouseList: any[];
  /** 是否支持批量多商品, TOB出库单支持 */
  allowBatch?: boolean;
  /** 外层表单 */
  formIns: FormInstance<any>;
}
type TypeInitParams = {
  warehouseId?: string;
  initSkuArr?: string[];
};

/** 仓库搜索时数据类型 */
const searchWarehouseTypeMap = {
  /** 物理仓 */
  actual: '1',
  /** 虚拟仓
   * 这个搜索条件和仓库自身属性warehouseType重名,虚拟仓的warehouseType值是9
   */
  virtual: '2',
};

/** 商品清单 */
function FormGoodsChoose({
  value,
  onGoodsSelect,
  onGoodsRemove,
  warehouseList,
  allowBatch,
  formIns,
}: GoodsTableFormProps) {
  const { hasVirtualWarehouse } = useHasVirtualWarehouse({ formIns });
  const modalTableRef = useRef<ActionType>();
  const [visible, toggleVisible] = useState(false);
  const [activeKey, setActiveKey] = useState(searchWarehouseTypeMap.actual);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRow, setSelectedRow] = useState<any[]>([]);
  const [initParams, setInitParams] = useState<TypeInitParams>({});
  const { columns } = useConfig({
    formIns,
    allowBatch,
    warehouseList,
    onGoodsRemove,
    initParams,
  });

  const handleShowModal = () => {
    const companyId = formIns.getFieldValue('companyId');

    if (!companyId) {
      message.warning(textMap.txtPleaseChooseCompany);
      return;
    }

    const skuArr = value && value.length ? value.map((o) => o.warehouseIdSku) : [];
    const newInitParams = {
      warehouseId: allowBatch ? value?.[0]?.warehouseId : undefined,
      initSkuArr: skuArr,
    };
    /** 这个功能坏掉了, 后端给的warehouseType都是真实物理仓的类型, 不可能为虚拟仓, 先这样吧
     * @ 回填效果暂时失效
     */
    const isVirtualWarehouse =
      Array.isArray(value) && value?.[0]?.warehouseType === EnumWarehouseType.virtual;

    setSelectedRowKeys(skuArr);
    setSelectedRow(value || []);
    setInitParams(newInitParams);
    setActiveKey(
      isVirtualWarehouse ? searchWarehouseTypeMap.virtual : searchWarehouseTypeMap.actual,
    );
    toggleVisible(true);
  };

  // 新增箱-左侧勾选change事件（新增的合并）
  const handleTableRowSelectedChange = (keys: any[], rows: any[]) => {
    setSelectedRowKeys(keys);
    setSelectedRow(rows);
  };

  const handleGetGoodsList = async (params: any) => {
    const { pageSize, current: currentPage, warehouseId, sku } = params;
    const { businessType, companyId } = formIns?.getFieldsValue() || {};

    const condition: any = {
      /** 获取订单类型 */
      businessType,
      companyId,
      warehouseType: activeKey,
    };

    if (warehouseId) {
      condition.warehouseIds = [warehouseId];
    }
    if (sku) {
      condition.sku = sku;
    }

    const { data } = await apiOutboundGoodsList({
      pageSize,
      currentPage,
      condition,
    });

    return {
      data: data?.records || [],
      total: data?.totalSize || 0,
    };
  };

  const handleModalCancel = () => {
    toggleVisible(false);
    setSelectedRowKeys([]);
    setSelectedRow([]);
    setInitParams({});
  };

  const handleModalOk = async () => {
    if (selectedRow.length) {
      onGoodsSelect(selectedRow);
    }
    handleModalCancel();
  };

  const rowSelection = {
    type: allowBatch ? 'checkbox' : 'radio',
    hideSelectAll: true,
    selectedRowKeys,
    onChange: handleTableRowSelectedChange,
    getCheckboxProps: (record: any) => {
      const disabled = {
        disabled: true,
      };

      if (record.goodQuantity <= 0) {
        /** 商品数量少于1 */
        return disabled;
      }
      if (
        allowBatch &&
        selectedRow.length > 0 &&
        selectedRow[0].warehouseId !== record.warehouseId
      ) {
        /** 批量选择商品, 但是后选商品和先选的商品 仓库id不一致 */
        return disabled;
      }
      if (allowBatch && initParams.initSkuArr?.includes(record.warehouseIdSku)) {
        /** 批量出库, 已添加至页面的商品,check按钮置灰 */
        return disabled;
      }
    },
  } as ProTableProps<any, any>['rowSelection'];

  return (
    <div className={css['form-goods-choose']}>
      <Item hidden>
        <TextValue {...{ label: textMap.txtWmsSystemCode, name: 'wmsSystemCode' }} />
        <TextValue
          {...{ label: textMap.txtOutboundWarehouseId, name: 'createDeliveryWarehouseId' }}
        />
        <TextValue {...{ label: '发货仓库code', name: 'deliveryWarehouseCode' }} />
      </Item>
      <Space style={{ marginBottom: 20 }}>
        <Button type="primary" onClick={handleShowModal}>
          {textMap.txtAddGoods}
        </Button>
        {/* <Button>批量导入</Button> <Button type="link">点击下载导入模版</Button> */}
      </Space>
      <ProTable
        columns={columns}
        dataSource={value}
        rowKey="sku"
        size="small"
        pagination={false}
        search={false}
        options={false}
        cardProps={false}
        scroll={{ x: 'max-content' }}
      />

      <Modal
        width="90%"
        visible={visible}
        title={textMap.txtChooseGoods}
        okText={textMap.txtConfirmAdd}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        destroyOnClose
        maskClosable={false}
        className={css['goods-table-form']}
      >
        <ProTable
          rowKey="warehouseIdSku"
          actionRef={modalTableRef}
          request={(params) => handleGetGoodsList(params)}
          columns={
            columns.filter(
              (o) => includesInArray(['option', 'totalQuantity'], o.dataIndex) === false,
            ) as ProColumns<any>[]
          }
          bordered
          options={false}
          size="small"
          cardProps={{
            bodyStyle: {
              padding: 0,
            },
          }}
          rowSelection={rowSelection}
          pagination={{ pageSize: 10, showSizeChanger: false }}
          tableAlertRender={false}
          scroll={{ x: 'max-content' }}
          {...{
            toolbar: {
              menu: hasVirtualWarehouse
                ? {
                    type: 'tab',
                    activeKey: activeKey,
                    items: [
                      {
                        key: searchWarehouseTypeMap.actual,
                        label: textMap.txtActualWare,
                      },
                      {
                        key: searchWarehouseTypeMap.virtual,
                        label: textMap.txtVirtualWare,
                      },
                    ],
                    onChange: (key) => {
                      setActiveKey(`${key}`);
                      modalTableRef.current?.setPageInfo?.({ current: 1 });
                      modalTableRef.current?.reload();
                    },
                  }
                : undefined,
            },
          }}
        />
      </Modal>
    </div>
  );
}

export default FormGoodsChoose;

function useConfig({
  formIns,
  warehouseList,
  onGoodsRemove,
  initParams,
  allowBatch,
}: {
  formIns: FormInstance;
  warehouseList: any[];
  onGoodsRemove: any;
  initParams: TypeInitParams;
  allowBatch?: boolean;
}) {
  const columns = [
    {
      title: textMap.txtWarehouseOrganization,
      key: 'warehouseId',
      dataIndex: 'warehouseCode',
      ellipsis: true,
      initialValue: initParams.warehouseId,
      formItemProps: {
        name: 'warehouseId',
      },
      renderFormItem: () => {
        return (
          <SearchSelect
            {...{
              disabled: initParams.warehouseId !== undefined,
              options: warehouseList,
            }}
          />
        );
      },
    },
    {
      title: textMap.txtGoodsSku,
      dataIndex: 'sku',
    },
    {
      title: textMap.txtGoodsPicture,
      dataIndex: 'goodsPicture',
      render: (_: any, record: any) => {
        return <GoodsImage src={record.goodsPicture} />;
      },
      search: false,
    },
    {
      title: textMap.txtGoodsName,
      dataIndex: 'goodsName',
      render: (_: any, record: any) => {
        return (
          <div>
            <div>{record.goodsName}</div>
            <div>{record.goodsEnName}</div>
          </div>
        );
      },
      search: false,
    },
    {
      title: textMap.txtGoodsNetWeight,
      dataIndex: 'weight',
      search: false,
      render: (_: any, record: TypeOutboundGoodsTB) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title: textMap.txtGoodsVolume,
      dataIndex: 'goodsVolume',
      search: false,
      render: (_: any, record: TypeOutboundGoodsTB) =>
        `${record.volumeUnit}: ${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`,
    },
    {
      title: textMap.txtInventoryQuantity,
      dataIndex: 'goodQuantity',
      search: false,
    },
    {
      title: textMap.txtOutboundQuantity,
      dataIndex: 'totalQuantity',
      width: 210,
      render: (dom, record, index) => {
        if (allowBatch) {
          const range = [1, record.goodQuantity];

          return (
            <Form.Item
              {...{
                name: ['goodsList', index, 'totalQuantity'],
                rules: [
                  {
                    required: true,
                    message: textMap.txtMsgTotalQuantity,
                  },
                  {
                    transform: (val) => Number(val),
                    async validator(rule, val) {
                      if (range[1] < 1) {
                        throw new Error(textMap.txtInventoryNotEnough);
                      }
                      if (
                        val < range[0] ||
                        val > range[1] ||
                        ruleUtils.ruleNoZeroPositiveInteger.pattern.test(val as string) === false
                      ) {
                        throw new Error(
                          textMap.txtMsgTotalValid({ val1: range[0], val2: range[1] }),
                        );
                      }
                    },
                  },
                ],
              }}
            >
              <InputNumber
                {...{
                  onChange() {
                    formIns.validateFields(['goodsList']);
                  },
                }}
              />
            </Form.Item>
          );
        }
        /** 不支持批量商品出库 就展示默认1 */
        return 1;
      },
    },
    {
      title: textMap.txtOperation,
      dataIndex: 'option',
      fixed: 'right',
      width: 100,
      render: (dom, record) => {
        return (
          <Popconfirm title={textMap.txtMsgConfirmDelete} onConfirm={() => onGoodsRemove(record)}>
            <a>{textMap.txtDelete}</a>
          </Popconfirm>
        );
      },
    },
  ] as ProColumns<any>[];

  return {
    columns,
  };
}

function useHasVirtualWarehouse({ formIns }: { formIns: FormInstance }) {
  const companyId = Form.useWatch('companyId', formIns);
  const [hasVirtualWarehouse, setHasVirtualWarehouse] = useState(false);

  useEffect(() => {
    (async () => {
      if (!companyId) {
        setHasVirtualWarehouse(false);
        return;
      }
      const { data } = await apiOutboundHasVirtualWarehouse({ companyId });

      setHasVirtualWarehouse(data === true);
    })();
  }, [companyId]);

  return {
    hasVirtualWarehouse,
  };
}
