import { request } from 'umi';
import { fileListDeconStruct } from './components';

/** 退件列表 */
export function apiReturnList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** tab名称 PENDING_ACCEPT-待认领, CLAIMED-已认领	 */
      tab: 'PENDING_ACCEPT' | 'CLAIMED';
      /** 申请单号 */
      podOrderNoList?: string[];
      /** 业务类型 300-未知退件, 301-物流退件, 302-客户退件 */
      businessTypeList?: number[];
      /** 订单状态 301-待认领, 302-已认领, 330-标记退件, 390-标记销毁 */
      orderStatusList?: number[];
      /** 审核类型 1-待审核, 2-已审核 */
      orderReviewTypeList?: number[];
      /** 退件追踪单号 */
      returnTrackingNoList?: string[];
      /** 包裹状态 1-完好, 2-破损, 99-未知 */
      packageStatusList?: number[];
      /** 创建时间 */
      createDate?: {
        /** 最小值 */
        min?: string;
        /** 最大值 */
        max?: string;
        /** 是否包含最小值 */
        includeMin?: boolean;
        /** 是否包含最大值 */
        includeMax?: boolean;
      };
    };
  }>,
) {
  return request<
    NsApi.TypeResponseData<{
      records: TypeReturnRecord[];
      tabNum: {
        /** 待认领 */
        PENDING_ACCEPT: number;
        /** 已认领 */
        CLAIMED: number;
      };
    }>
  >('/zouwu-oms-order/portal/return/order/page', { data, method: 'POST' });
}

/** 退件订单导出 */
export function apiReturnOrderExport(
  data: TypeReturnOrderFormData & {
    /** 自定义文件名 */
    fileName?: string;
  },
) {
  return request('/zouwu-oms-system/portal/return/order/export', { data, method: 'POST' });
}

/** 退件订单详情 */
export function apiReturnOrderDetail(data: { podOrderNo?: string }) {
  return request<NsApi.TypeResponseData<TypeReturnOrderDetail>>(
    '/zouwu-oms-order/portal/return/order/detail',
    { data, method: 'POST' },
  ).then((res) => {
    const detailData = res.data || {};

    return {
      ...res,
      data: {
        ...detailData,
        ...fileListDeconStruct(detailData.orderFileList || []),
        companyInfo: detailData.companyId
          ? { label: detailData.companyName, value: detailData.companyId }
          : undefined,
      },
    };
  });
}

/** 退件订单编辑, 门户没有新增, 只是占位 */
export function apiReturnOrderCreate(data: TypeReturnOrderFormData) {
  return async () => {
    // kiwi-disable-next-line
    console.error('门户没有新增, 只是占位, 逻辑不应该被执行到');
  };
}

/** 退件订单编辑 */
export function apiReturnOrderEdit(data: TypeReturnOrderFormData) {
  return request<NsApi.TypeResponseData<TypeReturnOrderDetail>>(
    '/zouwu-oms-order/portal/return/order/edit',
    { data, method: 'POST' },
  );
}

/**
 * 获取退件订单号
 */
export function apiReturnOrderNoCreate() {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-order/portal/common/getOrderNo', {
    method: 'POST',
    data: {
      type: 3,
    },
  });
}

/**
 * 退件订单认领
 */
export function apiReturnOrderClaim(data: { podOrderNo: string }) {
  return request<NsApi.TypeResponseData<string>>('/zouwu-oms-order/portal/return/order/claim', {
    method: 'POST',
    data,
  });
}

/** 退件订单标记销毁 */
export function apiReturnOrderMaskDestroy(data: { podOrderNo: string }) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/mark/destroy',
    {
      method: 'POST',
      data,
    },
  );
}

/**
 * 退件订单标记入库
 * @deprecated 原标入库和换标入库上线后废弃
 */
export function apiReturnOrderMaskInbound(data: { podOrderNo: string }) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/mark/inbound',
    {
      method: 'POST',
      data,
    },
  );
}

/**
 * 校验sku是否属于认领客户下
 * @deprecated 原标入库和换标入库上线后废弃
 */
export function apiReturnOrderCheckSkuForCompany(data: { podOrderNo: string }) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/checkSkuForCompany',
    {
      method: 'POST',
      data,
    },
  );
}

/** 退件订单换标入库 */
export function apiReturnOrderSkuChangeInbound(data: { podOrderNo: string; skuChangeMap: string }) {
  return request('/zouwu-oms-order/portal/return/order/inbound/change', {
    method: 'POST',
    data,
  });
}

/** 退件订单原标入库 */
export function apiReturnOrderSkuOriginInbound(data: { podOrderNo: string }) {
  return request('/zouwu-oms-order/portal/return/order/inbound/origin', {
    method: 'POST',
    data,
  });
}

/**
 * 批量认领退件订单
 */
export function apiReturnOrderBatchClaim(data: string[]) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/batch/claim',
    {
      method: 'POST',
      data,
    },
  );
}

/** 批量标记销毁退件订单 */
export function apiReturnOrderBatchMarkDestroy(data: string[]) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/batch/mark/destroy',
    {
      method: 'POST',
      data,
    },
  );
}

/** 批量原标入库退件订单 */
export function apiReturnOrderBatchSkuOriginInbound(data: string[]) {
  return request<NsApi.TypeResponseData<string>>(
    '/zouwu-oms-order/portal/return/order/batch/inbound/origin',
    {
      method: 'POST',
      data,
    },
  );
}
