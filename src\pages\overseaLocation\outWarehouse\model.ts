import type { Effect, Reducer } from 'umi';
import {
  queryOutBoundOrderList,
  queryOutBoundOrderDetail,
  queryOutBoundOrderForm,
  queryOverseaLocationInventoryList,
  queryOverseaLocationCombinedGoodsInventoryList,
  sendCreateOutBoundOrder,
  sendEditOutBoundOrder,
  sendSaveOutBoundOrder,
  sendCancelOutBoundOrder,
  sendDeleteOutBoundOrder,
} from '@/service/overseaLocation';
import {
  apiBatchOutOrderSaveDraft,
  apiBatchOutOrderSubmit,
  apiBatchOutWarehouseRemoveOne,
} from '@/pages/overseaLocation/importManage/importApi';

export interface StateType {
  outWarehouseList: any[];
  outWarehouseTotal: number;
  outWarehouseStatusCount: any;
}

export interface Modal {
  namespace: string;
  state: StateType;
  effects: {
    getOutWarehouseList: Effect;
    getOutWarehouseDetail: Effect;
    getOutWarehouseForm: Effect;

    getSingleGoodsList: Effect;
    getCombinedGoodsList: Effect;
    createOutBoundOrder: Effect;
    editOutBoundOrder: Effect;
    saveOutBoundOrder: Effect;
    cancelOutBoundOrder: Effect;
    deleteOutBoundOrder: Effect;
  };
  reducers: {
    saveOutWarehouseList: Reducer<any>;
  };
}

const Model: Modal = {
  namespace: 'out_warehouse',
  state: {
    outWarehouseList: [],
    outWarehouseTotal: 0,
    outWarehouseStatusCount: {},
  },
  effects: {
    *getOutWarehouseList({ payload }, { call, put }) {
      const response = yield call(queryOutBoundOrderList, payload);

      if (response && response.success) {
        yield put({
          type: 'saveOutWarehouseList',
          payload: response.result,
        });
        return response.result;
      }
    },
    *getOutWarehouseDetail({ payload }, { call, put }) {
      const response = yield call(queryOutBoundOrderDetail, payload);

      if (response && response.success) {
        return response.result;
      }
    },
    /** 已废弃,迁移至  apiOutWarehouseGetFormData*/
    *getOutWarehouseForm({ payload }, { call, put }) {
      const response = yield call(queryOutBoundOrderForm, payload);

      if (response && response.success) {
        return response.result;
      }
    },
    *getSingleGoodsList({ payload }, { call, put }) {
      const response = yield call(queryOverseaLocationInventoryList, payload);

      return response;
    },
    *getCombinedGoodsList({ payload }, { call, put }) {
      const response = yield call(queryOverseaLocationCombinedGoodsInventoryList, payload);

      if (response && response.success) {
        return {
          data:
            response.result.records && response.result.records.length
              ? convertCombinedGoodsRecord(response.result.records)
              : [],
          total:
            response.result && response.result.totalSize ? Number(response.result.totalSize) : 0,
        };
      }
    },
    /** 已废弃 */
    *createOutBoundOrder({ payload }, { call }) {
      /** orderSource: 5 表示批量创建出库 */
      const { orderSource } = payload;
      const response = yield call(
        Number(orderSource) === 5 ? apiBatchOutOrderSubmit : sendCreateOutBoundOrder,
        payload,
      );

      if (response && response.success) {
        return response.success;
      }
    },
    *editOutBoundOrder({ payload }, { call }) {
      const response = yield call(sendEditOutBoundOrder, payload);

      if (response && response.success) {
        return response.success;
      }
    },
    /** 废弃 */
    *saveOutBoundOrder({ payload }, { call }) {
      /** orderSource: 5 表示批量创建出库 */
      const { orderSource } = payload;
      const response = yield call(
        Number(orderSource) === 5 ? apiBatchOutOrderSaveDraft : sendSaveOutBoundOrder,
        payload,
      );

      if (response && response.success) {
        return response.success;
      }
    },
    /** 门户出库取消接口废弃 */
    *cancelOutBoundOrder({ payload }, { call }) {
      const response = yield call(sendCancelOutBoundOrder, payload);

      if (response && response.success) {
        return response;
      }
    },
    /** @deprecated 已废弃迁移至OMS */
    *deleteOutBoundOrder({ payload }, { call }) {
      /** orderSource: 5 通过批量出库创建的订单 */
      const { orderSource } = payload;
      const response = yield call(
        Number(orderSource) === 5 ? apiBatchOutWarehouseRemoveOne : sendDeleteOutBoundOrder,
        payload,
      );

      if (response && response.success) {
        return response;
      }
    },
  },
  reducers: {
    saveOutWarehouseList(state, { payload }) {
      return {
        ...state,
        outWarehouseList: payload.page && payload.page.records ? payload.page.records : [],
        outWarehouseTotal:
          payload.page && payload.page.totalSize ? Number(payload.page.totalSize) : 0,
        outWarehouseStatusCount: (payload && payload.tabNum) || null,
      };
    },
  },
};

export default Model;

const convertCombinedGoodsRecord = (records: any[]) => {
  const getPriceValue = (data: any[]) => {
    if (data && data.length) {
      const target = data[0];

      return {
        goodsValue: target.goodsDeclaredValue,
        valueUnit: target.currency,
      };
    }

    return {};
  };

  return records.map((o) => ({
    sku: o.sku,
    id: o.id,
    goodsName: o.name,
    goodsEnName: o.enName,
    goodsPicture: o.pictureUrl,
    goodQuantity: o.goodQuantity,
    warehouseId: o.warehouseId,
    warehouseName: o.warehouseName,
    warehouseCode: o.warehouseCode,
    ...getPriceValue(o.destinationGoodsValues),
    goodsList: o.goodsList.map((c: any) => ({
      id: c.id,
      sku: c.sku,
      goodsName: c.name,
      goodsEnName: c.enName,
      goodsPicture: c.pictureUrl,
      ...getPriceValue(c.destinationGoodsValues),
    })),
  }));
};
