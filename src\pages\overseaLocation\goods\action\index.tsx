import React, { useState, useEffect } from 'react';
import { FooterToolbar } from '@ant-design/pro-layout';
import {
  Spin,
  Form,
  Card,
  Row,
  Col,
  Space,
  Input,
  Radio,
  message,
  Button,
  InputNumber,
} from 'antd';
import { cloneDeep } from 'lodash';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import styles from './index.less';
import {
  YES_OR_NO_OPTIONS,
  BALE_TYPE_OPTIONS,
  CONTAIN_BATTERY_OPTIONS,
  BATTERY_TYPE_OPTIONS,
  PACKING_TYPE_OPTIONS,
  EnumActionType,
} from '@/utils/const';
import { CustomizeDivider } from '@/views';
import GoodsBoxTableForm from '../components/GoodsBoxTableForm';
import GoodsServiceSetTableForm from '../components/GoodsServiceSetTableForm';
import GoodsPlatformSkuTableForm from '../components/GoodsPlatformSkuTableForm';
import GoodsImageUpload from '../components/GoodsImageUpload';
import { AddDeletedFlagByList, Exchange, GetPageQuery } from '@/utils/util';
import { CommonRulesMap } from '@/utils/rules';
import { historyGoPrev, tipsUtils } from '@/pages/overseaLocation/utils';
import { apiGoodsCreate, apiGoodsDetails, apiGoodsUpdate } from '../goodsApi';
import { LoadButton } from '../../components';
import { FormPalletConfig } from '../components';
import { overseaRulesMap } from '@/utils-business';

const { Item } = Form;

type TypeProps = {};

/** 商品编辑页面 */
export default function GoodsActionPage(props: TypeProps) {
  const [form] = Form.useForm();
  const [electrified, setElectrified] = useState<boolean>(false);
  const [baseInfo, setBaseInfo] = useState<TypeGoodsBaseInfo>({});
  const [goods, setGoods] = useState<TypeGoodsTB>({});
  const { type = EnumActionType.ADD, id = '' } = GetPageQuery();

  const unitMap = {
    measureType: 1,
    weightUnit: 'KG',
    measureUnit: 'CM',
  };

  useEffect(() => {
    asyncInit();
  }, []);

  async function asyncInit() {
    if (id) {
      const { data: result } = await apiGoodsDetails({ id });

      if (result) {
        setElectrified(!!result?.electrified);
        result.file = { url: result.pictureUrl, name: result.serverPictureName };
        (result.goodsBoxList || []).forEach((item) => {
          item.file = { url: item.pictureUrl, name: item.pictureName };
        });
        if (type === EnumActionType.COPY) {
          delete result.id;
          delete result.sku;
          delete result.pictureUrl;
          delete result.file;
          delete result.serverPictureName;
          (result.goodsPlatformList || []).forEach((item) => {
            delete item.id;
            delete item.goodsId;
          });
          // (result.goodsPallet || []).forEach((item) => {
          //   delete item.goodsId;
          //   delete item.id;
          // });
          result.goodsPallet?.goodsId;
          result.goodsPallet?.id;
        }
        setGoods(result);
        setBaseInfo({
          length: result.length,
          width: result.width,
          height: result.height,
          weight: result.weight,
          file: result.file,
        });
        form.setFieldsValue(result);
      }
    }
  }

  // 返回
  const handleGoBack = () => {
    historyGoPrev();
  };

  const setBaseInfoFieldValue = (field: string, value: number | string | undefined) => {
    const goodsBaseInfo: any = cloneDeep(baseInfo);

    goodsBaseInfo[field] = value;
    setBaseInfo(goodsBaseInfo);
  };

  async function asyncSubmit() {
    try {
      await form.validateFields();
    } catch (err) {
      console.error(err);
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      onFinishFailed(err);
      return Promise.reject();
    }
    const formData = getFormData(form.getFieldsValue());

    await (formData?.id ? apiGoodsUpdate : apiGoodsCreate)(formData);

    message.success(
      type === EnumActionType.EDIT
        ? I18N.Src__Pages__OverseaLocation__Goods__Action.Index.modificationSucceeded
        : I18N.Src__Pages__OverseaLocation__Goods__Action.Index.successfullyAdded,
    );
    historyGoPrev();
  }

  const getFormData = (values: any) => {
    values.pictureUrl = values.file?.url;
    values.serverPictureName = values.file?.name;

    for (let i = 0; i < values.goodsBoxList?.length; i += 1) {
      const box = values.goodsBoxList[i];

      box.pictureUrl = box.file?.url;
      box.pictureName = box.file?.name;
    }

    const formData = { ...values, ...unitMap };

    if (type === EnumActionType.EDIT) {
      return {
        ...goods,
        ...formData,
        goodsCustomsInformationUpdateList: AddDeletedFlagByList(
          goods.goodsCustomsInformationList || [],
          formData.goodsCustomsInformationList || [],
        ),
        goodsPlatformUpdateList: AddDeletedFlagByList(
          goods.goodsPlatformList || [],
          formData.goodsPlatformList || [],
        ),
        goodsBoxUpdateList: AddDeletedFlagByList(
          goods.goodsBoxList || [],
          formData.goodsBoxList || [],
        ),
        // goodsPallet: AddDeletedFlagByList(goods.goodsPallet || [], formData.goodsPallet || []),
      };
    }

    return formData;
  };

  const onFinishFailed = (errorInfo: any) => {
    const names: string[] = errorInfo.errorFields[0].name;
    const labelNode = document.querySelector(
      `label[for="${names.length > 1 ? `${names[0]}_${names[1]}` : `${names[0]}`}"]`,
    );

    if (labelNode) {
      labelNode.scrollIntoView({ block: 'center' });
    } else {
      form.scrollToField(errorInfo.errorFields[0].name.toString());
    }
  };

  /** 长宽高重 编辑模式禁止修改, 以前是已复核禁止编辑
   * @ LWHW 长宽高重缩写
   */
  const disabledLWHW = type === EnumActionType.EDIT;

  return (
    <PageContainer title={false} fixedHeader className="container-layout-card">
      <Spin spinning={false}>
        <Card>
          <Form form={form} layout="vertical">
            <CustomizeDivider title={I18N.Src__Pages__Order__Detail.SoPane.essentialInformation} />
            <Row gutter={24}>
              <Col span={6}>
                <Item name="file">
                  <GoodsImageUpload
                    onChange={(file?: any) => {
                      setBaseInfoFieldValue('file', file);
                    }}
                  />
                </Item>
              </Col>
              <Col span={18}>
                <Row gutter={24}>
                  <Col span={8}>
                    <Item
                      name="sku"
                      label="SKU"
                      rules={[...overseaRulesMap.overseaSKU]}
                      normalize={(value) => Exchange(value, true)}
                    >
                      <Input
                        placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
                        disabled={type === EnumActionType.EDIT && !!goods?.id}
                      />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      name="name"
                      label={I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName}
                      rules={[CommonRulesMap.commonStrVerify(255, true)]}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      name="enName"
                      label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.commodityEnglish}
                      rules={[
                        CommonRulesMap.commonStrVerify(255, true),
                        CommonRulesMap.commonNoChinese,
                      ]}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      {...{
                        name: 'skuRemark',
                        label: '客户SKU',
                        tooltip: '只做记录，无业务逻辑。',
                        rules: [CommonRulesMap.commonStrVerify(50)],
                      }}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      {...{
                        name: 'otherCode',
                        label: I18N.Src__Pages__OverseaLocation__Goods__Action.Index.otherCodes,
                        tooltip: '商品条码；有值时，值为商品条码；为空时，则SKU为商品条码。',
                        rules: [CommonRulesMap.commonStrVerify(50)],
                      }}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item name="upc" label="UPC" rules={[...overseaRulesMap.overseaUPC]}>
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item name="ean" label="EAN" rules={[CommonRulesMap.commonStrVerify(50)]}>
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      name="safeNums"
                      label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.safetyStock}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={1}
                        maxLength={9}
                        placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter}
                        precision={0}
                      />
                    </Item>
                  </Col>
                  <Col span={16}>
                    <Item
                      name="comment"
                      label={
                        I18N.Src__Pages__OverseaLocation__Goods__Action.Index.productDescription
                      }
                      rules={[CommonRulesMap.commonStrVerify(255)]}
                    >
                      <Input placeholder={I18N.Src__Pages__Company__DepManager.Index.pleaseEnter} />
                    </Item>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.long}
                  name="length"
                  rules={[{ required: true }, CommonRulesMap.commonFloatTwo]}
                >
                  <Input
                    addonAfter={unitMap.measureUnit}
                    onBlur={(e) => setBaseInfoFieldValue('length', e.target.value)}
                    disabled={disabledLWHW}
                  />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.wide}
                  name="width"
                  rules={[{ required: true }, CommonRulesMap.commonFloatTwo]}
                >
                  <Input
                    addonAfter={unitMap.measureUnit}
                    onBlur={(e) => setBaseInfoFieldValue('width', e.target.value)}
                    disabled={disabledLWHW}
                  />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.high}
                  name="height"
                  rules={[{ required: true }, CommonRulesMap.commonFloatTwo]}
                >
                  <Input
                    addonAfter={unitMap.measureUnit}
                    onBlur={(e) => setBaseInfoFieldValue('height', e.target.value)}
                    disabled={disabledLWHW}
                  />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__Order__Detail.SiItem.weight}
                  name="weight"
                  rules={[{ required: true }, CommonRulesMap.commonFloatThree]}
                >
                  <Input
                    addonAfter={unitMap.weightUnit}
                    onBlur={(e) => setBaseInfoFieldValue('weight', e.target.value)}
                    disabled={disabledLWHW}
                  />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.brand}
                  name="brand"
                  rules={[CommonRulesMap.commonStrVerify(50)]}
                >
                  <Input />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.materialScience}
                  name="material"
                  rules={[CommonRulesMap.commonStrVerify(50)]}
                >
                  <Input />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.specifications}
                  name="specifications"
                  rules={[CommonRulesMap.commonStrVerify(50)]}
                >
                  <Input />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.purpose}
                  name="purpose"
                  rules={[CommonRulesMap.commonStrVerify(50)]}
                >
                  <Input />
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.whetherCharged}
                  name="electrified"
                  initialValue={false}
                >
                  <Radio.Group
                    /* 开放编辑,后端控制是否允许编辑的逻辑 */
                    // disabled={type === EnumActionType.EDIT}
                    onChange={(e) => setElectrified(e.target.value)}
                  >
                    {YES_OR_NO_OPTIONS.map((item) => (
                      <Radio value={item.value} key={item.label}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.isItOrderly}
                  name="serialNumber"
                  initialValue={false}
                >
                  <Radio.Group>
                    {YES_OR_NO_OPTIONS.map((item, index) => (
                      <Radio value={item.value} key={index}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.packagingSettings}
                  name="baleType"
                  initialValue={1}
                >
                  <Radio.Group>
                    {BALE_TYPE_OPTIONS.map((item) => (
                      <Radio value={item.value} key={item.value}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Item>
              </Col>
              <Col span={6}>
                <Item
                  label={I18N.Src__Pages__Order__Components.ContainerModal.packagingType}
                  name="packingType"
                  initialValue={1}
                >
                  <Radio.Group>
                    {PACKING_TYPE_OPTIONS.map((item) => (
                      <Radio value={item.value} key={item.value}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Item>
              </Col>
            </Row>
            {electrified === true && (
              <Row gutter={24} className={styles['goods-battery-wrapper']}>
                <Col span={12}>
                  <Item
                    label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.whetherTheBattery}
                    name="containBattery"
                    initialValue
                  >
                    <Radio.Group>
                      {CONTAIN_BATTERY_OPTIONS.map((item, index) => (
                        <Radio value={item.value} key={index}>
                          {item.label}
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Item>
                </Col>
                <Col span={12}>
                  <Item
                    label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.batteryType}
                    name="batteryType"
                    initialValue={2}
                  >
                    <Radio.Group>
                      {BATTERY_TYPE_OPTIONS.map((item) => (
                        <Radio value={item.value} key={item.value}>
                          {item.label}
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Item>
                </Col>
              </Row>
            )}
            <CustomizeDivider
              title={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.boxGauge1}
            />
            <GoodsBoxTableForm
              name="goodsBoxList"
              rules={[
                {
                  validator: async (_: any, names: any[]) => {
                    if (!names || names.length < 1) {
                      return Promise.reject(
                        new Error(
                          I18N.Src__Pages__OverseaLocation__Goods__Action.Index.boxGaugeAtLeast,
                        ),
                      );
                    }
                  },
                },
              ]}
              unitMap={unitMap}
              goodsBaseInfo={baseInfo}
            />
            <CustomizeDivider
              title={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.serviceSettings}
            />
            <Row gutter={24}>
              {/* <Col span={6}>
                <Item
                  label={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.insuranceServices}
                  name="insure"
                  initialValue={false}
                >
                  <Radio.Group>
                    {YES_OR_NO_OPTIONS.map((item, index) => (
                      <Radio value={item.value} key={index}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Item>
              </Col> */}
            </Row>
            <GoodsServiceSetTableForm
              name="goodsCustomsInformationList"
              rules={[
                {
                  validator: async (_: any, names: any[]) => {
                    if (form.getFieldValue('insure')) {
                      if (!names || names.length < 1 || names.length > 100) {
                        return Promise.reject(
                          new Error(
                            I18N.Src__Pages__OverseaLocation__Goods__Action.Index.insuranceSettings,
                          ),
                        );
                      }
                    }
                  },
                },
              ]}
              // customerCurrency="USD"
              // customerCurrency={accountDetail.customerCurrency!}
            />
            <CustomizeDivider title={'打托设置'} />
            <FormPalletConfig />
            <CustomizeDivider
              title={I18N.Src__Pages__OverseaLocation__Goods__Action.Index.eCommercePlatform}
            />
            <GoodsPlatformSkuTableForm
              name="goodsPlatformList"
              rules={[
                {
                  validator: async (_: any, names: any[]) => {
                    if (names?.length > 100) {
                      return Promise.reject(
                        new Error(
                          I18N.Src__Pages__OverseaLocation__Goods__Action.Index.platformAtMost,
                        ),
                      );
                    }
                  },
                },
              ]}
            />
          </Form>
        </Card>
        <FooterToolbar
          style={{ textAlign: 'center' }}
          extra={
            <Space>
              <Button onClick={handleGoBack}>{I18N.Src__Pages__Order__Detail.Index.return}</Button>
              <LoadButton type="primary" onClick={asyncSubmit}>
                {I18N.Src__Components__UploadFileModal.Index.submit}
              </LoadButton>
            </Space>
          }
        />
      </Spin>
    </PageContainer>
  );
}
