import { Button, Divider, Form, Input, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
import moment from 'moment';
import type { ActionType, ProColumns, ProFormInstance } from '@/common-import';
import { PageContainer, ProFormTextArea } from '@/common-import';
import { LoadButton, SearchSelect, ZouProTable } from '@/components';
import type { TypeZouProTableProps } from '@/components';
import {
  eR,
  emptyRenderArray,
  historyGoChild,
  historyGoPush,
  includesInArray,
  strUtils,
  useModalSet,
} from '@/utils';
import {
  apiReturnList,
  apiReturnOrderBatchClaim,
  apiReturnOrderBatchMarkDestroy,
  apiReturnOrderBatchSkuOriginInbound,
  apiReturnOrderCheckSkuForCompany,
  apiReturnOrderClaim,
  apiReturnOrderExport,
  apiReturnOrderMaskDestroy,
  apiReturnOrderMaskInbound,
  apiReturnOrderSkuOriginInbound,
} from './ReturnApi';
import { AsyncExportButton, imagePreviewIns, ListImageItem } from '@/views';
import { apiMapDictType, apiQueryWarehouseOptions } from '../../api';
import { EnumReturnFileType, EnumReturnOrderStatus } from './ReturnOrderEnum';
import { ModalSkuChangeInbound } from './components';
import TextArea from 'antd/lib/input/TextArea';
import { asyncModalConfirm } from '@/utils-business';

/** 退件列表 */
function ReturnList() {
  const { config, getSearchData, modalNodeList, asyncBatchProcess, activeKey } = useConfig();

  return (
    <PageContainer>
      {modalNodeList}
      <ZouProTable
        {...config}
        toolBarRender={() => [
          activeKey === 'CLAIMED' && (
            <LoadButton
              key={'maskDestroy'}
              {...{
                type: 'default',
                async onClick() {
                  return asyncBatchProcess({ type: 'maskDestroy' });
                },
              }}
            >
              标记销毁
            </LoadButton>
          ),
          activeKey === 'CLAIMED' && (
            <LoadButton
              key={'skuOriginInbound'}
              {...{
                type: 'default',
                async onClick() {
                  return asyncBatchProcess({ type: 'skuOriginInbound' });
                },
              }}
            >
              原标入库
            </LoadButton>
          ),
          activeKey === 'PENDING_ACCEPT' && (
            <LoadButton
              key={'claim'}
              {...{
                type: 'default',
                async onClick() {
                  return asyncBatchProcess({ type: 'claim' });
                },
              }}
            >
              认领
            </LoadButton>
          ),
          <AsyncExportButton
            key={'export'}
            {...{
              children: '导出',
              buttonProps: {
                async onClick(e, params) {
                  params.form.setFieldsValue({
                    // kiwi-disable-next-line
                    fileName: `退件导出${moment().format('YYYYMMDD')}`,
                  });
                },
              },

              async request({ form }) {
                const { fileName } = form.getFieldsValue();
                const condition = getSearchData();

                await apiReturnOrderExport({ ...condition, fileName });
              },
            }}
          />,
        ]}
      />
    </PageContainer>
  );
}

export default ReturnList;

function useConfig() {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const { columns, modalNodeList } = useColumns();
  const activeKeyDefault = 'CLAIMED';
  const [activeKey, setActiveKey] = useState<string>(activeKeyDefault);
  const [tabNum, setTabNum] = useState<{ CLAIMED?: number; PENDING_ACCEPT?: number }>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<TypeReturnRecord[]>([]);
  const queryParams = useRef<{ tab: string }>({ tab: activeKeyDefault });
  const setQueryParams = (params: { tab?: string }) => {
    queryParams.current = { tab: queryParams.current?.tab, ...params };
    setActiveKey(queryParams.current.tab);
    actionRef.current?.setPageInfo?.({ current: 1 });
    actionRef.current?.reload();
  };
  const config = {
    rowKey: 'podOrderNo',
    columns,
    actionRef,
    formRef,
    scroll: { x: 'max-content' },
    search: {
      labelWidth: 110,
    },
    rowSelection: {
      selectedRowKeys,
      onChange(newSelectedRowKeys: string[], newSelectedRows: TypeReturnRecord[]) {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(newSelectedRows);
      },
    },
    toolbar: {
      menu: {
        type: 'tab',
        activeKey,
        items: [
          {
            key: 'CLAIMED',
            label: (
              <span>
                已认领
                {tabNum.CLAIMED ? ` (${tabNum.CLAIMED})` : null}
              </span>
            ),
          },
          {
            key: 'PENDING_ACCEPT',
            label: (
              <span>
                待认领
                {tabNum.PENDING_ACCEPT ? ` (${tabNum.PENDING_ACCEPT})` : null}
              </span>
            ),
          },
        ],
        onChange: (key: string) => {
          setQueryParams({ tab: key });
        },
      },
    },
    async request(params) {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
          ...queryParams.current,
        },
      } as Parameters<typeof apiReturnList>[0];
      const { data } = await apiReturnList(query);
      const _tabNum = data.tabNum || {};

      setTabNum(_tabNum);

      return {
        data: data.records,
        success: true,
        // total: data.totalSize,
        total: Number(_tabNum[query.condition.tab]),
      };
    },
  } as TypeZouProTableProps<any, any>;

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      ...formData,
      ...queryParams.current,
    } as Parameters<typeof apiReturnList>[0];
  }

  /** 批量处理 */
  async function asyncBatchProcess({
    type,
  }: {
    type: 'maskDestroy' | 'skuOriginInbound' | 'claim';
  }) {
    if (selectedRowKeys.length === 0) {
      message.error('请选择要操作的单据');
      return;
    }
    const operateInfoMap = {
      maskDestroy: {
        typeName: '标记销毁',
        msgError: '存在不允许标记销毁的退件单',
        api: apiReturnOrderBatchMarkDestroy,
      },
      skuOriginInbound: {
        typeName: '原标入库',
        msgError: '存在不允许原标入库的退件单',
        api: apiReturnOrderBatchSkuOriginInbound,
      },
      claim: {
        typeName: '认领',
        msgError: '存在不允许认领的退件单',
        api: apiReturnOrderBatchClaim,
      },
    };
    const { typeName, msgError } = operateInfoMap[type];

    if (includesInArray(['skuOriginInbound'], type)) {
      /** 全部已认领才可以进行操作 */
      const isNotAllow =
        selectedRows.every((row) =>
          includesInArray(
            [EnumReturnOrderStatus.CLAIMED, EnumReturnOrderStatus.MARK_INBOUND],
            row.orderStatus,
          ),
        ) === false;

      if (isNotAllow) {
        message.error(msgError);
        return;
      }
    }

    if (includesInArray(['maskDestroy'], type)) {
      /** 全部已认领才可以进行操作 */
      const isNotAllow =
        selectedRows.every((row) => row.orderStatus === EnumReturnOrderStatus.CLAIMED) === false;

      if (isNotAllow) {
        message.error(msgError);
        return;
      }
    }

    if (type === 'claim') {
      /** 全部未认领才可以进行操作 */
      const isNotAllow =
        selectedRows.every((row) => row.orderStatus === EnumReturnOrderStatus.UNCLAIMED) === false;

      if (isNotAllow) {
        message.error(msgError);
        return;
      }
    }

    await asyncModalConfirm({
      title: `确定${typeName}吗?`,
      async onOk() {
        try {
          await operateInfoMap[type].api(selectedRows.map((row) => row.podOrderNo!) || []);
          message.success(`${typeName}成功`);
        } catch (error) {
          console.error(error);
        } finally {
          actionRef.current?.reload();
          setSelectedRowKeys([]);
          setSelectedRows([]);
        }
      },
    });
  }

  return {
    config,
    getSearchData,
    modalNodeList,
    selectedRowKeys,
    setSelectedRowKeys,
    selectedRows,
    setSelectedRows,
    asyncBatchProcess,
    activeKey,
  };
}

function useColumns() {
  const { modalActions, modalNodeList } = useModalSet({ skuChangeInbound: ModalSkuChangeInbound });
  const columns = [
    {
      title: '单号',
      dataIndex: 'noSearchValue',
      search: {
        transform(val, namePath, formData) {
          const { noSearchValue, _noType } = formData;

          console.log('=======> formData:', formData);
          return {
            noSearchValue: undefined,
            podOrderNoList:
              _noType === '1' ? strUtils.strSplitForOrderNo(noSearchValue) : undefined,
            returnTrackingNoList:
              _noType === '2' ? strUtils.strSplitForOrderNo(noSearchValue) : undefined,
          };
        },
      },
      render(dom, record) {
        return (
          <div>
            <div>跟踪单号: {eR(record.returnTrackingNo)}</div>
            <div>退件单号: {eR(record.podOrderNo)}</div>
          </div>
        );
      },
      formItemProps: {
        label: (
          <span onClick={(e) => e.preventDefault()}>
            <Form.Item {...{ noStyle: true, name: '_noType', initialValue: '1' }}>
              <SearchSelect
                {...{
                  style: { width: 98 },
                  allowClear: false,
                  options: [
                    { label: '退件单号', value: '1' },
                    { label: '跟踪单号', value: '2' },
                  ],
                }}
              />
            </Form.Item>
          </span>
        ),
        name: 'noSearchValue',
      },
      renderFormItem(schema, config, form) {
        return <ProFormTextArea noStyle />;
      },
    },
    {
      title: '商品SKU',
      dataIndex: 'sku',
      render(dom, record) {
        return emptyRenderArray(record.orderGoodsList, (item) => {
          return (
            <div key={item.id}>
              {item.sku} {`x${item.totalQuantity}`}
            </div>
          );
        });
      },
    },
    {
      title: '换标SKU',
      dataIndex: 'changedSku',
      render(dom, record) {
        const changedSkuArr = record.orderGoodsList?.filter((item) => item.changedSku) || [];

        return emptyRenderArray(changedSkuArr, (item) => {
          return (
            <div key={item.id}>
              {item.changedSku} {`x${item.totalQuantity}`}
            </div>
          );
        });
      },
    },
    {
      title: '海外仓附件',
      dataIndex: 'orderFileList',
      render(dom, record) {
        const arr = (record.orderFileList || [])
          .filter((item) => item.fileType === EnumReturnFileType.oms)
          .map((item) => ({ ...item, url: item.filePath }));

        if (arr.length === 0) {
          return '-';
        }

        return (
          <>
            {arr.map((item) => {
              return (
                <ListImageItem
                  key={item.id}
                  {...{
                    src: item.filePath,
                    preview: { visible: false },
                    onClick(e) {
                      imagePreviewIns?.open({
                        imgList: arr,
                        curUrl: item.filePath,
                      });
                    },
                  }}
                />
              );
            })}
          </>
        );
      },
    },
    {
      title: '客户附件',
      dataIndex: 'orderFileList',
      render(dom, record) {
        const arr = (record.orderFileList || [])
          .filter((item) => item.fileType === EnumReturnFileType.portal)
          .map((item) => ({ ...item, url: item.filePath }));

        if (arr.length === 0) {
          return '-';
        }

        return (
          <>
            {arr.map((item) => {
              return (
                <ListImageItem
                  key={item.id}
                  {...{
                    src: item.filePath,
                    preview: { visible: false },
                    onClick(e) {
                      imagePreviewIns?.open({
                        imgList: arr,
                        curUrl: item.filePath,
                      });
                    },
                  }}
                />
              );
            })}
          </>
        );
      },
    },
    {
      title: '仓库',
      dataIndex: 'targetWarehouseCode',
      search: true,
      valueType: 'searchSelect',
      fieldProps: {
        request: (queryParam: string) =>
          apiQueryWarehouseOptions({ queryParam }, { valueName: 'code' }),
      },
    },
    {
      title: '客户名称',
      dataIndex: 'companyName',
      search: false,
    },
    {
      title: '包裹状态',
      dataIndex: 'packageStatus',
      search: true,
      valueType: 'select',
      request: apiMapDictType.returnPackageStatus,
    },
    { title: '退件原因', dataIndex: 'returnReason' },
    { title: '客户备注', dataIndex: 'customerRemark' },
    {
      title: '处理状态',
      dataIndex: 'orderStatus',
      search: true,
      valueType: 'select',
      request: apiMapDictType.returnOrderStatus,
    },
    {
      title: '退件类型',
      dataIndex: 'businessType',
      search: true,
      valueType: 'select',
      request: apiMapDictType.returnBusinessType,
    },
    { title: '预计销毁时间', dataIndex: 'expectDestroyTime' },
    { title: '创建时间', dataIndex: 'createDate', search: true, valueType: 'dateRange2' },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      width: 'auto',
      render: (text, record, index, action) => {
        const { podOrderNo, orderStatus } = record as Required<TypeReturnRecord>;
        /** 原标入库 */
        const skuOriginInboundNode = (
          <LoadButton
            {...{
              popconfirmProps: {
                title: '确定原标入库吗?',
              },
              async onClick() {
                await apiReturnOrderSkuOriginInbound({ podOrderNo });
                message.success('原标入库成功');
                action?.reload();
              },
            }}
          >
            原标入库
          </LoadButton>
        );
        /** 换标入库 */
        const skuChangeInboundNode = (
          <LoadButton
            {...{
              async onClick() {
                modalActions.skuChangeInbound?.open(
                  { podOrderNo, skuChangeList: record.orderGoodsList || [] },
                  {
                    submitSuccessCB() {
                      action?.reload();
                    },
                  },
                );
              },
            }}
          >
            换标入库
          </LoadButton>
        );

        return (
          <Space
            {...{
              size: 0,
              style: { maxWidth: 180 },
              wrap: true,
              split: <Divider type="vertical" />,
            }}
          >
            {orderStatus === EnumReturnOrderStatus.UNCLAIMED && (
              <LoadButton
                {...{
                  popconfirmProps: {
                    title: '确定认领吗?',
                  },
                  async onClick() {
                    await apiReturnOrderClaim({ podOrderNo });
                    message.success('认领成功');
                    action?.reload();
                  },
                }}
              >
                认领
              </LoadButton>
            )}

            {orderStatus === EnumReturnOrderStatus.CLAIMED && (
              <>
                {skuOriginInboundNode}
                {skuChangeInboundNode}
                <LoadButton
                  {...{
                    popconfirmProps: {
                      title: '确认标记销毁吗?',
                    },
                    async onClick() {
                      await apiReturnOrderMaskDestroy({ podOrderNo });
                      message.success('标记销毁成功');
                      action?.reload();
                    },
                  }}
                >
                  标记销毁
                </LoadButton>
                {/* <LoadButton
                  {...{
                    popconfirmProps: {
                      title: '确认标记入库吗?',
                    },
                    async onClick() {
                      await apiReturnOrderMaskInbound({ podOrderNo });
                      message.success('标记入库成功');
                      action?.reload();
                    },
                  }}
                >
                  标记入库
                </LoadButton> */}
                <a
                  onClick={() =>
                    historyGoChild({
                      pathname: 'return-edit',
                      query: { podOrderNo },
                      newTab: true,
                    })
                  }
                >
                  编辑
                </a>
              </>
            )}

            {orderStatus === EnumReturnOrderStatus.MARK_INBOUND && (
              <>
                {/* <LoadButton
                {...{
                  async onClick() {
                    await apiReturnOrderCheckSkuForCompany({ podOrderNo });
                    historyGoPush({
                      pathname: '/oversea-location/enter-warehouse/action-create',
                      query: { podOrderNoInReturnInfo: podOrderNo },
                      newTab: true,
                    });
                  },
                }}
              >
                退件入库
              </LoadButton> */}
                {skuOriginInboundNode}
                {skuChangeInboundNode}
              </>
            )}

            <a
              onClick={() =>
                historyGoChild({
                  pathname: 'return-detail',
                  query: { podOrderNo },
                  newTab: true,
                })
              }
            >
              详情
            </a>
          </Space>
        );
      },
    },
  ] as ProColumns<TypeReturnRecord>[];

  return { columns, modalNodeList };
}
