/* eslint-disable no-continue */
import { isEmpty, debounce } from 'lodash';
import { useEffect } from 'react';
import { useModel } from 'umi';

type TUseSensorsProps = {
  /**
   * TODO
   * 是否需要开启自动收集上报埋点
   */
  needAuto?: boolean;
  /**
   * 模块基础数据，后续会加入到 `data` 中
   *
   * @da
   */
  pageData?: Record<string, any>;
} & Partial<RenameKeyToWWLPortalOptions> &
  TTrackParams['options'];

type TTrackParams = {
  /**
   * 埋点事件名
   */
  name: string;
  // name: S extends `WWL_PORTAL_${infer K}_V2` ? `WWL_PORTAL_${K}` : never;
  /**
   * 埋点相关属性
   *
   * 常用 `wwl_portal_`
   */
  data?: Record<string, any>;
  options?: {
    /**
     * 是否保留原有埋点（事件名、属性名不转换）
     */
    isOriginal?: boolean;

    /**
     * 是否加入用户信息数据
     * @default true
     */
    addUserInfo?: boolean;
  };
};

/** 忽略webUser中不需要的key */
const ignoreWebUserKey = ['password', 'permissionsList'];

interface RenameKeyToWWLPortalOptions {
  /**
   * 前缀字符串
   * @default 'wwl_portal_'
   */
  prefix?: string;
  /** 中间拼接字符串 */
  midfix?: string;
  /**
   * 后缀字符串
   * @default 'v2'
   */
  suffix?: string;
  /**
   * 是否对 `属性key` 转换 `大写`
   * @default false
   */
  isUpperCase?: boolean;
  /** 忽略发送的属性key */
  ignore?: string[];
}

/** 命名默认属性 */
const defaultRenameOptions: RenameKeyToWWLPortalOptions = {
  prefix: 'wwl_portal_',
  midfix: '',
  suffix: '_v2',
  isUpperCase: false,
};

/** 发送埋点数据默认属性 */
const defaultTrackerOptions: TTrackParams['options'] = {
  isOriginal: false,
  addUserInfo: true,
};

export function useSensors(props?: TUseSensorsProps) {
  const {
    initialState: { webUser = {} },
  } = useModel('@@initialState');
  const propsSensorsOptions = {
    ...defaultRenameOptions,
    ...defaultTrackerOptions,
    ...props,
  };

  useEffect(() => {
    if (props && props.needAuto && !window.WWL_PORTAL_autoOrderTracker) {
      window.WWL_PORTAL_autoOrderTracker = true;
      window.addEventListener('click', autoOrderTracker);
    }

    return () => {
      window.WWL_PORTAL_autoOrderTracker = false;
      window.removeEventListener('click', autoOrderTracker);
    };
  }, []);

  /**
   * 处理object字符串
   *
   * 处理后格式 `wwl_portal_operation_v2`
   *
   * @param renameOptions.prefix key值前缀
   * @default renameOptions.prefix `wwl_portal_`
   *
   * @param renameOptions.midfix key值模块
   *
   * @param renameOptions.suffix key值后缀
   * @default renameOptions.suffix `v2`
   *
   * @example
   * ``` ts
   * renameKeyToWWLPortal('test', {
   *  midfix: 'test'
   * });
   *
   * `wwl_portal_test_v2`
   * ```
   */
  const renameKeyToWWLPortal = (defaultKey: string, renameOptions: RenameKeyToWWLPortalOptions) => {
    const { prefix, midfix, suffix, isUpperCase } = {
      ...propsSensorsOptions,
      ...renameOptions,
    };

    if (!midfix || typeof midfix !== 'string') {
      console.warn('midfix is not the desired format in the sensors.');
    }

    const lowerDefaultKey = defaultKey.toLocaleLowerCase();
    let returnKey = '';

    if (lowerDefaultKey.startsWith(prefix!) && lowerDefaultKey.endsWith(suffix!)) {
      returnKey = defaultKey;
    } else if (!lowerDefaultKey.startsWith(prefix!) && lowerDefaultKey.endsWith(suffix!)) {
      returnKey += `${prefix}${midfix ? `${midfix}_` : ''}${defaultKey}`;
    } else if (lowerDefaultKey.startsWith(prefix!) && !lowerDefaultKey.endsWith(suffix!)) {
      returnKey = `${defaultKey}${suffix}`;
    } else {
      returnKey += `${prefix}${midfix ? `${midfix}_` : ''}${defaultKey}${suffix}`;
    }

    return isUpperCase ? returnKey.toLocaleUpperCase() : returnKey.toLocaleLowerCase();
  };

  const objectRename = (obj: Record<string, any>, options: RenameKeyToWWLPortalOptions) => {
    const newObj = {} as Record<string, any>;

    for (const key of Object.keys(obj)) {
      if ((options.ignore && options.ignore.includes(key)) || obj[key] === undefined) continue;
      newObj[renameKeyToWWLPortal(key, options) as string] = obj[key];
    }
    return newObj;
  };

  // 驼峰转下划线
  const toLine = (str: string) => {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  };

  const trackerSend = ({ name = '', data = {}, options = {} }: TTrackParams) => {
    if (!name || typeof name !== 'string') {
      console.error('The sensors name of the midfix is not needed');
      return;
    }

    const mergerOptions = {
      isOriginal: propsSensorsOptions.isOriginal,
      addUserInfo: propsSensorsOptions.addUserInfo,
      ...options,
    };

    console.log(mergerOptions, propsSensorsOptions, options);

    // 所有埋点事件增加公共事件属性，表示门户
    let currentSensorsData: Record<string, any> = {
      wwl_portal_site: 'WWL_PORTAL',
      wwl_portal_site_href: location.pathname,
    };

    // 如果需要加入用户信息
    if (mergerOptions.addUserInfo) {
      currentSensorsData = {
        ...currentSensorsData,
        ...objectRename(webUser, {
          // 给用户信息加入前缀和后缀
          midfix: 'webuser',
          ignore: ignoreWebUserKey,
          suffix: '',
        }),
      };
    }

    const sensorsKey = renameKeyToWWLPortal(name, {
      isUpperCase: true,
    });

    const sensorsData = mergerOptions.isOriginal
      ? { ...data, ...currentSensorsData, ...propsSensorsOptions.pageData }
      : {};

    if (!mergerOptions.isOriginal && data && !isEmpty(data)) {
      const mergerOriginalData = { ...data, ...propsSensorsOptions.pageData };

      for (const dataKey of Object.keys(mergerOriginalData)) {
        sensorsData[renameKeyToWWLPortal(toLine(dataKey), { isUpperCase: false })] =
          mergerOriginalData[dataKey];
      }
    }

    // @ts-ignore
    if (window.WWL_SENSORS_DEBUG) {
      console.log(`==============${sensorsKey}==============`);
      console.table(mergerOptions);
      console.table(Object.assign(sensorsData, currentSensorsData));
      // @ts-ignore
      (window as any).WWL_SENSORS_DEBUG_HANDLE &&
        window.WWL_SENSORS_DEBUG_HANDLE(sensorsKey, Object.assign(sensorsData, currentSensorsData));
    }

    if (window.sensorsTrack) {
      sensorsTrack(sensorsKey, Object.assign(sensorsData, currentSensorsData));
    }
  };

  // TODO: 后续根据data-* 自定义属性区分module
  const autoOrderTracker = debounce((e: Event) => {
    const { target }: any = e;

    const flag = target?.getAttribute('data-order-track-flag');

    const trackType = target?.getAttribute('data-order-track-type');

    const orderTrackNo = target?.getAttribute('data-order-track-no');

    if ((flag || trackType) && props?.needAuto) {
      trackerSend({
        name: 'WWL_PORTAL_ORDER_ALL_PRESS',
        data: {
          // order_pathname: props?.pathname,
          order_click_type: trackType || target?.innerText,
          order_click_no: orderTrackNo,
        },
      });
    }
  }, 500);

  return {
    trackerSend,
    autoOrderTracker,
  };
}
