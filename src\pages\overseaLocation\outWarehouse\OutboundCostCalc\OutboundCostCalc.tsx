import React, { useState } from 'react';
import { FooterToolbar } from '@ant-design/pro-layout';
import { PageContainer } from '@/common-import';
import { Button, Card, Form, InputNumber, message, Space, Spin, Switch } from 'antd';
import { LoadButton, SearchSelect } from '@/components';
import { historyGoPrev, tipsUtils, useModalSet } from '@/utils';
import { CustomizeDivider, RowLayout, TextValue } from '@/views';
import I18N from '@/utils/I18N';
import { CommonRulesMap } from '@/utils/rules';
import { apiQueryWarehouseOptions } from '@/api';
import { apiOutboundCostCalc } from './OutboundCostCalcApi';
import { transOutboundFormData } from '../OutboundApi';
import { goodsRelationFieldClear } from '../OutboundEdit';
import { FormLogisticService, FormRecipientAddress } from '../components';
import { ModalCostCalcResult } from './components';
import type { FormInstance } from 'antd/es/form';
import { EnumOutboundBusinessType } from '../OutboundEnum';
import type { TypeWarehouseOption } from '@/pages/pagesApi';

const { Item } = Form;
const unitMap = {
  measureType: 1,
  weightUnit: 'KG',
  /** 接口定义如此, 长度单位取这个值 */
  volumeUnit: 'CM',
};

/** 出库尾程费用试算 */
export default function OutboundCostCalc() {
  const { form, asyncCostCalc, costCalcModalNode } = useConfig();
  const [pageLoading, setPageLoading] = useState(false);

  return (
    <PageContainer title={false}>
      {costCalcModalNode}
      <Spin spinning={pageLoading}>
        <Card>
          <Form
            {...{
              form,
              layout: 'vertical',
              initialValues: {
                businessType: EnumOutboundBusinessType.normal,
              },
            }}
          >
            <Item hidden>
              <TextValue {...{ label: '出库单类型', name: 'businessType' }} />
            </Item>
            <CustomizeDivider title={'基础信息'} />
            <RowLayout columnNum={4}>
              <Item
                {...{
                  label: '发货仓库',
                  name: 'deliveryWarehouseCode',
                  rules: [{ required: true }],
                }}
              >
                <SearchSelect
                  {...{
                    request: (queryParam) =>
                      apiQueryWarehouseOptions(
                        { queryParam, warehouseEnable: true },
                        { valueName: 'code' },
                      ),
                    onChange(val, option?: TypeWarehouseOption) {
                      form.setFieldsValue({
                        createDeliveryWarehouseId: option?.warehouseId,
                        express: {
                          insuredAmountCurrency: option?.currency,
                        },
                      });
                      goodsRelationFieldClear(form);
                    },
                  }}
                />
              </Item>
              <Item
                {...{
                  label: '是否带电',
                  name: ['goodsList', 0, 'electrified'],
                  valuePropName: 'checked',
                  initialValue: false,
                }}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Item>
            </RowLayout>
            <Item hidden>
              <Item
                {...{
                  label: '长度单位',
                  name: ['goodsList', 0, 'volumeUnit'],
                  initialValue: unitMap.volumeUnit,
                }}
              />
              <Item
                {...{
                  label: '重量单位',
                  name: ['goodsList', 0, 'weightUnit'],
                  initialValue: unitMap.weightUnit,
                }}
              />
              <Item
                {...{
                  label: '商品数量',
                  name: ['goodsList', 0, 'totalQuantity'],
                  initialValue: 1,
                }}
              />
            </Item>
            <Form.List {...{ name: 'goodsList' }}>
              {function () {
                return (
                  <RowLayout columnNum={4}>
                    <Item
                      {...{
                        label: I18N.Src__Pages__OverseaLocation__Goods__Action.Index.long,
                        name: [0, 'goodsLength'],
                        rules: [{ required: true }, CommonRulesMap.commonFloatTwo],
                      }}
                    >
                      <InputNumber addonAfter={unitMap.volumeUnit} stringMode />
                    </Item>
                    <Item
                      {...{
                        label: I18N.Src__Pages__OverseaLocation__Goods__Action.Index.wide,
                        name: [0, 'goodsWidth'],
                        rules: [{ required: true }, CommonRulesMap.commonFloatTwo],
                      }}
                    >
                      <InputNumber addonAfter={unitMap.volumeUnit} stringMode />
                    </Item>
                    <Item
                      {...{
                        label: I18N.Src__Pages__OverseaLocation__Goods__Action.Index.high,
                        name: [0, 'goodsHeight'],
                        rules: [{ required: true }, CommonRulesMap.commonFloatTwo],
                      }}
                    >
                      <InputNumber addonAfter={unitMap.volumeUnit} stringMode />
                    </Item>
                    <Item
                      {...{
                        label: I18N.Src__Pages__Order__Detail.SiItem.weight,
                        name: [0, 'goodsWeight'],
                        rules: [{ required: true }, CommonRulesMap.commonFloatThree],
                      }}
                    >
                      <InputNumber addonAfter={unitMap.weightUnit} stringMode />
                    </Item>
                  </RowLayout>
                );
              }}
            </Form.List>
            <CustomizeDivider title={'物流服务'} />
            <FormLogisticService {...{ form }} />
            <CustomizeDivider title={'收件人信息'} />
            <FormRecipientAddress {...{ form, wmsSystemCode: '' }} />
          </Form>
        </Card>
        <FooterToolbar
          {...{
            style: { textAlign: 'center' },
            extra: (
              <Space>
                <Button onClick={() => historyGoPrev()}>{'返回'}</Button>
                <LoadButton
                  {...{
                    type: 'primary',
                    async onClick() {
                      setPageLoading(true);
                      await asyncCostCalc().finally(() => setPageLoading(false));
                    },
                  }}
                >
                  费用试算
                </LoadButton>
              </Space>
            ),
          }}
        />
      </Spin>
    </PageContainer>
  );
}

/** 费用试算逻辑统一处理 */
export function useCostCalc({ form }: { form: FormInstance<TypeOutboundFormData> }) {
  const { modalActions, modalNodeList: costCalcModalNode } = useModalSet({
    calcResult: ModalCostCalcResult,
  });

  async function asyncCostCalc() {
    // console.log(transOutboundFormData(form.getFieldsValue()));
    /** 读取原始值, 校验完成后复原, 因为创建出库单和费用试算公用同一个form表单 */
    const originWmsSystemCode = form.getFieldValue('wmsSystemCode');
    const { goodsList, businessType } = form.getFieldsValue();
    const sum = (goodsList || []).reduce(
      (prevVal, curVal) => prevVal + Number(curVal?.totalQuantity || 0),
      0,
    );

    form.setFieldsValue({
      wmsSystemCode: 'COST_CALC',
    });
    try {
      await form.validateFields();
    } catch (err) {
      console.error(err);
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      return Promise.reject();
    } finally {
      form.setFieldsValue({ wmsSystemCode: originWmsSystemCode });
    }
    if (sum !== 1 || Number(businessType) !== EnumOutboundBusinessType.normal) {
      message.error('试算仅支持标准出库, 一票一件');
      return;
    }
    const formData = form.getFieldsValue();
    const { data: result } = await apiOutboundCostCalc(formData);

    modalActions.calcResult?.open(result);
  }

  return {
    asyncCostCalc,
    costCalcModalNode,
  };
}

function useConfig() {
  const [form] = Form.useForm();
  const { asyncCostCalc, costCalcModalNode } = useCostCalc({ form });

  return { form, asyncCostCalc, costCalcModalNode };
}
