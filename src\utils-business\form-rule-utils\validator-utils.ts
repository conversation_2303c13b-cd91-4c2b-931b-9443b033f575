/** 校验重复项 */
export async function asyncCheckRepeat(
  valArr: Record<string, any>[],
  params: { findValue: string | undefined; valueName: string },
) {
  const { findValue, valueName } = params;
  const trimValue = findValue?.trim();

  /** 非数组不进入判断 */
  if (Array.isArray(valArr) === false || !findValue) {
    return;
  }
  /** 仅查找当前重复 */
  const count = (valArr || []).filter((item) => item[valueName]?.trim() === trimValue).length;

  if (count > 1) {
    throw new Error('存在重复项');
  }
}
