# start project

## Getting Started

Install dependencies,

```bash
$ yarn
```

Start the dev server,

```bash
$ yarn start
```



## **git commit message规范**



> **Commit message一般包括三部分：`Header`、`Body`和`Footer`**



````
<type>(<scope>): <subject>

<BLANK LINE>

<body>

<BLANK LINE>
````



### Header

Header部分只有一行，包括三个字段：`type`（必需）、`scope`（可选）和`subject`（必需）。



#### type

`type`：用于说明commit的类别，规定为如下几种



- feat：新增功能；

* fix：修复bug；

* docs：修改文档；

- refactor：代码重构，未新增任何功能和修复任何bug；

- build：改变构建流程，新增依赖库、工具等（例如webpack修改）；

- style：仅仅修改了空格、缩进等，不改变代码逻辑；

- perf：改善性能和体现的修改；

- chore：非src和test的修改；

- test：测试用例的修改；

- ci：自动化流程配置修改；

- revert：回滚到上一个版本；





####  scope

`scope`【可选】用于说明commit的影响范围

* all :表示影响面大 ，如修改了网络框架  会对真个程序产生影响
* loation :表示影响小，某个小小的功能
* module :表示会影响某个模块 如登录模块、首页模块 、用户管理模块等等



####  subject

`subject` 是commit的简要说明，尽量简短



### **Body**



对本次commit的详细描述，可分多行，下面是一个范例

````
More detailed explanatory text, if necessary.  Wrap it to 
about 72 characters or so. 

Further paragraphs come after blank lines.

- Bullet points are okay, too
- Use a hanging indent
````





### **尾部（Footer）**



> 不兼容变动：需要描述相关信息

> 关闭指定Issue：输入Issue信息