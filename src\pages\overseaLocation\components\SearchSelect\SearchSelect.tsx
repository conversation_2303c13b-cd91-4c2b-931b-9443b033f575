/* eslint-disable @typescript-eslint/no-unused-expressions */
import { Select } from 'antd';
import type { SelectProps } from 'antd/es/select';
import type { Ref } from 'react';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useRequest } from 'umi';
import type { BaseOptions } from '@ahooksjs/use-request/lib/types';
import classnames from 'classnames';
import { compUtils, typeUtils } from '@/utils';
import './SearchSelect.less';
import { textMap } from './text-map';

export type TypeSearchSelectRef = {
  /** 当前Options结果 */
  options?: Record<string, any>[];
  /** 是否请求中 */
  isLoading: boolean;
  /** 重新请求Options,
   * @返回结果 Promise<null>
   * @感觉是useRequest V2的缺陷, 据说V3 runAsync可以 */
  fetchOptions: (...args: any[]) => Promise<any>;
  /** 用于联动时发起Options请求,会立刻屏蔽旧的Options */
  fetchOptionsForCascade: (...args: any[]) => Promise<any>;
};
export type TypeSearchSelectProps<VT> = SelectProps<VT, any> & {
  /** 请求方法 */
  request?: (...args: any[]) => Promise<any[]>;
  /** useRequest options参数类型 */
  requestOptionsParams?: BaseOptions<any, any[]>;
  /** 延迟时间, 默认 500 ms */
  debounceTimeout?: number;
  /** 是否自动加载数据,默认 是 */
  autoLoad?: boolean;
  /** 默认 是,为 否 则永不执行request */
  allowRequest?: boolean;
  /** focus是否可以加载数据 默认 是 */
  allowFocusLoad?: boolean;
  /** 过滤时inputValue转换,默认trim */
  filterInputTrans?: (inputValue: string) => string;
  /** 通过已有数据进行搜索, 默认 否 */
  searchByLocal?: boolean;
  /** 根据多个属性值匹配下拉 */
  searchKeys?: TypeCalcOptionsParams['searchKeys'];
  /** 允许大小写模糊搜索, 采用全转小写, 默认true */
  allowLowercaseSearch?: boolean;
  /** 根据value展示指定选项,其余过滤,优先级高于exclude */
  include?: TypeCalcOptionsParams['include'];
  /** 根据value过滤指定选项,其余展示 */
  exclude?: TypeCalcOptionsParams['exclude'];
  /** 多列下拉定义 */
  columnsTable?: TypeCalcOptionsParams;
};
/**
 * 基于useRequest实现的搜索下拉框
 * VT === ValueType
 */
const SearchSelect = function <VT>(
  {
    request,
    /** 如果options !== undefined, 则不会触发fetchOptions */
    options,
    debounceTimeout = 500,
    autoLoad = true,
    allowRequest = true,
    allowFocusLoad = true,
    searchByLocal = false,
    allowLowercaseSearch = true,
    requestOptionsParams,
    include,
    exclude,
    searchKeys,
    columnsTable,
    filterInputTrans = (inputValue) => inputValue?.trim() ?? '',
    /** 不应该存在解构非自定义属性
     * @select内部的属性都通过 props.xxxx 去调用
     */
    ...props
  }: TypeSearchSelectProps<VT>,
  ref: Ref<TypeSearchSelectRef | undefined>,
) {
  const searchValueRef = useRef('');
  /** 是否因联动发起请求, 会屏蔽options展示,直到新的请求完成 */
  const [isCascadeLoading, setIsCascadeLoading] = useState(false);
  const {
    loading,
    data: dataOptions,
    run,
  } = useRequest<{ data: any[] }>(request!, {
    debounceInterval: debounceTimeout,
    formatResult: (response) => response,
    ...requestOptionsParams,
    manual: true,
    onSuccess() {
      setIsCascadeLoading((oldVal) => {
        return oldVal === true ? false : oldVal;
      });
    },
  });

  const fetchOptions = async (...args: any[]) => {
    /** useRequest V2 run并不是根据请求完成来的,无法判断请求是否完成
     * @所以通过onSuccess实现
     * @v3有一个runAsync 或许可以,没有细研究
     */
    return options === undefined && request && allowRequest ? run(...args) : undefined;
  };
  const fetchOptionsForCascade = async (...args: any[]) => {
    setIsCascadeLoading(true);
    const res = fetchOptions(...args);

    return res;
  };
  const { innerSearchKeys, newOptions, tableHeaderNode, selectOptionsNode } = calcNewOptions({
    ...columnsTable,
    searchKeys,
    options: options || (dataOptions as any[] | undefined),
    include,
    exclude,
  });

  useEffect(() => {
    autoLoad && fetchOptions();
  }, []);

  const defaultProps = {
    filterOption: (inputValue, option) => {
      let inVal = `${filterInputTrans(inputValue)}`;
      const optionFilterProp = props.optionFilterProp || defaultProps.optionFilterProp!;
      /** 允许将自定义字符串作为搜索区域 */
      let optionString =
        innerSearchKeys?.map((key) => option?.origin?.[key] || '').join(' ') ||
        option?.[optionFilterProp] ||
        option?.origin?.[optionFilterProp];

      /** 转换成字符串,保障小写安全 */
      optionString = optionString ? `${optionString}` : '';
      allowLowercaseSearch && (optionString = optionString.toLowerCase());
      allowLowercaseSearch && (inVal = inVal.toLowerCase());

      return optionString?.indexOf(inVal) > -1;
    },
    showSearch: true,
    optionFilterProp: 'label',
    placeholder: textMap.placeholder,
    allowClear: true,
    ...(tableHeaderNode
      ? {
          /** 有tableHeaderNode时,说明是多列下拉, 有一些默认属性 */
          optionLabelProp: 'label',
          dropdownMatchSelectWidth: 360,
          dropdownRender(menu) {
            return (
              <div className="zw-search-select__dropdowns-table">
                {tableHeaderNode}
                {menu}
              </div>
            );
          },
        }
      : undefined),
  } as SelectProps<VT>;

  const newProps = compUtils.propsMerge(defaultProps, props, {
    onSearch:
      props.showSearch !== false &&
      searchByLocal !== true &&
      ((searchValue) => {
        /** search 搜索后如果选择了其中一项, 后续不会触发onSearch, 没选择就会触发
         * @手动在onChange中触发一次
         */
        searchValueRef.current = searchValue;
        fetchOptions(filterInputTrans(searchValue));
      }),
    onFocus() {
      allowFocusLoad && fetchOptions();
    },
    onDropdownVisibleChange(isOpen) {
      /** 如果最后一次search结果不为空, 主动触发一次search */
      isOpen === false && searchValueRef.current !== '' && newProps?.onSearch('');
    },
  });

  const valueEnum = useMemo(() => {
    const ve: Record<string, boolean> = {};

    newOptions?.forEach((item) => {
      ve[item.value] = true;
    });
    return ve;
  }, [newOptions]);
  /** 如果value在valueEnum中存在结果,则将value转string */
  const newValue = calcValue(newProps.value, { valueEnum, options: newOptions || [] });

  useImperativeHandle(ref, () => ({
    options: newOptions,
    isLoading: loading,
    fetchOptions,
    fetchOptionsForCascade,
  }));

  return (
    <Select
      loading={loading}
      {...newProps}
      {...{
        value: newValue,
        className: classnames('zw-search-select', newProps.className),
        dropdownClassName: classnames(
          'zw-search-select__dropdown',
          columnsTable?.columns && 'zw-search-select__table',
          newProps.dropdownClassName,
        ),
      }}
      /** 比较特殊,所以没有写在propsMerge里面
       * @需要自己处理外部onChange触发 */
      onChange={(value, matchOption) => {
        // console.log('=======> value:', props.value, value, matchOption);
        /**
         * 因为Select.Option 绑驼峰值会报大量警告, dom 元素不支持
         * 统一放到origin中再解构出来
         * labelInValue开启时,合并返回整个option选项
         */
        const newOption = Array.isArray(matchOption)
          ? matchOption.map((item, i) => {
              const outerValueArr = props.value as any[] | undefined;
              const curVal = (value as any)[i];

              return {
                /* matchOption 会根据value值匹配, 如果childOptions没有结果,或者没有初始化,会导致labelInValue直接设置值匹配不到,然后删除异常
                 */
                ...(item.origin ||
                  outerValueArr?.find(
                    /** labelInValue 状态下, 如果match匹配不到值, matchOption是一个空对象, 需要从外部props.value中取一次值赋值 */
                    (outerItem) =>
                      /** 此处key值必须有效, 否则undefined相等没意义 */
                      (outerItem.key !== undefined && outerItem.key === curVal?.key) ||
                      (outerItem.value !== undefined && outerItem.value === curVal?.value),
                  )),
                /**
                 * 这里将labelInValue的结果覆盖上去, 保障无下拉选项时, 删除仍然有效
                 */
                ...curVal,
              };
            })
          : matchOption && { ...matchOption.origin };

        newProps?.onChange?.(props.labelInValue ? newOption : value, newOption);
      }}
    >
      {isCascadeLoading ? undefined : props.children || selectOptionsNode}
    </Select>
  );
};

export default React.forwardRef(SearchSelect);

/** 计算value值
 * @尝试解决num和numString无法匹配的问题
 * @默认options的value类型都是一致的
 */
function calcValue<T>(
  value: T,
  {
    valueEnum,
    options,
  }: {
    valueEnum: Record<string, boolean>;
    options: any[];
  },
) {
  if (typeUtils.isSafeNumOrNumString(value) && valueEnum[value as string] && options?.length > 0) {
    const transMap = {
      string: String,
      number: Number,
    };

    return transMap[typeof options[0]?.value as keyof typeof transMap]?.(value);
  }
  return value;
}

/** 计算options参数 */
type TypeCalcOptionsParams = {
  /** 搜索时, 用于匹配的值 */
  searchKeys?: string[];
  /** 当前options传参 */
  options?: any[];
  /** 根据value展示指定选项,其余过滤,优先级高于exclude */
  include?: any[];
  /** 根据value过滤指定选项,其余展示 */
  exclude?: any[];
  /** 是否允许换行, 默认不允许 */
  allowWrap?: boolean;
  /** 多列下拉定义 */
  columns?: {
    title: string;
    dataIndex: string;
    /** 当列宽足够分配时,最大列宽才有效, 默认列宽平分 */
    maxWidth?: number | string;
    render?: (val: any, option: any) => React.ReactNode;
  }[];
};

/** 计算newOptions */
function calcNewOptions(params: TypeCalcOptionsParams) {
  const { columns, searchKeys } = params;
  const validOptions = filterValidOptions(params);

  if (columns) {
    return calcColumnsOptions({ ...params, options: validOptions });
  }
  return {
    innerSearchKeys: searchKeys,
    newOptions: validOptions,
    tableHeaderNode: undefined,
    selectOptionsNode: renderSelectOptionsNode({ ...params, options: validOptions }),
  };
}

/** 计算多列下拉 */
function calcColumnsOptions(params: TypeCalcOptionsParams) {
  const { options, columns, allowWrap, searchKeys } = params;
  const tableHeaderNode = (
    <div className="zw-search-select__table-header">
      {columns?.map((column) => {
        const { maxWidth, title, dataIndex } = column;

        return (
          <div
            key={dataIndex}
            {...{
              title,
              className: 'zw-search-select__table-header-cell',
              style: { maxWidth },
            }}
          >
            {title}
          </div>
        );
      })}
    </div>
  );
  const newOptions = options?.map((option) => {
    const newOption = {
      ...option,
      optionNode: (
        <>
          {columns?.map((column, i) => {
            const { dataIndex, render, maxWidth } = column;
            const value = option[dataIndex];

            return (
              <div
                key={dataIndex}
                {...{
                  className: classnames(
                    'zw-search-select__table-option-cell',
                    allowWrap && 'zw-search-select__allow-wrap',
                  ),
                  title: value,
                  style: { maxWidth },
                }}
              >
                {render ? render(value, option) : value}
              </div>
            );
          })}
        </>
      ),
    };

    return newOption;
  });

  return {
    innerSearchKeys: searchKeys ?? columns?.map((column) => column.dataIndex),
    newOptions,
    tableHeaderNode,
    selectOptionsNode: renderSelectOptionsNode({ ...params, options: newOptions }),
  };
}

/** 渲染Select.Option */
function renderSelectOptionsNode(params: TypeCalcOptionsParams) {
  const { options } = params;

  return options?.map((item: any) => {
    const { label, value, disabled } = item;

    return (
      <Select.Option
        /** 优先使用key, 否则使用value */
        key={item.key ?? value}
        {...{
          className: classnames(item.className, 'zw-search-select__table-option'),
          value,
          label,
          disabled,
          origin: item,
        }}
      >
        {item.optionNode || label}
      </Select.Option>
    );
  });
}

/** 过滤有效的options */
function filterValidOptions(params: TypeCalcOptionsParams) {
  const { options, include, exclude } = params;

  return options
    ?.map((item?: any) => {
      const { value } = item;
      let isHide = item?._isHide ?? false;

      if (include && include.length > 0) {
        /** 正选时include必须有值才生效 */
        include.indexOf(value) === -1 && (isHide = true);
      } else if (exclude) {
        exclude.indexOf(value) !== -1 && (isHide = true);
      }

      if (isHide || !item) {
        return null;
      }

      return { ...item };
    })
    .filter(Boolean);
}
