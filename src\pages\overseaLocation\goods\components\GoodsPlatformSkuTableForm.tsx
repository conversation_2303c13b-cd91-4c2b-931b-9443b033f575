import I18N from '@/utils/I18N';
import React, { useEffect, useState } from 'react';
import { Form, Table, Space, Select, Input, Button, Popconfirm } from 'antd';
import type { ValidatorRule } from 'rc-field-form/lib/interface';
import type { Dispatch } from 'umi';
import { getLocale, useDispatch } from 'umi';
import styles from './GoodsPlatformSkuTableForm.less';
import { CommonRulesMap } from '@/utils/rules';
import { SearchSelect } from '../../components';
import { apiMapDictType } from '../../api';

interface IProps {
  name: string;
  rules?: ValidatorRule[];
}

/** 电商平台SKU */
function GoodsPlatformSkuTableForm({ name: prefix }: IProps) {
  const [platformOptions, setPlatformOptions] = useState<any[]>([]);
  const isZhCn: boolean = getLocale() === 'zh-CN';
  const dispatch = useDispatch();

  // useEffect(() => {
  //   initPaltformOptions();
  // }, []);

  const initPaltformOptions = async () => {
    const result = await dispatch({
      type: 'global/getCommercePaltformRichSelectData',
      payload: { dataDictCode: 'commerce_platform' },
    });

    setPlatformOptions(result || []);
  };

  return (
    <Form.List name={prefix}>
      {(fields, { add, remove }) => {
        const columns = [
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsPlatformSkuTableForm
                .platform,
            width: 150,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'platformCode']}
                  fieldKey={[field.fieldKey, 'platformCode']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect,
                    },
                  ]}
                >
                  <SearchSelect {...{ request: apiMapDictType.orderSourcePlatform }} />
                  {/* <Select optionLabelProp="label" showSearch allowClear>
                    {platformOptions.map((item) => {
                      return (
                        <Select.Option
                          value={item.code}
                          key={item.code}
                          label={item[`${isZhCn ? 'cn' : 'en'}Name`]}
                        >
                          <div className={styles.optionLabel}>
                            <img src={item.remark} alt="" style={{ width: 60, height: 30 }} />
                            <span>{item[`${isZhCn ? 'cn' : 'en'}Name`]}</span>
                          </div>
                        </Select.Option>
                      );
                    })}
                  </Select> */}
                </Form.Item>
              );
            },
          },
          {
            title: 'SKU',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'platformSku']}
                  fieldKey={[field.fieldKey, 'platformSku']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    {
                      max: 50,
                      message:
                        I18N.Src__Pages__OverseaLocation__Goods__Components
                          .GoodsPlatformSkuTableForm.upToCharacters,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              );
            },
          },
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsPlatformSkuTableForm.sale,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'platformSaleUrl']}
                  fieldKey={[field.fieldKey, 'platformSaleUrl']}
                  rules={[
                    CommonRulesMap.commonUrl,
                    {
                      max: 255,
                      message:
                        I18N.Src__Pages__OverseaLocation__Goods__Components
                          .GoodsPlatformSkuTableForm.mostSold,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Order__Detail.SiItem.remarks,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'platformRemark']}
                  fieldKey={[field.fieldKey, 'platformRemark']}
                  rules={[
                    {
                      max: 255,
                      message:
                        I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm
                          .mostRemarks,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Common__Template.Index.operation,
            width: 80,
            render: (_: any, field: any) => {
              return (
                <Form.Item {...field} fieldKey={[field.fieldKey, 'action']}>
                  <span>
                    <Popconfirm
                      title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                      onConfirm={() => {
                        remove(field.name);
                      }}
                    >
                      <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                    </Popconfirm>
                  </span>
                </Form.Item>
              );
            },
          },
        ];

        return (
          <Table
            title={() => (
              <Space>
                <Button type="primary" onClick={() => add()}>
                  {
                    I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsPlatformSkuTableForm
                      .addPlatform
                  }
                </Button>
              </Space>
            )}
            columns={columns}
            dataSource={fields}
            pagination={false}
            size="small"
            scroll={{ x: 'max-content' }}
          />
        );
      }}
    </Form.List>
  );
}

export default GoodsPlatformSkuTableForm;
