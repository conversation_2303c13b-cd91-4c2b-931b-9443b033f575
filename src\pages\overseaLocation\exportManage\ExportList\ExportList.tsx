import { But<PERSON>, Divider, <PERSON> } from 'antd';
import React, { useRef } from 'react';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable, PageContainer } from '@/common-import';
import { apiExportList } from './ExportApi';
import { LoadButton } from '@/pages/overseaLocation/components';
import { dateUtils } from '@/utils';
import { apiMapDictType } from '@/pages/overseaLocation/api';
import { downloadFileByFullUrl } from '@/pages/overseaLocation/utils';

const { transformDate } = dateUtils;

function ExportList() {
  const { config } = useConfig();

  return (
    <PageContainer>
      <ProTable
        {...config}
        {
          ...{
            // headerTitle: (
            //   <Button onClick={() => {}} type="primary">
            //     新增
            //   </Button>
            // ),
          }
        }
      />
    </PageContainer>
  );
}

export default ExportList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const { columns } = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiExportList>[0];
      const { data } = await apiExportList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<TypeExportListTB, any>;

  return {
    config,
  };
}

function useColumns() {
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.applicationTime,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform: (val, field, record) => {
          return {
            dateStart: transformDate(val[0], 'START'),
            dateEnd: transformDate(val[1], 'END'),
          };
        },
      },
      render(dom, record) {
        return record.createDate || '-';
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.chineseSymbols,
      dataIndex: 'businessType',
      valueType: 'select',
      search: true,
      request: apiMapDictType.exportDataSource,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.fileName,
      dataIndex: 'fileName',
      search: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.state,
      dataIndex: 'status',
      valueType: 'select',
      search: true,
      request: apiMapDictType.exportStatus,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.operation,
      dataIndex: 'action',
      valueType: 'option',
      key: 'action',
      fixed: 'right',
      search: false,
      width: 100,
      render: (dom, record) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <LoadButton
              {...{
                disabled: record.status !== 2 /** 解析完成 */,
                onClick() {
                  downloadFileByFullUrl(record.url!);
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.download}
            </LoadButton>
          </Space>
        );
      },
    },
  ] as ProColumns<TypeExportListTB>[];

  return { columns };
}
