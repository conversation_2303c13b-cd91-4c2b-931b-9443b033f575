import { downloadFileByFullUrl } from '@/utils';
import { request } from 'umi';

/** 下载模板 */
export function apiDownloadTemplate(importType?: string) {
  return request('/zouwu-oms-system/portal/common/imports/getImportTemplateUrl', {
    data: { importType },
    method: 'POST',
  }).then((res) => downloadFileByFullUrl(res.data));
}

/** 公共导入接口 */
export function apiCommonImport(data: {
  /** 导入类型 */
  importType?: string;
  /** 导入文件名称 */
  fileName?: string;
  /** 导入文件地址 */
  fileUrl?: string;
  /** 通用导入额外参数 */
  extParams?: {
    /** 单据识别对象 */
    identityNoType?: string;
  };
}) {
  return request('/zouwu-oms-system/portal/common/imports/imports', {
    data,
    method: 'POST',
  });
}
