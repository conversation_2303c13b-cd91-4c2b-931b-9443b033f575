export default {
  // 支持值为 Object 和 Array
  'POST /api/website/web/outstorehouse/listByPage.do': {
    traceId: '',
    code: '',
    message: '',
    data: {
      totalSize: 0,
      totalPage: 0,
      pageSize: 0,
      currentPage: 0,
      currentSize: 0,
      records: [
        {
          id: 0,
          businessType: '101',
          'batchNo	': '',
          importNum: 0,
          submitNum: 0,
          draftNum: 0,
          exceptionNum: 0,
          state: 1,
          createDate: null,
          originalFilePath: '',
        },
        {
          id: 1,
          businessType: '102',
          'batchNo	': '',
          importNum: 0,
          submitNum: 0,
          draftNum: 0,
          exceptionNum: 0,
          state: 2,
          createDate: null,
          originalFilePath: '',
        },
      ],
    },
  },
  'POST /api/website/web/outstorehouse/outboundOrderImportPage.do': {
    "traceId": "",
    "code": "",
    "message": "",
    "data": {
      "detailConditionPage": {
        "totalSize": 0,
        "totalPage": 0,
        "pageSize": 0,
        "currentPage": 0,
        "currentSize": 0,
        "records": [
          {
            "id": 0,
            "podOrderNo": "",
            "orderType": 0,
            "orderSourcePlatformName": "",
            "platformSellerId": "",
            "outboundChildList": [
              {
                "id": 0,
                "complianceNo": "",
                "podOrderNo": "",
                "deliveryWarehouse": "",
                "goodsList": [
                  {
                    "id": 0,
                    "podOrderNo": "",
                    "complianceNo": "",
                    "sku": "1111",
                    "goodsName": "",
                    "totalQuantity": 0
                  }
                ],
                "express": {
                  "id": 0,
                  "podOrderNo": "",
                  "recipientCountryName": "",
                  "recipientProvinceName": "",
                  "recipientCityName": "",
                  "recipientAddress1": "",
                  "recipientName": "",
                  "recipientAddressShow": "",
                  "recipientPhoneNumber1": "",
                  "recipientPostcode": ""
                },
                "delivery": {
                  "id": 0,
                  "podOrderNo": "",
                  "expressId": 0,
                  "dispatchServiceType": 1,
                  "dispatchServiceName": 2,
                  "trackingNo": "",
                  "orderFile": ""
                }
              }
            ],
            "state": 1,
            "exceptionMsg": "错误1;错误2;错误3",
          }
        ]
      },
      "orderImportRecord": {
        "batchNo": "",
        "importNum": 0,
        "submitNum": 0,
        "draftNum": 0,
        "exceptionNum": 0,
        "state": 0
      }
    }
  }
};
