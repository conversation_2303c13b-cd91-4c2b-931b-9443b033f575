/* 样式用于组件结构
<Form.List><Table/></Form.List>
 */
.FormListTable {
  :global {
    .ant-table-thead > tr > th {
      font-weight: initial;

      :local(.plainTable)& {
        background-color: initial;
        &::before {
          display: none;
        }
      }
    }

    .ant-table-cell {
      vertical-align: top;
      border: 0;
    }

    th.ant-table-cell {
      padding-top: 8px;
      padding-bottom: 8px;
    }

    td.ant-table-cell {
      padding-top: 4px;
      padding-bottom: 0;

      .ant-form-item-label {
        /* 方便table中仍然使用formItem.label属性,而不展示 */
        display: none;
      }
      .ant-form-item {
        /* table中的formItem下边距 */
        margin-bottom: 6px;
      }
    }
  }

  /* table 新增按钮 */
  .btnAdd {
    display: flex;
    justify-content: center;
    padding: 4px 0;
    border: 1px dashed #ccc;
    border-radius: 10px;
    cursor: pointer;
  }
}
