import type { FC } from 'react';
import React, { useEffect, useState, useRef } from 'react';

import type { FormInstance } from 'antd';
import { Button, Space, Table, Modal, Select, Popconfirm, Input, Form, InputNumber } from 'antd';
import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import { includesInArray, regexUtils } from '@/pages/overseaLocation/utils';
import css from './goodsTableForm.less';
import { ZSearchSelect } from '../../components';
import { apiQueryWarehouseOptions } from '@/pages/overseaLocation/api';
import type { TypeOutboundGoodsTB } from './outboundApi';
import { apiOutboundHasVirtualWarehouse, apiOutboundGoodsList } from './outboundApi';
import { EnumWarehouseType } from '@/pages/pagesApi';

export interface GoodsTableFormProps {
  value?: any[];
  onGoodsSelect: (goods: any) => void;
  onGoodsRemove: (record: any) => void;
  warehouseList: any[];
  /** 出库单类型 */
  outboundBusinessType?: string;
  /** 是否支持批量多商品, TOB出库单支持 */
  allowBatch?: boolean;
  /** 外层表单 */
  formIns: FormInstance<any>;
}
type TypeInitParams = {
  warehouseId?: string;
  initSkuArr?: string[];
};

/** 仓库类型 */
const warehouseTypeMap = {
  /** 物理仓 */
  actual: '1',
  /** 虚拟仓 */
  virtual: '2',
};

const GoodsTableForm: FC<GoodsTableFormProps> = ({
  value,
  // onChange,
  onGoodsSelect,
  onGoodsRemove,
  warehouseList,
  allowBatch,
  outboundBusinessType,
  formIns,
}) => {
  const { hasVirtualWarehouse } = useHasVirtualWarehouse();
  const modalTableRef = useRef<ActionType>();
  const [visible, toggleVisible] = useState(false);
  const [activeKey, setActiveKey] = useState(warehouseTypeMap.actual);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRow, setSelectedRow] = useState<any[]>([]);
  const [initParams, setInitParams] = useState<TypeInitParams>({});
  const { columns } = useConfig({
    visible,
    allowBatch,
    warehouseList,
    onGoodsRemove,
    initParams,
  });

  const handleShowModal = () => {
    const skus = value && value.length ? value.map((o) => o.warehouseIdSku) : [];
    const newInitParams = {
      warehouseId: allowBatch ? value?.[0]?.warehouseId : undefined,
      initSkuArr: skus,
    };
    const isVirtualWarehouse =
      Array.isArray(value) && value?.[0]?.warehouseType === EnumWarehouseType.virtual;

    setSelectedRowKeys(skus);
    setSelectedRow(value || []);
    setInitParams(newInitParams);
    setActiveKey(isVirtualWarehouse ? warehouseTypeMap.virtual : warehouseTypeMap.actual);
    toggleVisible(true);
  };

  // 新增箱-左侧勾选change事件（新增的合并）
  const handleTableRowSelectedChange = (keys: any[], rows: any[]) => {
    setSelectedRowKeys(keys);
    setSelectedRow(rows);
  };

  const handleGetGoodsList = async (params: any) => {
    const { pageSize, current: currentPage, warehouseId, sku } = params;

    const condition: any = {
      /** 获取订单类型 */
      businessType: formIns?.getFieldsValue()?.businessType,
      warehouseType: activeKey,
    };

    if (warehouseId) {
      condition.warehouseIds = [warehouseId];
    }
    if (sku) {
      condition.sku = sku;
    }

    const { data } = await apiOutboundGoodsList({
      pageSize,
      currentPage,
      condition,
    });

    return {
      data: data?.records || [],
      total: data?.totalSize || 0,
    };
  };

  const handleModalOk = async () => {
    if (selectedRow.length) {
      onGoodsSelect(selectedRow);
    }
    handleModalCancel();
  };

  const handleModalCancel = () => {
    toggleVisible(false);
    setSelectedRowKeys([]);
    setSelectedRow([]);
    setInitParams({});
  };

  const rowSelection = {
    type: allowBatch ? 'checkbox' : 'radio',
    hideSelectAll: true,
    selectedRowKeys,
    onChange: handleTableRowSelectedChange,
    getCheckboxProps: (record: any) => {
      const disabled = {
        disabled: true,
      };

      if (record.goodQuantity <= 0) {
        /** 商品数量少于1 */
        return disabled;
      }
      if (
        allowBatch &&
        selectedRow.length > 0 &&
        selectedRow[0].warehouseId !== record.warehouseId
      ) {
        /** 批量选择商品, 但是后选商品和先选的商品 仓库id不一致 */
        return disabled;
      }
      if (allowBatch && initParams.initSkuArr?.includes(record.warehouseIdSku)) {
        /** 批量出库, 已添加至页面的商品,check按钮置灰 */
        return disabled;
      }
    },
    // ({
    //   disabled:
    //     record.goodQuantity <= 0 ||
    //     (allowBatch && selectedRow.length > 0 && selectedRow[0].warehouseId !== record.warehouseId),
    // }),
  } as ProTableProps<any, any>['rowSelection'];

  return (
    <div className={css['goods-table-form']}>
      <Space style={{ marginBottom: 20 }}>
        <Button type="primary" onClick={handleShowModal}>
          {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.addItem}
        </Button>
        {/* <Button>批量导入</Button> <Button type="link">点击下载导入模版</Button> */}
      </Space>
      <ProTable
        columns={columns}
        dataSource={value}
        rowKey="sku"
        size="small"
        pagination={false}
        search={false}
        options={false}
        cardProps={false}
        scroll={{ x: 'max-content' }}
      />

      <Modal
        width="90%"
        visible={visible}
        title={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.selectProduct}
        okText={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.confirmAdd}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        destroyOnClose
        maskClosable={false}
        className={css['goods-table-form']}
      >
        <ProTable
          rowKey="warehouseIdSku"
          actionRef={modalTableRef}
          request={(params) => handleGetGoodsList(params)}
          columns={
            columns.filter(
              (o) => includesInArray(['option', 'totalQuantity'], o.dataIndex) === false,
            ) as ProColumns<any>[]
          }
          bordered
          options={false}
          size="small"
          cardProps={{
            bodyStyle: {
              padding: 0,
            },
          }}
          rowSelection={rowSelection}
          pagination={{ pageSize: 10, showSizeChanger: false }}
          tableAlertRender={false}
          scroll={{ x: 'max-content' }}
          {...{
            toolbar: {
              menu: hasVirtualWarehouse
                ? {
                    type: 'tab',
                    activeKey: activeKey,
                    items: [
                      {
                        key: warehouseTypeMap.actual,
                        label:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm
                            .chineseSymbols,
                      },
                      {
                        key: warehouseTypeMap.virtual,
                        label:
                          I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeWarehouse
                            .chineseSymbols2,
                      },
                    ],
                    onChange: (key) => {
                      setActiveKey(`${key}`);
                      modalTableRef.current?.setPageInfo?.({ current: 1 });
                      modalTableRef.current?.reload();
                    },
                  }
                : undefined,
            },
          }}
        />
      </Modal>
    </div>
  );
};

export default GoodsTableForm;

function useConfig({
  warehouseList,
  onGoodsRemove,
  initParams,
  allowBatch,
  visible,
}: {
  warehouseList: any[];
  onGoodsRemove: any;
  initParams: TypeInitParams;
  allowBatch?: boolean;
  visible: boolean;
}) {
  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.warehouseOrganization,
      key: 'warehouseId',
      dataIndex: 'warehouseName',
      ellipsis: true,
      initialValue: initParams.warehouseId,
      formItemProps: {
        name: 'warehouseId',
      },
      renderFormItem: () => {
        return (
          // <ZSearchSelect
          //   allowClear
          //   showSearch
          //   placeholder="请选择"
          //   disabled={initParams.warehouseId !== undefined}
          //   filterOption={(input, option: any) =>
          //     option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          //   }
          //   searchByLocal
          //   request={() => apiQueryWarehouseOptions()}
          // />
          <Select
            allowClear
            showSearch
            placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
            disabled={initParams.warehouseId !== undefined}
            filterOption={(input, option: any) =>
              option.children.toLowerCase().indexOf(input.toLowerCase().trim()) >= 0
            }
          >
            {warehouseList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.code}
              </Select.Option>
            ))}
          </Select>
        );
      },
      render: (_: any, record: any) => record.warehouseCode,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_: any, record: any) => {
        return <GoodsImage src={record.goodsPicture} />;
      },
      search: false,
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      dataIndex: 'goodsName',
      render: (_: any, record: any) => {
        return (
          <div>
            <div>{record.goodsName}</div>
            <div>{record.goodsEnName}</div>
          </div>
        );
      },
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'weight',
      search: false,
      render: (_: any, record: TypeOutboundGoodsTB) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'goodsVolume',
      search: false,
      render: (_: any, record: TypeOutboundGoodsTB) =>
        `${record.volumeUnit}: ${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__Inventory__Detail__List.Index.inventoryQuantity,
      dataIndex: 'goodQuantity',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm
          .numberOfOutboundPieces1,
      dataIndex: 'totalQuantity',
      width: 210,
      render: (dom, record, index) => {
        if (allowBatch) {
          const range = [1, record.goodQuantity];

          return (
            <Form.Item
              {...{
                name: ['goodsList', index, 'totalQuantity'],
                rules: [
                  {
                    required: true,
                    message:
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm
                        .pleaseInput,
                  },
                  {
                    transform: (val) => Number(val),
                    async validator(rule, val) {
                      if (range[1] < 1) {
                        throw new Error(
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm.inventoryQuantity,
                        );
                      }
                      if (
                        val < range[0] ||
                        val > range[1] ||
                        regexUtils.nonZeroPositiveInteger.test(val as string) === false
                      ) {
                        throw new Error(
                          I18N.template(
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.GoodsTableForm
                              .numberOfOutboundPieces,
                            { val1: range[0], val2: range[1] },
                          ),
                        );
                      }
                    },
                  },
                ],
              }}
            >
              <InputNumber />
            </Form.Item>
          );
        }
        /** 不支持批量商品出库 就展示默认1 */
        return 1;
      },
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      dataIndex: 'option',
      fixed: 'right',
      width: 100,
      render: (dom, record) => {
        return (
          <Popconfirm
            title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
            onConfirm={() => onGoodsRemove(record)}
          >
            <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
          </Popconfirm>
        );
      },
    },
  ] as ProColumns<any>[];

  return {
    columns,
  };
}

function useHasVirtualWarehouse() {
  const [hasVirtualWarehouse, setHasVirtualWarehouse] = useState(false);

  useEffect(() => {
    (async () => {
      const { data } = await apiOutboundHasVirtualWarehouse();

      setHasVirtualWarehouse(data === true);
    })();
  }, []);

  return {
    hasVirtualWarehouse,
  };
}
