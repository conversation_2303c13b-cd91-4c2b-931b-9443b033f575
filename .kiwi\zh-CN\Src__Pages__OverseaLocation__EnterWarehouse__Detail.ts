export default {
  Index: {
    transactionNote: 'WMS交易单据号',
    inventoryTransactions: '库存交易单据号',
    triggerTime: '触发时间',
    abnormalParts: '异常件:',
    badParts: '坏件:',
    goodPiece: '好件:',
    productQuality: '货品质量及数量',
    executed: '已执行数量详情',
    quantityInWarehouse: '到仓数量详情',
    process: '进程',
    confirmTheWholeDocument: '确认整单签收',
    warehousePerformance: '仓库履约',
    commodityList: '商品清单',
    documentDetails: '单据明细',
    lastModification: '最后修改人',
    lastModification1: '最后修改时间',
    systemInformation: '系统信息',
    targetType: '目标类型',
    targetInventory: '目标库存组织',
    entrustmentRemarks: '委托备注',
    applicant: '申请方公司',
    orderSource: '订单来源',
    orderType: '订单类型',
    documentProcess: '单据流程',
    confirmationSucceeded: '确认成功',
    whetherToConfirm: '是否确认签收：{val1}',
    informationConfirmation: '信息确认',
    wholeOrderConfirmation: '整单确认成功',
    whetherToConfirm1: '是否确认整单签收',
    whetherToConfirm2: '是否确认{val1}此订单',
    confirmSigning: '确认签收',
    goodsReceipt: '商品签收状态',
    warehousePerformance1: '仓库履约单',
    completionOfPerformance: '履约完结标记',
    abnormalParts1: '异常件',
    badParts1: '坏件',
    goodPiece1: '好件',
    executed1: '已执行数量',
    quantityInWarehouse1: '到仓数量',
    numberToBeExecuted: '待执行数量',
    commodityCode: '商品代码(SKU)',
    serialNo: '序号',
  },
} as const;
