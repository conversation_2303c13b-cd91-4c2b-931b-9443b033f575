export default {
  GoodsTableForm: {
    numberOfOutboundPieces: '出库件数必须在整数{val1}-{val2}之间',
    inventoryQuantity: '库存数量不足',
    pleaseInput: '请输入出库件数',
    numberOfOutboundPieces1: '出库件数',
    selectProduct: '选择商品',
    addItem: '添加商品',
    chineseSymbols: '物理仓',
  },
  Index: {
    noNameDefined: '未定义名称-->{val1}',
    probablyAfter: '-->可能是后端存在脏数据',
    warehouseList: '仓库列表找不到此仓库{val1}--->',
    submitIssue: '提交出库申请',
    pleaseEnterTheSubscription: '请输入订单备注',
    lengthCannot: '长度不能超过200位',
    thisNoteLetter: '此备注信息只有仓库操作人员可见，海外买家不可见',
    orderRemarks: '订单备注',
    pleaseEnterTheCertificate: '请输入证件号',
    lengthCannot1: '长度不能超过30位',
    orderContact: '订单联系人证件类型及证件号',
    pleaseEnterTheSubscription1: '请输入订单联系人姓名',
    lengthCannot2: '长度不能超过50位',
    additionalInformation: '附加信息',
    pleaseInput: '请输入Ebay交易ID号',
    transactionNo: 'Ebay交易ID号',
    pleaseEnterFlat: '请输入Ebay平台交易单号',
    platformTransaction: 'Ebay平台交易单号',
    pleaseEnterPin: '请输入销售金额',
    pleaseEnterCurrency: '请输入币种',
    orderSales: '订单销售金额及币种',
    pleaseEnterFlat1: '请输入平台卖家ID',
    pleaseEnterFlat2: '请输入平台店铺ID',
    platformShop: '平台店铺ID',
    pleaseEnterTheCustomer: '请输入客户销售单号',
    eCommercePlatform: '电商平台建议输入平台交易号',
    pleaseEnterTheCustomer1: '请输入客户关联单号',
    customerSystem: '客户系统下单号或管控单号',
    pleaseSelect: '请选择来源平台',
    sourceInformation: '来源信息',
    pleaseEnterReceipt: '请输入收件人公司名称',
    recipient: '收件人公司名称',
    pleaseEnterReceipt1: '请输入收件人EORI号',
    recipientNo: '收件人EORI号',
    consigneesCertificate: '收货人证件类型及证件号',
    recipientsLetter: '收件人信息',
    pleaseAddConfiguration: '请添加配送单',
    logisticsServices: '物流服务',
    currentOrder: '当前订单类型仅允许单件商品',
    pleaseAddASupplier: '请添加商品',
    temporaryIssue: '暂存出库单成功',
    newIssue: '新增出库单成功',
    currentWarehouse:
      '当前仓库已有物流追踪单信息，若删除商品，原录入追踪单信息将自动删除',
    deleteReminder: '删除提醒',
    confirmSwitching: '确定切换',
    currentWarehouse1:
      '当前仓库已有物流追踪单信息，若切换仓库，原录入追踪单信息将自动删除',
    toggleReminder: '切换提醒',
  },
  SendTableForm: {
    otherAccessories: '其他附件',
    enclosure: 'Label附件',
    numberOfPalletsIs: '托盘数为正整数且不超过5000',
    numberOfPallets1: '托盘数',
    pleaseUploadIt: '请上传其他附件',
    pleaseUploadTheAttachment: '请上传label附件',
    pleaseEnterSomething: '请输入物流追踪单号',
    distributionType: '配送类型',
    trackingSheetNo: '',
    signingServices: '签署服务:',
    addShipping: '添加配送单',
    pleaseAddFirst: '请先添加商品',
    onlyAddingIsSupported: '只支持添加一条配送单',
    fileSize: '文件大小超出20M',
    pleaseEnterSomething1: '请输入物流追踪单号',
    chineseSymbols: '投保金额:',
    chineseSymbols1: '多商品不支持投保',
    chineseSymbols2: '是否投保:',
  },
} as const;
