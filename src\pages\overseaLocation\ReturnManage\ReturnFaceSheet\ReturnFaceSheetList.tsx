import I18N from '@/utils/I18N';
import { Divider, message, Space } from 'antd';
import React, { useRef } from 'react';
import type { ActionType, ProColumns, ProFormInstance } from '@/common-import';
import { ProFormSelect, PageContainer, ProFormTextArea } from '@/common-import';
import { LoadButton, ZouProTable } from '@/components';
import type { TypeZouProTableProps } from '@/components';
import { apiReturnFaceSheetExport, apiReturnFaceSheetList } from './ReturnFaceSheetApi';
import type { TypeModalLogisticsTrackRef } from '@/views';
import {
  ModalLogisticsTrack,
  ElementCopy,
  renderLogisticsTrackNode,
  AsyncExportButton,
} from '@/views';

import { apiMapDictType, apiQueryWarehouseOptions } from '@/api';
import {
  eR,
  emptyRenderArray,
  emptyRenderArrayJoin,
  includesInArray,
  strUtils,
  transformDate,
} from '@/utils';
import { goodsSizeSortStr } from '@/utils-business';
import moment from 'moment';

function ReturnFaceSheetList() {
  const { config, getSearchData, modalLogisticsTrackRef } = useConfig();

  return (
    <PageContainer>
      <ModalLogisticsTrack {...{ ref: modalLogisticsTrackRef }} />
      <ZouProTable
        {...config}
        toolBarRender={() => [
          <AsyncExportButton
            key="export"
            {...{
              children: '导出',
              buttonProps: {
                type: 'primary',
                async onClick(e, params) {
                  params.form.setFieldsValue({
                    fileName: `退货面单${moment().format('YYYYMMDD')}`,
                  });
                },
              },
              async request({ form }) {
                const { fileName } = form.getFieldsValue();
                const condition = getSearchData();

                await apiReturnFaceSheetExport({ ...condition, fileName });
              },
            }}
          />,
        ]}
      />
    </PageContainer>
  );
}

export default ReturnFaceSheetList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { columns, modalLogisticsTrackRef } = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    formRef,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      // defaultCollapsed: false,
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiReturnFaceSheetList>[0];

      const { data } = await apiReturnFaceSheetList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as TypeZouProTableProps<TypeReturnFaceSheetTB, any>;

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      ...formData,
    } as Parameters<typeof apiReturnFaceSheetList>[0]['condition'];
  }

  return {
    config,
    actionRef,
    modalLogisticsTrackRef,
    getSearchData,
  };
}

function useColumns() {
  const modalLogisticsTrackRef = useRef<TypeModalLogisticsTrackRef>();
  const noOptions = [
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols14,
      value: 'podOrderNo',
    },
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .customerSales,
      value: 'customerSalesNo',
    },
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .customerAssociation,
      value: 'customerRelatedNo',
    },
  ] as const;
  const columns = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .oddNumbers,
      dataIndex: 'noInfo',
      search: {
        transform: (val, field, formData) => {
          const { noInfo, noType } = formData;

          return {
            [noType]: noInfo,
            noType: undefined,
          };
        },
      },
      fieldProps: {
        placeholder:
          I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
            .chineseSymbols13,
        addonBefore: (
          <ProFormSelect
            {...{
              width: 130,
              name: 'noType',
              initialValue: noOptions[0].value,
              noStyle: true,
              allowClear: false,
              options: noOptions as any,
            }}
          />
        ),
      },
      formItemProps: {
        noStyle: true,
      },
      render(dom, record) {
        return (
          <>
            {[
              ...noOptions,
              ...([
                { label: '平台店铺ID', value: 'platformStoreId' },
                { label: '平台卖家ID', value: 'platformSellerId' },
              ] as const),
            ].map((item) => (
              <div key={item.value}>
                {I18N.template(
                  I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                    .LogisticsInterceptList.chineseSymbols12,
                  { val1: item.label, val2: eR(record[item.value]) },
                )}
              </div>
            ))}
          </>
        );
      },
    },
    {
      title: '平台店铺ID',
      dataIndex: 'platformStoreIdListStr',
      search: {
        transform: (val, field, record) => {
          return {
            platformStoreIdList: strUtils.strSplitForOrderNo(val),
            platformStoreIdListStr: undefined,
          };
        },
      },
      hideInTable: true,
      renderFormItem() {
        return <ProFormTextArea noStyle placeholder={'请输入，支持批量检索'} />;
      },
    },
    {
      title: '平台卖家ID',
      dataIndex: 'platformSellerIdListStr',
      search: {
        transform: (val, field, record) => {
          return {
            platformSellerIdList: strUtils.strSplitForOrderNo(val),
            platformSellerIdListStr: undefined,
          };
        },
      },
      hideInTable: true,
      renderFormItem() {
        return <ProFormTextArea noStyle placeholder={'请输入，支持批量检索'} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .state,
      dataIndex: 'returnStatusName',
      valueType: 'select',
      search: true,
      request: apiMapDictType.returnGoodsLabelStatus,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'returnStatusList',
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .recipientsLetter,
      dataIndex: 'recipientInfo',
      render(dom, record) {
        const {
          recipientName,
          recipientCountry,
          recipientProvince,
          recipientCity,
          recipientPostcode,
          recipientPhoneNumberList,
          recipientAddress1,
          recipientAddress2,
        } = record.recipientDetail || {};
        const returnSkuList =
          record.returnSkuList && record.returnSkuList.length > 0 ? record.returnSkuList : [{}];

        return (
          <ElementCopy>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.addressee
              }
              {eR(recipientName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.recipientCountry
              }
              {eR(recipientCountry)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols11
              }
              {eR(recipientProvince)}
            </div>
            <div>收件人城市：{eR(recipientCity)}</div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.recipientEmail
              }
              {eR(recipientPostcode)}
            </div>
            <div>收件人电话：{emptyRenderArrayJoin(recipientPhoneNumberList, ', ')}</div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols10
              }
              {eR(recipientAddress1)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols9
              }
              {eR(recipientAddress2)}
            </div>
            {returnSkuList.map((item) => {
              const { goodsLength, goodsWidth, goodsHeight } = item;
              const sizeArr = [goodsLength, goodsWidth, goodsHeight];

              return (
                <div key={item.sku}>
                  <div>SKU：{eR(item.sku)}</div>
                  <div>重KG：{eR(item.goodsWeight)}</div>
                  <div>尺寸CM：{goodsSizeSortStr(sizeArr)}</div>
                </div>
              );
            })}
          </ElementCopy>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols8,
      dataIndex: 'saleInfo',
      render(dom, record) {
        const {
          dispatchServiceProviderName,
          customerChannelCode,
          dispatchServiceNameName,
          trackingNo,
        } = record.outboundDelivery || {};

        return (
          <>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols5
              }
              {eR(dispatchServiceProviderName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.logisticsChannel
              }
              {eR(customerChannelCode || dispatchServiceNameName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols4
              }
              {eR(trackingNo)}
            </div>
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .commodity,
      dataIndex: 'skuList',
      render(dom, record) {
        return emptyRenderArray(record.skuList, (item) => <div key={item}>{item}</div>);
      },
    },
    {
      title: '销售运单',
      dataIndex: 'trackingNoString',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入，支持批量检索',
      },
      search: {
        transform: (val, field, record) => {
          const trackingNoList = strUtils.strSplitForOrderNo(record.trackingNoString);

          return {
            trackingNoList,
            trackingNoString: undefined,
          };
        },
      },
      renderFormItem() {
        return <ProFormTextArea noStyle />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet.ReturnFaceSheetList
          .chineseSymbols3,
      dataIndex: 'returnInfo',
      search: true,
      fieldProps: {
        placeholder:
          I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
            .chineseSymbols7,
      },
      formItemProps: {
        label:
          I18N.Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet.ReturnFaceSheetList
            .chineseSymbols4,
        name: 'returnTrackingNo',
      },
      render(dom, record) {
        const {
          currency,
          returnTrackingNo,
          returnLabelFile,
          returnLabelFileName,
          receivableFee,
          latestNode,
        } = record;

        return (
          <>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet.ReturnFaceSheetList
                  .chineseSymbols2
              }
              {eR(returnTrackingNo)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet.ReturnFaceSheetList
                  .chineseSymbols1
              }
              {eR(returnLabelFile, () => (
                <a target="_blank" download href={returnLabelFile}>
                  {returnLabelFileName}
                </a>
              ))}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__ReturnFaceSheet.ReturnFaceSheetList
                  .chineseSymbols
              }
              {`${eR(receivableFee)} ${currency || ''}`}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.logisticsProgress
              }
              {renderLogisticsTrackNode({
                ...record,
                onClick() {
                  modalLogisticsTrackRef.current?.open({
                    orderLogisticsId: record.orderLogisticsId,
                  });
                  ssTrack(EnumSensor.RETURN_GOODS_LABEL_LOGISTICS_TRACK);
                },
              })}
            </div>
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .warehouse,
      dataIndex: 'warehouseCode',
      valueType: 'searchSelect',
      search: true,
      fieldProps: {
        mode: 'multiple',
        request: async (queryParam: string) =>
          apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
      },
      formItemProps: {
        name: 'warehouseIdList',
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .creationTime,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            createDateStart: transformDate(value[0], 'START'),
            createDateEnd: transformDate(value[1], 'END'),
          };
        },
      },
    },
    /*  {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      render: (text, record, index, action) => {
        return <Space wrap split={<Divider type="vertical" />} size={0} />;
      },
    }, */
  ] as ProColumns<TypeReturnFaceSheetTB>[];

  return {
    columns,
    modalLogisticsTrackRef,
  };
}
