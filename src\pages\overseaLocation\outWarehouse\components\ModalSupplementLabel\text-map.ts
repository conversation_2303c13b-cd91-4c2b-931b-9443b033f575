import I18N from '@/utils/I18N';

export const textMap = {
  txtPodOrderNo:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.applicationNo,
  txtCustomerChannelCode:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.logisticsChannel,
  txtTrackingNo:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.chineseSymbols4,
  txtLabelFile:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.enclosure,
  txtUploadFile:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.chineseSymbols2,
  txtLabelFilePath:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.chineseSymbols1,
  txtLabelFileName:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.chineseSymbols,
  txtSupplementarySheet:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.supplementarySheet,
  txtMsgChooseFile:
    I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalSupplementLabel
      .ModalSupplementLabel.chineseSymbols3,
};
