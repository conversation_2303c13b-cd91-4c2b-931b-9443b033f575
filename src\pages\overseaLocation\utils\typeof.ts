/* eslint-disable eqeqeq */

/** 获取数据类型 */
export function superTypeof(val: any) {
  let ans = typeof val;

  if (ans === 'object') {
    ans = Object.prototype.toString.call(val).slice(8, -1).toLowerCase() as any;
  }
  return ans as typeof ans | 'array' | 'date' | 'regexp' | 'math' | 'error';
}

function isObject(val: any): val is object {
  return superTypeof(val) === 'object';
}

function isObjectEmpty(val: any): val is object {
  return isObject(val) && Object.keys(val).length === 0;
}

function isFunction(val: any): val is Function {
  return superTypeof(val) === 'function';
}

/** 判断是数字或字符串形式的数字
 * @ 如果需要判断数字在安全范围内, 需要使用 isSafeNumOrNumString
 */
function isNumOrNumString(val: any): val is number | string {
  if (['number', 'string'].includes(superTypeof(val)) === false) {
    /** 必须是number 或者 string 类型才有判断的价值 */
    return false;
  }
  return val !== '' && (val as number) - 0 == val;
}

/** 判断是安全范围内的数字 */
function isSafeNumOrNumString(val: any): val is number | string {
  /** Number.MAX_SAFE_INTEGER */
  const MAX_SAFE_INTEGER = 9007199254740991;

  if (isNumOrNumString(val) === false) {
    return false;
  }
  const value = Number(val);

  if (value > MAX_SAFE_INTEGER || value < -MAX_SAFE_INTEGER) {
    return false;
  }
  return true;
}

export const typeUtils = {
  isObject,
  isObjectEmpty,
  isFunction,
  isNumOrNumString,
  isSafeNumOrNumString,
};
