/** 神策事件定义
 * @ DTC_OMS 表示运营侧
 * @ DTC_WWL 表示门户侧
 */
export enum EnumSensor {
  /**
   * ======================
   * 出库订单管理
   * ======================
   */
  /** 出库物流跟踪导出
   * @ 门户
   */
  OUTBOUND_LOGISTICS_EXPORT = 'OUTBOUND_LOGISTICS_EXPORT',
  /** 出库物流轨迹查询
   * @ OMS/门户
   */
  OUTBOUND_LOGISTICS_TRACK = 'OUTBOUND_LOGISTICS_TRACK',
  /** 出库异常单修改地址
   * @ OMS/门户
   */
  OUTBOUND_ADDRESS_MODIFY = 'OUTBOUND_ADDRESS_MODIFY',
  /** 出库单索赔申请/处理
   * @ OMS/门户
   */
  OUTBOUND_COMPENSATE_APPLY = 'OUTBOUND_COMPENSATE_APPLY',
  /** 出库TOB订单取消
   * @ OMS
   */
  OUTBOUND_TOB_CANCEL = 'OUTBOUND_TOB_CANCEL',

  /**
   * ======================
   * 退货管理
   * ======================
   */
  /** 物流拦截物流轨迹查询
   * @ OMS/门户
   */
  RETURN_INTERCEPT_LOGISTICS_TRACK = 'RETURN_INTERCEPT_LOGISTICS_TRACK',
  /** 物流拦截处理/申请
   * @ OMS
   */
  RETURN_INTERCEPT_PROCESS = 'RETURN_INTERCEPT_PROCESS',
  /** 申请退货面单
   * @ 门户
   */
  RETURN_GOODS_LABEL_APPLY = 'RETURN_GOODS_LABEL_APPLY',
  /** 退货面单物流轨迹查询
   * @ OMS/门户
   */
  RETURN_GOODS_LABEL_LOGISTICS_TRACK = 'RETURN_GOODS_LABEL_LOGISTICS_TRACK',
  /** 退货面单处理
   * @ OMS
   */
  RETURN_GOODS_LABEL_PROCESS = 'RETURN_GOODS_LABEL_PROCESS',
  /** 退货面单取消
   * @ OMS
   */
  RETURN_GOODS_LABEL_CANCEL = 'RETURN_GOODS_LABEL_CANCEL',
}

declare module './types' {
  namespace NsSensor {
    interface attrMap {
      /** 门户/OMS 操作类型 */
      operate_type: 'edit' | 'copy' | 'delete' | 'detail';
    }

    interface dataCustomizeMap {
      /** 在此处添加自定义ssTrack data的数据结构 */
    }
  }
}
