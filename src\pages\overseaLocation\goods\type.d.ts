type TypeGoodsBaseInfo = {
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
  file?: { url?: string; name?: string };
};

/** 商品列表 */
type TypeGoodsTB = {
  /** 商品唯一 id */
  id?: string;
  /** 商品sku */
  sku?: string;
  /** 商品名称 */
  name?: string;
  /** 商品英文名 */
  enName?: string;
  /** 国际商代码 */
  upc?: string;
  /** 欧洲商编码 */
  ean?: string;
  /** 商品类型 1：商品 2：组合商品,示例值(1) */
  goodsType?: number;
  /** 其他编码 */
  otherCode?: string;
  /** 安全库存,示例值(1) */
  safeNums?: number;
  /** 商品图片 */
  pictureUrl?: string;
  /** 商品图片名称 */
  serverPictureName?: string;
  /** 图片文件 */
  file?: { url?: string; name?: string };
  /** 商品描述 */
  comment?: string;
  /** 商品长度,示例值(1.55) */
  length?: number;
  /** 商品宽度,示例值(1.55) */
  width?: number;
  /** 商品高度,示例值(1.55) */
  height?: number;
  /** 商品计量单位 */
  measureUnit?: 'CM' | 'IN';
  /** 环世条码 */
  wwlSku?: string;
  /** 商品重量,示例值(1.55) */
  weight?: number;
  /** 商品重量单位 */
  weightUnit?: 'KG' | 'G' | 'LB' | 'OZ';
  /** 计量类型：1公制 g/cm 2英制oz.av/in,示例值(1) */
  measureType?: 1 | 2;
  /** 商品品牌 */
  brand?: string;
  /** 商品材料 */
  material?: string;
  /** 商品规格 */
  specifications?: string;
  /** 商品用途 */
  purpose?: string;
  /** 是否需要签收 1：是 2：否,示例值(true) */
  sign?: boolean;
  /** 是否需要投保 1：是2：否,示例值(true) */
  insure?: boolean;
  /** 是否需要序列号 1：是 2：否,示例值(true) */
  serialNumber?: boolean;
  /** 包装类型 1：无包装2：自带包装3：特殊包装,示例值(1) */
  packingType?: number;
  /** 是否带电 1：是 2否,示例值(true) */
  electrified?: boolean;
  /** 打包设置1：无设置2：独立打包3：合并打包,示例值(1) */
  baleType?: number;
  /** 是否包含电池 1：是 2否,示例值(true) */
  containBattery?: boolean;
  /** 电池类型 1：锂离子电池2：锂金属电池3：其他,示例值(1) */
  batteryType?: number;
  /** 投保服务设置 */
  goodsCustomsInformationList?: NsGoods.TypeGoodsInsureItem[];
  /** 商家平台SKU */
  goodsPlatformList?: NsGoods.TypesPlatformItem[];
  /** 箱规尺寸 */
  goodsBoxList?: NsGoods.TypeGoodsBoxItem[];
  /** 打托设置, 详情使用 */
  goodsPallet?: NsGoods.TypeGoodsPalletItem;
};

/** 商品编辑表单 */
type TypeGoodsFormData = TypeGoodsTB & {
  /** 箱规尺寸, 新增编辑时使用 */
  goodsBoxUpdateList?: NsGoods.TypeGoodsBoxItem[];
  /** 商家平台SKU, 新增编辑时使用 */
  goodsPlatformUpdateList?: NsGoods.TypesPlatformItem[];
  /** 投保服务设置, 新增编辑时使用 */
  goodsCustomsInformationUpdateList?: NsGoods.TypeGoodsInsureItem[];
};

declare namespace NsGoods {
  /** 商品箱规 */
  type TypeGoodsBoxItem = {
    /** 临时的行key值 */
    key: string | number;
    /** 箱规id */
    id?: string;
    /** 商品ID,示例值(1) */
    goodsId?: number;
    /** 单箱数量,示例值(1) */
    boxNums?: number;
    /** 单箱长度,示例值(1.55) */
    boxLength?: number;
    /** 单箱宽度,示例值(1.55) */
    boxWidth?: number;
    /** 单箱高度,示例值(1.55) */
    boxHeight?: number;
    /** 单箱高度单位 */
    boxMeasureUnit?: string;
    /** 单箱重量,示例值(1.55) */
    boxWeight?: number;
    /** 单箱重量单位 */
    boxWeightUnit?: string;
    /** 箱规图片 */
    pictureUrl?: string;
    /** 箱规图片名称 */
    pictureName?: string;
    /** 备注 */
    remark?: string;
    /** 标记删除 */
    deleted?: boolean;
    // 标记
    disabled?: boolean;
    /** 文件对象 */
    file?: { url?: string; name?: string };
  };

  /** 商品投保 */
  type TypeGoodsInsureItem = {
    /** 历史名称: 保险价值  | 货品价值 */
    goodsInsuranceValue?: number;
    /** 历史名称: 申报价值 | 现在对应 投保金额 */
    goodsDeclaredValue?: number;
    /** 币种 */
    currency?: string;
    /** 目的国 */
    countryName?: string;
  };

  /** 商品打托设置 */
  type TypeGoodsPalletItem = {
    /** id */
    id?: string;
    /** 是否删除 */
    deleted?: boolean;
    /** 商品ID */
    goodsId?: number;
    /** 48x40 inch（标准）、65x40 inch、80x40 inch、96x40 inch、其他尺寸；枚举值可数据字典维护，后期可增加； */
    palletType?: number;
    /** 托盘尺寸数量 */
    goodsNum?: number;
    /** 打托备注 */
    remark?: string;
  };

  /** 商品电商平台SKU */
  type TypesPlatformItem = {
    /** 临时的行key值 */
    key: string | number;
    // 主键
    id?: string;
    /** 商品ID,示例值(1) */
    goodsId?: number;
    /** 商家平台ID,示例值(1) */
    platformId?: number;
    /** 商家平台销售URL */
    platformSaleUrl?: string;
    /** 备注 */
    platformRemark?: string;
    /** 平台SKU */
    platformSku?: string;
    /** 标记删除 */
    deleted?: boolean;
    /** 平台code */
    platformCode?: string;
  };
}
