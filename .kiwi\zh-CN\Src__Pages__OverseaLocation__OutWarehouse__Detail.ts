export default {
  Index: {
    wholeOrderCancellation: '整单取消失败',
    operationFailed: '操作失败，状态已变更，请刷新。',
    wholeOrderCancellation1: '整单取消成功',
    wholeOrderCancellation2: '整单取消中',
    whetherToConfirm: '是否确认申请取消此订单',
    signingServices: '签署服务',
    customerBookkeeping: '客户记账码',
    actualVolume: '实际体积',
    actualWeight: '实际重量',
    billingWeight: '计费重',
    associatedLogistics: '关联物流单',
    remoteOrNot: '是否偏远',
    residentialOrNot: '是否住宅地址',
    whether1: '是否FBA',
    identificationNumber: '证件号码',
    recipientCertificate: '收件人证件类型',
    detailedAddress: '详细地址{val1}',
    urbanArea: '城市/区',
    provincialAndStateCapitals: '省/州/府',
    contactNumber: '联系电话{val1}',
    orderWitness: '订单人证件号及类型',
    orderersLastName: '订单人姓名',
    orderSales: '订单销售金额',
    orderSales1: '订单销售币种',
    customerAssociation: '客户关联单',
    customerSales: '客户销售单',
    associatedCustomers: '关联客户信息',
    overseasTransit: '海外中转仓',
    standardOverseas: '标准海外仓',
    shipmentInventory: '发货库存组织',
    customerApplication: '客户申请数量',
    tradeName: '商品名称(EN)',
    tradeName1: '商品名称(中文)',
    total: '总件数(PCS)',
    deliveryService: '配送服务',
    chineseSymbols: '🚀 ~ file: index.tsx ~ line 734 ~ arr',
    chineseSymbols1:
      '🚀 ~ file: index.tsx ~ line 50 ~ OutWarehouseDetailPage ~ id',
    chineseSymbols2: '投保币种',
    no: '否',
    yes: '是',
    chineseSymbols3: '是否投保',
  },
} as const;
