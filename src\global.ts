import { SentryUtil } from '@portal/hive-sdk';

console.log('PACK_TIME-->', process.env.PACK_TIME);
console.log('git last commit---->', process.env.UMI_APP_VERSION);

if (process.env.NODE_ENV !== 'development') {
  const packageJson = require('../package.json');
  const packageName = packageJson.name;
  const { client } = SentryUtil.init({
    /** 在 sentry后台 Client Keys (DSN) 查看 */
    dsn: 'https://<EMAIL>/49',
    environment: process.env.NODE_ENV,
    release: process.env.UMI_APP_VERSION,
    packageName,
  });
}
