import React from 'react';
import { FormProDescriptions } from '@/views';
import { eR, emptyRenderArrayJoin } from '@/utils';
import { apiMapDictType } from '@/api';
import { renderDownloadableFileList } from '@/utils-business';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import { EnumOutboundBusinessType } from '../../OutboundEnum';
import { isOMS, isPortal } from '@/oms-portal-diff-import';
import { yesOrNoItemMap } from '@/constants/enum';
import { ZouProTable } from '@/components';

type TypeProps = {
  detailData?: TypeOutboundDetail;
};

/** 关联物流单 */
export default function DetailLogisticsService(props: TypeProps) {
  const { extraColumnsMap } = useColumns();
  const deliveryInfo = props.detailData?.deliveryList?.[0] || {};
  const { businessType } = props.detailData || {};
  const express = props.detailData?.orderExpressDTO || {};

  return (
    <>
      <FormProDescriptions<typeof deliveryInfo & typeof express>
        {...{
          title: isPortal ? '关联物流单' : undefined,
          dataSource: {
            ...deliveryInfo,
            ...express,
          },
          columns: [
            { title: '配送类型', dataIndex: 'dispatchServiceTypeValue' },
            isPortal && {
              title: '物流渠道',
              dataIndex: 'customerChannelCode',
              render: (val, record) =>
                eR(record?.customerChannelCode || record?.dispatchServiceNameValue),
            },
            isOMS && { title: '客户渠道', dataIndex: 'customerChannelCode' },
            isOMS && { title: '配送渠道', dataIndex: 'dispatchServiceNameValue' },
            { title: '物流跟踪单号', dataIndex: 'trackingNo' },
            {
              title: 'Label附件',
              dataIndex: 'fileListLabel',
              render: renderDownloadableFileList,
            },
            {
              title: '签署服务',
              dataIndex: 'signatureType',
              valueType: 'select',
              request: apiMapDictType.outboundSignatureTypes,
            },
            {
              title: '投保服务',
              dataIndex: 'insuredAmount',
              render(val, record) {
                return `${eR(yesOrNoItemMap[`${record.insured}`]?.label)} ( ${eR(val)} ${eR(
                  record.insuredAmountCurrency,
                )} )`;
              },
            },
            ...(extraColumnsMap[businessType as EnumOutboundBusinessType] || []),
          ],
        }}
      />
      {EnumOutboundBusinessType.tob === businessType && (
        <FormProDescriptions
          {...{
            columns: [
              {
                title: '托盘尺寸',
                dataIndex: 'palletSizeList',
                span: 24,
                render() {
                  return (
                    <ZouProTable<NsOutbound.TypePalletSizeItem, any>
                      {...{
                        rowKey: 'palletId',
                        dataSource: deliveryInfo.palletSizeList,
                        style: { width: '100%' },
                        tableType: 'simple',
                        columns: [
                          {
                            title: '托盘尺寸',
                            dataIndex: 'palletSizeType',
                            valueType: 'select',
                            request: apiMapDictType.palletSizeType,
                          },
                          { title: '尺寸托数', dataIndex: 'palletSizeQuantity' },
                          {
                            title: '指定SKU',
                            dataIndex: 'skuList',
                            render(dom, record) {
                              return emptyRenderArrayJoin(record.skuList, ', ');
                            },
                          },
                          { title: '打托备注', dataIndex: 'remark' },
                        ],
                      }}
                    />
                  );
                },
              },
            ],
          }}
        />
      )}
    </>
  );
}

export function useColumns() {
  const fileListOther = {
    title: '其它附件',
    dataIndex: 'fileListOther',
    render: renderDownloadableFileList,
  };
  const fileListPOD = isOMS && {
    title: 'POD附件',
    dataIndex: 'fileListPOD',
    render: renderDownloadableFileList,
  };
  const fileListBOL = {
    title: 'BOL',
    dataIndex: 'fileListBOL',
    render: renderDownloadableFileList,
  };

  const extraColumnsMap = {
    [EnumOutboundBusinessType.normal]: [
      { title: '客户记账码', dataIndex: 'accountNumber' },
      {
        title: '地址类型',
        dataIndex: 'residential',
        render(val) {
          return eR(val, (item) => (item ? '住宅' : '非住宅'));
        },
      },
      { title: 'ZONE', dataIndex: 'zone' },
      { title: 'ODA', dataIndex: 'remoteName' },
      fileListOther,
      fileListPOD,
      fileListBOL,
    ],
    [EnumOutboundBusinessType.tob]: [
      {
        title: '预计提货时间',
        dataIndex: 'estimatedPickupDate',
      },
      {
        title: '是否打托',
        dataIndex: 'isPalletize',
        render(val, record) {
          return `${eR(yesOrNoItemMap[val as string]?.label)} ( ${eR(record.palletQuantity)} 托 )`;
        },
      },
      {
        title: '托盘贴数',
        dataIndex: 'palletNumber',
        render(val) {
          return `${eR(val)} 张/托`;
        },
      },
      {
        title: '箱唛贴数',
        dataIndex: 'markQuantity',
        render(val) {
          return `${eR(val)} 张/箱`;
        },
      },
      {
        title: '产品贴数',
        dataIndex: 'productQuantity',
        render(val) {
          return `${eR(val)} 张/箱`;
        },
      },
      {
        title: '托盘贴',
        dataIndex: 'fileListPallet',
        render: renderDownloadableFileList,
      },
      {
        title: '箱唛贴',
        dataIndex: 'fileListBoxMark',
        render: renderDownloadableFileList,
      },
      {
        title: '产品贴',
        dataIndex: 'fileListProduct',
        render: renderDownloadableFileList,
      },
      {
        title: 'AMZX附件',
        dataIndex: 'fileListAMZX',
        render: renderDownloadableFileList,
      },
      fileListBOL,
      fileListOther,
      fileListPOD,
    ],
  } as Record<number, ProDescriptionsItemProps[]>;

  return { extraColumnsMap };
}
