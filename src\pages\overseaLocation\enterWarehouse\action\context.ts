import React from 'react';

export const initState = {
  cacheGoodsList: [],
};

export type StateType = typeof initState;

export enum ContextType {
  SET_EDIT_INIT_DATA = 'SET_EDIT_INIT_DATA',
}

export const contextReducer = (state: StateType, action: any) => {
  switch (action.type) {
    case ContextType.SET_EDIT_INIT_DATA:
      return {
        ...state,
        cacheGoodsList: action.payload.goodsList,
      };
    default:
      break;
  }
};

const EnterWarehouseContext = React.createContext<{
  contextState: StateType;
  contextDispatch: any;
}>(null as any);

export default EnterWarehouseContext;
