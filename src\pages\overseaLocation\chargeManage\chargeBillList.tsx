import React, { useRef } from 'react';
import { Link } from '@portal/hive-sdk';
import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProFormInstance, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';
import { PageContainer } from '@/components/PageContainer';
import { dateUtils } from '@/utils';
import {
  apiChargeBillPollingExport,
  apiChargeBillRecordsExport,
  apiChargeBillRecordsList,
} from './chargeApi';
import { LoadButton, ZSearchSelect } from '@/pages/overseaLocation/components';
import { combineLinkPath, historyCreateLocationForHive } from '@/pages/overseaLocation/utils';
import {
  apiAccountCurrencyOptions,
  apiChargeItemOptions,
  apiMapDictType,
} from '@/pages/overseaLocation/api';
import { AsyncExportButton, AmountMoney } from '@/views';
import { exportUtils } from '@/utils-business';

const { isBatchAllowDaysLimit } = exportUtils;

/** 费用清单列表 */
function ChargeBillList() {
  const { config, getSearchData } = useConfig();

  return (
    <PageContainer>
      <ProTable
        {...config}
        {...{
          headerTitle: (
            <AsyncExportButton
              {...{
                buttonProps: {
                  type: 'primary',
                  async onClick() {
                    const searchData = getSearchData();
                    const { startTime, endTime, settlementDateStart, settlementDateEnd } =
                      searchData;

                    if (
                      isBatchAllowDaysLimit([
                        [startTime, endTime],
                        [settlementDateStart, settlementDateEnd],
                      ]) === false
                    ) {
                      return Promise.reject();
                    }
                  },
                },
                async request({ form }) {
                  const searchData = getSearchData();
                  const { fileName } = form.getFieldsValue();

                  await apiChargeBillRecordsExport({ ...searchData, fileName });
                },
              }}
            >
              {I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.export}
            </AsyncExportButton>
          ),
        }}
      />
    </PageContainer>
  );
}

export default ChargeBillList;

function useConfig() {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const columns = useColumns();

  function getSearchData() {
    const formData = formRef.current?.getFieldsFormatValue?.() || {};

    return {
      /* 1 : 应收 */
      costType: 1,
      ...formData,
    } as Parameters<typeof apiChargeBillRecordsExport>[0];
  }

  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    formRef,
    scroll: { x: 'max-content' },
    search: {
      labelWidth: 100,
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...getSearchData(),
          ...args,
        },
      } as Parameters<typeof apiChargeBillRecordsList>[0];
      const { data } = await apiChargeBillRecordsList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    getSearchData,
  };
}

function useColumns() {
  /** 处理业务场景是 3 对应仓租,计算数量和单价展示- */
  const showContent = (_: any, businessType?: number) => {
    return businessType === 3 ? '-' : _;
  };

  const columns = [
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.expenseDocNo,
      dataIndex: 'chargeRecordBillNo',
      search: true,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.businessAssociation,
      dataIndex: 'podOrderNo',
      search: true,
      renderText: (text, record) => {
        const { businessType, podOrderNo } = record;
        const pathnameMap = {
          1: '/oversea-location/enter-warehouse/detail',
          2: '/oversea-location/out-warehouse/detail',
        };
        const pathname = pathnameMap[businessType as keyof typeof pathnameMap];

        if (!pathname) {
          return text;
        }

        return (
          text && (
            <Link
              to={
                historyCreateLocationForHive({
                  pathname,
                  query: { id: text, podOrderNo: podOrderNo! },
                }) /* `${pathname}?id=${text}podOrderNo=${podOrderNo}` */
              }
              rel="noreferrer"
            >
              {text}
            </Link>
          )
        );
      },
    },

    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.settlementStatus,
      dataIndex: 'status',
      search: true,
      valueType: 'select',
      request: apiMapDictType.chargeRecordBillStatus,
      fieldProps: {
        mode: 'multiple',
        showSearch: false,
      },
      // 默认选中已结算
      initialValue: ['2'],
      formItemProps: {
        name: 'statusList',
      },
    },
    {
      title: I18N.Src__Pages__Freight__Lcl.List.expenseName,
      dataIndex: 'gmChargeName',
      search: true,
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: (gmChargeName) => apiChargeItemOptions({ gmChargeName }),
            }}
          />
        );
      },
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'gmChargeCodeList',
        label: I18N.Src__Pages__Freight__Lcl.List.expenseName,
      },
    },
    /** 产品说运营觉得 费用CODE给客户看到不太好, 希望撤掉 */
    // {
    //   title: '费用CODE',
    //   dataIndex: 'gmChargeCode',
    //   search: false,
    // },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.settlementAmount,
      dataIndex: 'settlementTotal',
      search: false,
      renderText: (text) => {
        return <AmountMoney num={text} />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.settlementCurrency,
      dataIndex: 'settlementCurrency',
      search: true,
      /** 产品要求字段放在最后 */
      order: -100,
      fieldProps: {
        mode: 'multiple',
        request: (currency: string) => apiAccountCurrencyOptions({ currency }),
      },
      formItemProps: {
        name: 'chargeCurrencyList',
      },
      renderFormItem() {
        return <ZSearchSelect />;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.invoicingStatus,
      dataIndex: 'invoiceStatus',
      search: true,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'invoiceStatusList',
      },
      valueType: 'select',
      request: apiMapDictType.invoiceStatus,
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: apiMapDictType.invoiceStatus,
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.businessScenario,
      dataIndex: 'businessType',
      search: true,
      valueType: 'select',
      formItemProps: {
        name: 'businessTypeList',
      },
      request: apiMapDictType.chargeRecordBillBusinessType,
      fieldProps: {
        mode: 'multiple',
      },
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: apiMapDictType.chargeRecordBillBusinessType,
              /** 门户这里只显示入库和出库;  OMS显示除充值以外的选项;
               *  因为门户把仓租单独独立了，oms是含仓租的
               */
              exclude: ['3' /** 仓租 */],
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.expenseCategory,
      dataIndex: 'chargeType',
      search: true,
      formItemProps: {
        name: 'chargeTypeList',
      },
      valueType: 'select',
      request: apiMapDictType.chargeType,
      fieldProps: {
        mode: 'multiple',
      },
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: apiMapDictType.chargeType,
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.warehouse,
      dataIndex: 'warehouseName',
      render: (dom, record) => {
        return [record.warehouseCode].filter(Boolean).join('-');
      },
      search: false,
    },
    // {
    //   title: '计费数量',
    //   dataIndex: 'chargeQuantity',
    //   search: false,
    //   render: (_, record) => {
    //     return showContent(_, record?.businessType);
    //   },
    // },
    // {
    //   title: '计费单价',
    //   dataIndex: 'chargeUnitPrice',
    //   search: false,
    //   render: (_, record) => {
    //     return showContent(_, record?.businessType);
    //   },
    // },

    // {
    //   title: '计算金额',
    //   dataIndex: 'chargeTotal',
    //   search: false,
    //   renderText: (text) => {
    //     return <AmountMoney num={text} />;
    //   },
    // },

    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.orderCreation,
      dataIndex: 'orderCreateDate',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.completionOfBusiness,
      dataIndex: 'complianceOverDate',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.expenseSettlement,
      dataIndex: 'settlementDate',
      valueType: 'dateRange',
      render(dom, record) {
        return record.settlementDate;
      },
      search: {
        transform: (val) => {
          const { transformDate } = dateUtils;

          return {
            settlementDateStart: transformDate(val[0], 'START'),
            settlementDateEnd: transformDate(val[1], 'END'),
          };
        },
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.chineseSymbols,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform: (val, field, record) => {
          const { transformDate } = dateUtils;

          return {
            startTime: transformDate(val[0], 'START'),
            endTime: transformDate(val[1], 'END'),
          };
        },
      },
      render(dom, record) {
        return record.createDate;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.invoiceNo,
      dataIndex: 'invoiceNo',
      search: false,
      render: (dom, record) => {
        // 门户的发票链接都可以点击,默认打开新窗口
        return record.invoiceUrl ? (
          <a href={combineLinkPath(record.invoiceUrl)} target="_blank" rel="noreferrer">
            {record?.invoiceNo}
          </a>
        ) : (
          dom
        );
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.chineseSymbols1,
      dataIndex: 'createDeliveryWarehouseCode',
      search: false,
    },
  ] as ProColumns<TypezChargeBillRecordsTB>[];

  return columns;
}
