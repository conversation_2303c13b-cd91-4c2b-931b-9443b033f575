import I18N from '@/utils/I18N';
import type { ProCardProps } from '@ant-design/pro-card';
import type { CardProps } from 'antd';
import { Card, Col, Row, Space, Statistic } from 'antd';
import React, { useMemo } from 'react';
import { AmountMoney } from '@/views';

type TypeProps = {
  dataSource: TypeCashFlowCount;
};
export default function (props: TypeProps) {
  const { cardColumn } = useConfig(props);

  return (
    <>
      <Row gutter={16} justify="center">
        {cardColumn.map((item, i) => {
          return (
            <Col span={6} key={i}>
              <Card {...item} />
            </Col>
          );
        })}
      </Row>
    </>
  );
}

function useConfig(props: TypeProps) {
  const { dataSource } = props;
  const bodyStyle = {
    paddingTop: '0',
    paddingBottom: '8px',
    paddingLeft: '10px',
    paddingRight: '10px',
    display: 'flex',
    justifyContent: 'left',
  };
  const headStyle = {
    borderBottom: 0,
    paddingTop: '4px',
    paddingLeft: '10px',
    paddingRight: '10px',
  };
  const normalProps = {
    size: 'small',
    bordered: false,
    bodyStyle,
    headStyle,
  };
  const cardColumn = [
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow__Components.CashFlowBoard.totalRecharge,
      children: <AmountMoney num={dataSource?.rechargeAmount} isThousands />,

      ...normalProps,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow__Components.CashFlowBoard.totalDeduction,

      children: <AmountMoney num={dataSource?.deductAmount} isThousands />,

      ...normalProps,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow__Components.CashFlowBoard.totalRefund,

      children: <AmountMoney num={dataSource?.refundAmount} isThousands />,
      ...normalProps,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__CashFlow__Components.CashFlowBoard.totalFrozenFunds,

      children: <AmountMoney num={dataSource?.freezeAmount} isThousands />,

      ...normalProps,
    },
  ] as CardProps[];

  return {
    cardColumn,
  };
}
