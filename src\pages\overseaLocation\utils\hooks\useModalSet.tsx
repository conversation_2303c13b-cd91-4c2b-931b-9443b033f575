import type { ElementRef, JSXElementConstructor } from 'react';
import React, { useRef, Fragment } from 'react';

type ModalComponent = JSXElementConstructor<any>;

type ModalComplexComponent = {
  component: ModalComponent;
  props: any;
};

type ModalSet = Readonly<Record<string, ModalComplexComponent | ModalComponent>>;

type ModalKeys<T> = keyof T;

type ModelRefSet<T> = {
  [K in ModalKeys<T>]: (T[K] extends JSXElementConstructor<any> ? ElementRef<T[K]> : never) | null;
};

/**
 * 判断是否是 { component, props } 这种形式的值
 */
const isComplexComponent = (item: any): item is ModalComplexComponent => {
  return item && !!item.component;
};

/**
 * 将对象值都转为 null
 */
const transObjValueToNull = <T extends Record<string, any>>(obj: T) => {
  return Object.keys(obj).reduce((preObj, curKey: keyof T) => {
    preObj[curKey] = null;
    return preObj;
  }, {} as Record<keyof T, null>);
};

/**
 * 弹窗集合
 */
const useModalSet = <T extends ModalSet>(modalSet: T) => {
  const modalSetRef = useRef<ModelRefSet<T>>(transObjValueToNull(modalSet));

  const modalNodeList = React.createElement(
    Fragment,
    {},
    Object.keys(modalSet).map((key: ModalKeys<T>) => {
      const item = modalSet[key];

      let component: ModalComponent;
      let props = {};

      if (isComplexComponent(item)) {
        component = item.component;
        props = item.props;
      } else {
        component = item;
      }

      Object.assign(props, {
        ref: (r: any) => (modalSetRef.current[key] = r),
        key,
      });

      return React.createElement(component, props);
    }),
  );

  return {
    modalActions: modalSetRef.current,
    modalNodeList,
  } as const;
};

export default useModalSet;
