import I18N from '@/utils/I18N';
import React, { useEffect } from 'react';
import { Form, Table, Space, Input, Button, Popconfirm, InputNumber } from 'antd';
import type { ValidatorRule } from 'rc-field-form/lib/interface';
import GoodsImageUpload, { EnumUploadType } from './GoodsImageUpload';
import { CommonRulesMap } from '@/utils/rules';
import { TextValue } from '@/views';
import GoodsImage from '@/components/GoodsImage';
import { GetPageQuery } from '@/utils/util';
import { EnumActionType } from '@/utils/const';

interface IProps {
  name: string;
  unitMap: any;
  rules: ValidatorRule[];
  goodsBaseInfo: TypeGoodsBaseInfo;
}

/** 箱规 */
export default function GoodsBoxTableForm({ name: prefix, unitMap, goodsBaseInfo, rules }: IProps) {
  const unitCombine = (src: string, unit?: string) => {
    return `${src} (${unit || ''})`;
  };
  const form = Form.useFormInstance();
  const { type = EnumActionType.ADD } = GetPageQuery();

  useEffect(
    function () {
      if (type === EnumActionType.EDIT) {
        return;
      }
      /** 箱规暂时只读, 避免输入错误 */
      form.setFields([
        {
          name: prefix,
          value: [
            {
              boxNums: 1,
              boxLength: goodsBaseInfo.length,
              boxWidth: goodsBaseInfo.width,
              boxHeight: goodsBaseInfo.height,
              boxWeight: goodsBaseInfo.weight,
              file: goodsBaseInfo.file,
            },
          ],
        },
      ]);
    },
    [goodsBaseInfo, prefix],
  );

  return (
    <Form.List name={prefix} rules={rules}>
      {(fields, { add, remove }, { errors }) => {
        const columns = [
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBoxTableForm
                .quantityOfSingleBox,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'boxNums']}
                  fieldKey={[field.fieldKey, 'boxNums']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonPositiveInt,
                  ]}
                >
                  <TextValue />
                  {/* <InputNumber style={{ width: '100%' }} min={1} maxLength={9} /> */}
                </Form.Item>
              );
            },
          },
          {
            title: unitCombine(
              I18N.Src__Pages__OverseaLocation__Goods__Action.Index.long,
              unitMap?.measureUnit,
            ),
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'boxLength']}
                  fieldKey={[field.fieldKey, 'boxLength']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatThree,
                  ]}
                >
                  <TextValue />
                  {/* <InputNumber style={{ width: '100%' }} /> */}
                </Form.Item>
              );
            },
          },
          {
            title: unitCombine(
              I18N.Src__Pages__OverseaLocation__Goods__Action.Index.wide,
              unitMap?.measureUnit,
            ),
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'boxWidth']}
                  fieldKey={[field.fieldKey, 'boxWidth']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatThree,
                  ]}
                >
                  <TextValue />
                  {/* <InputNumber style={{ width: '100%' }} /> */}
                </Form.Item>
              );
            },
          },
          {
            title: unitCombine(
              I18N.Src__Pages__OverseaLocation__Goods__Action.Index.high,
              unitMap?.measureUnit,
            ),
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'boxHeight']}
                  fieldKey={[field.fieldKey, 'boxHeight']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatThree,
                  ]}
                >
                  <TextValue />
                  {/* <InputNumber style={{ width: '100%' }} /> */}
                </Form.Item>
              );
            },
          },
          {
            title: unitCombine(I18N.Src__Pages__Order__Detail.SiItem.weight, unitMap?.weightUnit),
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'boxWeight']}
                  fieldKey={[field.fieldKey, 'boxWeight']}
                  rules={[
                    {
                      required: true,
                      message: I18N.Src__Pages__Company__DepManager.Index.pleaseEnter,
                    },
                    CommonRulesMap.commonFloatThree,
                  ]}
                >
                  <TextValue />
                  {/* <InputNumber style={{ width: '100%' }} /> */}
                </Form.Item>
              );
            },
          },
          {
            title:
              I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBoxTableForm
                .pictureOfOuterBox,
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'file']}
                  fieldKey={[field.fieldKey, 'file']}
                >
                  <TextValue>
                    {function ({ value }) {
                      return <GoodsImage src={value?.url} />;
                    }}
                  </TextValue>
                  {/* <GoodsImageUpload uploadType={EnumUploadType.goodsBoxPicture} /> */}
                </Form.Item>
              );
            },
          },
          {
            title: I18N.Src__Pages__Order__Detail.SiItem.remarks,
            width: '15%',
            render: (_: any, field: any) => {
              return (
                <Form.Item
                  {...field}
                  name={[field.name, 'remark']}
                  fieldKey={[field.fieldKey, 'remark']}
                  rules={[
                    {
                      max: 255,
                      message:
                        I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBelongSkuTableForm
                          .mostRemarks,
                    },
                  ]}
                >
                  <TextValue />
                  {/* <Input /> */}
                </Form.Item>
              );
            },
          },
          // {
          //   title: I18N.Src__Pages__Common__Template.Index.operation,
          //   width: '8%',
          //   render: (_: any, field: any) => {
          //     return (
          //       <Form.Item {...field} fieldKey={[field.fieldKey, 'action']}>
          //         <span>
          //           <Popconfirm
          //             title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
          //             onConfirm={() => {
          //               remove(field.name);
          //             }}
          //           >
          //             <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
          //           </Popconfirm>
          //         </span>
          //       </Form.Item>
          //     );
          //   },
          // },
        ];

        return (
          <>
            <Table
              title={() => {
                // return (
                //   <Space>
                //     <Button type="primary" onClick={() => add()}>
                //       {
                //         I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBoxTableForm
                //           .addBoxGauge
                //       }
                //     </Button>
                //     <Button
                //       onClick={() =>
                //         add({
                //           boxNums: 1,
                //           boxLength: goodsBaseInfo.length,
                //           boxWidth: goodsBaseInfo.width,
                //           boxHeight: goodsBaseInfo.height,
                //           boxWeight: goodsBaseInfo.weight,
                //           file: goodsBaseInfo.file,
                //         })
                //       }
                //     >
                //       {
                //         I18N.Src__Pages__OverseaLocation__Goods__Components.GoodsBoxTableForm
                //           .addSinglePiece
                //       }
                //     </Button>
                //   </Space>
                // );
              }}
              columns={columns}
              dataSource={fields}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
            <Form.ErrorList errors={errors} />
          </>
        );
      }}
    </Form.List>
  );
}
