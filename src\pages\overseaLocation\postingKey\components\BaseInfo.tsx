import React, { useImperativeHandle, useEffect } from 'react';
import <PERSON>Form, {
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import type { ProFormProps } from '@ant-design/pro-form';

import { Col, Form, Row } from 'antd';
import I18N from '@/utils/I18N';
import { booleanOptions } from '../../enum';
import { DISPATCH_SERVICE_PROVIDER_OPTIONS, ORDER_SOURCE_PLATFORM } from '@/utils/const';
import { initNormalize } from '@/pages/overseaLocation/utils';
import { SearchSelect, ZSearchSelect } from '@/pages/overseaLocation/components';
import { apiMapDictType, apiQueryWarehouseOptions } from '@/pages/overseaLocation/api';

type TypeBaseInfo = {
  /** 是否是编辑 */
  isUpdate?: boolean;
  /** 编辑数据 */
  editData?: Record<any, any>;
};
const BaseInfo = React.forwardRef((props: TypeBaseInfo, ref) => {
  const { isUpdate } = props;
  const { config, form } = useConfig(props);

  useImperativeHandle(ref, () => ({ form }));

  const required = true;
  const disabled = isUpdate;

  return (
    <ProForm {...config}>
      <ProFormText name="id" formItemProps={{ hidden: true }} />
      <ProFormText name="enable" formItemProps={{ hidden: true }} />

      <Row gutter={24}>
        <Col span={12}>
          <ProForm.Item
            {...{
              name: 'warehouseIds',
              label: I18N.Src__Pages__OverseaLocation__ChargeManage.ChargeBillList.warehouse,
              rules: [{ required }],
            }}
          >
            <ZSearchSelect
              {...{
                style: { width: '100%' },
                disabled,
                labelInValue: true,
                mode: 'multiple',
                request: async (queryParam: string) => {
                  return apiQueryWarehouseOptions(
                    { queryParam, warehouseEnable: true },
                    { valueName: 'id' },
                  );
                },
              }}
            />
          </ProForm.Item>
        </Col>
        <Col span={12}>
          <ProFormSelect
            label={
              I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                .orderSource
            }
            rules={[{ required }]}
            name="orderSourcePlatformCode"
            request={apiMapDictType.orderSourcePlatform}
            // options={ORDER_SOURCE_PLATFORM}
            fieldProps={{
              labelInValue: true,
              showSearch: false,
            }}
            formItemProps={{
              normalize: initNormalize,
            }}
          />
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <ProFormRadio.Group
            label={
              I18N.Src__Pages__OverseaLocation__PostingKey__Components.BaseInfo.designatedStore
            }
            options={booleanOptions}
            name="appointStore"
          />
        </Col>
        <Col span={12}>
          {/* 依赖配置 */}
          <ProFormDependency name={['appointStore']}>
            {(depValues) => {
              return (
                <ProFormText
                  label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.platformShop}
                  name="platformStoreId"
                  fieldProps={{
                    disabled: !depValues?.appointStore,
                  }}
                  formItemProps={{
                    rules: [
                      {
                        required: depValues?.appointStore,
                      },
                    ],
                  }}
                />
              );
            }}
          </ProFormDependency>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <ProForm.Item
            {...{
              label: I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.expressService,
              rules: [{ required }],
              name: 'dispatchServiceProvider',
            }}
          >
            <SearchSelect {...{ request: apiMapDictType.accountingCodeExpressService }} />
          </ProForm.Item>
          {/* <ProFormSelect
            label={I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.expressService}
            rules={[{ required }]}
            name="dispatchServiceProvider"
            options={DISPATCH_SERVICE_PROVIDER_OPTIONS}
            formItemProps={{
              normalize: initNormalize,
            }}
          /> */}
        </Col>
        <Col span={12}>
          <ProFormText
            label={I18N.Src__Pages__OverseaLocation__PostingKey.PostingKeyList.postingCode}
            rules={[{ required }]}
            name="postingKey"
          />
        </Col>
      </Row>
    </ProForm>
  );
});

export default BaseInfo;

function useConfig(props: TypeBaseInfo) {
  const { editData = {} } = props;
  const form = Form.useForm()[0];

  useEffect(() => {
    form?.setFieldsValue(editData);
  }, []);

  const config = {
    submitter: false,
    form,
    initialValues: {
      /** 默认不指定店铺 */
      appointStore: false,
      /** 新增默认启用 */
      enable: 1,
    },
    onValuesChange: (changeValues) => {
      if (Object.prototype.hasOwnProperty.call(changeValues, 'appointStore')) {
        form.setFieldsValue({ platformStoreId: '' });
      }
    },
  } as ProFormProps;

  return {
    config,
    form,
  };
}
