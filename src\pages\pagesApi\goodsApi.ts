import { request } from 'umi';

/*
商品SKU下拉options
 */
export function apiGoodsSkuOptions(data: {
  /** 商品sku */
  sku?: string;
  /** 批量sku查询, 精确匹配, 大小写模糊 */
  skuList?: string[];
  /** 客户公司ID */
  companyId?: string;
  /** 客户多公司搜索 */
  companyIdList?: string[];
  /** 商品名称 */
  name?: string;
}) {
  return request<NsApi.TypeResponseList<TypeGoodsTB[]>>('/zouwu-oms-goods/portal/goods/list', {
    method: 'POST',
    data: {
      pageSize: 20,
      currentPage: 0,
      condition: data,
    },
  }).then((res) => {
    return (res.data?.records || []).map((item) => ({
      key: item.id,
      value: item.sku,
      label: item.sku,
    }));
  });
}
