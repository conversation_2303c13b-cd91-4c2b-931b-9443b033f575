import I18N from '@/utils/I18N';
import { Divider, message, Modal, Space } from 'antd';
import React, { useRef } from 'react';
import type { ActionType, ProColumns } from '@/common-import';
import { ProFormSelect, PageContainer } from '@/common-import';

import { ZouProTable } from '@/components';
import type { TypeZouProTableProps } from '@/components';
import { apiLogisticsInterceptList, apiReturnFaceSheetApply } from './LogisticsInterceptApi';
import type { TypeModalLogisticsTrackRef } from '@/views';
import { ModalLogisticsTrack, ElementCopy, renderLogisticsTrackNode } from '@/views';

import { eR, emptyRenderArray, transformDate } from '@/utils';
import { apiMapDictType, apiQueryWarehouseOptions } from '@/api';

function LogisticsInterceptList() {
  const { config, modalLogisticsTrackRef } = useConfig();

  return (
    <PageContainer>
      <ModalLogisticsTrack {...{ ref: modalLogisticsTrackRef }} />
      <ZouProTable {...config} />
    </PageContainer>
  );
}

export default LogisticsInterceptList;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const { columns, modalLogisticsTrackRef } = useColumns();
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      // defaultCollapsed: false,
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
        },
      } as Parameters<typeof apiLogisticsInterceptList>[0];

      // return {
      //   data: [{}],
      //   success: true,
      // };
      const { data } = await apiLogisticsInterceptList(query);

      return {
        data: data.records,
        success: true,
        total: data.totalSize,
      };
    },
  } as TypeZouProTableProps<TypeLogisticsInterceptTB, any>;

  return {
    config,
    actionRef,
    modalLogisticsTrackRef,
  };
}

function useColumns() {
  const modalLogisticsTrackRef = useRef<TypeModalLogisticsTrackRef>();
  const noOptions = [
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols14,
      value: 'podOrderNo',
    },
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .customerSales,
      value: 'customerSalesNo',
    },
    {
      label:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .customerAssociation,
      value: 'customerRelatedNo',
    },
  ] as const;
  const columns = [
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .oddNumbers,
      dataIndex: 'noInfo',
      search: {
        transform: (val, field, formData) => {
          const { noInfo, noType } = formData;

          return {
            [noType]: noInfo,
            noType: undefined,
          };
        },
      },
      fieldProps: {
        placeholder:
          I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
            .chineseSymbols13,
        addonBefore: (
          <ProFormSelect
            {...{
              width: 130,
              name: 'noType',
              initialValue: noOptions[0].value,
              noStyle: true,
              allowClear: false,
              options: noOptions as any,
            }}
          />
        ),
      },
      formItemProps: {
        noStyle: true,
      },
      render(dom, record) {
        return (
          <>
            {noOptions.map((item) => (
              <div key={item.value}>
                {I18N.template(
                  I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                    .LogisticsInterceptList.chineseSymbols12,
                  { val1: item.label, val2: eR(record[item.value]) },
                )}
              </div>
            ))}
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .state,
      dataIndex: 'interceptStatusName',
      valueType: 'select',
      search: true,
      request: apiMapDictType.orderLogisticsInterceptStatus,
      fieldProps: {
        mode: 'multiple',
      },
      formItemProps: {
        name: 'interceptStatusList',
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .recipientsLetter,
      dataIndex: 'recipientInfo',
      render(dom, record) {
        const {
          recipientName,
          recipientCountry,
          recipientProvince,
          recipientPostcode,
          recipientAddress1,
          recipientAddress2,
        } = record.orderExpress || {};

        return (
          <ElementCopy>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.addressee
              }
              {eR(recipientName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.recipientCountry
              }
              {eR(recipientCountry)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols11
              }
              {eR(recipientProvince)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.recipientEmail
              }
              {eR(recipientPostcode)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols10
              }
              {eR(recipientAddress1)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols9
              }
              {eR(recipientAddress2)}
            </div>
          </ElementCopy>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols8,
      dataIndex: 'saleInfo',
      search: true,
      fieldProps: {
        placeholder:
          I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
            .chineseSymbols7,
      },
      formItemProps: {
        label:
          I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
            .chineseSymbols6,
        name: 'trackingNo',
      },
      render(dom, record) {
        const {
          dispatchServiceProviderName,
          customerChannelCode,
          dispatchServiceNameName,
          trackingNo,
          trackNode,
          trackingProgress,
        } = record.orderDelivery || {};

        return (
          <>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols5
              }
              {eR(dispatchServiceProviderName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.logisticsChannel
              }
              {eR(customerChannelCode || dispatchServiceNameName)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.chineseSymbols4
              }
              {eR(trackingNo)}
            </div>
            <div>
              {
                I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                  .LogisticsInterceptList.logisticsProgress
              }
              {renderLogisticsTrackNode({
                latestNode: trackingProgress,
                trackNode,
                onClick() {
                  modalLogisticsTrackRef.current?.open({ orderId: record.orderId });
                  ssTrack(EnumSensor.RETURN_INTERCEPT_LOGISTICS_TRACK);
                },
              })}
            </div>
          </>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .commodity,
      dataIndex: 'skuList',
      render(dom, record) {
        return emptyRenderArray(record.skuList, (item) => <div key={item}>{item}</div>);
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .warehouse,
      dataIndex: 'warehouseCode',
      valueType: 'searchSelect',
      search: true,
      fieldProps: {
        mode: 'multiple',
        request: async (queryParam: string) =>
          apiQueryWarehouseOptions({ queryParam }, { valueName: 'id' }),
      },
      formItemProps: {
        name: 'warehouseIdList',
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__OutWarehouse__Components__ModalInterceptApply
          .ModalInterceptApply.chineseSymbols,
      dataIndex: 'remark',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .creationTime,
      dataIndex: 'createDate',
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            startCreateDate: transformDate(value[0], 'START'),
            endCreateDate: transformDate(value[1], 'END'),
          };
        },
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .operation,
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      width: 'auto',
      render: (text, record, i, action) => {
        const { orderId } = record;

        return (
          <Space split={<Divider type="vertical" />} size={0}>
            {/** 申请退货面单 */}
            {renderReturnFaceSheetApply({ record, action })}
          </Space>
        );
      },
    },
  ] as ProColumns<TypeLogisticsInterceptTB>[];

  return { columns, modalLogisticsTrackRef };
}

/** 渲染申请退货面单按钮 */
export function renderReturnFaceSheetApply({
  action,
  reload,
  record,
}: {
  action: ActionType | undefined;
  reload?: () => void;
  record: {
    /** 出库订单id */
    orderId?: string;
    /** 拦截状态 */
    interceptStatus?: string;
    /** 出库列表订单状态 */
    orderStatusMapping?: string;
    /** 出库单件数 */
    orderPackageSize?: number;
  };
}) {
  const { orderId } = record;
  const content =
    record.orderPackageSize! > 1
      ? '当前订单为一单多件，确定申请退货面单吗？客户使用后会增加对应的退货快递费用，且仅支持全部退货。'
      : I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols2;

  if (
    /** 物流拦截列表根据这个条件判断 */
    record.interceptStatus !== 'FAILED' /** 拦截失败 */ &&
    /** 出库列表根据这个判断 */
    record.orderStatusMapping !== 'FULFILLMENT_OVER' /** 履约完结 */
  ) {
    return undefined;
  }

  return (
    <a
      onClick={async () => {
        Modal.confirm({
          title:
            I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
              .LogisticsInterceptList.chineseSymbols3,
          content,
          async onOk() {
            await apiReturnFaceSheetApply({ orderId: orderId! });
            ssTrack(EnumSensor.RETURN_GOODS_LABEL_APPLY);
            message.success(
              I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept
                .LogisticsInterceptList.chineseSymbols1,
            );
            (reload || action?.reload)?.();
          },
        });
      }}
    >
      {
        I18N.Src__Pages__OverseaLocation__ReturnManage__LogisticsIntercept.LogisticsInterceptList
          .chineseSymbols
      }
    </a>
  );
}
