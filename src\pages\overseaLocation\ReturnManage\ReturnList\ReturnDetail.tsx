import { useEffect, useState } from 'react';
import { PageContainer } from '@/common-import';
import { ZouProTable } from '@/components';
import { cssContainer } from '@/styles';
import { historyGetQuery } from '@/utils';
import { renderImageFileList } from '@/utils-business';
import { FormProCard, FormProDescriptions } from '@/views';
import { apiReturnOrderDetail } from './ReturnApi';
import { apiMapDictType } from '@/api';
import React from 'react';

/** 退件详情 */
export default function ReturnDetail() {
  const { detailData } = useDetail();

  return (
    <PageContainer className={cssContainer['container-layout-card']}>
      <FormProCard title="基础信息">
        <FormProDescriptions
          {...{
            dataSource: detailData,
            columns: [
              { title: '退件单号', dataIndex: 'podOrderNo' },
              { title: '跟踪单号', dataIndex: 'returnTrackingNo' },
              { title: '仓库组织', dataIndex: ['orderChild', 'targetWarehouseCode'] },
              { title: '客户名称', dataIndex: 'companyName' },
              { title: '申请人', dataIndex: 'createByName' },
            ],
          }}
        />
      </FormProCard>
      <FormProCard title="商品清单">
        <ZouProTable
          {...{
            rowKey: 'id',
            tableType: 'simple',
            dataSource: detailData.orderGoodsList,
            columns: [
              { title: '商品SKU', dataIndex: 'sku' },
              { title: '件数(PCS)', dataIndex: 'totalQuantity' },
            ],
          }}
        />
      </FormProCard>
      <FormProCard title="其他信息">
        <FormProDescriptions
          {...{
            dataSource: detailData,
            columns: [
              {
                title: '包裹状态',
                dataIndex: 'packageStatus',
                valueType: 'select',
                request: apiMapDictType.returnPackageStatus,
              },
              { title: '退货原因', dataIndex: 'returnReason' },
              { title: '客户备注', dataIndex: 'customerRemark' },
              {
                title: '海外仓附件',
                dataIndex: 'fileListOms',
                render: renderImageFileList,
              },
              {
                title: '客户附件',
                dataIndex: 'fileListPortal',
                render: renderImageFileList,
              },
            ],
          }}
        />
      </FormProCard>
    </PageContainer>
  );
}

function useDetail() {
  const { podOrderNo } = historyGetQuery();
  const [detailData, setDetailData] = useState<TypeReturnOrderDetail>({});

  async function asyncRefreshDetail() {
    if (!podOrderNo) {
      return;
    }
    const { data } = await apiReturnOrderDetail({ podOrderNo });

    setDetailData(data);
  }

  useEffect(() => {
    asyncRefreshDetail();
  }, []);

  return { detailData };
}
