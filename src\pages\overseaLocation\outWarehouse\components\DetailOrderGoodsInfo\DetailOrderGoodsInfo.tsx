import { emptyRenderArrayJoin, eR } from '@/utils';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import React, { useRef, useState } from 'react';
import type { TypeOutboundGoodsTB } from '../FormGoodsChoose';
import { goodsSizeSortStr, renderDownloadableFile } from '@/utils-business';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import { ZouProTable } from '@/components';

type TypeProps = {
  detailData?: TypeOutboundDetail;
};

/** 单据明细 */
export default function DetailOrderGoodsInfo(props: TypeProps) {
  const { detailData } = props;
  const { columnsMap } = useColumnsMap();
  const [activeKey, setActiveKey] = useState<string>('goodsList');
  const actionRef = useRef<ActionType>();
  const dataMap: Record<any, any> = {
    goodsList: detailData?.goodsList || [],
    performanceList: detailData?.performanceList || [],
    packageList: detailData?.packageList || [],
  };

  return (
    <ZouProTable
      {...{
        rowKey(record: any) {
          return record.id || record.sku;
        },
        columns: columnsMap[activeKey],
        dataSource: dataMap[activeKey],
        tableType: 'simple',
        size: 'small',
        toolBarRender: undefined,
        options: false,
        bordered: true,
        actionRef,
        scroll: { x: 'max-content' },
        toolbar: {
          multipleLine: true,
          tabs: {
            activeKey,
            onChange: (key) => {
              setActiveKey(key as string);
            },
            items: [
              {
                key: 'goodsList',
                tab: '产品明细',
              },
              {
                key: 'packageList',
                tab: '包裹信息',
              },
              /* TODO 产品说移除 仓库履约, 门户侧没有仓库信息 */
              // {
              //   key: 'performanceList',
              //   tab: '仓库履约',
              // },
            ],
          },
        },
      }}
    />
  );
}

function useColumnsMap() {
  /** 产品明细 */
  const goodsList = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_, record) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName1,
      dataIndex: 'goodsName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName,
      dataIndex: 'goodsEnName',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'goodsWeight',
      render: (_, record) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'volumeUnit',
      render: (_, record) => (
        <span>
          {record.volumeUnit}: {`${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`}
        </span>
      ),
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.total,
      dataIndex: 'totalQuantity',
    },
  ] as ProColumnType<TypeOutboundGoodsTB>[];
  const performanceList = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      render: (_, record) => <GoodsImage src={record.goodsPicture} />,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName1,
      dataIndex: 'goodsName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.tradeName,
      dataIndex: 'goodsEnName',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Detail.Index.customerApplication,
      dataIndex: 'totalQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.numberToBeExecuted,
      dataIndex: 'notExecutedQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.executed1,
      dataIndex: 'executedQuantity',
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.warehousePerformance1,
      dataIndex: 'complianceNo',
    },
    { title: I18N.Src__Pages__Order__Detail.SiItem.remarks, dataIndex: 'remark' },
  ] as ProColumnType<NsOutboundDetail.TypeOrderOutPerformanceData>[];
  /** 物流包裹 */
  const packageList = [
    {
      title: '包裹编号',
      dataIndex: 'packageCode',
      // render(_, record, index) {
      //   return <span>{index + 1}</span>;
      // },
    },
    {
      title: '商品SKU',
      dataIndex: 'sku',
      render(_, record) {
        return emptyRenderArrayJoin(
          (record.packageGoodsList || []).map((item) => item.sku),
          ', ',
        );
      },
    },
    {
      title: '商品总件数',
      dataIndex: 'totalQuantity',
      render(_, record) {
        const totalQuantityArr = (record.packageGoodsList || []).map((item) => item.totalQuantity);

        return totalQuantityArr.reduce(function (pre, cur) {
          return (pre || 0) + (cur || 0);
        }, 0);
      },
    },
    {
      title: '包裹重量（KG）',
      dataIndex: 'packageWeight',
    },
    {
      title: '包裹尺寸（CM）',
      dataIndex: 'sizeArr',
      render(_, record) {
        return goodsSizeSortStr([record.packageLength, record.packageWidth, record.packageHeight]);
      },
    },
    {
      title: '物流跟踪单号',
      dataIndex: 'trackingNo',
    },
    {
      title: 'Label附件',
      dataIndex: 'file',
      render(_, record) {
        return eR(record.file, renderDownloadableFile);
      },
    },
  ] as ProColumnType<NsOutboundDetail.TypeOutboundPackageItem>[];

  return {
    columnsMap: {
      goodsList,
      performanceList,
      packageList,
    } as Record<string, ProColumnType<any>[]>,
  };
}
