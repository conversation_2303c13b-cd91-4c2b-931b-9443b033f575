import React, { forwardRef, useImperativeHandle, useState, useRef } from 'react';
import type { TableColumnsType, FormInstance } from 'antd';
import { Row, Col, Table, Space, Button, Form, InputNumber, Modal, Popconfirm, Input } from 'antd';
import type { Dispatch } from '@umijs/max';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { cloneDeep, uniqBy } from 'lodash';
import I18N from '@/utils/I18N';
import GoodsImage from '@/components/GoodsImage';
import { TextTooltip } from '@/components';
import { apiGoodsListForGoodsChoose } from '@/pages/overseaLocation/goods/goodsApi';
import css from './goodsTableForm.less';
import { findConstEnum } from '../../utils';
import { strUtils } from '@/utils';

type TypeProps = {
  ref: any;
  form: FormInstance<any>;
  dispatch: Dispatch;
  formItemName: string;
  value?: any[];
  onChange?: (skuList: any[]) => void;
};

/** 入库-商品信息
 * @deprecated 已废弃,重构为FormGoodsBoxInfo
 */
const GoodsTableForm: React.FC<TypeProps> = forwardRef((props, ref) => {
  const { value: inSkuList, onChange } = props;
  const [visible, toggleVisible] = useState(false);
  const [skuForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const removeRow = (sku: string) => {
    onChange?.((inSkuList || [])?.filter((item) => item.sku !== sku));
  };
  const { columns, columnsForChoose } = useColumns({ removeRow, props });
  const getSkuKeys = () => (inSkuList || []).map((o) => o.sku as string);

  useImperativeHandle(ref, () => ({
    init: async (initData: any) => {},
  }));

  // 新增箱-左侧勾选change事件（新增的合并）
  const handleTableRowSelectedChange = (keys: any[], rows: any[]) => {
    const inSkuKeys = getSkuKeys();
    const { skuList = [] } = skuForm.getFieldsValue();
    /** rows 必定包含了右侧数据,这步将右侧表单勾选和左侧同步 */
    const filterSkuList = skuList.filter((o: any) => keys.includes(o.sku));

    // uniqBy合并去重是为了保留编辑结果
    const uniqSkuList = uniqBy([...filterSkuList, ...rows].filter(Boolean), 'sku').filter(
      /** 过滤已添加至页面的数据 */
      (o: any) => inSkuKeys.includes(o.sku) === false,
    );

    skuForm.setFieldsValue({
      skuList: uniqSkuList,
      boxType: 1 /** 默认整箱 */,
      boxQuantity: undefined,
    });

    setSelectedRowKeys(keys);
  };

  /** 确认选中当前 */
  const handleModalOk = async (type?: string) => {
    const values = await skuForm.validateFields();
    const { skuList } = values;
    const oldSkuList = inSkuList ? cloneDeep(inSkuList) : [];

    /** 总件数 = 单箱包装数量 * 箱数
     * 因为一箱一件货, 所以 箱数 === 总件数
     */
    /** 单箱包装数量 */
    const quantityPerBox = 1;
    const generateNextSkuList = (list: TypeGoodsTableForm[]) => {
      return list.map((o, i) => {
        return {
          ...o,
          boxQuantity: o.totalQuantity! / quantityPerBox,
          /** 混箱id, id一致表示在同箱里, 会生成一致的箱唛号, 由前端生成, 后端会做校验必填,
           * 很重要, 有历史包袱 */
          boxUniqueId: o.boxUniqueId ? o.boxUniqueId : `${o.sku}-${new Date().getTime()}`,
          quantityPerBox,
          boxType: 1 /** 默认都是整箱 */,
        };
      });
    };

    onChange?.([...oldSkuList, ...generateNextSkuList(skuList)]);

    handleModalCancel(type || '');
  };

  // 新增箱modal取消
  const handleModalCancel = (type?: string) => {
    if (type !== 'next') {
      toggleVisible(false);
    }
    skuForm.resetFields();
    setSelectedRowKeys([]);
  };

  return (
    <div className={css.goodsTableForm}>
      <Space style={{ marginBottom: 10 }}>
        <Button
          type="primary"
          onClick={() => {
            toggleVisible(true);
            setSelectedRowKeys((inSkuList || []).map((o) => o.sku));
          }}
        >
          {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.oneInABox1}
        </Button>
      </Space>
      <Table
        className={css.tableForm}
        rowKey="sku"
        columns={columns}
        dataSource={inSkuList}
        size="small"
        pagination={false}
        scroll={{ x: 'max-content' }}
        bordered
      />

      <Modal
        className={css.goodsTableForm}
        title={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.oneInABox}
        visible={visible}
        width="90%"
        destroyOnClose
        maskClosable={false}
        onCancel={() => handleModalCancel()}
        footer={
          <Space>
            <Button onClick={() => handleModalCancel()}>
              {I18N.Src__Components__UploadFileModal.Index.cancel}
            </Button>
            <Button type="primary" onClick={() => handleModalOk()}>
              {I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.confirmAdd}
            </Button>
          </Space>
        }
      >
        <Row gutter={24}>
          <Col span={12}>
            <ProTable
              rowKey="sku"
              cardProps={false}
              className={css.modalTableForm}
              request={async (params) => {
                const { pageSize, current: currentPage, ...condition } = params;
                const { sku = '' } = condition;
                const skuList = strUtils.strSplitForSku(sku);

                const { data } = await apiGoodsListForGoodsChoose({
                  currentPage,
                  pageSize,
                  condition: {
                    skuList: skuList.length > 1 ? skuList : undefined,
                    sku: skuList.length === 1 ? skuList[0] : undefined,
                  },
                });

                return {
                  data: data.records,
                  success: true,
                  total: data.totalSize,
                };
              }}
              columns={columnsForChoose}
              bordered
              form={{
                isKeyPressSubmit: false,
              }}
              options={false}
              // options={{
              //   search: {
              //     addonBefore:
              //       I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
              //         .commodity1,
              //     name: 'condition',
              //     allowClear: true,
              //     style: { width: '100%' },
              //   },
              //   density: false,
              //   fullScreen: false,
              //   setting: false,
              // }}
              size="small"
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
              }}
              rowSelection={{
                preserveSelectedRowKeys: true,
                selectedRowKeys,
                type: 'checkbox',
                onChange: handleTableRowSelectedChange,
                getCheckboxProps: (record) => {
                  const isExist = (inSkuList || []).find((o) => o.sku === record.sku) !== undefined;

                  return {
                    disabled: isExist,
                  };
                },
              }}
              tableAlertRender={false}
              scroll={{ x: 'max-content' }}
            />
          </Col>
          <Col span={12}>
            <Form form={skuForm} layout="vertical">
              <Form.List name="skuList">
                {(fields, { add, remove }) => {
                  const data = (skuForm.getFieldValue('skuList') as any[]) || [];
                  const skuColumns = [
                    {
                      title:
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
                          .commodity1,
                      dataIndex: 'sku',
                      render: (_, { field, record }) => {
                        return record.sku;
                      },
                    },
                    {
                      title: I18N.Src__Pages__Order__Detail.PrePane.total2,
                      render: (_, { field }) => {
                        return renderTotalQuantity([field.name], { props });
                      },
                    },
                    {
                      title: I18N.Src__Pages__Common__Template.Index.operation,
                      fixed: 'right',
                      render: (_, { field, record }) => {
                        return (
                          <span>
                            <Popconfirm
                              title={
                                I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete
                              }
                              onConfirm={() => {
                                setSelectedRowKeys((rows) =>
                                  rows.filter((o, i) => i !== field.name),
                                );
                                remove(field.name);
                              }}
                            >
                              <a>{I18N.Src__Pages__Common__Template.Index.delete}</a>
                            </Popconfirm>
                          </span>
                        );
                      },
                    },
                  ] as ProColumns<any>[];

                  return (
                    <ProTable
                      className={css.tableForm}
                      columns={skuColumns}
                      toolbar={{
                        search: (
                          <div>
                            {
                              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                                .GoodsTableForm.addList
                            }
                          </div>
                        ),
                      }}
                      cardProps={false}
                      search={false}
                      options={false}
                      pagination={false}
                      size="small"
                      dataSource={data.map((record, i) => {
                        return {
                          record,
                          field: fields[i],
                        };
                      })}
                      bordered
                      scroll={{ x: '100%' }}
                    />
                  );
                }}
              </Form.List>
            </Form>
          </Col>
        </Row>
      </Modal>
    </div>
  );
});

export default GoodsTableForm;

/** 渲染总件数 */
function renderTotalQuantity(namePrefix: any[], { props }: { props: TypeProps }) {
  return (
    <Form.Item
      {...{
        name: [...namePrefix, 'totalQuantity'],
        rules: [
          {
            required: true,
            message:
              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
                .pleaseEnterTheTotal,
          },
          {
            async validator(rule, inVal) {
              const val = Number(inVal);

              if (val <= 0 || `${val}`.length > 9) {
                throw new Error(
                  I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.totalNumberOfPieces,
                );
              }
            },
          },
        ],
      }}
    >
      <InputNumber
        {...{
          placeholder:
            I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
              .pleaseEnterTheTotal,
          precision: 0,
          stringMode: true,
          style: { width: '100%' },
          onChange(val) {
            props.form.setFields([{ name: [...namePrefix, 'boxQuantity'], value: val }]);
          },
        }}
      />
    </Form.Item>
  );
}

function useColumns({ removeRow, props }: { removeRow: (sku: string) => void; props: TypeProps }) {
  /** 用于一箱一件货展示 */
  const columnsForChoose = [
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
      render: (sku, record: any) => {
        return sku;
      },
      renderFormItem() {
        return <Input.TextArea />;
      },
    },
    {
      title: I18N.Src__Pages__Order__Components__HsCodeForm.Index.tradeName,
      dataIndex: 'goodsName',
      search: false,
      render: (_: any, record: any) => {
        return <TextTooltip title={record.goodsName} />;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
          .netWeightOfCommodity,
      dataIndex: 'goodsWeight',
      search: false,
      render: (_: string, record: any) =>
        record.goodsWeight ? `${record.weightUnit}: ${record.goodsWeight}` : '-',
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodityVolume,
      dataIndex: 'goodsVolume',
      search: false,
      render: (_: any, record: any) => {
        return `${record.volumeUnit}: ${record.goodsLength}*${record.goodsWidth}*${record.goodsHeight}`;
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.productPicture,
      dataIndex: 'goodsPicture',
      search: false,
      render: (src: string, record: any) => {
        return record.goodsPicture ? <GoodsImage src={record.goodsPicture} /> : '-';
      },
      width: 100,
    },
  ] as ProColumns<any>[];

  const columns = [
    ...columnsForChoose,
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.singleBoxPackaging,
      dataIndex: 'quantityPerBox',
      search: false,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.totalPieces,
      dataIndex: 'totalQuantity',
      search: false,
      render: (text, record, index) => {
        return renderTotalQuantity([props.formItemName, index], { props });
      },
    },
    {
      title: I18N.Src__Pages__Order__Detail.SiItem.packingMethod,
      dataIndex: 'boxType',
      search: false,
      render: (value: any, row: any) => {
        const dict = {
          1: I18N.Src__Pages__Home.Index.fullContainer,
          2: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.mixedBox,
        } as const;

        return findConstEnum(dict, value);
      },
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.numberOfCases,
      dataIndex: 'boxQuantity',
      render: (value: string, row: any) => {
        return value;
      },
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      key: 'option',
      width: 100,
      fixed: 'right',
      render: (_, record) => {
        /** 已执行数量大于0, 不展示修改和删除 */
        const hasReceivedQuantity = record.receivedQuantity > 0;

        return (
          <Space>
            {hasReceivedQuantity ? null : (
              <Popconfirm
                title={I18N.Src__Pages__Order__Si__TableForm.GoodsDetail.doYouWantToDelete}
                onConfirm={() => {
                  removeRow(record.sku);
                }}
              >
                <a>
                  {
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm
                      .deleteBox
                  }
                </a>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ] as TableColumnsType<any>;

  return {
    columns,
    columnsForChoose,
  };
}
