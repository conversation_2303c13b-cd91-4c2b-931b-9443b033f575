import React from 'react';
import type { DividerProps } from 'antd';
import { Divider } from 'antd';
import classnames from 'classnames';
import './CustomizeDivider.less';
import { compUtils } from '@/utils';

type TypeCustomizeDividerProps = {
  /** 标题名称 */
  title?: React.ReactNode;
  /** 颜色类型 */
  colorType?: 'blue';
} & DividerProps;

/** 自定义分割线
 * @deprecated 已废弃, 使用@/views/CustomizeDivider 替代
 */
function CustomizeDivider(inProps: TypeCustomizeDividerProps) {
  const { title, colorType = 'blue', ...dividerProps } = inProps;
  const defaultDividerProps = {
    /** 默认title 靠左 */
    orientation: 'left' as const,
    className: classnames('customize-divider', colorType && `customize-divider__${colorType}`),
  };
  const props = compUtils.propsMerge(defaultDividerProps, dividerProps);

  return <Divider {...props}>{title}</Divider>;
}

export default CustomizeDivider;
