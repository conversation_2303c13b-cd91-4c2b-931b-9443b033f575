/** 退货附件类型 */
export enum EnumReturnFileType {
  /** 海外仓附件 */
  oms = 1,
  /** 门户附件 */
  portal = 2,
}

/*
public enum ReturnPackageStatusEnum {
  GOOD(1, "完好"),
  DAMAGED(2, "破损"),
  UNKNOWN(99, "未知"),
*/
/** 退货包裹状态 */
export enum EnumReturnPackageStatus {
  /** 未知 */
  UNKNOWN = 99,
}

/*
后端定义
public enum ReturnOrderStatusEnum {
    UNCLAIMED(301, "待认领", 1),
    CLAIMED(302, "已认领", 2),
    MARK_INBOUND(330, "标记入库", 30), // 历史状态
    ORIGIN_INBOUND(331, "原标入库", 31, PortalReturnTabEnum.CLAIMED.name()),
    CHANGE_INBOUND(332, "换标入库", 32, PortalReturnTabEnum.CLAIMED.name())
    MARK_DESTROY(390, "标记销毁", 90),
}
*/
/** 退货单状态 */
export enum EnumReturnOrderStatus {
  /** 待认领 */
  UNCLAIMED = 301,
  /** 已认领 */
  CLAIMED = 302,
  /** 标记入库
   * @deprecated 已作废, 业务拆分成 331和332状态了
   */
  MARK_INBOUND = 330,
  /** 原标入库 */
  ORIGIN_INBOUND = 331,
  /** 换标入库 */
  CHANGE_INBOUND = 332,
  /** 标记销毁 */
  MARK_DESTROY = 390,
}
