import React, { useState, useEffect, useReducer, useMemo, useRef } from 'react';
import type { Dispatch } from '@umijs/max';
import { history, connect, useModel, useParams } from '@umijs/max';
import H from 'history';
import { FooterToolbar } from '@ant-design/pro-layout';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import {
  Spin,
  Card,
  Row,
  Col,
  Button,
  Descriptions,
  Select,
  Form,
  Input,
  Space,
  Cascader,
  InputNumber,
  Switch,
  message,
  Modal,
} from 'antd';
import { debounce, differenceBy } from 'lodash';
import { HiveModule } from '@portal/hive-sdk';
import { PageContainer } from '@/components/PageContainer';
import I18N from '@/utils/I18N';
import { uniqBy } from '@/utils/utils-three';
import { historyGoPrev } from '@/pages/overseaLocation/utils';
import { CustomizeDivider } from '@/views';
import { ORDER_SOURCE_PLATFORM, CERTIFICATE_TYPE } from '@/utils/const';
import REGEX from '@/utils/regex';
import { GetPageQuery } from '@/utils/util';
import Context, { initState, contextReducer, ContextType } from './context';
import GoodsTableForm from './goodsTableForm';
import SendTableForm from './sendTableForm';

import styles from './index.less';
import { LoadButton, ZSearchSelect } from '@/pages/overseaLocation/components';

import { apiMapDictType, useDictTypeValueEnum } from '@/pages/overseaLocation/api';
import { FormRecipientAddress } from '../components';
import { asyncGoodsInfoRefresh } from '@/pages/overseaLocation/goods/utils';
import {
  apiOutWarehouseGetFormData,
  apiOutWarehouseSaveDraft,
  apiOutWarehouseSubmit,
} from '../OutboundApi';
import { apiCustomerChannelTree, apiMixWarehouseOptions } from '@/pages/pagesApi';
import { EnumOutOrderSource } from '../OutboundEnum';
import type { TypeOutboundGoodsTB } from './outboundApi';

const { Item } = Form;
const { Option } = Select;

/** 设置默认国家code */
// const defaultCountryCode = 'US';
/** @deprecated 通过接口动态获取 */
// const defaultCurrency = 'USD';

interface IProps {
  match: any;
  dispatch: Dispatch;
  pageLoading: boolean;
  // countryLoading: boolean;
  warehouseList: any[];
}

/** @deprecated 废弃历史出库编辑页面, 实现太脏了
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 *  @deprecated 改用 OutboundEdit
 */
const OutWarehouseLActionPage: React.FC<IProps> = ({ dispatch, pageLoading }) => {
  // console.log('warehouseList', warehouseList);
  const [newPageLoading, setNewPageLoading] = useState(false);
  const { warehouseList, asyncGetWarehouseList } = useOutWarehouseOptions();
  /** 是否为复制单, id存在且订单状态不为200, 因为200以上的单子禁止编辑
   * 这个页面区分复制,编辑,新增代码逻辑极差
   */
  const { id, orderStatus } = GetPageQuery();
  /** 是否为草稿单 */
  const isDraft = Number(orderStatus) === 200;
  /** 是否为编辑状态, 因为200以上的单子禁止编辑, 复制出来的单子不为200 */
  const isUpdate = Number(orderStatus) === 200 && id;
  /** 复制单判断逻辑:  不为编辑单, 且id存在 */
  const isCopy = () => isUpdate === false && !!id;
  /** 判断纯新建单子 */
  const isCreate = id === undefined;
  const { initialState, setInitialState } = useModel('@@initialState');
  const { webUser } = initialState as any;
  const [contextState, contextDispatch] = useReducer<any>(contextReducer, initState) as any;
  const [podNo, setPodNo] = useState('');
  const [form] = Form.useForm();

  const [orderSourcePlatformCode, setOrderSourcePlatformCode] = useState<string>('');
  const [allowBatchGoods, setAllowBatchGoods] = useState(true);
  const detailRef = useRef<TypeOutboundFormData>({});
  const addressRef = useRef<any>();
  const { dictTypeMap } = useDictTypeValueEnum(['orderSourcePlatform']);

  useEffect(() => {
    init();
    setInitialState({
      ...initialState,
      collapsed: true,
    } as any);
  }, []);
  const init = async () => {
    // 不为待提交状态（200）重新获取podNo
    if (Number(orderStatus) !== 200) {
      const result = await dispatch({
        type: 'oversea_location/getPodNo',
        payload: {
          type: 2,
        },
      });

      if (result) {
        setPodNo(result as any);
      }
    }

    if (id) {
      setNewPageLoading(true);
      try {
        const { data: detail } = await apiOutWarehouseGetFormData({ id });
        /*  const detail = await dispatch({
        type: 'out_warehouse/getOutWarehouseForm',
        payload: { id },
      }); */

        detailRef.current = {
          ...detail,
          /** 不是编辑模式, 默认订单状态是 门户建单 */
          orderSource: isUpdate ? detail.orderSource : EnumOutOrderSource.portal,
        };
        await fullDetailInfo(detail);
      } finally {
        setNewPageLoading(false);
      }
    } else {
      /** 获取地址选择初始数据 */
      addressRef.current?.addressInit?.();
    }
  };

  useEffect(() => {
    // 当商品中仓库信息修改后，重新根据仓库id获取新的配送列表
    if (contextState.currentWarehouseId) {
      asyncRefreshDispatchServiceList(contextState.currentWarehouseId);
    }
  }, [contextState.currentWarehouseId]);

  const fullDetailInfo = async (data: TypeOutboundFormData) => {
    const { express, ...rest } = data || {};
    const goodsList = data?.goodsList || [];

    let nextGoodsList: any[] = [];

    const nextExpress = {
      ...express,
    };

    /** 设置国家、省、城市的内容,无论接口是否返回国家数据都需要设置，否则会使用表单设置的初始值 */
    // nextExpress.recipientCountry = express.recipientCountry;
    /** 产品说: 有城市才可以展示省市, 也就是省市选择必须2级
     * 这期多国家依然保留这个逻辑
     */
    // if (express.recipientCity) {
    //   nextExpress.address = [express.recipientProvince, express.recipientCity];
    // }
    if (express?.recipientAddressList?.length === 0) {
      nextExpress.recipientAddressList = [''];
    }
    if (express?.recipientPhoneNumberList?.length === 0) {
      nextExpress.recipientPhoneNumberList = [''];
    }
    // 商品列表传值时是没有仓库信息，由于商品可能要和订单的仓库信息一致，所以手动set
    /** 很难理解, 门户商品的仓库信息需要前端去组合拼接 */
    if (goodsList.length && data.createDeliveryWarehouseId) {
      nextGoodsList = goodsList.map((o) => ({
        ...o,
        warehouseIdSku: `${data.createDeliveryWarehouseId}-${o.sku}`,
        warehouseId: data.createDeliveryWarehouseId,
        warehouseName: data.createDeliveryWarehouseName,
        warehouseCode: data.createDeliveryWarehouseCode,
        warehouseType: data.createDeliveryWarehouseType,
      }));
    }

    /** 草稿单需要更新商品信息 */
    if (isCopy() || isDraft) {
      nextGoodsList = await asyncGoodsInfoRefresh(nextGoodsList);
    }

    setAllowBatchGoods(isAllowBatchGoods(data?.businessType));
    form.setFieldsValue({
      ...rest,
      orderSourcePlatformCode: data.orderSourcePlatformCode,
      goodsList: nextGoodsList,
      express: nextExpress,
      /** 复制出库单清除物流服务 */
      deliveryList: isCopy()
        ? []
        : (express?.deliveryList || []).map((item: any) => {
            return {
              ...item,
              dispatchServiceType: item.dispatchServiceType
                ? {
                    label: item.dispatchServiceTypeStr,
                    value: item.dispatchServiceType,
                  }
                : null,
              dispatchServiceName: item.customerChannelCode
                ? {
                    // label: item.dispatchServiceNameStr,
                    // value: item.dispatchServiceName,
                    label: item.customerChannelCode,
                    value: item.customerChannelCode,
                  }
                : null,
            };
          }),
    });
    /** 手动获取省州、城市数据 */
    addressRef.current?.addressInit?.();

    // 待提交状态（200）使用详情中的podNo
    if (Number(orderStatus) === 200) {
      setPodNo(data.podOrderNo!);

      contextDispatch({
        type: ContextType.SET_EDIT_INIT_DATA,
        payload: data,
      });
    }

    // 存储初始化存在的仓库信息
    if (data.createDeliveryWarehouseId) {
      contextDispatch({
        type: ContextType.SET_WAREHOUSE_ID,
        payload: data.createDeliveryWarehouseId,
      });

      asyncRefreshDispatchServiceList(data.createDeliveryWarehouseId);
    }
    /** 设置全局的仓库系统标识 */
    contextDispatch({
      type: ContextType.SET_WAREHOUSE_SYS_MARK,
      payload: data.wmsSystemCode,
    });

    setOrderSourcePlatformCode(data.orderSourcePlatformCode!);
    // 存储初始化存在的物流信息
  };

  // 出库派送类型需要根据仓库来筛选
  const asyncRefreshDispatchServiceList = async (warehouseId: string) => {
    const result = await apiCustomerChannelTree({ warehouseId });
    /*
    // /api/website/web/zouwu/getDcChannelTree.do
    const result = await dispatch({
      type: 'oversea_location/getDistributionChannel',
      payload: {
        businessType: 2,
        warehouseId,
        // warehouseId
      },
    }); */
    /** 格式化数据 数组转对象，用在选择配送类型关联的配送名称上 */
    const arrToObject = () => {
      const newObj: Record<string, any> = {};

      result.forEach((item: any) => {
        newObj[item.value] = item.children;
      });

      return newObj;
    };
    const arrObject = arrToObject();

    /** 设置配送服务类型 */
    contextDispatch({
      type: ContextType.SET_DISPATCH_SERVICES_LIST,
      payload: result,
    });
    /** 设置配送服务名称 */
    contextDispatch({
      type: ContextType.SET_DISPATCH_SERVICES_NAME_LIST,
      payload: arrObject,
    });
  };

  /** 添加商品功能 */
  const handleGoodsSelect = (records: TypeOutboundGoodsTB[]) => {
    const { express, deliveryList = [], goodsList = [], businessType } = form.getFieldsValue();
    const newRecords = (records || [])?.map((item) => ({
      ...item,
      totalQuantity: 1 /** 默认1件 */,
    }));

    /** 是否支持批量多商品 */
    const allowBatch = isAllowBatchGoods(businessType);
    const newGoodsList = uniqBy(
      [...(allowBatch ? goodsList : []), ...newRecords],
      'warehouseIdSku',
    );
    const record = records[0] || {};
    const newData = {
      goodsList: newGoodsList,
      salesCurrency: record?.warehouseCurrency,
      express: {
        insuredAmount: record?.portalGoodsCustomValueResp?.goodsDeclaredValue,
        insuredAmountCurrency:
          record?.portalGoodsCustomValueResp?.currency || record?.warehouseCurrency,
      },
    };

    if (contextState.currentWarehouseId !== record.warehouseId && deliveryList.length) {
      Modal.confirm({
        title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.toggleReminder,
        content: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.currentWarehouse1,
        okText: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.confirmSwitching,
        onOk: () => {
          form.setFieldsValue({
            ...newData,
            deliveryList: [],
          });

          contextDispatch({
            type: ContextType.SET_WAREHOUSE_ID,
            payload: record.warehouseId,
          });
          /** 设置全局的仓库系统标识 */
          contextDispatch({
            type: ContextType.SET_WAREHOUSE_SYS_MARK,
            payload: record.wmsSystemCode,
          });
        },
      });
    } else {
      form.setFieldsValue({
        ...newData,
      });

      contextDispatch({
        type: ContextType.SET_WAREHOUSE_ID,
        payload: record.warehouseId,
      });
      /** 设置全局的仓库系统标识 */
      contextDispatch({
        type: ContextType.SET_WAREHOUSE_SYS_MARK,
        payload: record.wmsSystemCode,
      });
      /** 找后端确认了一下,这个商品接口返回不存在这些字段 */
      // if (record && express.signature === undefined) {
      //   form.setFieldsValue({
      //     express: {
      //       signature: record.sign,
      //     },
      //   });
      // }
      // if (record && express.electric === undefined) {
      //   form.setFieldsValue({
      //     express: {
      //       electric: record.electrified,
      //     },
      //   });
      // }
      // if (record && express.insured === undefined) {
      //   form.setFieldsValue({
      //     express: {
      //       insured: record.insure,
      //     },
      //   });
      // }
    }
  };

  // 删除已选择商品，如果删除的是最后一条，并且存在物流信息，提醒
  const handleGoodsRemove = (record: any) => {
    const { deliveryList = [], goodsList = [] } = form.getFieldsValue();
    /** 无商品则需要清除仓库信息 */
    const clearWarehouseInfo = () => {
      form.setFieldsValue({
        /** 清除仓库关联币种 */
        salesCurrency: undefined,
        /** 投保服务金额和币种 */
        express: {
          insuredAmount: undefined,
          insuredAmountCurrency: undefined,
        },
      });
      contextDispatch({
        type: ContextType.SET_WAREHOUSE_ID,
        payload: undefined,
      });
    };

    if (deliveryList?.length && goodsList?.length === 1) {
      Modal.confirm({
        title: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.deleteReminder,
        content: I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.currentWarehouse,
        okText: I18N.Src__Pages__OverseaLocation__Goods__Components.DeleteGoodsModal.okToDelete,
        onOk: () => {
          form.setFieldsValue({
            goodsList: [],
            deliveryList: [],
          });
          clearWarehouseInfo();
        },
      });
    } else {
      if (goodsList?.length === 1) {
        clearWarehouseInfo();
      }
      form.setFieldsValue({
        goodsList: goodsList.filter((item: any) => record.warehouseIdSku !== item.warehouseIdSku),
      });
    }
    form.validateFields([['express', 'insured'], ['goodsList']]);
  };

  const rewriteData = (data: any) => {
    const { cacheGoodsList = [], cacheDeliveryList } = contextState;
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { express, goodsList = [], deliveryList = [], orderSourcePlatformCode, ...rest } = data;

    const nextDeliveryList =
      deliveryList && deliveryList.length
        ? deliveryList.map((o: any) => ({
            ...o,
            // 如果存在id，判断是编辑还是复制，除了编辑，id都置为undefined
            id: isUpdate ? o.id : undefined,
            // 补充对应派送单的type，根据name
            dispatchServiceType: o.dispatchServiceType?.value,
            // dispatchServiceName: o.dispatchServiceName?.value,
            dispatchServiceName: undefined,
            customerChannelCode: o.dispatchServiceName?.value,
          }))
        : [];

    const hasDeleteGoods = differenceBy(cacheGoodsList, goodsList, 'id').map((o) => ({
      ...o,
      deleted: true,
    }));

    // 派送单整条删除
    const hasDeleteDelivery = differenceBy(cacheDeliveryList, nextDeliveryList, 'id').map(
      (o: any) => ({
        ...o,
        fileList: o.fileList ? o.fileList.map((f: any) => ({ ...f, deleted: true })) : [],
        deleted: true,
      }),
    );

    // 针对单独删除的file记录delete
    const checkDeliveryList = nextDeliveryList.map((item: any) => {
      // 存在id（编辑时保留id，走检测deleted逻辑），其余都清空原有id值
      if (item.id) {
        const cacheDeliveryItem = cacheDeliveryList.filter((o: any) => o.id === item.id)[0];
        const hasDeletedFile = differenceBy(
          cacheDeliveryItem.fileList || [],
          item.fileList || [],
          'id',
        ).map((o) => ({
          ...o,
          deleted: true,
        }));

        return {
          ...item,
          // id: item.id ? Number(orderStatus) === 200 ? item.id : undefined : undefined,
          fileList: item.fileList.concat(hasDeletedFile),
        };
      }

      return {
        ...item,
        fileList:
          item.fileList && item.fileList.length
            ? item.fileList.map((o: any) => ({ ...o, id: undefined }))
            : [],
      };
    });

    const singleList = goodsList
      ? goodsList.map((o: any) => ({
          ...o,
          id: isUpdate ? o.id : undefined,
          totalQuantity: allowBatchGoods ? o.totalQuantity || null : 1,
          goodsType: 1 /** 商品 */,
        }))
      : [];

    let result: any = {
      podOrderNo: podNo,
      ...rest,
      /** 非草稿单统一来源是门户建单 */
      orderSource: isUpdate ? detailRef.current?.orderSource : EnumOutOrderSource.portal,
      express: {
        ...express,
        deliveryList: checkDeliveryList.concat(hasDeleteDelivery),
        recipientAddressList: express.recipientAddressList.filter((o: any) => o),
        recipientPhoneNumberList: express.recipientPhoneNumberList.filter((o: any) => o),
        // recipientProvince:
        //   express.address && express.address.length ? express.address[0] : undefined,
        // recipientCity: express.address && express.address.length ? express.address[1] : undefined,
      },
      goodsList: singleList.concat(hasDeleteGoods),
      orderSourcePlatformCode,
      orderSourcePlatformName: dictTypeMap.orderSourcePlatform[orderSourcePlatformCode],
    };

    if (isUpdate) {
      result.id = id;
    }

    result = Object.assign(
      result,
      findWarehouseInfo(contextState.currentWarehouseId, warehouseList),
    );

    return result;
  };

  const onFinish = async (values: any) => {
    const data = rewriteData(values);

    try {
      setNewPageLoading(true);
      await apiOutWarehouseSubmit({
        ...data,
      });
    } finally {
      setNewPageLoading(false);
    }

    message.success(I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.newIssue);
    handleGoBack();
  };

  const onFinishFailed = (errors: any) => {
    if (!errors) return null;

    const { name } = errors.errorFields[0];
    const keys = name.join('_');

    const labelNode = document.querySelector(`label[for="${keys}"]`);

    if (labelNode) {
      labelNode.scrollIntoView({ block: 'center' });
    } else {
      form.scrollToField(name);
    }

    return true;
  };

  const handleSave = async () => {
    const values = form.getFieldsValue();

    // const result = await dispatch({
    //   type: 'out_warehouse/saveOutBoundOrder',
    //   payload: {
    //     ...rewriteData(values),
    //   },
    // });
    try {
      setNewPageLoading(true);
      await apiOutWarehouseSaveDraft(rewriteData(values));
    } finally {
      setNewPageLoading(false);
    }

    message.success(I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.temporaryIssue);
    handleGoBack();
  };

  // 返回
  const handleGoBack = () => {
    historyGoPrev();
  };

  return (
    <Context.Provider value={{ contextState, contextDispatch }}>
      <PageContainer title={false}>
        <Spin spinning={pageLoading === true || newPageLoading === true}>
          <Card>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                express: {
                  recipientPhoneNumberList: [''],
                  recipientAddressList: ['', ''],
                  /** 投保金额及币种 */
                  // insuredAmountCurrency: defaultCurrency,
                  /** 既不是copy单也不是编辑单, 纯新建单默认美国 */
                  recipientCountry: isCreate ? 'US' : undefined,
                },
                businessType: '201' /** 标准出库 */,
                /** 通过接口获取币种 */
                // salesCurrency: defaultCurrency,
              }}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              onValuesChange={(changedValues, values) => {
                if (changedValues.businessType) {
                  setAllowBatchGoods(isAllowBatchGoods(changedValues.businessType));
                  if (values.goodsList?.length > 0) {
                    form.validateFields(['goodsList']);
                  }
                }
              }}
            >
              <Descriptions
                title={
                  <Space>
                    <span>
                      {
                        I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList
                          .applicationNo
                      }
                    </span>
                    <span style={{ fontSize: 14, color: '#333' }}>{podNo}</span>
                  </Space>
                }
              >
                <Descriptions.Item
                  label={
                    I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.corporateName
                  }
                >
                  {webUser.companyName}
                </Descriptions.Item>
                <Descriptions.Item
                  label={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.Index.applicant}
                >
                  {webUser.userName}
                </Descriptions.Item>
              </Descriptions>
              <Item
                {...{
                  label: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.orderType,
                  name: 'businessType',
                  rules: [{ required: true }],
                }}
              >
                <ZSearchSelect
                  {...{
                    style: { width: '30%' },
                    allowClear: false,
                    showSearch: false,
                    include: ['201' /** 标准出库 */, '203' /** TOB出库 */],
                    /** 只有完全新增才允许修改, 复制单和修改单都不能改 */
                    disabled: isCreate === false,
                    request: apiMapDictType.outboundBusinessType,
                  }}
                />
              </Item>
              <CustomizeDivider
                title={I18N.Src__Pages__OverseaLocation__EnterWarehouse__Detail.Index.commodityList}
              />
              <Item
                name="goodsList"
                rules={[
                  {
                    validator: async (rule, value) => {
                      if (!value || !value.length) {
                        throw new Error(
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseAddASupplier,
                        );
                      }
                      if (allowBatchGoods === false && value?.length > 1) {
                        throw new Error(
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.currentOrder,
                        );
                      }
                    },
                    // message: ,
                  },
                ]}
              >
                <GoodsTableForm
                  onGoodsSelect={handleGoodsSelect}
                  onGoodsRemove={handleGoodsRemove}
                  warehouseList={warehouseList}
                  allowBatch={allowBatchGoods}
                  formIns={form}
                />
              </Item>
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.logisticsServices
                }
              />
              {/* <Row gutter={20} style={{ marginTop: 20 }}>
                <Col span={6}>
                  <Item label="商品是否带电" name={['express', 'electric']} valuePropName="checked">
                    <Switch checkedChildren="是" unCheckedChildren="否" />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    label="是否含保险服务"
                    name={['express', 'insured']}
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="是" unCheckedChildren="否" />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item label="投保金额及币种">
                    <Input.Group compact>
                      <Item name={['express', 'insuredAmountCurrency']} noStyle>
                        <Input style={{ width: '30%' }} placeholder="请输入币种" disabled />
                      </Item>
                      <Item name={['express', 'insuredAmount']} noStyle>
                        <InputNumber
                          style={{ width: '70%' }}
                          placeholder="请输入投保金额"
                          maxLength={10}
                          precision={2}
                          min={0.01}
                        />
                      </Item>
                    </Input.Group>
                  </Item>
                </Col>
              </Row> */}
              {/** 物流服务 */}
              <SendTableForm
                form={form}
                name="deliveryList"
                rule={[
                  {
                    validator: (rule, value) => {
                      if (!value || !value.length) {
                        return Promise.reject();
                      }
                      return Promise.resolve();
                    },
                    message:
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                        .pleaseAddConfiguration,
                  },
                ]}
              />
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.recipientsLetter
                }
              />
              <FormRecipientAddress
                form={form}
                ref={addressRef}
                wmsSystemCode={contextState.wmsSystemCode}
                mode="createOrEdit"
              />
              <Row gutter={10}>
                <Col span={8}>
                  <Item
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                        .consigneesCertificate
                    }
                  >
                    <Input.Group compact>
                      <Item name={['express', 'recipientIdType']} noStyle>
                        <Select
                          style={{ width: '30%' }}
                          placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
                        >
                          {CERTIFICATE_TYPE.map((item) => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Item>
                      <Item
                        name={['express', 'recipientIdNo']}
                        noStyle
                        rules={[
                          {
                            max: 30,
                            message:
                              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                                .lengthCannot1,
                          },
                          {
                            pattern: REGEX.NUMBER_AND_ENGLISH,
                            message:
                              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                                .LogisticsTableForm.pleaseEnterTheNumber,
                          },
                        ]}
                      >
                        <Input
                          style={{ width: '70%' }}
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                              .pleaseEnterTheCertificate
                          }
                        />
                      </Item>
                    </Input.Group>
                  </Item>
                </Col>
                <Col span={8}>
                  <Item
                    name={['express', 'recipientEoriNo']}
                    label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.recipientNo}
                    rules={[
                      {
                        max: 30,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .lengthCannot1,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterReceipt1
                      }
                    />
                  </Item>
                </Col>
                <Col span={8}>
                  <Item
                    name={['express', 'recipientCompanyName']}
                    label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.recipient}
                    rules={[
                      {
                        max: 50,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .lengthCannot2,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterReceipt
                      }
                    />
                  </Item>
                </Col>
              </Row>
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.sourceInformation
                }
              />
              <Row gutter={10}>
                <Col span={6}>
                  <Item
                    name="orderSourcePlatformCode"
                    label={
                      I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                        .BatchOrderDetail.orderSource
                    }
                  >
                    <ZSearchSelect
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseSelect
                      }
                      request={apiMapDictType.orderSourcePlatform}
                      onChange={(value) => {
                        form.setFieldsValue({
                          ebayPlatformSalesNo: undefined,
                          customerSalesNo: undefined,
                        });
                        setOrderSourcePlatformCode(value as any as string);
                      }}
                    />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    name="customerRelatedNo"
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList
                        .customerAssociation1
                    }
                    tooltip={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.customerSystem
                    }
                    rules={[
                      {
                        pattern: REGEX.NUMBER_AND_ENGLISH,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                            .LogisticsTableForm.pleaseEnterTheNumber,
                      },
                      {
                        max: 50,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterTheCustomer1
                      }
                    />
                  </Item>
                </Col>

                <Col span={6}>
                  <Item
                    name="customerSalesNo"
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse.OutWarehouseList.customerSales1
                    }
                    tooltip={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.eCommercePlatform
                    }
                    rules={[
                      {
                        pattern: REGEX.NUMBER_AND_ENGLISH,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                            .LogisticsTableForm.pleaseEnterTheNumber,
                      },
                      {
                        max: 50,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterTheCustomer
                      }
                    />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    name="platformStoreId"
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.platformShop
                    }
                    rules={[
                      {
                        pattern: REGEX.NUMBER_AND_ENGLISH,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                            .LogisticsTableForm.pleaseEnterTheNumber,
                      },
                      {
                        max: 30,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .lengthCannot1,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterFlat2
                      }
                    />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    name="platformSellerId"
                    label={
                      I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                        .BatchOrderDetail.platformSeller
                    }
                    rules={[
                      {
                        pattern: REGEX.NUMBER_AND_ENGLISH,
                        message:
                          I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                            .LogisticsTableForm.pleaseEnterTheNumber,
                      },
                      {
                        max: 30,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .lengthCannot1,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterFlat1
                      }
                    />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    label={I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.orderSales}
                  >
                    <Input.Group compact>
                      <Item name="salesCurrency" noStyle>
                        <Input
                          style={{ width: '30%' }}
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                              .pleaseEnterCurrency
                          }
                          disabled
                        />
                      </Item>
                      <Item name="salesAmount" noStyle>
                        <InputNumber
                          style={{ width: '70%' }}
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                              .pleaseEnterPin
                          }
                          max={9999999999}
                          precision={2}
                          min={0.01}
                        />
                      </Item>
                    </Input.Group>
                  </Item>
                </Col>

                {Number(orderSourcePlatformCode) === 2 ? (
                  <>
                    <Col span={6}>
                      <Item
                        name="ebayPlatformSalesNo"
                        label={
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .platformTransaction
                        }
                        rules={[
                          {
                            pattern: REGEX.NUMBER_AND_ENGLISH,
                            message:
                              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                                .LogisticsTableForm.pleaseEnterTheNumber,
                          },
                          {
                            max: 30,
                            message:
                              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                                .lengthCannot1,
                          },
                        ]}
                      >
                        <Input
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                              .pleaseEnterFlat
                          }
                        />
                      </Item>
                    </Col>
                    <Col span={6}>
                      <Item
                        name="ebaySalesId"
                        label={
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.transactionNo
                        }
                        rules={[
                          {
                            pattern: REGEX.NUMBER_AND_ENGLISH,
                            message:
                              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                                .LogisticsTableForm.pleaseEnterTheNumber,
                          },
                          {
                            max: 30,
                            message:
                              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                                .lengthCannot1,
                          },
                        ]}
                      >
                        <Input
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.pleaseInput
                          }
                        />
                      </Item>
                    </Col>
                  </>
                ) : null}
              </Row>
              <CustomizeDivider
                title={
                  I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.additionalInformation
                }
              />
              <Row gutter={10}>
                <Col span={6}>
                  <Item
                    name="orderOwnerName"
                    label={I18N.Src__Pages__Order__TrailerOrder.Index.orderContact}
                    rules={[
                      { type: 'string' },
                      {
                        max: 50,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                            .lengthCannot2,
                      },
                    ]}
                  >
                    <Input
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterTheSubscription1
                      }
                    />
                  </Item>
                </Col>
                <Col span={6}>
                  <Item
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.orderContact
                    }
                  >
                    <Input.Group compact>
                      <Item name="orderOwnerIdType" noStyle>
                        <Select
                          style={{ width: '30%' }}
                          placeholder={I18N.Src__Pages__Enterprise__Index.Index.pleaseSelect}
                        >
                          {CERTIFICATE_TYPE.map((item) => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Item>
                      <Item
                        name="orderOwnerIdNo"
                        noStyle
                        rules={[
                          {
                            max: 30,
                            message:
                              I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                                .lengthCannot1,
                          },
                          {
                            pattern: REGEX.NUMBER_AND_ENGLISH,
                            message:
                              I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action
                                .LogisticsTableForm.pleaseEnterTheNumber,
                          },
                        ]}
                      >
                        <Input
                          style={{ width: '70%' }}
                          placeholder={
                            I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                              .pleaseEnterTheCertificate
                          }
                        />
                      </Item>
                    </Input.Group>
                  </Item>
                </Col>
                <Col span={12}>
                  <Item
                    name="warehouseRemark"
                    label={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.orderRemarks
                    }
                    tooltip={
                      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.thisNoteLetter
                    }
                    rules={[
                      { type: 'string' },
                      {
                        max: 200,
                        message:
                          I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.lengthCannot,
                      },
                    ]}
                  >
                    <Input.TextArea
                      rows={4}
                      placeholder={
                        I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index
                          .pleaseEnterTheSubscription
                      }
                    />
                  </Item>
                </Col>
              </Row>
            </Form>
          </Card>

          <FooterToolbar
            style={{ textAlign: 'center' }}
            extra={
              <Space>
                <Button onClick={handleGoBack}>
                  {I18N.Src__Pages__Order__Detail.Index.return}
                </Button>
                {
                  // 新增和待提交的订单可以进行暂存（新增的暂存走草稿接口、暂存订单走编辑接口）
                  isCreate || isCopy() || isDraft ? (
                    <LoadButton type="default" onClick={handleSave}>
                      {I18N.Src__Pages__Order__LclBooking.Index.staging}
                    </LoadButton>
                  ) : null
                }
                <Button type="primary" loading={newPageLoading} onClick={form.submit}>
                  {I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.submitIssue}
                </Button>
              </Space>
            }
          />
        </Spin>
      </PageContainer>
    </Context.Provider>
  );
};

// export default overseaLocationOrder;
export default connect(({ loading, oversea_location }: any) => ({
  pageLoading:
    loading.models.oversea_location ||
    loading.models.out_warehouse ||
    loading.effects['global/doGlobalUploadFile'],
  // countryLoading: loading.effects['global/getGlobalCountryCityList'],
  warehouseList: oversea_location.warehouseList,
}))(OutWarehouseLActionPage);

/** 查找指定仓库信息 */
function findWarehouseInfo(warehouseId: string | undefined, warehouseList: any[]) {
  if (!warehouseId) {
    return {
      deliveryWarehouseId: undefined,
      deliveryWarehouseName: undefined,
      deliveryWarehouseCode: undefined,
      deliveryWarehouseType: undefined,
    };
  }
  const target = warehouseList.find((o) => o.id === warehouseId) || undefined;

  if (!target) {
    console.error(
      I18N.template(I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.warehouseList, {
        val1: warehouseId,
      }),
      warehouseList,
      I18N.Src__Pages__OverseaLocation__OutWarehouse__Action.Index.probablyAfter,
    );
    return undefined;
  }

  return {
    deliveryWarehouseId: target.id,
    deliveryWarehouseName: target.name,
    deliveryWarehouseCode: target.code,
    deliveryWarehouseType: target.warehouseType,
  };
}

/** 是否允许批量商品 */
function isAllowBatchGoods(businessType?: any) {
  // return `${businessType}` === '203'; /** TOB出库订单支持 */
  return true; /** 所有类型订单都支持多商品 */
}

/** 获取仓库Options */
function useOutWarehouseOptions() {
  const [warehouseList, setWarehouseList] = useState<any[]>([]);

  function asyncGetWarehouseList() {
    return apiMixWarehouseOptions({ warehouseEnable: true }, { valueName: 'id' }).then(
      (records) => {
        setWarehouseList(records);
        return records;
      },
    );
  }

  useEffect(() => {
    asyncGetWarehouseList();
  }, []);
  return { warehouseList, asyncGetWarehouseList };
}
