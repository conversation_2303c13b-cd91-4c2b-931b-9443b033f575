.inventory-search-form {
  &:global(.ant-card) {
    margin-bottom: 20px;
  }
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label > label {
      font-size: 12px;
    }
  }
}
.inventory-list {
  padding-top: 20px;
  background-color: #fff;
  .goods-name-text {
    display: block;
    width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .table-normal-text {
    display: block;
    width: 150px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}

:global(.ant-tabs) {
  margin-bottom: 8px;
  background-color: #fff;
}
.tab {
  font-weight: bold;
}

.display-inline {
  display: flex;
  align-items: center;
  justify-content: left;

  > div:not(.split) {
    flex: 1;
  }
}

.option-label {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:nth-child(1) {
    flex: 0 0 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &:nth-child(2) {
    width: 100%;
    // overflow: hidden;
  }
}
