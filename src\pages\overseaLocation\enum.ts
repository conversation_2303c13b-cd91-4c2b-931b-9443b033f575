/** 仓租结算周期 */
import I18N from '@/utils/I18N';

export const OVERSEA_CHARGE_SETTLEMENT_INTERVAL = {
  1: I18N.Src__Pages__OverseaLocation.Enum.dailySettlement,
} as const;

/** 费用账单业务场景类型
 * @deprecated 准备作废,不要再用了
 */
export const OVERSEA_CHARGE_BILL_BUSINESS_SCENE_TYPE = {
  1: I18N.Src__Pages__OverseaLocation.Enum.receiptOrder,
  2: I18N.Src__Pages__OverseaLocation.Enum.issueOrder,
  // 3: '仓租', // 费用账单筛选的结果与仓租列表获取数据维度不一致，因此在费用账单的业务类型删除仓租类型
  // 4: '充值', //账单页面不显示充值
} as const;

/** 停用启用 */
export const ableOptions = [
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.enable,
    value: 1,
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.deactivate,
    value: 0,
  },
];

/** 是否
 * @deprecated 废弃改用 optionsYesOrNo
 */
export const booleanOptions = [
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.yes,
    value: true,
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.no,
    value: false,
  },
];

/** 物流渠道商 logisticsChannelProviderType
 * @deprecated 逐步废弃改用字典接口apiMapDictType
 */
export const LOGISTICS_CHANNEL_PROVIDER_TYPE = [
  {
    label: 'USPS',
    value: '1',
  },
  {
    label: 'UPS',
    value: '2',
  },
  {
    label: 'FedEx',
    value: '3',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.other,
    value: '4',
  },
];

/** 出库tab类型
 * @hidden: true 表示隐藏, 列表中翻译还是要用的 */
export const OUT_WAREHOUSE_OPTIONS = [
  {
    label: I18N.Src__Pages__Freight__Component__TagSelect.Index.whole,
    value: -1,
    tabKey: 'total',
    color: '',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.toBeSubmitted,
    value: 200,
    tabKey: 'DRAFT',
    color: '#FFA940',
  },
  {
    label: I18N.Src__Pages__Enterprise__Account.Index.toBeReviewed,
    value: 201,
    tabKey: 'ORDER_CREATE',
    color: '#FFA940',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.chineseSymbols,
    value: 220,
    tabKey: 'WAIT_UPLOAD_LABEL',
    color: '#d9001b8f',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.processing,
    value: 209 /* 209, 230 客户渠道转配送渠道 */,
    tabKey: 'ORDER_HANDLE',
    color: '#00C853',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.insufficientFunds,
    value: 211,
    tabKey: 'ORDER_INSUFFICIENT_FROZEN_AMOUNT_ERROR',
    color: '#FE0013',
  },
  //  新增异常单tab
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.exceptionList,
    value: 210,
    tabKey: 'ORDER_ERROR',
    color: '#FE0013',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.toBeShipped,
    value: 203,
    tabKey: 'NOTIFY_WAREHOUSE',
    color: '#00C853',
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance1,
    value: 205,
    tabKey: 'FULFILLMENT_OVER',
    color: '#00AAFF',
  },
  {
    label: I18N.Src__Pages__Order__TrailerOrder.Config.canceled,
    value: 208,
    tabKey: 'ORDER_CANCEL',
    color: '#999',
  },
  /** hidden 表示tab栏不展示了, 但是列表翻译中还是有可能继续使用 */
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.cancelConfirmation,
    value: 0,
    tabKey: 'CANCEL_IN',
    color: '',
    hidden: true,
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.shipped,
    value: 204,
    color: '#00C853',
    tabKey: '',
    hidden: true,
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.toBeDetermined,
    value: 206,
    color: '#00AAFF',
    tabKey: '',
    hidden: true,
  },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.settledBill,
    value: 207,
    color: '#00AAFF',
    tabKey: '',
    hidden: true,
  },
] as const;

/** 因为tabKey不唯一, 不可以用来做匹配翻译, 会导致hidden部分匹配错误 */
export const OUT_WAREHOUSE_OPTIONS_MAP = {} as Record<
  string,
  typeof OUT_WAREHOUSE_OPTIONS[number]
> & { hidden?: boolean };
OUT_WAREHOUSE_OPTIONS.forEach((item) => {
  OUT_WAREHOUSE_OPTIONS_MAP[item.tabKey] = item;
});

/** 出库tabKeys */
export const OUT_WAREHOUSE_TAB_KEYS = OUT_WAREHOUSE_OPTIONS.map((item) => item.tabKey);

/** 出库详情流程节点 */
export const OUT_WAREHOUSE_LINK_OPTIONS = [
  // { label: '全部', value: -1 },
  // { label: '待提交', value: 200 },
  { label: I18N.Src__Pages__OverseaLocation.Enum.createABill, value: 201, tabKey: 'ORDER_CREATE' },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.orderReview,
    value: 202,
    tabKey: 'TO_BE_SUBMITTED',
  },
  { label: I18N.Src__Pages__OverseaLocation.Enum.processing, value: 209, tabKey: 'ORDER_HANDLE' },
  {
    label: I18N.Src__Pages__Order__TrailerOrder.Config.ordersReceived,
    value: 203,
    tabKey: 'NOTIFY_WAREHOUSE',
  },
  { label: I18N.Src__Pages__OverseaLocation.Enum.shipped, value: 204, tabKey: 'DELIVERED' },
  {
    label: I18N.Src__Pages__OverseaLocation.Enum.completionOfPerformance,
    value: 205,
    tabKey: 'FULFILLMENT_OVER',
  },
  // { label: '费用确认', value: 206 },
  // { label: '结单', value: 207 },
  {
    label: I18N.Src__Pages__Order__TrailerOrder.Config.canceled,
    value: 208,
    tabKey: 'ORDER_CANCEL',
  },
] as const;

/** 出库异常单类型 */
export enum EnumOutboundErrorType {
  /** ERP拉取订单报错 (订单建单异常) */
  ERPOrderFail = 1,
  /** 通知仓库时，面单传输失败 (附件异常) */
  faceSheetFail = 2,
  /** 面单报错 */
  // 后端(lz)说这步不会报错, 无法区分用户是主动没传, 还是异常没传,
  /** 订单查询异常 */
  orderSearchFail = 3,
  /** 资金冻结异常 */
  fundsFreezeFail = 4,
  /** 冻结金额余额不足 */
  fundsNotEnough = 5,
  /** 仓库状态异常 */
  warehouseAbnormal = 6,
  /** 下快递失败卡单 (快递流程异常) */
  expressFail = 7,
  /** 通知仓库失败 (通知仓库发货异常) */
  noticeWarehouseFail = 8,
  /** 查询仓库发货异常 */
  queryWarehouseDeliveryError = 9,
  /** 渠道处理异常 */
  CHANNEL_HANDLER_ERROR = 10,
  /** 通知仓库建单过程异常 */
  CREATE_WMS_ORDER_PROCESS_ERROR = 11,
  /** 更新包裹快递信息异常 */
  UPDATE_PACKAGE_EXPRESS_INFO_ERROR = 12,
}

/** 允许修改地址的异常状态 */
export const arrAllowEditAddress = [1, 6, 7, 8, 9, 10, 11, 12];
