type TypeExportListTB = {
  /** 主键 */
  id?: number;
  /** oss下载地址 */
  url?: string;
  /** 服务类型 1-charge 2-order 3-goods 4-system 5-stock */
  serviceType?: number;
  /** 来源类型 1-oms 2-门户 */
  dataSourceType?: number;
  /** 导出数据总量 */
  recordTotalNum?: number;
  /** 当前执行数据数量 */
  executeNum?: number;
  /** 业务类型 1-费用账单 (同业务数据源) */
  businessType?: number;
  /** 状态 1-解析中，2-解析完成，3-解析失败 */
  status?: number;
  /** 原始数据 */
  sourceData?: string;
  /** 创建人id */
  createById?: string;
  /** 创建人名 */
  createByName?: string;
  /** 自定义文件名 */
  fileName?: string;
  /** 创建时间 (同业务申请时间) */
  createDate?: string;
};
