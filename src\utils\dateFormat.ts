import moment from 'moment';

/**
 * @description YYYY-MM-DD  -> YYYY-MM-DD 00:00:00 || YYYY-MM-DD 23:59:59
 * @description 用于格式化时间 type=START 00:00:00  type=END 23:59:59
 * @param inp 需要格式化的值
 * @param type 格式化类型
 */
export function transformDate<T>(inp: T, type: 'START' | 'END') {
  const typeMap = {
    START: 'YYYY-MM-DD 00:00:00',
    END: 'YYYY-MM-DD 23:59:59',
  } as const;

  return inp ? moment(inp).format(typeMap[type]) : inp;
}

/** 转换时间范围格式 */
export function transRangeDate(arr: any[]) {
  return {
    min: transformDate(arr?.[0], 'START'),
    max: transformDate(arr?.[1], 'END'),
    includeMin: true,
    includeMax: true,
  };
}

/**
 * 日期时间格式化
 */

function dateTimeFormatter(
  inp: any,
  format: 'YYYY-MM-DD HH:mm:ss' | 'YYYY-MM-DD HH:mm' = 'YYYY-MM-DD HH:mm:ss',
) {
  return inp ? moment(inp).format(format) : inp;
}

/** 日期格式化 */
export function dateFormatter(inp: any, format = 'YYYY-MM-DD' as const) {
  return inp ? moment(inp).format(format) : inp;
}

/**
 * 列表日期数据格式化操作
 * @deprecated 因为接口逐步迁移至OMS, 这个处理方法逐步作废
 */
function listDateFormatter<T extends any[]>(
  response: NsApi.TypeResponseList<T>,
  valueFields: (keyof T[0])[],
) {
  return {
    ...response,
    result: {
      ...response.result,
      records: response.result.records.map((record) => {
        valueFields.forEach((field) => {
          record[field] = dateTimeFormatter(record[field]);
        });
        return record;
      }) as T,
    },
  };
}

/** 时间格式化工具库 */
export const dateUtils = {
  transformDate,
  dateFormatter,
  dateTimeFormatter,
  listDateFormatter,
};
