import { promise<PERSON>ache<PERSON>pi } from '@/utils';
import { request } from 'umi';

type TypeListCondition = {
  /** 需要创建退货面单的出库订单id */
  podOrderNo?: string;
  /** 客户销售单号（出库） */
  customerSalesNo?: string;
  /** 客户关联单号（出库） */
  customerRelatedNo?: string;
  /** 平台卖家id */
  platformSellerIdList?: string[];
  /** 平台店铺id */
  platformStoreIdList?: string[];
  /** 退货运单 */
  returnTrackingNo?: string;
  /** 销售运单 */
  trackingNo?: string;
  /** 状态 10:待上传面单 20:已上传面单 35:取消 */
  returnStatusList?: number[];
  /** 仓库code */
  warehouseIdList?: string;
  /** 企业id */
  companyIdList?: number;
  /** 创建时间开始 */
  createDateStart?: string;
  /** 创建时间结束 */
  createDateEnd?: string;
};

/** 退货面单列表查询 */
export function apiReturnFaceSheetList(
  data: NsApi.TypeRequestListQuery<{
    condition: TypeListCondition;
  }>,
) {
  return request('/zouwu-oms-order/portal/order/returnGoodsLabel/list', { method: 'POST', data });
}

/** 退货面单导出 */
export function apiReturnFaceSheetExport(
  data: TypeListCondition & { /** 自定义文件名 */ fileName?: string },
) {
  return request('/zouwu-oms-system/portal/return/goods/export', { method: 'POST', data });
}
