import type { UploadRequestOption as RcUploadRequestOption } from 'rc-upload/lib/interface';
import { default as OSSA<PERSON> } from 'ali-oss';
import { apiOssAliToken, asyncGetStsToken } from './ali-oss-api';
import type { RcFile } from 'antd/lib/upload';
import moment from 'moment';
import type { TypeSimpleUploadProps } from '@/components';
import type { TypeCacheInfo } from './cache-file-manager';
import asyncComputeFileMD5, { cacheFileManager } from './cache-file-manager';

/** 获取阿里云OSS客户端实例,每个文件一个 */
async function asyncGetAliOSSClient() {
  const stsToken = await asyncGetStsToken();
  const ossClient = new OSSAli({
    ...stsToken,
    secure: true,
    refreshSTSToken() {
      return apiOssAliToken();
    },
  }) as OSSAli & {
    /** 类型定义中没有cancel, 但实际存在且需要 */
    cancel?: () => void;
  };

  return {
    stsToken,
    ossClient,
  };
}

type TypeOssUploadParams = {
  /** 过期类型, 不传不过期, 默认无过期 */
  expirationType?: 'month';
};

/** 错误次数统计, 防止特定error巨量刷新 */
const errorCountMap: Record<'InvalidAccessKeyIdError', number> = { InvalidAccessKeyIdError: 0 };

/** 阿里OSS上传文件
 * @ 适配antd4 Upload.customRequest 参数
 */
async function asyncAliOSSUpload(
  inParams: RcUploadRequestOption,
  uploadParams: TypeOssUploadParams = {},
) {
  const { expirationType } = uploadParams;
  const params = inParams as {
    /** 感觉antd.Upload 这部分类型定义有问题,和rc-upload的定义不一致, 所以他屏蔽了onProgress */
    onProgress?: (percent: { percent: number }, file: File) => void;
    file: RcFile & { url: string; md5: string };
  } & Omit<RcUploadRequestOption, 'onProgress' | 'file'>;
  const { file } = params;

  if (file instanceof File !== true) {
    return;
  }

  const md5 = await asyncComputeFileMD5(file);

  file.md5 = md5;
  const curCacheInfo = cacheFileManager.getCacheInfo(file);

  if (curCacheInfo) {
    /** 缓存命中, 注册回调信息, 会自动同步数据 */
    cacheFileManager.registerRequestOption(file, params);
    return;
  }

  const { ossClient, stsToken } = await asyncGetAliOSSClient();
  /** 过期路径 */
  const specialPath =
    expirationType === 'month' ? `expiration_month/${moment().format('YYYYMM')}` : 'tep';
  const namePath = `upload_public/${stsToken.rootPath}/${specialPath}/${md5}/${file.name}`;
  const url = `${stsToken.host}${namePath}`;
  const { cacheInfo, control } = cacheFileManager.registerRequestOption(file, params);
  let checkpoint: OSSAli.Checkpoint;
  let isPaused = false;

  try {
    /** 文件已存在,直接返回信息,秒传 */
    const headRes = await ossClient.head(namePath);

    cacheFileManager.setCacheInfo(file, { url: url, status: 'done', data: headRes.res });
    control?.onSuccess?.(headRes.res);
    return;
  } catch (error: any) {
    if (error?.code === 'NoSuchKey') {
      /** 文件不存在, 继续走上传流程, catch 不用处理 */
    } else {
      console.error('head oss error ---> : ', error);
    }
  }

  async function asyncMultipartUpload(
    options: OSSAli.MultipartUploadOptions,
    curFile: NonNullable<TypeCacheInfo['file']>,
  ) {
    try {
      const data = await ossClient.multipartUpload(namePath, curFile, {
        headers: {},
        ...options,
        progress: (percentage, cpt: OSSAli.Checkpoint, ...args) => {
          checkpoint = cpt;
          const percent = Math.min(percentage * 100, 100);

          if (percent === 0) {
            /** 上传开始 */
            cacheFileManager.setCacheInfo(curFile, {
              async abort() {
                await ossClient.abortMultipartUpload(namePath, checkpoint?.uploadId);
              },
              cancel() {
                /** 取消后可以恢复上传,但是 abort 后不行, abort 是中止上传任务 */
                ossClient.cancel?.();
                isPaused = true;
              },
              async resume() {
                /** 断点续传的文件需要从缓存中获取, 因为uid可能已经变了 */
                try {
                  //   throw new Error('暂不支持断点续传');
                  /** 暂停恢复调用自己 */
                  await asyncMultipartUpload(
                    { checkpoint: cacheInfo?.checkpoint ?? checkpoint },
                    cacheInfo.file!,
                  );
                } catch (error: any) {
                  console.error('resume error: ', error);
                  await asyncMultipartUpload({}, cacheInfo.file!);
                }
              },
            });
          }

          cacheFileManager.setCacheInfo(curFile, { checkpoint, status: 'uploading' });
          control?.onProgress?.({ percent }, curFile);
        },
      });

      cacheFileManager.setCacheInfo(curFile, { url, status: 'done', data });
      /** data会写入file.response */
      control?.onSuccess?.(data);
    } catch (error: any) {
      console.error(error);
      if (error?.name === 'InvalidAccessKeyIdError') {
        errorCountMap.InvalidAccessKeyIdError += 1;
        if (errorCountMap.InvalidAccessKeyIdError > 2) {
          console.error('InvalidAccessKeyIdError--->', '异常重试超出指定次数: ', 2);
          control?.onError?.(error, 'InvalidAccessKeyIdError');
          cacheFileManager.clearCacheInfo(file);
          errorCountMap.InvalidAccessKeyIdError = 0;
          return;
        }
        /** 秘钥失效, 刷新token, 重新走一遍上传 */
        await apiOssAliToken();
        cacheFileManager.clearCacheInfo(file);
        console.warn('重新上传');
        asyncAliOSSUpload(inParams, uploadParams);
        return;
      }
      /** abort时error结果 {status: 0, name: 'cancel'}
       * cancel() 结果同上, 但是 abort 是中止上传任务, 无法恢复, 所以要区分,  cancel不作为异常处理
       */
      let errMsg = `file upload failed: ${curFile.name}`;

      if (isPaused) {
        console.info('=======> file upload paused: ', curFile.name);
        cacheFileManager.setCacheInfo(curFile, { status: 'paused' });
        isPaused = false;
        return;
      }

      if (error?.name === 'cancel') {
        errMsg = `file upload canceled: ${curFile.name}`;
      }
      cacheFileManager.setCacheInfo(curFile, { status: 'error' });
      control?.onError?.(error, errMsg);
    }
  }

  await asyncMultipartUpload({}, file);
}

/** 计算断点续传Upload组件属性 */
function calcMultipartUploadProps(
  props?: TypeSimpleUploadProps,
  uploadParams: TypeOssUploadParams = {},
) {
  const newProps = {
    ...props,
    allowManual: false,
    allowValidFileStatus: true,
    listenFileChange: cacheFileManager.listenFileChange,
    customRequest(options) {
      return asyncAliOSSUpload(options, uploadParams);
    },
  } as TypeSimpleUploadProps;

  return newProps;
}

export const aliOssUtils = {
  asyncAliOSSUpload,
  calcMultipartUploadProps,
};
