import type { ColProps } from 'antd';
import { Col, Row } from 'antd';
import React, { Children } from 'react';
import type { TypeRowLayoutProps } from './RowLayout.types';
import RowInner from './RowInner';
import { RowContext } from './RowLayoutContext';

/** row 简化布局 */
export default function RowLayout(inProps: TypeRowLayoutProps) {
  const {
    columnNum = 3,
    useFragment = false,
    ifShow = true,
    gutter = [16, 16],
    children,
    ...restProps
  } = inProps;

  if (ifShow !== true) {
    return null;
  }

  const span = 24 / (inProps.columnNum || 3);
  /** 真实默认列宽, 仅在xl尺寸下生效 */
  const mediaSpan = inProps.columnNum ? span : 8;
  const mediaParams = {
    xs: 24,
    sm: 12,
    md: Math.max(mediaSpan, 8),
    lg: Math.max(mediaSpan, 8),
    xl: mediaSpan,
  };
  const childrenNodeList = Children.map(inProps.children, (child) => {
    if (!child) {
      return undefined;
    }
    if (
      React.isValidElement(child) &&
      [RowLayout, RowLayout.RowInner].includes(child.type as any)
    ) {
      return child;
    }
    let newChild: React.ReactNode = child;
    let innerColProps;

    if (React.isValidElement(child)) {
      if (child.type === Col) {
        const childProps = child.props as ColProps;

        newChild = childProps.children;
        innerColProps = childProps;
      } else {
        const { colProps, ...childProps } = child.props as any;

        childProps && (newChild = React.createElement(child.type, childProps as any));
        innerColProps = colProps;
      }
    }

    return <Col {...{ span, ...mediaParams, ...innerColProps }}>{newChild}</Col>;
  });

  const rowNode = useFragment ? (
    <React.Fragment>{childrenNodeList}</React.Fragment>
  ) : (
    <Row {...{ gutter, ...restProps, children: childrenNodeList }} />
  );

  return <RowContext.Provider value={{ parentProps: inProps }}>{rowNode}</RowContext.Provider>;
}

RowLayout.RowInner = RowInner;
