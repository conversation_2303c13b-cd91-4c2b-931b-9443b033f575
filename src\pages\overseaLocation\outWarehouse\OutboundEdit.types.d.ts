/** 出库草稿和复制表单详情接口 */
type TypeOutboundFormData = {
  /** 订单id */
  id?: string;
  /** 是否预算费用 0-否 1-是 */
  isCalculateCosts?: boolean;
  /** 建单用户名 */
  userName?: string;
  /** 申请单号 */
  podOrderNo?: string;
  /** 履约单号 */
  complianceNo?: string;
  /** 业务类型 */
  businessType?: number;
  /** 订单来源 */
  orderSource?: number;
  /** 订单来源平台code */
  orderSourcePlatformCode?: string;
  /** 订单来源平台名称 */
  orderSourcePlatformName?: string;
  /** 客户销售单号 */
  customerSalesNo?: string;
  /** 客户关联单号 */
  customerRelatedNo?: string;
  /** 平台卖家id */
  platformSellerId?: string;
  /** 平台店铺id */
  platformStoreId?: string;
  /** 订单销售金额 */
  salesAmount?: number;
  /** 订单销售币种 */
  salesCurrency?: string;
  /** Ebay交易ID号 */
  ebaySalesId?: string;
  /** Ebay平台交易单号 */
  ebayPlatformSalesNo?: string;
  /** 订单备注 */
  warehouseRemark?: string;
  /** 订单人姓名 */
  orderOwnerName?: string;
  /** 订单人证件类型 */
  orderOwnerIdType?: number;
  /** 订单人证件号码 */
  orderOwnerIdNo?: string;
  /** 发货仓库id */
  deliveryWarehouseId?: string;
  /** 发货仓库名称 */
  deliveryWarehouseName?: string;
  /** 发货仓库编码 */
  deliveryWarehouseCode?: string;
  /** 发货仓库类型 1-标准海外仓，2-海外中转仓 */
  deliveryWarehouseType?: number;
  /** 下单仓库id */
  createDeliveryWarehouseId?: string;
  /** 下单仓库名称 */
  createDeliveryWarehouseName?: string;
  /** 下单仓库编码 */
  createDeliveryWarehouseCode?: string;
  /** 发货仓库类型 1-标准海外仓，2-海外中转仓 */
  createDeliveryWarehouseType?: number;
  /** 申请人id */
  createById?: string;
  /** 申请人名称 */
  createByName?: string;
  /** 申请公司id */
  companyId?: string;
  /** 申请公司 */
  companyName?: string;
  /** 仓库系统code */
  wmsSystemCode?: string;
  /** 已删除的带id的文件, 说明是接口返回数据 */
  cacheDeletedFileList?: NsOutbound.TypeFileItem[];
  /** 前端定义属性, 决定渠道类型 */
  frontChannelType?: string;
  /** 预计提货时间 */
  estimatedPickupDate?: string;
  /** 托盘尺寸信息 */
  palletSizeList?: NsOutbound.TypePalletSizeItem[];
  /** 物流快递信息 */
  express?: {
    /** 申请单号 */
    podOrderNo?: string;
    /** 履约单号 */
    complianceNo?: string;
    /** 收件人国家 */
    recipientCountry?: string;
    /** 收件人省/州 */
    recipientProvince?: string;
    /** 收件人城市 */
    recipientCity?: string;
    /** 收件人详细地址列表 */
    recipientAddressList?: string[];
    /** 收件人门牌号 */
    recipientHouseNumber?: string;
    /** 收件人姓名 */
    recipientName?: string;
    /** 收件人证件类型 */
    recipientIdType?: number;
    /** 收件人证件号码 */
    recipientIdNo?: string;
    /** 收件人联系电话列表 */
    recipientPhoneNumberList?: string[];
    /** 收件人邮箱 */
    recipientEmail?: string;
    /** 收件人邮编 */
    recipientPostcode?: string;
    /** 收件人分邮编 */
    recipientBranchPostcode?: string;
    /** 收件人EORI号 */
    recipientEoriNo?: string;
    /** 收件人公司 */
    recipientCompanyName?: string;
    /** 收件人是否FBA */
    recipientIsFba?: boolean;
    /** 签名服务 */
    signature?: boolean;
    /** 签名类型 */
    signatureType?: string;
    /** 是否带电 */
    electric?: boolean;
    /** 保险服务 */
    insured?: boolean;
    /** 投保金额 */
    insuredAmount?: number;
    /** 投保币种 */
    insuredAmountCurrency?: string;
    /** 是否打包 */
    isPack?: boolean;
    /** 物流服务信息, 编辑页面获取出库详情, 保存草稿使用这个, 此时无deliveryDTO */
    deliveryList?: [NsOutbound.TypeDeliveryInfo];
    /** 物流服务信息:  来源提交出库时,此时不用 deliveryList */
    deliveryDTO?: NsOutbound.TypeDeliveryInfo;
  };
  /** 商品清单 */
  goodsList?: NsOutbound.TypeGoodsChooseTB[];
  /** 包材信息 */
  packageMaterial?: {
    /** 包材规格枚举: DEFAULT: 默认包材, CUSTOMER: 客户自备, BAG: 快递袋, CARTON: 纸盒 */
    materialType?: 'DEFAULT' | 'CUSTOMER' | 'BAG' | 'CARTON';
    /** 包材长度（cm） */
    materialLength?: number;
    /** 包材宽度（cm） */
    materialWidth?: number;
    /** 包材高度（cm） */
    materialHeight?: number;
    /** 包材重量（kg） */
    materialWeight?: number;
  };
  /** 派送服务信息 */
  deliveryInfo?: NsOutbound.TypeDeliveryInfo;
};

declare namespace NsOutbound {
  /** 派送服务信息 */
  export type TypeDeliveryInfo = {
    /** 物流派送单id */
    id?: string;
    /** 派送服务类型 */
    dispatchServiceType?: number;
    /** 派送服务名称, 历史原因后缀是Name, 其实寸的是code */
    dispatchServiceName?: string;
    /** 派送服务类型翻译 */
    dispatchServiceTypeStr?: string;
    /** 物流类型后端翻译 */
    dispatchServiceTypeValue?: string;
    /** 派送服务名称翻译 (编辑表单详情返回) */
    dispatchServiceNameStr?: string;
    /** 物流渠道名称, 后端翻译 (列表和详情返回) */
    dispatchServiceNameValue?: string;
    /** 客户渠道名称, 门户不使用 */
    customerChannelName?: string;
    /** 客户渠道CODE */
    customerChannelCode?: string;
    /** 追踪单号 */
    trackingNo?: string;
    /** 回邮单号 | 退货物流号 */
    returnShippingNum?: string;
    /** 删除标志 */
    deleted?: boolean;
    /** 是否打托 0-否 1-是 */
    isPalletize?: boolean;
    /** 是否打托, 打托数量 */
    palletQuantity?: number;
    /** 箱唛贴数 */
    markQuantity?: number;
    /** 产品贴数 */
    productQuantity?: number;
    /** 托盘贴数 */
    palletNumber?: number;
    /** 预计提货时间 */
    estimatedPickupDate?: string;
    /** 托盘尺寸信息 */
    palletSizeList?: TypePalletSizeItem[];
    /** 附件列表 */
    fileList?: TypeFileItem[];
    /** label 附件 */
    fileListLabel?: TypeFileItem[];
    /** 其它附件 */
    fileListOther?: TypeFileItem[];
    /** 箱唛贴文件 */
    fileListBoxMark?: TypeFileItem[];
    /** 产品贴文件 */
    fileListProduct?: TypeFileItem[];
    /** 托盘贴文件 */
    fileListPallet?: TypeFileItem[];
    /** AMZX附件 */
    fileListAMZX?: TypeFileItem[];
    /** BOL附件 */
    fileListBOL?: TypeFileItem[];
    /** POD 附件,签收履约完结后,后端从17渠道下载
     * @ 仅详情展示
     */
    fileListPOD?: TypeFileItem[];
  };

  /** 托盘尺寸Item */
  type TypePalletSizeItem = {
    /** 当前物流信息id */
    deliveryId?: string;
    /** 同行尺寸Id */
    palletId?: string;
    /** 10-48x40 inch（标准）、20-65x40 inch、30-80x40 inch、40-96x40 inch、99-其他尺寸 */
    palletSizeType?: number;
    /** 托盘尺寸数量 */
    palletSizeQuantity?: number;
    /** 打托备注 */
    remark?: string;
    /** sku */
    skuList?: string[];
    /** 是否删除 */
    deleted?: boolean;
  };

  /** 选中商品对象 */
  type TypeGoodsChooseTB = {
    /** id */
    id?: string;
    /** SKU */
    sku?: string;
    /** 商品类型 */
    goodsType?: number;
    /** 关联组合商品sku */
    combinedGoodsSku?: string;
    /** 商品图片 */
    goodsPicture?: string;
    /** 商品名称 */
    goodsName?: string;
    /** 商品英文名称 */
    goodsEnName?: string;
    /** 平台编码 */
    platformCode?: string;
    /** 商品申报价值 */
    goodsValue?: number;
    /** 价值单位 */
    valueUnit?: string;
    /** 商品净重 */
    goodsWeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 体积单位 */
    volumeUnit?: string;
    /** 总件数 */
    totalQuantity?: number;
    /** 好件数 */
    goodQuantity?: number;
    /** 货品价值 */
    goodsInsuranceValue?: number;
    /** 删除标志 */
    deleted?: boolean;
  };

  /** file文件对象 */
  type TypeFileItem = {
    /** id */
    id?: string;
    /** 附件类型 */
    fileType?: import('./OutboundEnum').EnumOutboundFileType;
    /** 附件路径 */
    url?: string;
    /** 附件名称 */
    fileName?: string;
    /** 附件路径 */
    filePath?: string;
    /** 删除标志 */
    deleted?: boolean;
  };
}
