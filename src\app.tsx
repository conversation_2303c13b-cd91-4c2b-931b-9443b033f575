import I18N from '@/utils/I18N';
import '@/styles';
import React from 'react';
import type { RequestConfig } from '@umijs/max';
import { HiveModule } from '@portal/hive-sdk';
import { ConfigProvider } from 'antd';

import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/es/locale/en_US';
import Cookies from 'js-cookie';
import { requestInterceptors, responseInterceptors, errorHandler } from './request.config';
import { localePolyfill } from './utils/localePolyfill';
import type { TypeSessionInfo } from './utils/session';
import { session } from './utils/session';
import { sensorInit } from '@/sensor';
import { ImagePreview } from './views';

export function rootContainer(container: React.ReactNode) {
  const currentLanguage = Cookies.get('language_website');

  return (
    <ConfigProvider
      {...{
        locale: currentLanguage === 'en' ? enUS : zhCN,
      }}
    >
      <div id={'wwl-zouwu-front'}>
        <ImagePreview />
        {container}
      </div>
    </ConfigProvider>
  );
}

export async function getInitialState() {
  localePolyfill();
  sensorInit({ eventPrefix: 'DTC_WWL_' });
  try {
    // umi4的getInitialState触发机制以及下方的rootContainer中的Env拦截器决定此时appEnv必定存在
    const appEnv = await HiveModule.getAppEnvFlag();

    session.set('appEnv', appEnv);
    const userInfo = await HiveModule.initUserInfo();

    session.set('userInfo', userInfo?.webUser);

    return {
      ...(userInfo as { webUser: TypeSessionInfo['userInfo'] }),
      appEnv: appEnv as TypeSessionInfo['appEnv'],
    };
  } catch (error) {
    console.log(error);
  }
}

export const request: RequestConfig = {
  responseInterceptors: [responseInterceptors],
  requestInterceptors: [requestInterceptors],
  // credentials: 'include', // 默认请求是否带上cookie
  errorConfig: {
    errorHandler,
  },
};
