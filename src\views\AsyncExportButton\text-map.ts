import I18N from '@/utils/I18N';

export const textMap = {
  /** 按钮文字 */
  txtBtn: I18N.Src__Pages__OverseaLocation__CashFlow.CashFlowList.export,
  txtFileName: I18N.Src__Pages__OverseaLocation__ExportManage__ExportList.ExportList.fileName,
  txtAlert: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols5,
  txtModalTitle: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols4,
  txtModalCancel: I18N.Src__Views__AsyncExportButton.Text_map.cancel,
  txtModalConfirm: I18N.Src__Views__AsyncExportButton.Text_map.determine,
  txtNotifyMessage: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols3,
  txtGoto: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols2,
  txtDes1: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols1,
  txtDes2: I18N.Src__Routes.Index.chineseSymbols1,
  txtDes3: I18N.Src__Views__AsyncExportButton.Text_map.chineseSymbols,
} as const;
