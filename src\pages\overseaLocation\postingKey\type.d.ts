/** 记账码列表筛选数据 */
type TypeQueryData = {
  /** 电商平台 */
  orderSourcePlatformCodes?: string[];
  /** 仓库ids */
  warehouseIds?: string[];
  /** 电商名称 */
  orderSourcePlatformNames?: string[];
  /** 快递服务商 FedEx，UPS */
  dispatchServiceProviders?: string;
  /** 状态 0-停用，1-启用 */
  enable?: boolean;
};

/** 记账码列表数据 */
type TypePostingKeyRecord = {
  /** 主键id */
  id?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 仓库CODE */
  warehouseCode?: string;
  /** 仓库id */
  warehouseId?: string;
  /** 电商平台code */
  orderSourcePlatformCode?: string;
  /** 电商平台名称 */
  orderSourcePlatformName?: string;
  /** 快递服务商 */
  dispatchServiceProvider?: string;
  /** 记账码 */
  postingKey?: string;
  /** 状态 0-停用，1-启用 */
  enable?: number;
  /** 操作人 */
  updateByName?: string;
  /** 操作时间 */
  updateDate?: string;
  /** 是否可编辑 ture-可编辑，false-不可编辑	 */
  hasEdit?: boolean;
  /** 店铺id */
  platformStoreId?: string;
};
