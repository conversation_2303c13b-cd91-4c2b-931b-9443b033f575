/** 缓存api接口, 如select枚举结果等
 * @ 如果类型推导结果不正确, 加async来辅助推导结果
 */
export function promiseCacheApi<T extends (...args: any[]) => Promise<any>>(
  call: T,
  {
    expired,
  }: {
    /** 结果过期时间, setTimeout操作, 默认不过期 */
    expired?: number;
  } = {},
) {
  const cacheMap: Record<string, Promise<any> | undefined> = {};

  return function (...args: Parameters<T>) {
    const cacheKey = args.length > 0 ? JSON.stringify(args) : '_default';

    if (cacheMap[cacheKey] !== undefined) {
      return cacheMap[cacheKey];
    }
    const req = call(...args);
    /**
     * 默认调用即成功, 等失败再把缓存取消
     * 不然等成功后再缓存, 仍会导致一次发起多个请求
     * */

    cacheMap[cacheKey] = req;
    req.catch(() => {
      cacheMap[cacheKey] = undefined;
    });

    if (expired) {
      req.then(() => {
        setTimeout(() => {
          cacheMap[cacheKey] = undefined;
        }, expired);
      });
    }
    return req;
  } as (...args: Parameters<T>) => ReturnType<T>;
}

interface TypeNext<T = any> {
  (...args: any[]): Promise<void>;
  resolve: (value: T) => void;
  reject: (reason?: any) => void;
}

/** promise轮询 */
export function promisePolling<
  T extends (...args: any[]) => Promise<any>,
  R = Awaited<ReturnType<T>>,
>(
  asyncFunc: T,
  /** asyncFunc.then 回调 */
  callback: (params: { result: R; next: TypeNext<R> }) => void,
  options: {
    /** 轮询步长, 在上一个请求完成之后调用callback, 默认1s, 不会短于单次请求, 最短间隔 max(asyncFunc, step) */
    step?: number;
    /** 重试次数, 失败retry次后停止 */
    retry?: number;
  } = {},
) {
  const { step = 1000, retry } = options;
  let countCatch = 0;
  let params: Parameters<T> = [] as any;

  const next = async function (...args: any[]) {
    /** 如果存在参数, 则保存, 不存在则调用上一次参数 */
    args.length !== 0 && (params = args as Parameters<T>);
    const res = asyncFunc(...(args.length === 0 ? params : args));

    setTimeout(function () {
      res.then((result) => {
        try {
          callback({ result, next });
        } catch (err) {
          next.reject(err);
        }
      });
      res.catch((err) => {
        countCatch += 1;
        // console.log('enter exception', countCatch);
        if (retry === undefined) {
          next.reject?.(err);
          return;
        }
        if (countCatch >= retry) {
          next.reject?.(err);
          return;
        }
        /** 允许重试, 自动next, 这里未扩展catchCallback */
        next();
      });
    }, step);
  } as TypeNext;

  return function (...args: Parameters<T>) {
    return new Promise<R>((resolve, reject) => {
      next.resolve = resolve;
      next.reject = reject;
      next(...args);
    });
  };
}

/**
捕获异常并打印
@ 原先设计是给Form表单的onFinish使用的,因为它不会抛异常
@ 可以简化重复书写try catch
*/
export function catchConsole<T extends (...args: any[]) => ReturnType<T>>(fn: T) {
  return function (...args: Parameters<T>) {
    let result: ReturnType<T>;

    try {
      result = fn(...args);
      (result as any)?.catch((err: any) => console.error(err));
    } catch (err) {
      console.error(err);
      throw err;
    }
    return result;
  };
}

/** 实现自己的includes
 * @ Array自带的includes没有类型保护, 推导实现上不太好用
 */
export function includesInArray<T>(range: readonly T[], param: any): param is T {
  return range.includes(param);
}

/** 用于枚举常量的搜索查找 */
export function findConstEnum<T extends keyof P, P>(dict: P, k: T | string | number) {
  return dict[k as T] || undefined;
}

/** 有效数据转string */
export function transString<T>(inp: T[]): (undefined | string)[];
export function transString<T>(inp: T): undefined | string;
export function transString<T>(inp: T | T[]) {
  if (Array.isArray(inp)) {
    return inp.map((item) => transString(item));
  }
  return inp ? `${inp}` : undefined;
}
