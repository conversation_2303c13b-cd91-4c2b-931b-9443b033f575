import { request } from 'umi';

/** oss文件上传,通过website
 * @deprecated 废弃门户文件上传, 改用 apiUploadFileNew
 */
export function apiOssFileUploadByWebsite(data: { file: File }) {
  const formData = new FormData();

  Object.keys(data).forEach((key) => {
    formData.append(key, data[key as keyof typeof data]);
  });

  return request('/api/website/upload/uploadOne', {
    data: formData,
    method: 'POST',
    headers: {
      'Content-type': 'multipart/form-data',
    },
  }).then(
    (
      res: NsApi.TypeResponseData<{
        fileName: string;
        nameUrl: string;
        fileUrl: string;
      }>,
    ) => {
      if (res?.success === true) {
        return {
          ...res,
          data: res.result,
        };
      }
      return Promise.reject(res);
    },
  );
}
