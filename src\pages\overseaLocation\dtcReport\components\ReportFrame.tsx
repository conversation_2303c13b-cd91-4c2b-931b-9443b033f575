import type { PageContainerProps } from '@ant-design/pro-layout';
import React, { useEffect } from 'react';
import { PageContainer } from '@/components/PageContainer';
import styles from './ReportFrame.less';

type TypeReportFrame = {
  /** 访问资源路径 */
  iframeSrc?: string;
  /** PageContainerProps，默认不展示title  */
  pageContainerConfig?: PageContainerProps;
};
/** iframe 展示 */
function ReportFrame(props: TypeReportFrame) {
  const { iframeSrc, pageContainerConfig } = props;

  return (
    <PageContainer
      {...{
        header: {
          title: '',
        },
        ...pageContainerConfig,
      }}
    >
      <div className={styles['dtc-report-wrapper']}>
        <iframe key={iframeSrc} src={iframeSrc} className="dtc-report-frame" title="iframe" />
      </div>
    </PageContainer>
  );
}

export default ReportFrame;
