import React from 'react';
import { apiMapDictType, EnumUploadFileType, getUploadPropsConfig } from '@/api';
import type { TypeSimpleUploadProps } from '@/components';
import { LoadButton, SearchSelect, SimpleUpload } from '@/components';
import { isOMS, isPortal } from '@/oms-portal-diff-import';
import { imagePreviewIns, RowLayout, TextValue } from '@/views';
import type { FormInstance } from 'antd';
import { Form, Input } from 'antd';
import {
  EnumReturnFileType,
  EnumReturnOrderStatus,
  EnumReturnPackageStatus,
} from '../ReturnOrderEnum';

const FormItem = Form.Item;

/** 其他信息 */
export default function FormOtherInfo() {
  const form = Form.useFormInstance<TypeReturnOrderFormData>();
  const orderStatus = Form.useWatch('orderStatus', form);
  const isClaim = orderStatus === EnumReturnOrderStatus.CLAIMED;
  /** 门户已认领之外全部禁止编辑 */
  const isPortalDisabled = isPortal && isClaim === false;

  return (
    <div>
      <FormItem hidden>
        <TextValue {...{ label: '', name: '' }} />
      </FormItem>
      <RowLayout>
        <FormItem
          {...{
            label: '包裹状态',
            name: 'packageStatus',
            initialValue: EnumReturnPackageStatus.UNKNOWN,
            rules: [],
            children: (
              <SearchSelect
                {...{
                  disabled: isPortal,
                  request: apiMapDictType.returnPackageStatus,
                }}
              />
            ),
          }}
        />
        <FormItem
          {...{
            label: '退货原因',
            name: 'returnReason',
            rules: [{ max: 255 }],
            children: <Input.TextArea disabled={isPortal} />,
          }}
        />
        <FormItem
          {...{
            label: '客户备注',
            name: 'customerRemark',
            rules: [{ max: 255 }],
            children: <Input.TextArea {...{ disabled: isOMS || isPortalDisabled }} />,
          }}
        />
        <FormItem
          {...{
            label: '海外仓附件',
            name: 'fileListOms',
            rules: [],
            children: (
              <SimpleUpload
                {...{
                  ...calcUploadProps(EnumReturnFileType.oms, { form }),
                  disabled: isPortal,
                }}
              />
            ),
          }}
        />
        <FormItem
          {...{
            label: '客户附件',
            name: 'fileListPortal',
            rules: [],
            children: (
              <SimpleUpload
                {...{
                  ...calcUploadProps(EnumReturnFileType.portal, { form }),
                  disabled: isOMS || isPortalDisabled,
                }}
              />
            ),
          }}
        />
      </RowLayout>
    </div>
  );
}

/** 计算文件上传属性样式 */
function calcUploadProps(
  fileType: EnumReturnFileType,
  { form }: { form: FormInstance<TypeReturnOrderFormData> },
) {
  const nameMap = {
    [EnumReturnFileType.oms]: 'fileListOms',
    [EnumReturnFileType.portal]: 'fileListPortal',
  } as const;
  const uploadProps = {
    ...getUploadPropsConfig(),
    data: {
      pathType: EnumUploadFileType.picture,
    },
    allowManual: false,
    allowValidFileStatus: true,
    multiple: true,
    maxCount: 5,
    fileSuffix: ['jpg', 'jpeg', 'png'],
    onPreview(file) {
      if (!file.url) {
        return;
      }
      const fileListOms = form.getFieldsValue()[nameMap[fileType]] || [];

      imagePreviewIns?.open({ imgList: fileListOms, curUrl: file.url });
    },
    onOriginChange(info) {
      getUploadPropsConfig.fileDoneProcess(info.file);
      info.file.fileType = fileType;
    },
    children: <LoadButton>上传</LoadButton>,
  } as TypeSimpleUploadProps<{ fileType: EnumReturnFileType }>;

  return uploadProps;
}

/** 文件合并上传 */
export function fileListMerge(data: TypeReturnOrderFormData) {
  const { fileListOms, fileListPortal } = data;
  const orderFileList = [...(fileListOms || []), ...(fileListPortal || [])];

  return {
    orderFileList,
  };
}

/** 文件解构 */
export function fileListDeconStruct(fileList: NsReturn.TypeOrderFileItem[]) {
  const fileTypeMap: Record<EnumReturnFileType, NsReturn.TypeOrderFileItem[]> = {
    [EnumReturnFileType.oms]: [],
    [EnumReturnFileType.portal]: [],
  };

  fileList.forEach((file) => {
    const item = Object.assign(file, {
      name: file.fileName,
      url: file.filePath,
      status: 'done',
    });

    fileTypeMap[item.fileType!]?.push(item);
  });

  return {
    fileListOms: fileTypeMap[EnumReturnFileType.oms],
    fileListPortal: fileTypeMap[EnumReturnFileType.portal],
  };
}
