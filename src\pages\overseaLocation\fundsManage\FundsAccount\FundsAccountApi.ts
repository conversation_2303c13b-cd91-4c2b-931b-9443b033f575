import { request } from 'umi';

/** 资金账户列表 */
export function apiFundsAccountList(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 客户结算币种 */
      customerCurrency?: string;
    };
  }>,
) {
  return request<NsApi.TypeResponseList<TypeFundsAccountTB[]>>(
    '/zouwu-oms-charge/portal/charge/account/list',
    {
      method: 'POST',
      data,
    },
  ).then((res) => {
    return {
      ...res,
      data: {
        ...res.data,
        records: (res?.data?.records || []).map((item) => ({ ...item, accountId: item.id })),
      },
    };
  });
}

/** 信用额度变更记录 */
export function apiCreditLimitRecords(
  data: NsApi.TypeRequestListQuery<{
    condition: {
      /** 账户id */
      accountId: string;
    };
  }>,
) {
  return request('/zouwu-oms-charge/portal/charge/creditLimitRecord/list', {
    data,
    method: 'POST',
  });
}
