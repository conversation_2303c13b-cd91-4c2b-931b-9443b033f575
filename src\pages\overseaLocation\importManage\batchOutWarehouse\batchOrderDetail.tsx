import { Button, Space, Upload, Table, message, Divider } from 'antd';
import React, { useRef, useState } from 'react';
import { useLocation } from 'umi';
import { Link } from '@portal/hive-sdk';

import I18N from '@/utils/I18N';
import type { ActionType, ProColumns, ProTableProps } from '@/common-import';
import { ProTable } from '@/common-import';
import { PageContainer } from '@/components/PageContainer';
import {
  apiBatchOutWarehouseBatchImportFaceSheet,
  apiBatchOutWarehouseDetailList,
  apiBatchOutWarehouseRemoveOne,
  apiBatchOutWarehouseSubmitRecord,
  apiBatchOutWarehouseUploadFaceSheet,
} from '../importApi';
import { OVERSEA_LOCATION_BATCH_OUT_DETAIL_STATUS_ENUM } from '@/utils/const';
import {
  ListItemShowMore,
  LoadButton,
  ModalUpload,
  SingleUpload,
  ZSearchSelect,
} from '@/pages/overseaLocation/components';
import { ossPathCombine } from '@/pages/overseaLocation/utils';
import { apiMapDictType, useDictTypeValueEnum } from '@/pages/overseaLocation/api';
import { GetPageQuery } from '@/utils/util';

function BatchOrderDetail() {
  const { config, actionRef, selectedRowKeys, asyncBatchSubmit, importRecord, batchId } =
    useConfig();

  return (
    <PageContainer>
      <ProTable
        {...config}
        {...{
          headerTitle: I18N.template(
            I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
              .importOrder,
            {
              val1: importRecord.importNum || 0,
              val2: importRecord.submitNum || 0,
              val3: importRecord.draftNum || 0,
              val4: importRecord.exceptionNum || 0,
            },
          ),
          toolBarRender: () => [
            <LoadButton
              key="load"
              {...{
                type: 'primary',
                disabled: !selectedRowKeys.length,
                onClick: () => asyncBatchSubmit(),
              }}
            >
              {
                I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                  .selectToDistribute
              }
            </LoadButton>,
            // <LoadButton
            //   {...{
            //     type: 'default',
            //     onClick: () => asyncBatchSubmit(true),
            //   }}
            // >
            //   全部下发
            // </LoadButton>,
            <ModalUpload
              key="upload"
              {...{
                title:
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .batchSupplement,
                fileSuffix: ['zip'],
                maxFileSizeMb: 100,
                limitDesc: [
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .pleaseAttachTheAttachment,
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .attachments,
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .accordingToUpload,
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .ifTheAttachment,
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .onlyForNon,
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .foldersAnd,
                ],
                async asyncSubmit({ fileList }) {
                  const res = await apiBatchOutWarehouseBatchImportFaceSheet({
                    recordId: batchId,
                    directory: 'overseaLocation',
                    file: fileList[0].originFileObj!,
                  });

                  function resultRender(param: typeof res['data']) {
                    const { failedCount, succeedCount, totalCount } = param;

                    return (
                      <span>
                        {
                          I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                            .BatchOrderDetail.common
                        }
                        {totalCount}
                        {
                          I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                            .BatchOrderDetail.entriesImporting
                        }
                        <span style={{ color: 'green' }}>{succeedCount}</span>
                        {
                          I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                            .BatchOrderDetail.entriesFailed
                        }
                        <span style={{ color: 'red' }}>{failedCount}</span>
                        {
                          I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                            .BatchOrderDetail.article
                        }
                      </span>
                    );
                  }

                  if (res?.data) {
                    const result = resultRender(res.data);

                    return {
                      result,
                    };
                  }
                },
                onCancelAfterUpload() {
                  actionRef.current?.reload();
                },
              }}
            >
              <Button key="3">
                {
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .batchSupplement
                }
              </Button>
            </ModalUpload>,
          ],
        }}
      />
    </PageContainer>
  );
}

export default BatchOrderDetail;

function useConfig() {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [importRecord, setImportRecord] = useState<TypezOrderImportRecord>({});
  const columns = useColumns();
  const batchId = GetPageQuery().id;
  const asyncBatchSubmit = async (isAll: boolean = false) => {
    /** 批量下发 */
    const res = await apiBatchOutWarehouseSubmitRecord({
      id: batchId,
      orderIds: isAll ? undefined : selectedRowKeys,
    });

    // if (res?.success === true) {
    setSelectedRowKeys([]);
    actionRef?.current?.reload();
    message.success(
      I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
        .issuedSuccessfully,
    );
    // }
  };
  const isAllowChecked = (record: TypezBatchOutWarehouseOrderTB) => {
    /** 只允许勾选未下发订单 */
    return Number(record.state) === 1;
  };
  const config = {
    rowKey: 'id',
    columns,
    actionRef,
    scroll: { x: 'max-content' },
    search: {
      defaultCollapsed: false,
    },
    pagination: {
      position: ['bottomLeft'],
    },
    rowSelection: {
      selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
      alwaysShowAlert: false,
      selectedRowKeys,
      renderCell(checked, record, index, originNode) {
        if (React.isValidElement(originNode)) {
          return React.cloneElement(originNode, {
            disabled: isAllowChecked(record) === false,
          } as any);
        }
      },
      onChange: (keys: string[], selectedRows) => {
        setSelectedRowKeys(selectedRows.filter(isAllowChecked).map((record) => record.id));
      },
    },
    request: async (params) => {
      const { pageSize, current: currentPage, ...args } = params;
      const query = {
        currentPage,
        pageSize,
        condition: {
          ...args,
          id: batchId,
        },
      } as Parameters<typeof apiBatchOutWarehouseDetailList>[0];
      const {
        data: { detailConditionPage, orderImportRecord },
      } = await apiBatchOutWarehouseDetailList(query);

      setImportRecord(orderImportRecord);

      return {
        data: detailConditionPage.records,
        success: true,
        total: detailConditionPage.totalSize,
      };
    },
  } as ProTableProps<any, any>;

  return {
    config,
    actionRef,
    selectedRowKeys,
    asyncBatchSubmit,
    importRecord,
    batchId,
  };
}

function useColumns() {
  const { dictTypeMap } = useDictTypeValueEnum(['DcDispatchServiceType', 'DcDispatchServiceName']);
  const columns = [
    // { title: '主键id', dataIndex: 'id', search: false },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse.EnterWarehouseList.applicationNo,
      dataIndex: 'podOrderNo',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .deliveryService1,
      dataIndex: 'dispatchServiceType',
      search: true,
      order: 1,
      formItemProps: {
        labelCol: 4,
        name: 'dispatchServiceType',
      },
      render: (dom, record) => {
        const delivery = record?.outboundChildList?.[0]?.delivery || {};
        const dispatchServiceType = delivery.dispatchServiceType || '';
        const orderFile = delivery.orderFile;

        return (
          <div>
            <div>{dictTypeMap.DcDispatchServiceType[dispatchServiceType]}</div>
            {orderFile ? (
              <a
                target="_blank"
                href={ossPathCombine(orderFile, dispatchServiceType === 4)}
                rel="noreferrer"
              >
                {delivery?.orderFileName || orderFile}
              </a>
            ) : null}
          </div>
        );
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .deliveryService,
      dataIndex: 'dispatchServiceName',
      valueType: 'select',
      search: false,
      renderText: (dom, record) => {
        const { customerChannelCode, dispatchServiceName } =
          record?.outboundChildList?.[0]?.delivery || {};

        return customerChannelCode || dictTypeMap.DcDispatchServiceName[dispatchServiceName!];
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .receivingContact1,
      dataIndex: 'recipientName',
      search: false,
      renderText: (dom, record) => record?.outboundChildList?.[0]?.express?.recipientName,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .toAddress,
      dataIndex: 'recipientAddress1',
      search: false,
      renderText: (dom, record) => {
        const express = record?.outboundChildList?.[0]?.express || {};
        const {
          recipientCountryName,
          recipientProvinceName,
          recipientCityName,
          recipientAddress1,
        } = express;
        const showText = [
          recipientCountryName,
          recipientProvinceName,
          recipientCityName,
          recipientAddress1,
        ]
          .filter(Boolean)
          .join('-');

        return showText;
      },
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .receivingContact,
      dataIndex: 'recipientPhoneNumber1',
      search: false,
      renderText: (dom, record) => record?.outboundChildList?.[0]?.express?.recipientPhoneNumber1,
    },
    {
      title: I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.GoodsTableForm.commodity1,
      dataIndex: 'sku',
      search: false,
      render: (dom, record) =>
        record?.outboundChildList?.[0]?.goodsList?.map((item) => (
          <div key={item.sku}>{item.sku}</div>
        )),
    },
    // {
    //   title: '商品名称',
    //   dataIndex: 'goodsName',
    //   search: false,
    //   renderText: (dom, record) => record?.outboundChildList?.[0]?.goodsList?.[0]?.goodsName,
    // },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .totalNumberOfGoods,
      dataIndex: 'totalQuantity',
      search: false,
      // renderText: (dom, record) => record?.outboundChildList?.[0]?.goodsList?.[0]?.totalQuantity,
      renderText: (dom, record) =>
        record?.outboundChildList?.[0]?.goodsList?.reduce(
          (prev, curItem) => prev + (curItem.totalQuantity || 0),
          0,
        ),
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .shipmentOrganization,
      dataIndex: 'deliveryWarehouse',
      search: false,
      renderText: (dom, record) => {
        const { deliveryWarehouseCode, deliveryWarehouseName } =
          record?.outboundChildList?.[0] || {};

        const showText = [deliveryWarehouseCode].filter(Boolean).join('-');

        return showText;
      },
    },
    {
      title: I18N.Src__Pages__Home.Index.businessType,
      dataIndex: 'businessType',
      valueType: 'select',
      search: true,
      // valueEnum: WAREHOUSE_BUSINESS_TYPE_BATCH_OUT,
      request: apiMapDictType.outboundBusinessType,
      renderFormItem: () => {
        return (
          <ZSearchSelect
            {...{
              request: apiMapDictType.outboundBusinessType,
              include: ['201' /** 标准出库 */, '203' /** TOB出库 */],
            }}
          />
        );
      },
      order: 3,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action.LogisticsTableForm
          .logisticsTracking,
      dataIndex: 'trackingNo',
      search: false,
      renderText: (dom, record) => record?.outboundChildList?.[0]?.delivery?.trackingNo,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .orderSource,
      dataIndex: 'orderSourcePlatformName',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .platformSeller,
      dataIndex: 'platformSellerId',
      search: false,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
          .postalCode,
      dataIndex: 'recipientPostcode',
      search: false,
      renderText: (dom, record) => record?.outboundChildList?.[0]?.express?.recipientPostcode,
    },
    {
      title: I18N.Src__Pages__Message__Notice.Index.state,
      dataIndex: 'state',
      search: true,
      fixed: 'right',
      valueEnum: OVERSEA_LOCATION_BATCH_OUT_DETAIL_STATUS_ENUM,
      order: 2,
      width: 80,
    },
    {
      title:
        I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail.result,
      dataIndex: 'exceptionMsg',
      search: false,
      fixed: 'right',
      width: 160,
      render: (dom, record) => {
        const errorList = (record.exceptionMsg || null)?.split?.(';') || [];

        return (
          <ListItemShowMore
            {...{
              dataList: errorList.map((item) => ({
                label: I18N.template(
                  I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .error1,
                  { val1: item },
                ),
                label2: item,
              })),
              columns: [
                {
                  title:
                    I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                      .BatchOrderDetail.error,
                  dataIndex: 'label2',
                },
              ],
              amount: 1,
            }}
          />
        );
      },
    },
    {
      title: I18N.Src__Pages__Common__Template.Index.operation,
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      key: 'action',
      search: false,
      width: 180,
      render: (text, record, index, action) => {
        /** 是否为客户面单 */
        const isCustomer = record?.outboundChildList?.[0]?.delivery?.dispatchServiceType === 1;

        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <SingleUpload
              {...{
                fileSuffix: ['pdf', 'png'],
                maxFileSizeMb: 20,
                uploadRequest: async (params) => {
                  const res = await apiBatchOutWarehouseUploadFaceSheet({
                    outboundOrderId: record.id!,
                    directory: 'overseaLocation',
                    file: params.file as Blob,
                  });

                  // if (res?.success === true) {
                  action?.reload();
                  message.success(I18N.Src__Pages__Order__Components.Modals.uploadSucceeded);
                  // }
                },
                buttonProps: {
                  /* 已下发 或者 不是客户面单 */
                  disabled: record?.state === 2 || !isCustomer,
                },
              }}
            >
              {record?.outboundChildList?.[0]?.delivery?.orderFile
                ? I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .updateFaceSheet
                : I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse.BatchOrderDetail
                    .supplementarySheet}
            </SingleUpload>
            <LoadButton disabled={record?.state === 2 /** 已下发不可编辑 */}>
              <Link
                to={`/oversea-location/out-warehouse/outbound-edit?id=${record.id}&orderStatus=${record?.orderStatus}`}
              >
                {I18N.Src__Pages__Common__Template.Index.edit}
              </Link>
            </LoadButton>
            <LoadButton
              {...{
                disabled: [/** 已下发 */ 2].includes(record.state) === true,
                popconfirmProps: {
                  title:
                    I18N.Src__Pages__OverseaLocation__ImportManage__BatchOutWarehouse
                      .BatchOrderDetail.confirmDeletion,
                },
                onClick: async () => {
                  const res = await apiBatchOutWarehouseRemoveOne({ id: record.id! });

                  //   if (res?.success === true) {
                  action?.reload();
                  message.success(I18N.Src__Pages__Common__Template.Index.deletionSucceeded);
                  //   }
                },
              }}
            >
              {I18N.Src__Pages__Common__Template.Index.delete}
            </LoadButton>
          </Space>
        );
      },
    },
  ] as ProColumns<TypezBatchOutWarehouseOrderTB>[];

  return columns;
}
