import React from 'react';
import './FormProCard.less';
import { ProCard } from '@/common-import';
import classnames from 'classnames';
import { compUtils } from '@/utils';
import type { ProCardProps } from '@ant-design/pro-card';

export type TypeFormProCardProps = ProCardProps & {
  /** 默认无风格,  line-blue: 左侧会有一个小蓝标 */
  titleStyle?: 'line-blue';
};

export default function FormProCard(inProps: TypeFormProCardProps) {
  const { titleStyle, ...props } = inProps;
  const defaultProps = {
    className: classnames('form-pro-card', titleStyle && `form-pro-card-title__${titleStyle}`),
    /** 标题存在时默认分割线存在 */
    headerBordered: props?.title ? true : false,
  };

  return <ProCard {...compUtils.propsMerge(defaultProps, props)} />;
}
