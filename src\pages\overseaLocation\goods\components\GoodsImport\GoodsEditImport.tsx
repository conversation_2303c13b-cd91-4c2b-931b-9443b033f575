import { Button, message, Modal } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import ProTable from '@ant-design/pro-table';
import I18N from '@/utils/I18N';
import { LoadButton, ModalUpload } from '@/pages/overseaLocation/components';
import css from './GoodsImport.less';
import type { apiGoodsBatchImport } from '../../goodsApi';
import { apiGoodsEditBatchImport, apiGoodsImportTemplateDownload } from '../../goodsApi';
import { apiUploadFileNew, EnumUploadFileType } from '@/pages/overseaLocation/api';

type TypeProps = {
  submitSuccessCB?: () => void;
};

/** 商品批量编辑导入 */
export default function GoodsEditImport(props: TypeProps) {
  const errorModalRef = useRef<any>() as TypeModalErrorProps['modalRef'];

  return (
    <>
      <ModalUpload
        {...{
          className: css.GoodsImport,
          fileSuffix: ['xlsx', 'xls'],
          modalProps: {
            title: '批量编辑商品',
          },
          btnExtra: () => (
            <LoadButton
              onClick={async () => {
                await apiGoodsImportTemplateDownload();
              }}
            >
              {
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols14
              }
            </LoadButton>
          ),
          limitDesc: [
            <>
              <span className={css.tips}>长/宽/高/重：不可修改；</span>
              导入时不会生效；
            </>,
            <>
              修改商品时，维护的数据为
              <span className={css.tips}>全覆盖更新</span>
              ，若修改投保设置、电商平台设置请维护全量数据。
            </>,
            '单次修改商品数量限制500条，超过数量请分批操作。',
          ],
          async asyncSubmit({ fileList, modalControl }) {
            const file = fileList[0];
            const originFile = file.originFileObj;
            const { data: uploadRes } = await apiUploadFileNew({
              pathType: EnumUploadFileType.excel,
              file: originFile!,
            });

            const filePath = uploadRes.filePath;
            const { data } = await apiGoodsEditBatchImport({ filePath });

            if (data?.records?.length !== 0) {
              errorModalRef?.current?.open(data);
            } else {
              message.success('批量编辑商品成功');
              props.submitSuccessCB?.();
            }
            modalControl.close();
          },
        }}
      >
        <Button>批量编辑</Button>
      </ModalUpload>
      <ModalError modalRef={errorModalRef} />
    </>
  );
}

type TypeErrorData = Awaited<ReturnType<typeof apiGoodsBatchImport>>['data'];
type TypeModalErrorProps = {
  modalRef?: React.MutableRefObject<{
    open: (data: TypeErrorData) => void;
  }>;
};

/** 导入错误信息展示 */
function ModalError(props: TypeModalErrorProps) {
  const [visible, setVisible] = useState(false);
  const dataRef = useRef<TypeErrorData>();
  const { records, totalSize } = dataRef.current || {};

  useImperativeHandle(props.modalRef, () => ({
    open: (data) => {
      dataRef.current = data;
      setVisible(true);
    },
  }));

  return (
    <Modal
      {...{
        title:
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols4,
        width: 680,
        visible,
        maskClosable: false,
        keyboard: false,
        cancelText: I18N.Src__Pages__OverseaLocation__Components__ModalUpload.ModalUpload.close,
        okButtonProps: {
          style: { display: 'none' },
        },
        className: css.GoodsImport,
        onCancel() {
          setVisible(false);
        },
      }}
    >
      <div className={css.errorTips}>
        {I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport.common}
        {totalSize}
        {
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols3
        }
        <span className={css.tips}>{records?.length}</span>
        {
          I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
            .chineseSymbols2
        }
      </div>
      <ProTable<TypeErrorData['records'][number]>
        {...{
          cardProps: false,
          search: false,
          toolBarRender: false,
          dataSource: records,
          scroll: { x: '100%' },
          pagination: {
            showSizeChanger: false,
            pageSize: 10,
          },
          columns: [
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .serialNo,
              dataIndex: 'index',
            },
            { title: 'SKU', dataIndex: 'sku' },
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols1,
              dataIndex: 'recordLine',
            },
            {
              title:
                I18N.Src__Pages__OverseaLocation__Goods__Components__GoodsImport.GoodsImport
                  .chineseSymbols,
              dataIndex: 'message',
              render(dom, record) {
                return (
                  <>
                    {record.message
                      .split(';')
                      .filter(Boolean)
                      .map((item) => (
                        <div key={item}>{item};</div>
                      ))}
                  </>
                );
              },
            },
          ],
        }}
      />
    </Modal>
  );
}
