import { promiseCacheApi } from '@/utils';
import { request } from 'umi';

/** ============================================================== */
/*
    此文件不再新增仓库接口, 统一集成到 src\pages\pagesApi\warehouseApi.ts
*/

/** 物理仓仓库下拉列表, 默认停启用都会返回
 * @OMS侧叫apiWarehouseGetList, 历史原因所以名字不统一
 * @仅需要启用的统一使用 apiActualWarehouseOptions */
export function apiQueryWarehouseOptions(
  data: {
    /** 仓库code和name搜索条件 */
    queryParam?: string;
    /** 仓库类型 */
    warehouseType?: number;
    /** 仓库是否启用, 默认全部 */
    warehouseEnable?: boolean;
    /** 是否虚拟仓,  默认仅搜索物理仓 */
    virtual?: boolean;
    /** 客户公司id, 仅OMS侧有效 */
    companyId?: string;
  } = {},
  options: {
    /** value取值名称, 需要显式声明 */
    valueName: 'id' | 'code';
  },
) {
  return request<
    NsApi.TypeResponseData<{
      records: {
        /** 仓库id */
        id: string;
        /** 仓库名称 */
        name: string;
        /** 仓库code */
        code: string;
        /** 仓库类型 */
        warehouseType?: number;
        /** 币种 */
        currency?: string;
        /** 运营主体id */
        companyId?: string;
        /** 运营主体名称 */
        companyName?: string;
        /** 是否启用 */
        warehouseEnable?: boolean;
      }[];
    }>
  >('/zouwu-oms-system/portal/warehouse/list', {
    method: 'POST',
    data: {
      currentPage: 1,
      pageSize: 1000,
      condition: {
        /** 默认仅搜索物理仓 */
        virtual: false,
        ...data,
        /** 后端新版接口queryParam参数变更为codeOrName */
        codeOrName: data?.queryParam,
      },
    },
  }).then((res) => {
    const { valueName = 'code' } = options;

    return (res?.data?.records || []).map((item) => ({
      ...item,
      key: item.id,
      warehouseId: item.id,
      warehouseCode: item.code,
      warehouseName: item.name,
      value: item[valueName],
      // label: `${item.code}-${item.name}`,
      /** 产品要求这个接口展示仓库code, 名称会暴露不必要的信息给客户 */
      label: item.code,
    }));
  });
}
