import { trim } from 'lodash';
import type { Rule } from 'rc-field-form/lib/interface';
import I18N from '@/utils/I18N';
import REGEX from './regex';

/**
 * 通用验证规则
 */

export const CommonRulesMap = {
  commonStrVerify(len: number, isRequired: boolean = false) {
    return {
      type: 'string',
      required: isRequired,
      max: len,
      transform(value: any) {
        return trim(value);
      },
    } as Rule;
  },
  commonNumAndLetter: {
    type: 'string',
    message: I18N.Src__Utils.Rules.onlyWordsAreSupported,
    pattern: REGEX.NUMBER_AND_ENGLISH,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonNoChinese: {
    type: 'string',
    message: I18N.Src__Utils.Rules.notSupported,
    pattern: REGEX.NO_CHINESE,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonNoSpace: {
    type: 'string',
    message: I18N.Src__Utils.Rules.emptyIsNotSupported,
    pattern: REGEX.NO_SPACE,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonNoSomeSpecial: {
    type: 'string',
    message: I18N.Src__Utils.Rules.specialFeaturesAreNotSupported,
    pattern: REGEX.NO_SOME_SPECIAL,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonFloatTwo: {
    type: 'string',
    message: I18N.Src__Utils.Rules.supportInteger2,
    pattern: REGEX.REGX_NUM_AND_FLOAT_TWO,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonFloatThree: {
    type: 'string',
    message: I18N.Src__Utils.Rules.supportInteger1,
    pattern: REGEX.REGX_NUM_AND_FLOAT_THREE,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonFloatFour: {
    type: 'string',
    message: I18N.Src__Utils.Rules.supportInteger,
    pattern: REGEX.REGX_NUM_AND_FLOAT_FOUR,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonPositiveInt: {
    type: 'string',
    message:
      I18N.Src__Pages__OverseaLocation__EnterWarehouse__Action__Components.LogisticsInfo
        .pleaseEnterAPositive1,
    pattern: REGEX.POSITIVE_INGETER,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
  commonUrl: {
    type: 'string',
    message: I18N.Src__Utils.Rules.pleaseEnterAPositive,
    pattern: REGEX.HREF_RE,
    transform(value: any) {
      return trim(value);
    },
  } as Rule,
};
