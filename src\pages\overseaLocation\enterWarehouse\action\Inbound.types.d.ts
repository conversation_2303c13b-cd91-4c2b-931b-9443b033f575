/** 入库单草稿详情 */
type TypeInboundDraftDetail = {
  /** 单据流程 */
  orderStatusRecord?: {
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
    deleted?: boolean;
    /** 订单状态 1-客户建单，2-提交审核，3-补充头程物流信息，4-入库预报通知，5-运输中，6-签收中，7-履约完结，8-费用确认，9-结单 */
    orderStatus?: number;
    /** 申请单号 */
    podOrderNo?: string;
  }[];
  /** 订单信息 */
  order?: {
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
    deleted?: boolean;
    /** 备注 */
    remark?: string;
    /** 订单环节 1-草稿-待提交，2-缺少物流信息，3-头程运输中，4-运输异常，5-客户确认部分签收，6-履约完结，7-结单 */
    orderLinkType?: number;
    /** 订单状态 1-客户建单，2-提交审核，3-补充头程物流信息，4-入库预报通知，5-运输中，6-签收中，7-履约完结，8-费用确认，9-结单 */
    orderStatus?: number;
    /** 单据状态-value */
    orderStatusValue?: string;
    /** 下发类型(1.系统 2.人工) */
    issuanceType?: number;
    /** 子单id */
    orderChildId?: number;
    /** 实际签收时间 */
    receivedTime?: string;
    /** 预期交付时间 */
    expectTime?: string;
    /** 预计发货时间（入库） */
    expectSendTime?: string;
    /** 船公司 */
    shippingCompany?: string;
    /** 船公司 */
    shippingCompanyCode?: string;
    /** 目标仓库类型 1-标准海外仓，2-海外中转仓 */
    targetWarehouseType?: number;
    /** 目标仓库类型-vlaue */
    targetWarehouseTypeValue?: string;
    /** 目标仓库名称 */
    targetWarehouseName?: string;
    /** 订单来源 1-门户，2-erp，3-电商系统 */
    orderSource?: number;
    /** 订单来源-value */
    orderSourceValue?: string;
    /** 目标仓库id */
    targetWarehouseId?: number;
    /** 仓库编码 */
    targetWarehouseCode?: string;
    /** 业务类型 1-环世头程入库，2-代发头程入库，3-FBA中转入库，4-退货入库 */
    businessType?: number;
    /** 业务类型-value */
    businessTypeValue?: string;
    /** 申请人 */
    applyUser?: string;
    /** 申请公司 */
    companyName?: string;
    /** 申请公司id */
    companyId?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 销售公司id */
    sellerCompanyId?: string;
    /** 销售公司名称 */
    sellerCompanyName?: string;
    /** 销售员id */
    sellerId?: string;
    /** 销售员名称 */
    sellerName?: string;
    /** 发货类型 */
    sendType?: string;
    /** 订单类型 1-入库单，2-出库单 */
    orderType?: number;
    /** 订单类型-value */
    orderTypeValue?: string;
    /** 目标公司 */
    targetCompany?: string;
    /** 件数 */
    boxTotalQuantity?: number;
    /** 物流信息 */
    logisticsResultDTO?: {
      /** 主键 id */
      id?: string;
      /** 合作者 code */
      partnerCode?: string;
      /** 创建人类型 */
      createByType?: string;
      /** 创建人 ID */
      createById?: number;
      /** 创建人名 */
      createByName?: string;
      /** 创建时间 */
      createDate?: string;
      /** 创建人类型 */
      updateByType?: string;
      /** 更新人 ID */
      updateById?: number;
      /** 更新人 */
      updateByName?: string;
      /** 更新时间 */
      updateDate?: string;
      /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
      deleted?: boolean;
      /** 海运集装箱号 */
      containerNo?: string;
      /** 海运集装箱型号 */
      containerModel?: string;
      /** 集装箱数量 */
      containerNum?: number;
      /** 提单号 */
      logisticsNo?: string;
      /** 物流渠道商 1-UPS，2-USPS ... */
      logisticsProvider?: string;
      /** 物流渠道商 */
      logisticsChannelProvider?: string;
      /** 起运港 */
      pol?: string;
      /** 目的港 */
      pod?: string;
    };
    /** 商品总件数 */
    goodsNum?: number;
    /** 商品总体积 */
    goodsVolume?: number;
    /** 商品总体积 */
    goodsWeight?: number;
    /** 仓库供应商code */
    warehouseCompanyCode?: string;
    /** 仓库供应商名称 */
    warehouseCompanyName?: string;
    /** 追踪单号 */
    trackIngNos?: string[];
    /** 主追踪单号 */
    masterTrackIngNo?: string;
    /** 包裹信息 */
    orderPackageBOList?: {
      /** 主键 id */
      id?: number;
      /** 申请单号 */
      podOrderNo?: string;
      /** 履约单号 */
      complianceNo?: string;
      /** 包裹编号 */
      packageCode?: string;
      /** 包裹长度（cm） */
      packageLength?: number;
      /** 包裹宽度（cm） */
      packageWidth?: number;
      /** 包裹高度（cm） */
      packageHeight?: number;
      /** 包裹重量（kg） */
      packageWeight?: number;
      /** 投保金额 */
      insuredAmount?: number;
      /** 包裹商品列表 */
      packageGoodsList?: {
        /** id */
        id?: number;
        /** 申请单号 */
        podOrderNo?: string;
        /** 履约单号 */
        complianceNo?: string;
        /** SKU */
        sku?: string;
        /** 商品类型 1-商品，2-组合商品，3-子商品 */
        goodsType?: number;
        /** 关联组合商品sku（商品类型为子商品时必填） */
        combinedGoodsSku?: string;
        /** 商品图片 */
        goodsPicture?: string;
        /** 商品名称 */
        goodsName?: string;
        /** 商品英文名称 */
        goodsEnName?: string;
        /** 平台编码 */
        platformCode?: string;
        /** 商品申报价值 */
        goodsValue?: number;
        /** 价值单位 */
        valueUnit?: string;
        /** 商品净重 */
        goodsWeight?: number;
        /** 重量单位 */
        weightUnit?: string;
        /** 商品长度 */
        goodsLength?: number;
        /** 商品宽度 */
        goodsWidth?: number;
        /** 商品高度 */
        goodsHeight?: number;
        /** 体积单位 */
        volumeUnit?: string;
        /** 总件数 */
        totalQuantity?: number;
        /** 好件数 */
        goodQuantity?: number;
        /** 总箱数 */
        boxNum?: number;
        /** 待执行数量（出库） */
        pendingQuantity?: number;
        /** 已执行数量(出库) */
        executedQuantity?: number;
        /** 备注 */
        remark?: string;
        /** 已执行数量(出库) */
        upc?: string;
        /** 备注 */
        asin?: string;
        /** 修改时间 */
        updateDate?: string;
        /** 签收类型 */
        receivedType?: number;
        /** 删除标志 */
        deleted?: boolean;
        /** 是否带电 */
        electrified?: boolean;
      }[];
      /** 子追踪单号 */
      trackingNo?: string;
      /** 物流状态 */
      trackNode?: string;
      /** label附件ID */
      fileId?: number;
      /** 创建人id */
      createById?: string;
      /** 创建人名称 */
      createByName?: string;
      /** 创建时间 */
      createDate?: string;
      /** 更新人id */
      updateById?: string;
      /** 更新人名称 */
      updateByName?: string;
      /** 更新时间 */
      updateDate?: string;
      /** 公司唯一标识 */
      companyId?: string;
      /** 公司名称 */
      companyName?: string;
    }[];
    /** 仓库实际BUId */
    warehouseActualBUId?: string;
    /** 仓库实际BU */
    warehouseActualBU?: string;
    /** 仓库实际BU名称 */
    warehouseActualBUName?: string;
    /** 仓库实际au */
    warehouseActualAU?: string;
    /** 仓库实际au名称 */
    warehouseActualAUName?: string;
    /** 快递实际BUId */
    expressActualBUId?: string;
    /** 快递实际BU */
    expressActualBU?: string;
    /** 快递实际BU名称 */
    expressActualBUName?: string;
    /** 快递实际AU */
    expressActualAU?: string;
    /** 快递实际AU名称 */
    expressActualAUName?: string;
  };
  /** 物流信息-物流列表 */
  orderLogistics?: {
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
    deleted?: boolean;
    /** 海运集装箱号 */
    containerNo?: string;
    /** 海运集装箱型号 */
    containerModel?: string;
    /** 物流追踪单号 */
    logisticsNo?: string;
    /** 物流渠道商 */
    logisticsProvider?: string;
    /** 总托盘数 */
    totalTrayNum?: number;
    /** 物流渠道商-value */
    logisticsProviderValue?: string;
    /** 物流渠道商 */
    logisticsChannelProvider?: string;
    /** 物流渠道商-value */
    logisticsChannelProviderValue?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 物流状态 */
    currentStatus?: string;
    /** 物流节点 具体的物流节点数据 */
    nodeList?: {
      /** 节点代码 */
      code?: string;
      /** 节点顺序 */
      rank?: number;
      /** 节点名称 */
      name?: string;
      /** 节点准备好的状态描述 */
      readyStatus?: string;
      /** 节点未准备好的状态描述 */
      notReadyStatus?: string;
      /** 节点时间 */
      time?: string;
      /** 节点是否点亮 */
      active?: boolean;
      /** 是否预计时间1:是0:否 */
      isest?: boolean;
      /** 节点类型POL:头程，POD尾程 */
      type?: string;
    }[];
    /** 总箱数 */
    boxTotalQuantity?: number;
  }[];
  /** 单据明细-产品清单 */
  orderGoods?: {
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
    deleted?: boolean;
    /** 签收类型 1-未签收，2-部分签收，3-已签收，4-异常签收 */
    receivedType?: number;
    /** 签收类型-value */
    receivedTypeValue?: string;
    /** 混箱id */
    boxUniqueId?: string;
    /** 装箱类型  1混箱，2整箱 */
    boxType?: string;
    /** 装箱类型  1混箱，2整箱 */
    boxTypeValue?: string;
    /** 履约类型：1默认 2，人工确认 */
    fulfillmentType?: number;
    /** 履约类型：1默认 2，人工确认 */
    fulfillmentTypeValue?: string;
    /** 箱唛号 */
    shippingMarkNo?: string;
    /** 执行数 */
    receivedQuantity?: number;
    /** 到仓数量 */
    reachedQuantity?: number;
    /** 总件数 */
    totalQuantity?: number;
    /** 单箱数量 */
    quantityPerBox?: number;
    /** 保质期 */
    shelfLife?: string;
    /** 体积单位 */
    volumeUnit?: string;
    /** 产品体积 */
    goodsVolume?: number;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 产品净重 */
    goodsWeight?: number;
    /** 价值单位 */
    valueUnit?: string;
    /** 产品价值 */
    goodsValue?: number;
    /** 产品描述 */
    goodsDescribe?: string;
    /** 产品名称 */
    goodsName?: string;
    /** 产品图片 */
    goodsPicture?: string;
    /** SKU */
    sku?: string;
    /** 履约单号 */
    complianceNo?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 未执行数量 */
    notReceivedQuantity?: number;
    /** 执行类型 */
    executeTypeValue?: string;
    /** 订单类型 */
    orderType?: number;
    /** 总箱数 */
    boxQuantity?: number;
    /** 履约单状态 120-未签收,121-部分签收,122-完全签收,123-履约完结,124-结单，取消 */
    businessStatus?: number;
    /** 履约单状态 映射值 */
    businessStatusValue?: string;
  }[];
  /** 单据明细-仓库履约 */
  orderFulfillment?: {
    /** 主键 id */
    id?: string;
    /** 合作者 code */
    partnerCode?: string;
    /** 创建人类型 */
    createByType?: string;
    /** 创建人 ID */
    createById?: number;
    /** 创建人名 */
    createByName?: string;
    /** 创建时间 */
    createDate?: string;
    /** 创建人类型 */
    updateByType?: string;
    /** 更新人 ID */
    updateById?: number;
    /** 更新人 */
    updateByName?: string;
    /** 更新时间 */
    updateDate?: string;
    /** 逻辑删除标志
0 - 未删除 1 - 已删除 */
    deleted?: boolean;
    /** 签收类型 1-未签收，2-部分签收，3-已签收，4-异常签收 */
    receivedType?: number;
    /** 签收类型-value */
    receivedTypeValue?: string;
    /** 混箱id */
    boxUniqueId?: string;
    /** 装箱类型  1混箱，2整箱 */
    boxType?: string;
    /** 装箱类型  1混箱，2整箱 */
    boxTypeValue?: string;
    /** 履约类型：1默认 2，人工确认 */
    fulfillmentType?: number;
    /** 履约类型：1默认 2，人工确认 */
    fulfillmentTypeValue?: string;
    /** 箱唛号 */
    shippingMarkNo?: string;
    /** 执行数 */
    receivedQuantity?: number;
    /** 到仓数量 */
    reachedQuantity?: number;
    /** 总件数 */
    totalQuantity?: number;
    /** 单箱数量 */
    quantityPerBox?: number;
    /** 保质期 */
    shelfLife?: string;
    /** 体积单位 */
    volumeUnit?: string;
    /** 产品体积 */
    goodsVolume?: number;
    /** 商品长度 */
    goodsLength?: number;
    /** 商品宽度 */
    goodsWidth?: number;
    /** 商品高度 */
    goodsHeight?: number;
    /** 重量单位 */
    weightUnit?: string;
    /** 产品净重 */
    goodsWeight?: number;
    /** 价值单位 */
    valueUnit?: string;
    /** 产品价值 */
    goodsValue?: number;
    /** 产品描述 */
    goodsDescribe?: string;
    /** 产品名称 */
    goodsName?: string;
    /** 产品图片 */
    goodsPicture?: string;
    /** SKU */
    sku?: string;
    /** 履约单号 */
    complianceNo?: string;
    /** 申请单号 */
    podOrderNo?: string;
    /** 未执行数量 */
    notReceivedQuantity?: number;
    /** 执行类型 */
    executeTypeValue?: string;
    /** 订单类型 */
    orderType?: number;
    /** 总箱数 */
    boxQuantity?: number;
    /** 履约单状态 120-未签收,121-部分签收,122-完全签收,123-履约完结,124-结单，取消 */
    businessStatus?: number;
    /** 履约单状态 映射值 */
    businessStatusValue?: string;
    /** 损坏数量 */
    badQuantity?: number;
    /** 正常数量 */
    goodQuantity?: number;
    /** 异常数量 */
    errorQuantity?: number;
    /** 签收时间, 系统默认时区 */
    signTime?: string;
  }[];
  /** 物流信息-物流运输履约 */
  logisticsPerformance?: {
    /** 预期交付时间 */
    expectTime?: string;
    /** 海外仓港口提货服务 */
    overseasService?: string;
    /** 物流运输服务 */
    logisticsService?: string;
  };
  /** 收件人 */
  addresseeInfo?: {
    /** 国家编码 */
    country?: number;
    /** 省/州 */
    province?: number;
    /** 城市 */
    city?: number;
    /** 详细地址: */
    defaultAddress?: string;
    /** 详细地址2: */
    remarkAddress?: string;
    /** 公司 */
    companyId?: string;
    /** 姓名 */
    contactPerson?: string;
    /** 电话 */
    contactPersonTel?: string;
    /** 邮编 */
    postalCode?: string;
  };
  /** 收件人 */
  boxList?: NsInbound.TypeLogisticsBoxInfo[];
};
