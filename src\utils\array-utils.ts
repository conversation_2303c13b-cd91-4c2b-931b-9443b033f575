/** 根据下标给数组添加key */
export function arrayAddKey<T>(data?: T[], keyName?: keyof T) {
  if (Array.isArray(data)) {
    return data.map((item, i) => ({
      ...item,
      key: keyName ? item[keyName] : i,
    }));
  }
  return data;
}

/** 数组布尔值过滤, 带类型推导 */
export function arrayFilterBoolean<T>(arr: T[]) {
  return arr.filter(Boolean) as Exclude<T, false | undefined | null>[];
}

/** 数组转组数Item对象 */
export function arrayToItemMap<Item, K extends keyof Item>(arr: Item[], key: K) {
  const obj = {} as Record<string, Item | undefined>;

  if (Array.isArray(arr) === false) {
    return {};
  }

  arr.forEach((item) => {
    obj[`${item?.[key]}`] = item;
  });

  return obj;
}
